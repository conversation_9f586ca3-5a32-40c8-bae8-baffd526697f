<template>
  <div class="archivesEditContent">
    <div class="item">
      <div class="title"><span>基础信息</span></div>
      <div>
        <el-form :inline="true" label-width="140px" :model="query">
          <el-form-item class="formItem" prop="code" label="商家编码:" :rules="[
              { required: false, message: '请填写商家编码', trigger: 'blur' },
            ]">
            <el-input :disabled="isEdit" clearable style="width: 200px" v-model="query.code" placeholder="请填写商家编码"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="name" label="商家名称:" :rules="[
              { required: true, message: '请填写商家名称', trigger: 'blur' },
            ]">
            <el-input :disabled="isEdit" clearable style="width: 200px" v-model="query.name" placeholder="请填写商家名称"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="identifyCode" label="商家识别码:" :rules="[
              { required: true, message: '请填写商家识别码', trigger: 'blur' },
            ]">
            <el-input :disabled="isEdit" clearable style="width: 200px" v-model="query.identifyCode" placeholder="请填写商家识别码"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="socialCreditCode" label="社会统一信用代码:" :rules="[
              {
                required: true,
                message: '请填写社会统一信用代码',
                trigger: 'blur',
              },
            ]">
            <el-input :disabled="isEdit" clearable style="width: 200px" v-model="query.socialCreditCode" placeholder="请填写社会统一信用代码"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="legalPerson" label="法定代表人:" :rules="[
              { required: false, message: '请填写法定代表人', trigger: 'blur' },
            ]">
            <el-input clearable :disabled="isEdit" style="width: 200px" v-model="query.legalPerson" placeholder="请填写法定代表人"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="ceoName" label="负责人:" :rules="[
              { required: true, message: '请填写负责人', trigger: 'blur' },
            ]">
            <el-input clearable :disabled="isEdit" style="width: 200px" v-model="query.ceoName" placeholder="请填写负责人"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="ceoMobile" label="负责人手机:" :rules="[
              { required: true, message: '请填写负责人手机', trigger: 'blur' },
            ]">
            <el-input clearable :disabled="isEdit" style="width: 200px" v-model="query.ceoMobile" placeholder="请填写负责人手机"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="region" label="所在区域:" :rules="[
              { required: true, message: '请选择所在区域', trigger: 'blur' },
            ]">
            <el-select :disabled="isEdit" style="width: 200px" v-model="query.region" placeholder="请选择所在区域">
              <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item class="formItem" prop="registerAddress" label="注册地址:" :rules="[
              { required: true, message: '请填写注册地址', trigger: 'blur' },
            ]">
            <el-input clearable :disabled="isEdit" style="width: 200px" v-model="query.registerAddress" placeholder="请填写注册地址"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="ceoMobile" label="注册资金:" :rules="[
              {
                required: false,
                message: '请填写注册资金',
                trigger: 'blur',
              },
            ]">
            <el-input :disabled="isEdit" clearable style="width: 200px" v-model="query.ceoMobile" placeholder="请填写注册资金"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="siteName" label="店铺名称:" :rules="[
              { required: true, message: '请填写店铺名称', trigger: 'blur' },
            ]">
            <el-input :disabled="isEdit" clearable style="width: 200px" v-model="query.siteName" placeholder="请填写店铺名称"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="Data" label="经营时间:" :rules="[
              {
                required: false,
                message: '请选择经营时间',
                trigger: 'blur',
              },
            ]">
            <el-date-picker :disabled="isEdit" v-model="query.Data" type="date" style="width: 200px" placeholder="选择日期">
            </el-date-picker>
          </el-form-item>
        </el-form>
      </div>
    </div>

  </div>
</template>

<script>

export default {
  data() {
    return {
      isEdit: false,
      tableData: []
    }
  }
};
</script>

<style lang="less" scoped>
.archivesEditContent {
  margin: -30px -20px;
  border-top: 1px solid #ebecee;
  padding: 30px 20px;
  .item {
    width: 100%;
    margin-bottom: 30px;
    border-bottom: 1px solid #eeeeee;
    .title {
      padding: 0 0 15px;
      span {
        font-size: 16px;
        padding-left: 10px;
        border-left: 4px solid rgba(64, 158, 255, 1);
      }
    }
  }

  .uploadPic {
    padding-bottom: 100%;
    margin-bottom: -100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    > div {
      min-width: 100%;
      height: 25px;
    }
  }
  .productPicContent .text p {
    font-family: "PingFangSC-Regular", "PingFang SC", sans-serif,
      "PingFangSC-Regular", "PingFang SC", sans-serif-400;
    font-weight: 400;
    color: #aaaaaa;
    line-height: 20px;
    font-size: 13px;
    margin: 0;
  }
  .detailMsg {
    font-family: "PingFangSC-Regular", "PingFang SC", sans-serif,
      "PingFangSC-Regular", "PingFang SC", sans-serif-400;
    font-weight: 400;
    color: #aaaaaa;
    line-height: 20px;
    padding-bottom: 20px;
    font-size: 13px;
  }
  /deep/ .el-input.is-disabled .el-input__inner {
    color: #04060c;
    background-color: #eaf7e7;
  }
  /deep/ .is-checked .is-disabled .el-checkbox__inner {
    color: #2fa338;
    background-color: #1b9e38;
  }
  /deep/ .is-checked .el-checkbox__label{
    color: #04060c;
  }
  /deep/ .is-disabled .is-checked .el-radio__inner{
    background-color:#1b9e38;
  }
  /deep/ .is-disabled.is-checked.el-radio> .el-radio__label{
    color:#04060c;
  }
}
</style>
