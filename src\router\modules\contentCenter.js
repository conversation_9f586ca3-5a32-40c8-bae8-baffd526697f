import Layout from '@/layout'
const contentCenter = {
  path: '/contentCenter',
  name: 'contentCenter',
  redirect: 'contentCenter/announcement',
  meta: {
    title: '内容中心',
    icon: 'laba'
  },
  component: Layout,
  children: [
    {
      path: 'announcement',
      name: 'announcement',
      meta: { title: '公告通知' },
      component: () => import('@/views/contentCenter/announcement/list')
    },
    {
      path: 'announcementEdit',
      name: 'announcementEdit',
      hidden: true,
      meta: { title: '编辑公告通知', activeMenu: '/contentCenter/announcement'},
      component: () => import('@/views/contentCenter/announcement/editItem')
    },
    {
      path: 'informationList',
      name: 'informationList',
      meta: { title: '资讯列表' },
      component: () => import('@/views/contentCenter/informationList/list')
    },
    {
      path: 'informationEdit',
      name: 'informationEdit',
      hidden: true,
      meta: { title: '编辑资讯', activeMenu: '/contentCenter/informationList' },
      component: () => import('@/views/contentCenter/informationList/editItem')
    },
    {
      path: 'articleList',
      name: 'articleList',
      meta: { title: '文章列表' },
      component: () => import('@/views/contentCenter/articleList/list')
    },
    {
      path: 'articleEdit',
      name: 'articleEdit',
      hidden: true,
      meta: { title: '编辑文章', activeMenu: '/contentCenter/articleList' },
      component: () => import('@/views/contentCenter/articleList/editItem')
    }
    ,
    {
      path: 'informationCategory',
      name: 'informationCategory',
      meta: { title: '资讯分类' },
      component: () => import('@/views/contentCenter/informationCategory/list')
    }
    ,
    {
      path: 'chapterClassification',
      name: 'chapterClassification',
      meta: { title: '章节分类' },
      component: () => import('@/views/contentCenter/chapterClassification/list')
    }
  ]
}

export default contentCenter