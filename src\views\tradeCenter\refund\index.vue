<template>
  <div>
    <div class="search-wrapper">
      <search-pad @search="searchLoad" @reset="reload">
        <el-form-item>
          <el-input placeholder="请输入退款单号" v-model="model.rejectionNo"/>
        </el-form-item>
        <el-form-item>
          <el-input placeholder="请输入客户编码" v-model="model.code"/>
        </el-form-item>
        <el-form-item>
          <el-input placeholder="请输入客户名称" v-model="model.purMerchantName"/>
        </el-form-item>
        <el-form-item>
          <el-date-picker
            type="daterange"
            range-separator="至"
            v-model="model.during"
            value-format="yyyy-MM-dd"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>

      </search-pad>
    </div>
    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        :tabs="tabs"
        @change="handleChangeTab"
      >
        <template slot="button">
          <!-- <el-button  v-if="checkPermission(['admin','refund:exports'])">导出单据</el-button> -->
          <el-button  @click="reload">刷新</el-button>
        </template>
      </tabs-layout>
      <table-pager ref="todoTable" :options="tableTitle" :remote-method="load" :data.sync="tableData">
        <div slot-scope="props" style="width: 65px;">
          <el-link @click="$router.push({path: './detail',query:{id: props.row.id}})">查看详情</el-link>
        </div>

      </table-pager>
    </div>
  </div>
</template>

<script>
  const TableColumns = [
    { label: "退款单号", name: "salesRefundNo",prop: "salesRefundNo",width:'150'},
    { label: "银行流水", name: "bankNo", prop:'bankNo',width: '150'},
    { label: "申请时间", name: "salesRefundCreateTime",prop: 'salesRefundCreateTime',width: '160' },
    { label: "退款客户", name: "name", prop:'name',width: '130'  },
    { label: "退款方式", name: "salesRefundType",prop: 'salesRefundType',width:'165'},
    { label: "退还金额", name: "totalRealMoney",prop: 'totalRealMoney',width: '150' },
    { label: "退还资产", name: "totalRealAssets",prop:'totalRealAssets',width: '150' },
    { label: "退款状态", name: "baseCommonEnum.desc",prop:'baseCommonEnum.desc',  },
    { label: "退款时间", name: "salesRefundEndTime",prop: 'salesRefundEndTime'},
  ];
  const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({ key: i, ...TableColumns[i] });
  }
  import checkPermission from '@/utils/permission';
  import {salesRefundInfoList } from '@/api/trade'
  import TabsLayout from '@/components/TabsLayout'
  export default {
    components: {
      TabsLayout
    },
    data () {
      return {
        loading: '',
        search: '',
        controlType: '',
        currentTab: 0,
        tabs: [
          { name: '全部', value: '', hide: !checkPermission(['admin', 'refund:all']) },
          { name: '退款成功', value: '是', hide: !checkPermission(['admin', 'refund:success']) },
          { name: '退款失败', value: '否', hide: !checkPermission(['admin', 'refund:fail'])  },
        ],
        tableData: [],
        page: 1,
        pageSize: 10,
        totalPage: 0,
        total: 0,
        tableTitle: TableColumnList,
        model: {
          salesRefundAuditStatusEnum: '',
          purMerchantName: '',
          rejectionNo: '',
          during: '',
          code: ''
        },
      }
    },
    mounted() {
    },
    methods: {
      checkPermission,
      async load(params) {
        let listQuery = {
          model: {
            purMerchantName: this.model.purMerchantName,
            rejectionNo: this.model.rejectionNo,
            salesRefundAuditStatusEnum: this.model.salesRefundAuditStatusEnum,
            startTime: this.model.during[0],
            endTime: this.model.during[1],
          }
        }
        Object.assign(listQuery, params)
        this.loading = true
        return await salesRefundInfoList(listQuery)
      },
      searchLoad() {
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      handleChangeTab (tab) {
        this.currentTab = index
        this.model.baseCommonEnum = tab.value
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      reload() {
        this.currentTab = 0
        this.model = {
          deliveryStatus: '',
          purMerchantName: '',
          rejectionNo: '',
          code: '',
          during: ''
        }
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      handleRefresh(pageParams) {
        this.$refs.todoTable.doRefresh(pageParams)
      },
      splitString (val) {
        return val.split(',')
      },
      handleSelectionChange(val) {
        this.ids = val.map(function(item,index) {
          return item.id;
        })

      }
    }
  }
</script>

<style lang="scss" scoped>
</style>
