<template>
  <div class="temp_ProductTableButton">
    <el-button style="width:100px;padding:0 -10px; text-indent: -10px;" @click="showStoreTableDialogFun()">选择文章</el-button>
    <el-dialog v-if="showStoreTableDialog==true" append-to-body title="文章列表" :visible.sync="showStoreTableDialog" :before-close="closeDialogFun" :close-on-click-modal="false" width="80%" :show-close="false">
      <div style="position:absolute;top:10px; right:15px; background:#fff; height:38px;">
        <div>
          <el-button @click="clearFun()">取 消</el-button>
          <!-- <el-button type="primary" @click="submitFun()">确 定</el-button> -->
        </div>
      </div>
      <div class="showProductTable">
        <div class="temp_searchBox">
          <el-form ref="searchForm" :inline="true" :model="listQuery" class="form-inline">
            <el-form-item label="">
              <el-input v-model="listQuery.model.title" placeholder="请输入文章标题" style="width:250px" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="getList()">搜索</el-button>
              <el-button @click="resetForm">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="table">
          <el-table
            v-if="list"
            ref="table"
            v-loading="listLoading"
            :data="list"
            row-key="id"
            border
            fit
            highlight-current-row
            style="width: 100%"
          >
            <el-table-column
              align="center"
              width="50"
              label="序号"
              fixed
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                <span>{{ scope.$index+1 }}</span>
              </template>
            </el-table-column>
            <el-table-column v-for="(item, index) in tableTitle" :key="index" :min-width="(item.width?item.width:'350px')" :label="item.label" show-overflow-tooltip align="left">
              <template slot-scope="{row}">
                <span v-if="item.name === 'publishStatus' || item.name === 'isOpenWindow'">{{ row[item.name].desc }}</span>
                <span v-else>{{ row[item.name] }}</span>
              </template>
            </el-table-column>

            <el-table-column fixed="right" align="center" label="操作" width="150" class="itemAction">
              <template slot-scope="scope">
                <el-button type="text" @click="selectProductItemFun(scope.row)">选中</el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination v-show="total>0" :total="total" :page.sync="listQuery.current" :limit.sync="listQuery.size" @pagination="getList" />
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import { helpPage } from '@/api/businessCenter/businessList'
export default {
  filters: {
    imgFilter: function(value) {
      if (value != '') {
        return value.split(',')[0]
      }
    }
  },
  components: {
    Pagination
  },
  props: {
    selectItems: {
      type: String,
      default: '',
      required: true
    }
  },
  data() {
    return {
      tableTitle: [
        { key: 0,
          label: '文章分类',
          name: 'articleCategoryName',
          width: '80px'
        },
        { key: 2,
          label: '文章标题',
          name: 'title',
          width: '170px'
        },
        { key: 3,
          label: '是否发布',
          name: 'publishStatus',
          width: '170px'
        },
        { key: 4,
          label: '是否新窗口打开',
          name: 'isOpenWindow',
          width: '170px'
        }

      ],
      showStoreTableDialog: false,
      listQuery: {
        current: 1,
        size: 10,
        model: {
          title: '',
          whetherNeedHelpArticle: 'Y'
        }
      },
      selectRowItems: [],
      list: [],
      total: 0,
      listLoading: true
    }
  },
  computed: {
    selectRowVal: {
      get() {
        this.selectItems
      },
      set(val) {
        this.$emit('update:selectItems', val)
        this.$emit('confirm')
      }
    }
  },
  mounted() {
  },
  beforeDestroy() {},
  methods: {
    resetForm() {
      this.listQuery.model.title = ''
      this.getList()
    },
    clearFun() {
      this.listQuery = {
        current: 1,
        size: 10,
        model: {
          title: '',
          whetherNeedHelpArticle: 'Y'
        }
      }
      this.list = []
      this.total = 0
      this.listLoading = true
      this.showStoreTableDialog = false
    },
    showStoreTableDialogFun() {
      this.listQuery = {
        model: {
          title: '',
          whetherNeedHelpArticle: 'Y'
        },
        current: 1,
        size: 10
      },
      this.list = [],
      this.total = 0,
      this.listLoading = true,
      this.showStoreTableDialog = true
      this.getList()
    },
    async getList() {
      this.listLoading = true
      const { data } = await helpPage(this.listQuery)
      if (data != null) {
        this.list = data.records
        this.total = data.total
      }
      this.listLoading = false
      // this.displayTable()
    },
    closeDialogFun() {
      this.showStoreTableDialog = false
    },
    onAllSelect(selection) {
      this.onSelect(selection)
    },
    onSelect: function(val, row) {
      if (val.length < 10) {
        this.selectRowItems = val
      } else {
        this.$refs.table.toggleRowSelection(row, false)
      }
    },
    displayTable() {
      const vm = this
      vm.selectItems.forEach(item => {
        vm.list.forEach(rowItem => {
          if (item.id === rowItem.dataParam) {
            vm.$refs.table.toggleRowSelection(item, true)
          }
        })
      })
    },
    selectProductItemFun(item) {
      this.selectRowVal = item.id
      this.$emit('confirm')
      this.clearFun()
    }
  }
}
</script>
<style lang="less" scoped>
.temp_ProductTableButton{width:100%;

}
.showProductTable{
    margin: -30px -20px;
    border-top: 1px solid #ebecee;
    padding: 10px 20px;
}
.temp_searchBox{height:45px;overflow: hidden; margin-bottom: 0;  padding:0; border-left:none;}

</style>
