<template>
  <div class="archivesPageContent">
    <im-search-pad
      :is-expand.sync="isExpand"
      :has-expand="false"
      :model="listQuery"
      @reset="resetForm('searchForm')"
      @search="onSearchSubmitFun"
    >
      <im-search-pad-item prop="productName">
        <el-input
          v-model.trim="listQuery.productName"
          placeholder="请输入商品名称"
          style="width: 200px"
        ></el-input>
      </im-search-pad-item>
      <im-search-pad-item prop="manufacturer">
        <el-input
          v-model.trim="listQuery.manufacturer"
          placeholder="请输入生产厂家"
          style="width: 200px"
        ></el-input>
      </im-search-pad-item>
      <im-search-pad-item prop="manufacturer">
        <selectTime
          @getDetail="getList"
          :orderTypes="false"
          :Query.sync="query"
        ></selectTime>
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout :tabs="[{ name: '商品报表' }]" />
      <table-pager
        ref="table"
        :options="tableTitle"
        :remote-method="load"
        :data.sync="list"
        :selection="true"
        @selection-change="selectTableItemFun"
      >
        <div slot-scope="props">
          <el-row class="table-edit-row">
            <span class="table-edit-row-item">
              <el-button v-if="checkPermission(['admin', 'admin-report-product:detail'])" @click="detailFun(props.row)" type="text">查看详情</el-button>
            </span>
          </el-row>
        </div>
      </table-pager>
    </div>
  </div>
</template>

<script>
import selectTime from "@/views/dashboard/cards/components/selectTime";
import checkPermission from "@/utils/permission";
const TableColumns = [
  { label: "商品名称", name: "productName", prop: 'productName', width: "170px" },
  { label: "规格", name: "spec", prop: 'spec', width: "170px", selectable: true },
  { label: "生产厂家 ", name: "manufacturer", prop: 'manufacturer', width: "200px" },
  { label: "商品编码", name: "productNo", prop: 'productNo', width: "120px" },
  { label: "销售数量", name: "salesQuantity", prop: 'salesQuantity', width: "100px" },
  { label: "销售金额", name: "salesAmount", prop: 'salesAmount', width: "100px" }
]
const TableColumnList = [];
for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({ key: i, ...TableColumns[i] });
}
import Pagination from "@/components/Pagination"
import { getContextData } from "@/utils/auth"
import { productList } from "@/api/dashboard"
import TabsLayout from '@/components/TabsLayout'

export default {
  components: {
    Pagination,
    selectTime,
    TabsLayout
  },
  data() {
    return {
      isExpand: false,
      total: 0,
      list: [],
      tabTap: "list",
      tableTitle: TableColumnList,
      listLoading: false,
      listQuery: {
        current: 1,
        model: {
          endTime: "",
          manufacturer: "",
          productName: "",
          startTime: "",
        },
        size: 10,
      },
      selecttableItemIds: [],
      tableSelectTitle: [],
      query: {},
    };
  },
  methods: {
    checkPermission,
    detailFun(row) {
      this.$router.push('/reportCenter/detail?id=' + row.productId)
    },
    initTbaleTitle() {
      this.tableTitle = TableColumnList;
      this.tableSelectTitle = [];
    },
    refreshFun() {
      this.list = [];
      this.initTbaleTitle();
      this.listQuery = {
        current: 1,
        size: 10,
        model: {
          approvalStatus: "ACCEPTED",
        },
      };
      this.getList();
    },
    async getList() {
      let obj = {
        current: this.listQuery.current,
        model: {
          endTime: this.query.endTime + " 23:59:59",
          manufacturer: this.listQuery.manufacturer,
          productName: this.listQuery.productName,
          startTime: this.query.startTime + " 00:00:00",
        },
        size: this.listQuery.size,
      };
      this.listLoading = true;
      let { data } = await productList(obj);
      this.total = data.total;
      this.list = data.records;
      this.listLoading = false;
    },
    async load(params) {
      let listQuery = {
        model: {
          endTime: this.query.endTime + " 23:59:59",
          manufacturer: this.listQuery.manufacturer,
          productName: this.listQuery.productName,
          startTime: this.query.startTime + " 00:00:00",
        },
        size: this.listQuery.size,
      }
      Object.assign(listQuery, params)
      return await productList(listQuery)
    },
    selectTableItemFun(row) {
      this.selecttableItemIds = row;
    },
    onSearchSubmitFun() {
      this.getList()
    },
    resetForm() {
      this.listQuery = {
        current: 1,
        size: 10,
        model: {
          approvalStatus: this.listQuery.model.approvalStatus,
        },
      }
      this.getList()
    }
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (
        from.path === '/businessCentric/salesmanListeditItem' ||
        from.path === '/businessCentric/salesmanListdetail'
      ) {
        if (getContextData("salesmanList_list") !== '') {
          vm.listQuery = getContextData('salesmanList_list')
        }
      }
      vm.initTbaleTitle()
    })
  }
};
</script>

<style lang="less" scoped>
</style>
