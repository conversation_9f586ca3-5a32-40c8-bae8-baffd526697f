import requestAxios from '@/utils/requestAxios'
import request from '@/utils/request'

// 现行版本员工录入 begin
export function getUserByAccount(account) {
  return requestAxios({
    url: '/api/authority/user/getByAccount?account=' + account,
    method: 'get'
  })
}

export function page(data) {
  return requestAxios({
    url: '/api/authority/admin/employee/page',
    method: 'post',
    data
  })
}
export function editNewApi(data) {
  return requestAxios({
    url: '/api/authority/admin/employee',
    method: 'put',
    data
  })
}

export function addNewApi(data) {
  return requestAxios({
    url:'/api/authority/admin/employee/saveAll',
    method: 'post',
    data
  })
}


// 现行本员工录入 end


// 旧版本员工录入 begin
export function list(data) {
  return requestAxios({
    url: '/api/authority/user/page',
    method: 'post',
    data
  })
}

export function editApi(data) {
  return requestAxios({
    url: '/api/authority/user',
    method: data.id == 0 ? 'post' : 'put',
    data
  })
}


export function detail(id) {
  return requestAxios({
    url: '/api/authority/user/' + id,
    method: "get"
  })
}

// 旧版本员工录入 end