<template>
  <div class="systemPageContent">
    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        :tabs="[ { name: '字典类型'} ]"
        @change="handleChangeTab"
      >
        <template slot="button">
          <el-button
            v-if="checkPermission(['admin', 'admin-setting-dictionary:add'])"
            type="primary"

            @click="editFun({})"
          >新增字典类型</el-button>
          <el-button >刷新</el-button>
        </template>
      </tabs-layout>
      <div class="table">
        <el-table
          ref="tableDom"
          v-loading="listLoading"
          :data="list"
          row-key="id"
          border
          fit
          @filter-change="fliterChange"
        >
          <el-table-column
            prop="type"
            width="150"
            show-overflow-tooltip
            label="类型"
          />
          <el-table-column
            prop="name"
            width="150"
            show-overflow-tooltip
            label="名称"
          />
          <el-table-column
            prop="describe"
            show-overflow-tooltip
            label="描述"
            min-width="300"
          />
          <el-table-column
            width="100"
            show-overflow-tooltip
            label="状态"
            :filters="tableStutasList"
            :filter-multiple="false"
            column-key="status"
          >
            <template slot-scope="{ row }">
              <el-tag v-if="row['status'] == true" type="success" >启用</el-tag>
              <el-tag v-if="row['status'] == false" type="danger" >禁用</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="createTime"
            show-overflow-tooltip
            label="创建时间"
          />

          <el-table-column
            fixed="right"
            align="center"
            label="操作"
            width="200"
            class="itemAction"
          >
            <template slot-scope="scope">
              <!-- 根分类 -->
              <el-row class="table-edit-row">
                <span v-if="checkPermission(['admin', 'admin-setting-dictionary:detail'])" class="table-edit-row-item">
                  <el-button type="text"  @click.native="showItemView(scope.row)">查看类型项目</el-button>
                </span>
                <span v-if="checkPermission(['admin', 'admin-setting-dictionary:edit'])" class="table-edit-row-item">
                  <el-button type="text"  @click.native="editFun(scope.row)">编辑</el-button>
                </span>
                <!-- <span v-if="checkPermission(['admin', 'base-data:delete'])" class="table-edit-row-item">
                  <el-button type="text"  @click.native="deleteFun(scope.row)">删除</el-button>
                </span> -->
              </el-row>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="listQuery.current"
          :limit.sync="listQuery.size"
          @pagination="getList"
        />
      </div>
    </div>
    <edit
      v-if="showEdit"
      :visible.sync="showEdit"
      :is-reload.sync="submitReload"
      :row.sync="row"
    />

    <!-- 字典类型 新增-->
    <!-- 查看字典项 -->
    <el-dialog
      v-if="showItemTable"
      :title="'查看' + row.name + '字典项'"
      :visible.sync="showItemTable"
      width="60%"
      :close-on-click-modal="false"
    >
      <dictionaryItem
        :visible.sync="showItemTable"
        :row.sync="row"
      />
    </el-dialog>
    <!-- 查看字典项 -->

  </div>
</template>
<script>
import checkPermission from '@/utils/permission'
import { list, deleteApi } from '@/api/setting/data'
import edit from './edit'
import dictionaryItem from './dictionaryItem'
import Pagination from '@/components/Pagination'
import TabsLayout from '@/components/TabsLayout'
export default {
  components: {
    Pagination,
    edit,
    dictionaryItem,
    TabsLayout
  },
  data() {
    return {
      tableStutasList: [
        {
          text: '启用',
          value: true
        },
        {
          text: '禁用',
          value: false
        }
      ],
      showEdit: false,
      showItemTable: false,
      row: {},
      submitReload: false,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        size: 10,
        model: {},
        current: 1
      }
    }
  },
  watch: {
    submitReload: function(newVal, oldVal) {
      if (newVal) {
        this.submitReload = false
        this.getList()
      }
    }
  },
  mounted() {
    this.getList()
  },
  beforeDestroy() {},

  methods: {
    checkPermission,
    initQuery() {
      this.listQuery = {
        size: 10,
        model: {},
        current: 1
      }
    },
    onSearchSubmitFun() {
      this.listQuery.size = 1
      this.getList()
    },
    async getList() {
      this.listLoading = true
      const { data } = await list(this.listQuery)
      this.list = data.records
      this.total = data.total
      this.listLoading = false
    },
    showItemView(row) {
      this.row = row
      this.showItemTable = true
    },
    editFun(row) {
      this.row = row
      this.showEdit = true
    },
    deleteFun(row) {
      var _this = this
      this.$confirm('此操作将永久删除该信息, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        _this.actionDeleteFun(row)
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    async actionDeleteFun(row) {
      const data = await deleteApi(row.id)
      if (data.code == 0) {
        this.initQuery()
        this.getList()
      }
    },
    actionsTableFun(row) {
      this.row = row
      this.showItemTable = true
    },
    fliterChange(filters) {
      for (var key in filters) {
        this.listQuery.model[key] = filters[key][0]
      }
      this.getList()
    }
  }
}
</script>
<style lang="scss" scoped>
@import "@/styles/element-variables.scss";
.temp_searchBox{height: 64px;overflow: hidden; margin-bottom: 0; }
.form-inline{height:60px; overflow:hidden;}
.systemPageContent {
  padding:0;
  // padding: 15px;
  .title {
    // border-bottom: 2px solid #ebecee;
     margin-bottom:16px;
    border-bottom:2px solid #EBECEE;
    span {
      margin-bottom: -2px;
      padding: 0 15px;
      height: 40px;
      line-height: 30px;
      display: block;
      background: rgba(255, 255, 255, 0);
      border-bottom: 2px solid rgb(64, 158, 255);
      font-size: 16px;
      font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
        "PingFang SC"-400;
      font-weight: 400;
      color: rgb(64, 158, 255);
    }
  }
  .formItem {
    width: 586px;
  }
  .line {
    color: #dfe6ec;
    margin: 0 6px;
  }
  .el-dropdown-link {
    margin-left: 12px;
    cursor: pointer;
    font-size: 12px;
    color: #0056e5;
  }
  .el-icon-arrow-down {
    font-size: 12px;
  }
  // .formItem{width:586px;}
  // .line{color:#dfe6ec; margin:0 6px;}
}
</style>
