<template>
  <div class="staffPageContent">
    <im-search-pad
      :has-expand="false"
      :model="listQuery"
      ref="searchForm"
      @reset="resetForm"
      @search="onSearchSubmitFun"
    >
      <im-search-pad-item prop="account">
        <el-input v-model="listQuery.model.account" placeholder="输入员工账号" />
      </im-search-pad-item>
      <im-search-pad-item prop="name">
        <el-input v-model="listQuery.model.name" placeholder="输入员工姓名" />
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout
        ref="tabsLayout"
        v-model="listQuery.model.positionStatus"
        :tabs="tabs"
        @change="chageTabsFun"
      >
        <template slot="button">
          <el-button >刷新</el-button>
          <el-button v-if="checkPermission(['admin','admin-setting-permission-employee:staffAdd'])"  type="primary" @click="newFun">+新增员工</el-button>
        </template>
      </tabs-layout>
      <div class="table">
        <el-table
          v-loading="listLoading"
          :data="list"
          row-key="id"
          border
          fit
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column
            align="center"
            width="65"
            show-overflow-tooltip
            :render-header="renderHeader"
            fixed
          >
            <template slot-scope="scope">
              <span>{{ scope.$index+1 }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-for="(item, index) in tableTitle"
            :key="index"
            :width="item.width"
            :min-width="(item.width?item.width:'350px')"
            :label="item.label"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              <span v-if="item.name=='positionStatus'">{{ row[item.name].desc }}</span>
              <span v-else-if="item.name=='roleList'">
                <template v-for="item,index in row[item.name]">
                  {{ index>0?","+item.name:item.name }}
                </template>
              </span>
              <span v-else>{{ row[item.name] }}</span>
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            align="center"
            label="操作"
            width="180"
            class="itemAction"
          >
            <template slot-scope="scope">
              <el-row class="table-edit-row">
                <span v-if="checkPermission(['admin','admin-setting-permission-employee:staffAssign'])" class="table-edit-row-item">
                  <el-button type="text"  @click="editRoleClickFun(scope.row)">角色分派</el-button>
                </span>
                <span v-if="checkPermission(['admin','admin-setting-permission-employee:edit'])" class="table-edit-row-item">
                  <el-button type="text"  @click="editClickFun(scope.row)">编辑</el-button>
                </span>
                <span v-if="listQuery.model.positionStatus=='WORKING'&&checkPermission(['admin','admin-setting-permission-employee:staffLeave'])" class="table-edit-row-item">
                  <el-button type="text"  @click="setUserQuit(scope.row)">离职</el-button>
                </span>
                <span v-if="listQuery.model.positionStatus=='QUIT'&&checkPermission(['admin','admin-setting-permission-employee:staffReinstate'])" class="table-edit-row-item">
                  <el-button type="text"  @click="setUserWorking(scope.row)">复职</el-button>
                </span>
              </el-row>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="listQuery.current" :limit.sync="listQuery.size" @pagination="getList" />
      </div>
    </div>
    <!-- 设置 编辑 -->
    <edit v-if="showEdit" :visible.sync="showEdit" :is-reload.sync="submitReload" :row.sync="row" />
    <!-- 设置 编辑 -->
    <!-- 设置 角色 -->
    <setRoleTable v-if="showRoleEdit" :visible.sync="showRoleEdit" :is-reload.sync="submitReload" :row.sync="row" />

    <!-- 设置 角色 -->
  </div>
</template>
<script>
import checkPermission from '@/utils/permission'
import { query } from '@/api/setting/permission/userRole'
import { page, editNewApi } from '@/api/setting/staff'
import edit from '@/views/settingCenter/permission/staff/edit'
import setRoleTable from '@/views/settingCenter/permission/staff/setRoleTable'
import Pagination from '@/components/Pagination'
import TabsLayout from '@/components/TabsLayout'

export default {
  components: {
    Pagination,
    edit,
    setRoleTable,
    TabsLayout
  },
  data() {
    return {
      // roleList:[],
      showEdit: false,
      showRoleEdit: false,
      row: {

      },

      tableTitle: [
        {
          key: 0,
          label: '在职状态',
          name: 'positionStatus',
          width: '120px'
        },
        {
          key: 1,
          label: '员工账号',
          name: 'account',
          width: '150px'
        },
        {
          key: 2,
          label: '员工姓名',
          name: 'name',
          width: '150px'
        },
        {
          key: 3,
          label: '员工联系号码',
          name: 'contactNumber',
          width: '180px'
        },
        {
          key: 4,
          label: '角色',
          name: 'roleList',
          width: '180px'
        },
        {
          key: 5,
          label: '员工入职时间',
          name: 'createTime'
        }
      ],
      submitReload: false,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        model: {
          positionStatus: 'WORKING'
        },
        current: 1,
        size: 10,
        order: 'descending',
	      sort: 'id'
      }
    }
  },
  computed: {
    tabs() {
      return [
        {
          name: '在职',
          value: 'WORKING',
          hide: !checkPermission(['admin', 'admin-setting-permission-employee:incumbencyView'])
        },
        {
          name: '离职',
          value: 'QUIT',
          hide: !checkPermission(['admin', 'admin-setting-permission-employee:leaveView'])
        }
      ]
    }
  },
  watch: {
    submitReload: function(newVal, oldVal) {
      if (newVal) {
        this.submitReload = false
        this.getList()
      }
    }
  },
  mounted() {
    if (this.checkPermission(['admin', 'admin-setting-permission-employee:incumbencyView'])) {
      this.listQuery = {
        model: {
          positionStatus: 'WORKING'
        },
        current: 1,
        size: 10,
        order: 'descending',
        sort: 'id'
      }
    } else if (this.checkPermission(['admin', 'admin-setting-permission-employee:leaveView'])) {
      this.listQuery = {
        model: {
          positionStatus: 'QUIT'
        },
        current: 1,
        size: 10,
        order: 'descending',
        sort: 'id'
      }
    }
    this.getList()
  },
  beforeDestroy() {},
  methods: {
    checkPermission,
    newFun: function() {
      this.row = {}
      this.showEdit = true
    },
    editClickFun(row) {
      console.log('row----->',row);
      this.row = row
      this.showEdit = true
    },
    editRoleClickFun(row) {
      this.row = row
      this.showRoleEdit = true
    },
    renderHeader(h, { column }) {
      return (
        <div style='position:relative'>
          <div>
            <i class='el-icon-menu' />
          </div>
        </div>
      )
    },
    setHeaer: function() {
      this.showSelectTitle = !this.showSelectTitle
    },
    async getList() {
      this.listLoading = true
      const { data } = await page(this.listQuery)
      this.list = data.records
      this.total = data.total
      this.listLoading = false
    },
    chageTabsFun: function() {
      this.list = []
      this.listQuery.pag = 1
      
      this.getList()
    },
    onSearchSubmitFun: function() {
      this.list = []
      this.getList()
    },
    resetForm() {
      this.list = []
      this.listQuery.model.account=""
      this.listQuery.model.name=""
      this.listQuery.pag = 1
      this.getList()
    },
    
    // async getRoleList() {
    //   const { data } = await query({dsType: "ALL"})
    //   this.roleList = data
    // },
    // 设置离职
    setUserQuit(row) {
      var _this = this
      row.positionStatus = 'QUIT'

      this.$confirm('是否确认该员工离职?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        const data = await editNewApi(row)
        if (data.code == 0) {
          this.$message({
            type: 'success',
            message: '设置员工离职成功!'
          })
          _this.chageTabsFun()
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消离职'
        })
      })
    },
    async setUserWorking(row) {
      // 设置复职
      var _this = this
      row.positionStatus = 'WORKING'

      this.$confirm('是否确认该员工复职?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        const data = await editNewApi(row)
        if (data.code == 0) {
          this.$message({
            type: 'success',
            message: '设置员工复职成功!'
          })
          _this.chageTabsFun()
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消复职'
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import "@/styles/element-variables.scss";

.staffPageContent {
  padding:0 ;
  .temp_searchBox{height: 64px;overflow: hidden; margin-bottom: 0; }
  .form-inline{height:60px; overflow:hidden;}
  .title{
        border-bottom:2px solid #EBECEE;
        margin-bottom:16px;
      span{
        margin-bottom: -2px;
        padding:0 15px;
        height: 40px;
        line-height: 30px;
        display:block;
        background: rgba(255,255,255,0);
        border-bottom:2px solid rgb(64, 158, 255);
        font-size: 16px;
        font-family: 'PingFangSC-Regular', 'PingFang SC', 'PingFangSC-Regular', 'PingFang SC'-400;
        font-weight: 400;
        color:rgb(64, 158, 255);
      }
  }

  .formItem{width:586px;}
  .line{color:#dfe6ec; margin:0 6px;}
  .typeTabs{height: 40px;margin-bottom: -2px; margin-left:30px;}
}
</style>
