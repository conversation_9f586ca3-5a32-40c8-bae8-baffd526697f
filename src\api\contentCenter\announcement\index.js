import requestAxios from '@/utils/requestAxios'

// 分页数据获取
export  function list(data) {
  return requestAxios({
    url: '/api/general/admin/article/pageArticleContent',
    method: 'post',
    data
  })
}

// 获取单一公告文章
export  function getitem(data) {
  return requestAxios({
    url: '/api/general/admin/article/' + data,
    method: 'get',
  })
}

// 新增
export  function add(data) {
  return requestAxios({
    url: '/api/general/admin/article/saveArticleContent',
    method: 'post',
    data
  })
}

// 删除公告
export  function del(data) {
  return requestAxios({
    url: '/api/general/admin/article',
    method: 'delete',
    params: data
  })
}

// 修改公告
export function updateArticleContent(data) {
  return requestAxios({
    url: '/api/general/admin/article/updateArticleContent',
    method: 'put',
    data
  })
}

// 启用公告
export  function start(data) {
  return requestAxios({
    url: '/api/general/admin/article/updateArticleEnable/' + data,
    method: 'PUT',
  })
}

// 停用公告
export  function disable(data) {
  return requestAxios({
    url: '/api/general/admin/article/updateArticleStop/' + data,
    method: 'PUT',
  })
}
