import { deepClone } from '@/utils'

const initTableInfo = [
  {
    key: 0,
    label: '机构名称',
    name: 'name',
    width: '140px'
  },
  {
    key: 4,
    label: '所在地区',
    name: 'area',
    width: '200px'
  },
  {
    key: 5,
    label: '联系人',
    name: 'contacts',
    width: '100px'
  },
  {
    key: 13,
    label: '联系人手机',
    name: 'phone',
    width: '120px'
  },
  {
    key: 14,
    label: '服务状态',
    name: 'status',
    width: '80px'
  },
  {
    key: 9,
    label: '服务期限',
    name: 'beginTime', // endTime
    width: '255px'
  },
  {
    key: 9,
    label: '服务内容',
    name: 'note',
    width: '150px'
  },
  {
    key: 9,
    label: '开通账号数',
    name: 'accountTotal',
    width: '100px'
  }
]

export default {
  ALL: deepClone(initTableInfo)
}
