<template>
  <div class="archivesPageContent">
    <im-search-pad :is-expand.sync="isExpand" @reset="resetForm" @search="onSearchSubmitFun">
      <im-search-pad-item prop="productName">
        <el-input v-model="listQuery.model.agencyName" placeholder="请输入经销商名称" />
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout ref="tabs-layout" :tabs="approvalStatusList" v-model="listQuery.model.approvalStatus">
        <template slot="button">
          <div>
            <el-button @click="getList()">刷新</el-button>
          </div>
        </template>
      </tabs-layout>
      <div class="table">
        <el-table
          v-if="list"
          ref="table"
          @select="onSelect"
          @select-all="onAllSelect"
          v-loading="listLoading"
          :data="list"
          row-key="id"
          border
          fit
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column align="center" width="65" show-overflow-tooltip :render-header="renderHeader" fixed>
            <template slot-scope="scope">
              <span>{{ scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column :reserve-selection="true" align="center" type="selection" width="55" fixed>
          </el-table-column>
          <el-table-column
            v-for="(item, index) in tableTitle"
            :key="index"
            :min-width="item.width ? item.width : '180px'"
            :width="item.name == 'pictIdS' ? item.width : ''"
            :label="item.label"
          >
            <template slot-scope="{ row }">
              <span
                v-if="item.name === 'total'"
                style="display: flex; justify-content: space-between; align-items: center"
              >
                <span>{{ row[item.name] }}</span>
                <i
                  @click="showSetServiceOrgNumDialog(row)"
                  class="el-icon-edit-outline"
                  style="color: #0056e5; cursor: pointer"
                />
              </span>
              <span v-else-if="item.name === 'notUsedTotal'">{{ row['total'] - row['usedTotal'] }}</span>
              <span v-else>{{ row[item.name] }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" align="center" label="操作" class="itemAction">
            <template slot-scope="scope">
              <el-row class="table-edit-row">
                <span class="table-edit-row-item">
                  <el-link type="primary" @click="gotoDetail(scope.row)">查看详情</el-link>
                </span>
              </el-row>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="listQuery.current"
          :limit.sync="listQuery.size"
          @pagination="getList"
        />
      </div>
    </div>
    <SetServiceOrgNumDialog ref="SetServiceOrgNumRef" @setNumSuccess="onSearchSubmitFun" />
  </div>
</template>
<script>
import { getInstitutionsList } from '@/api/dealerManagement'
import SetServiceOrgNumDialog from './components/setServiceOrgNumDialog.vue'
import ExamineButton from '@/views/products/product/ExamineButton'
import tableInfo from './tableInfo'
import Pagination from '@/components/Pagination'
import TabsLayout from '@/components/TabsLayout'

export default {
  name: 'institutionalService',
  components: {
    Pagination,
    ExamineButton,
    TabsLayout,
    SetServiceOrgNumDialog
  },
  data() {
    return {
      isExpand: false,
      tableTitle: [],
      list: [],
      total: 0,
      listLoading: true,
      tableSelectTitle: [0, 1, 2, 3],
      listQuery: {
        model: {
          agencyName: ''
        },
        current: 1,
        size: 10
      }
    }
  },
  computed: {
    approvalStatusList() {
      return [
        {
          name: '机构服务',
          value: 'ALL'
        }
      ]
    }
  },
  methods: {
    // 查看详情
    gotoDetail({ total, usedTotal, id, agencyId, agencyName }) {
      this.$router.push({
        path: '/institutionalServiceDetail',
        query: {
          total,
          usedTotal,
          id,
          agencyId,
          agencyName
        }
      })
    },
    // 显示可服务机构数dialog
    showSetServiceOrgNumDialog(row) {
      this.$refs.SetServiceOrgNumRef.show(row)
    },
    renderHeader(h, { column }) {
      var titles = tableInfo[this.listQuery.model.approvalStatus]
      var titlesName = ['显示字段项', '隐藏字段项']
      return (
        <div style="position:relative">
          <div onClick={this.showHeaer}>
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            append-to-body
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="640px"
            center
          >
            <el-transfer
              vModel={this.tableSelectTitle}
              onChange={this.setleftTitleFun}
              data={titles}
              titles={titlesName}
            ></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button onClick={this.closeHeaer}>取消</el-button>
              <el-button type="primary" onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      )
    },
    setleftTitleFun(val) {
      this.tableSelectTitle = val
    },
    setHeaer: function () {
      var titles = tableInfo[0]
      var listinfo = titles.filter((element, index, self) => {
        return !this.tableSelectTitle.includes(element.key)
      })
      this.tableTitle = listinfo
      this.showSelectTitle = !this.showSelectTitle
    },
    showHeaer: function () {
      this.showSelectTitle = true
    },
    closeHeaer: function () {
      this.showSelectTitle = false
      this.tableSelectTitle = []
    },
    async getList() {
      this.listLoading = true
      const { data } = await getInstitutionsList(this.listQuery)
      this.list = data.records
      this.total = data.total
      this.listLoading = false
    },
    onSearchSubmitFun: function () {
      this.list = []
      this.getList()
    },
    resetForm() {
      ;(this.listQuery.model.agencyName = ''), this.getList()
    },
    initTbaleTitle() {
      this.tableSelectTitle = [99]
      this.tableTitle = tableInfo.ALL
      this.tableTitle = this.tableTitle.filter((item) => {
        return item.key != 99
      })
    },
    // table 选中
    onAllSelect(selection) {
      this.onSelect(selection)
    },
    onSelect: function (val) {
      this.multipleSelection = val
    }
  },
  mounted() {
    this.initTbaleTitle()
    this.getList()
  }
}
</script>
