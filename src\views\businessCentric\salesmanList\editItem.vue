<template>
  <div class="salesManDetail" v-loading="loadingFlag">
    <div class="top_title">
      <div class="flex_between_center">
        <div>{{ $route.query.id ? "编辑" : "新增" }}业务员档案</div>
        <div>
          <el-button @click="editCancel">取消</el-button>
          <el-button
            type="primary"
            @click="submit"
            :loading="isSubmit"
            v-if="!$route.query.id"
            >生成档案</el-button
          >
          <el-button
            v-if="$route.query.id"
            type="primary"
            @click="submit"
            :loading="isSubmit"
            >提交</el-button
          >
        </div>
      </div>
    </div>
    <div class="contemt">
      <el-form
        :inline="true"
        :label-width="'140px'"
        :model="salesManForm"
        ref="editForm"
        :rules="rules"
      >
        <div class="item">
          <div class="title"><span>基础信息</span></div>
          <div>
            <el-form-item class="formItem" prop="code" label="业务员编码:">
              <el-input
                :disabled="true"
                clearable
                v-model.trim="salesManForm.salesmanCode"
                placeholder="业务员编码由系统生成"
              ></el-input>
            </el-form-item>

            <el-form-item
              class="formItem"
              prop="realName"
              label="真实姓名:"
              :rules="[
                {
                  required: true,
                  message: '请输入业务员真实姓名',
                  trigger: 'blur',
                },
              ]"
            >
              <el-input
                clearable
                v-model.trim="salesManForm.realName"
                placeholder="请输入业务员真实姓名"
              ></el-input>
            </el-form-item>

            <!-- <el-form-item class="formItem" prop="idNumber" label="身份证号:" :rules="[{required: true, message: '请输入业务员身份证号', trigger:'blur'}]">
              <el-input clearable v-model.trim="salesManForm.idNumber" placeholder="请输入业务员身份证号"></el-input>
            </el-form-item> -->

            <el-form-item
              class="formItem"
              prop="contactNumber"
              label="手机号码:"
            >
              <el-input
                clearable
                v-model.trim="salesManForm.contactNumber"
                placeholder="请输入联系方式"
              ></el-input>
            </el-form-item>

            <el-form-item
              class="formItem"
              prop="area"
              label="所在区域:"
              :rules="[
                { required: true, message: '请选择所在区域', trigger: 'blur' },
              ]"
            >
              <!-- <el-input clearable v-model.trim="salesManForm.area" placeholder="请选择所在区域"></el-input> -->
              <!-- <selectAddr :defaultAddr.sync="address" /> -->
              <!-- <el-cascader
                ref="addr"
                style="width: 200px"
                placeholder="请选择所在区域"
                v-model="addr"
                :props="props"
                @change="addrcityChange"
                clearable
              >
              </el-cascader> -->
              <el-cascader
                ref="setaddr"
                style="width: 100%"
                v-model="address"
                placeholder="请选择所在区域"
                :props="{ value: 'id', label: 'label' }"
                :options="areasTree"
                @change="addrSelect"
                clearable
              >
              </el-cascader>
            </el-form-item>

            <el-form-item
              class="formItem"
              prop="sex"
              label="性别:"
              :rules="[
                {
                  required: true,
                  message: '请选择业务员性别',
                  trigger: 'blur',
                },
              ]"
            >
              <el-select
                v-model.trim="salesManForm.sex"
                placeholder="请选择业务员性别"
              >
                <el-option label="男" value="M"></el-option>
                <el-option label="女" value="W"></el-option>
                <el-option label="保密" value="N"></el-option>
              </el-select>
            </el-form-item>

            <!-- <el-form-item class="formItem" prop="idNumber" label="账户状态:" :rules="[{required: true, message: '请选择业务员账户状态', trigger:'blur'}]">
              <el-select v-model.trim="salesManForm.publishStatus" placeholder="请选择业务员账户状态">
                <el-option label="正常" value="Y"></el-option>
                <el-option label="冻结" value="N"></el-option>
              </el-select>
            </el-form-item> -->

            <el-form-item
              class="formItem"
              prop="positionStatus"
              label="在职状态:"
              :rules="[
                {
                  required: true,
                  message: '请选择业务员在职状态',
                  trigger: 'blur',
                },
              ]"
            >
              <el-select
                v-model.trim="salesManForm.positionStatus"
                placeholder="请选择业务员在职状态"
              >
                <el-option label="在职" value="WORKING"></el-option>
                <el-option label="离职" value="QUIT"></el-option>
              </el-select>
            </el-form-item>
          </div>
        </div>

        <div class="item">
          <div class="title"><span>账户信息</span></div>
          <el-form-item
            class="formItem"
            prop="account"
            label="登录账号:"
            :rules="[
              {
                required: true,
                message: '请输入业务员登录账号',
                trigger: 'blur',
              },
            ]"
          >
            <el-input
              :disabled="!!$route.query.id"
              clearable
              v-model.trim="salesManForm.account"
              placeholder="请输入业务员登录账号"
            ></el-input>
          </el-form-item>

          <el-form-item
            v-if="!$route.query.id"
            class="formItem"
            prop="password"
            label="登录密码:"
          >
            <el-input
              type="password"
              show-password
              clearable
              v-model.trim="salesManForm.password"
              placeholder="请输入业务员登录密码"
            ></el-input>
          </el-form-item>

          <el-form-item
            v-if="!$route.query.id"
            class="formItem"
            prop="confirmPassword"
            label="确认登录密码:"
          >
            <el-input
              type="password"
              show-password
              clearable
              v-model.trim="salesManForm.confirmPassword"
              placeholder="确认登录密码"
            ></el-input>
          </el-form-item>
        </div>

        <div class="item" v-if="bankCard">
          <div class="title"><span>银行卡信息</span></div>
          <el-form-item
            class="formItem"
            prop="idNumber"
            label="银行账户:"
            :rules="[
              {
                required: false,
                message: '请输入业务员登录账号',
                trigger: 'blur',
              },
            ]"
          >
            <el-input
              clearable
              disabled
              :value="bankCard.bankAccount"
            ></el-input>
          </el-form-item>

          <el-form-item
            class="formItem"
            prop="idNumber"
            label="银行卡号:"
            :rules="[
              {
                required: false,
                message: '请输入业务员登录账号',
                trigger: 'blur',
              },
            ]"
          >
            <el-input
              clearable
              disabled
              :value="bankCard.bankNumber"
            ></el-input>
          </el-form-item>

          <el-form-item
            class="formItem"
            prop="idNumber"
            label="开户银行:"
            :rules="[
              {
                required: false,
                message: '请输入业务员登录账号',
                trigger: 'blur',
              },
            ]"
          >
            <el-input clearable disabled :value="bankCard.bankName"></el-input>
          </el-form-item>
        </div>
        <div class="item" v-else>
          <div class="title"><span>银行卡信息</span></div>
          <el-form-item
            class="formItem"
            prop="idNumber"
            label="银行账户:"
            :rules="[
              {
                required: false,
                message: '请输入业务员登录账号',
                trigger: 'blur',
              },
            ]"
          >
            <el-input clearable disabled value="由业务员填写"></el-input>
          </el-form-item>

          <el-form-item
            class="formItem"
            prop="idNumber"
            label="银行卡号:"
            :rules="[
              {
                required: false,
                message: '请输入业务员登录账号',
                trigger: 'blur',
              },
            ]"
          >
            <el-input clearable disabled value="由业务员填写"></el-input>
          </el-form-item>

          <el-form-item
            class="formItem"
            prop="idNumber"
            label="开户银行:"
            :rules="[
              {
                required: false,
                message: '请输入业务员登录账号',
                trigger: 'blur',
              },
            ]"
          >
            <el-input clearable disabled value="由业务员填写"></el-input>
          </el-form-item>
        </div>

        <div class="item">
          <div class="title"><span>证件信息</span><i class="text">点击图片即可放大预览</i></div>
          <el-table
            v-if="list"
            :data="list"
            row-key="id"
            border
            fit
            highlight-current-row
            style="width: 100%"
          >
            <el-table-column
              prop="label"
              label="证件类型"
              min-width="100px"
            ></el-table-column>
            <el-table-column
              prop="idCardNumber"
              label="证件号"
              min-width="200px"
            >
              <template slot-scope="{ row }">
                <el-input
                  v-if="row.isEdit"
                  placeholder="请输入证件号"
                  v-model.trim="row.idCardNumber"
                ></el-input>
                <span v-else>{{ row.idCardNumber }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="idCardPastTime"
              label="过期时间"
              min-width="200px"
            >
              <template slot-scope="{ row }">
                <el-date-picker
                v-if="row.isEdit"
                  v-model="row.idCardPastTime"
                  :disabled="row.idCardIsForever"
                  type="date"
                  placeholder="选择日期"
                  value-format="yyyy-MM-dd"
                style="width: 150px; margin-right: 8px;"
                >
                </el-date-picker>
                <el-checkbox v-if="row.isEdit" v-model="row.idCardIsForever" @change="handleChangForever(row)">长期</el-checkbox>
                <span v-if="!row.isEdit">{{ row.idCardPastTime == ''? '长期': row.idCardPastTime }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="idCardFront" label="身份证人像面" class-name="img-cell">
              <template slot-scope="{ row }">
                <!-- <el-button v-if="!row.isEdit&&!row.idCardReverse" type="text" >查看示例图片</el-button> -->
              <el-upload
                v-if="row.isEdit"
                  class="avatar-uploader"
                  :action="$uploadUrl"
                  :data="insertProgram"
                  :headers="headersProgram"
                  :show-file-list="false"
                  :on-success="uploadFrontSuccess"
                  :before-upload="beforeUpload"
                  accept=".jpg,.png,.bmp,.jpeg"
                >
                  <img v-if="list[0].idCardFront" :src="list[0].idCardFront" class="avatar" />
                  <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                </el-upload>

                <span v-else>
                  <el-image
                    style="width: 50px; height: 50px"
                    :src="row.idCardFront"
                    :preview-src-list="[row.idCardFront]"
                  >
                  </el-image>
                  <!-- <img
                    v-for="file in row.idCardFrontList"
                    :key="file.url"
                    class="el-upload-list__item-thumbnail"
                    :src="file.url"
                    alt=""
                    style="
                      contain: cover;
                      width: 40px;
                      height: 40px;
                      margin-right: 5px;
                    "
                  /> -->
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="idCardReverse" label="身份证国徽面" class-name="img-cell">
              <template slot-scope="{ row }">
                <el-upload
                v-if="row.isEdit"
                  class="avatar-uploader"
                  :action="$uploadUrl"
                  :data="insertProgram"
                  :headers="headersProgram"
                  :show-file-list="false"
                  :on-success="uploadSuccess"
                  :before-upload="beforeUpload"
                  accept=".jpg,.png,.bmp,.jpeg"
                >
                  <img v-if="list[0].idCardReverse" :src="list[0].idCardReverse" class="avatar" />
                  <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                </el-upload>

                <span v-else>
                  <el-image
                    style="width: 50px; height: 50px"
                    :src="row.idCardReverse"
                    :preview-src-list="[row.idCardReverse]"
                  >
                  </el-image>
                </span>
              </template></el-table-column
            >
            <el-table-column label="操作" align="center" width="140px">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  @click="editIDcard(scope.row)"

                  v-show="!scope.row.isEdit"
                  >编 辑</el-button
                >
                <el-button
                  type="text"

                  v-show="scope.row.isEdit"
                  @click="cancelLisenceEdit"
                  style="color: rgb(127, 127, 127)"
                >
                  取 消
                </el-button>
                <el-button
                  type="text"

                  v-show="scope.row.isEdit"
                  @click="confirmLisenceEdit"
                >
                  确 定
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-form>

      <div style="height: 100px"></div>
    </div>
    <el-dialog
      v-if="showUploadFlag"
      :visible.sync="showUploadFlag"
      width="80%"
      :show-close="true"
      :close-on-click-modal="false"
      :append-to-body="true"
    >
      <div slot="title">
        <span>选择上传身份证图片系统识别信息</span>
        <span style="display: inline-block; width: 10px"></span>
      </div>
      <uploadId :visible.sync="showUploadFlag" @setIdMsg="setIdMsg"></uploadId>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import {
  getitem,
  addSalesMan,
  editSalesMan,
  upLoadIdCard,
} from "@/api/businessCentric/salesmanList";
import rule from "@/utils/rules";
import selectAddr from "@/components/eyaolink/selectAddr";
import { setContextData, getContextData } from "@/utils/auth";
import { areas } from "@/api/businessCenter/businessList";
import uploadId from "./idCardUpload";
export default {
  data() {
    let that = this;
    var validatePass2 = (rule, value, callback) => {
      if (value !== this.salesManForm.password) {
        callback(new Error("两次输入密码不一致!"));
      }
      callback();
    };
    return {
      loadingFlag: false,
      rules: {
        contactNumber: rule.phone,
        password: [
          { required: true, message: "确认密码不能为空", trigger: "blur" },
          {
            required: true,
            min: 6,
            max: 20,
            message: "长度在 6 到 20 个字符",
            trigger: "blur",
          },
        ],
        confirmPassword: [
          { required: true, message: "确认密码不能为空", trigger: "blur" },
          {
            required: true,
            min: 6,
            max: 20,
            message: "长度在 6 到 20 个字符",
            trigger: "blur",
          },
          { validator: validatePass2, trigger: "blur" },
        ],
      },
      salesManForm: {},
      address: {},
      headersProgram: {
        token: getToken(),
        Authorization: "Basic YWRtaW5fdWk6YWRtaW5fdWlfc2VjcmV0",
      },
      insertProgram: {
        folderId: 0,
      },
      list: [],
      oldRow: {},
      isSubmit: false,
      addr: "",
      defaultAddr: "",
      props: {
        lazy: true,
        async lazyLoad(node, resolve) {
          let { level } = node;
          let id = node.data ? node.data.id : "";
          let res = await areas({ parentId: id });
          let list = res.data;
          list.forEach((item) => {
            item.value = item.id;
            item.leaf = level >= 2;
          });
          if (!id && that.defaultAddr.region) {
            that.addr.regionId = ["0"];
            list.unshift({
              label: that.defaultAddr.region,
              leaf: true,
              provinceId: that.defaultAddr.provinceId,
              cityId: that.defaultAddr.cityId,
              countyId: that.defaultAddr.countyId,
              value: "0",
            });
          }
          resolve(list);
        },
      },
      areasTree: [],
      address: "",
      bankCard: false,
      showUploadFlag: false,
      frontIdCard: "",
      reverseIdCard: "",
    };
  },
  methods: {
    handleChangForever(e) {
      e.idCardIsForever ? e.idCardPastTime = '' : ''
    },
    setIdMsg(row) {
      this.salesManForm.realName = row.msg.name;
      this.salesManForm.sex =
        row.msg.sex == "男" ? "M" : row.msg.sex == "女" ? "W" : "";
      this.list[0].idCardPastTime = row.msg.expTime;
      this.list[0].idCardNumber = row.msg.idNumber;
      this.list[0].idCardFront = row.front;
      this.list[0].idCardReverse = row.back;
    },
    async upLoadIdcardFun() {
      if (this.frontIdCard == "") {
        this.$message.error("请输上传业务员身份证人面像！");
        return;
      }
      if (this.reverseIdCard == "") {
        this.$message.error("请输上传业务员身份证国徽像！");
        return;
      }
      let { data } = await upLoadIdCard();
    },
    frontIdCardUpFun(res, file) {
      this.frontIdCard = URL.createObjectURL(file.raw);
    },
    reverseIdCardUpFun(res, file) {
      this.reverseIdCard = URL.createObjectURL(file.raw);
    },
    addrSelect(row) {
      this.salesManForm.provinceId = row[0];
      this.salesManForm.cityId = row[1];
      this.salesManForm.districtId = row[2];
      this.salesManForm.area =
        this.$refs.setaddr.getCheckedNodes()[0].pathLabels[0] +
        "-" +
        this.$refs.setaddr.getCheckedNodes()[0].pathLabels[1] +
        "-" +
        this.$refs.setaddr.getCheckedNodes()[0].pathLabels[2];
    },
    async getareas() {
      let { data } = await areas();
      this.areasTree = data;
      // console.log(data);
    },
    editCancel() {
      this.$router.go(-1);
    },
    addrcityChange(row) {
      console.log(row);
      console.log(this.$refs.addr["$data"]["currentLabels"]);
    },
    confirmLisenceEdit() {
      const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
      if (!this.list[0].idCardNumber) {
        return this.$message.error("请填入身份证号");
      } else if (!reg.test(this.list[0].idCardNumber)) {
        return this.$message.error("身份证号有误");
      }
      if (this.list[0].idCardPastTime || this.list[0].idCardIsForever) {
      } else {
        return this.$message.error("请选择身份证过期时间");
      }
      if (!this.list[0].idCardFront) {
        return this.$message.error("请上传身份证人像面");
      }
      if (!this.list[0].idCardReverse) {
        return this.$message.error("请上传身份证国徽面");
      }
      this.list[0].isEdit = false;
    },
    cancelLisenceEdit() {
      this.$set(this.list, 0, this.oldRow);
    },
    uploadFrontSuccess(res, file, fileList) {
      this.list[0].idCardFront = res.data.url;
    },
    uploadSuccess(res, file, fileList) {
      this.list[0].idCardReverse = res.data.url
    },
    handleFrontRemove(file, fileList) {
      this.list[0].idCardFront = this.getFilePath(fileList);
      this.list[0].idCardFrontList = fileList;
    },
    editIDcard(row) {
      this.oldRow = JSON.parse(JSON.stringify(row));
      row.isEdit = true;
    },
    getsrc(str) {
      if (!str) {
        return [];
      } else {
        let arr = str.split(",");
        let list = [];
        arr.forEach((item) => {
          let obj = {
            response: {
              data: {
                url: "",
              },
            },
          };
          obj.response.data.url = item;
          obj.url = item;
          list.push(obj);
        });
        return list;
      }
    },
    getFilePath(fileList) {
      let str = "";
      fileList.forEach((item) => {
        let url = item.response.data.url;
        str += item.response.data.url + ",";
      });
      if(str !== '') {
        return str.substr(0, str.length - 1);
      } else {
        return ''
      }
    },
    async getItem() {
      this.loadingFlag = true;
      let { data } = await getitem(this.$route.query.id);
      this.salesManForm = {...data,sex:data.sex && data.sex.code};
      this.salesManForm.positionStatus = this.salesManForm.positionStatus.code;
      let obj = {
        label: "身份证",
        idCardNumber: data.idCardNumber,
        idCardPastTime:data.idCardPastTime? data.idCardPastTime.substr(0, 10) : '',
        idCardIsForever: data.idCardPastTime ? false  : true,
        idCardFront: data.idCardFront,
        idCardReverse: data.idCardReverse,
        isEdit: false,
      };
      this.list.push(obj);
      this.address = [data.provinceId, data.cityId, data.districtId];
      this.loadingFlag = false;
      this.bankCard = data.bankCard;
    },
    beforeUpload(file) {
      let fileTypeList = [
        "image/png",
        "image/pjpeg",
        "image/jpeg",
        "image/bmp",
      ];
      const isJPG = fileTypeList.indexOf(file.type) > -1;
      const isLt2M = file.size / 1024 / 1024 < 5;

      if (!isJPG) {
        this.$message.error("上传图片格式错误!");
      }
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 2MB!");
      }
      return isJPG && isLt2M;
    },
    submit() {
      this.$refs.editForm.validate(async (valid) => {
        if (valid) {
          if (this.$route.query.id) {
            this.isSubmit = true;
            this.salesManForm.idCardFront = this.list[0].idCardFront;
            this.salesManForm.idCardNumber = this.list[0].idCardNumber;
            this.salesManForm.idCardPastTime = this.list[0].idCardPastTime;
            this.salesManForm.idCardReverse = this.list[0].idCardReverse;
            delete this.salesManForm.bankCards;
            let { data } = await editSalesMan(this.salesManForm);
            this.isSubmit = false;
            if (data) {
              this.$message.success("修改成功");
              setContextData("salesmanList_list", {
                current: 1,
                size: 10,
                model: {
                  approvalStatus: "PENDING",
                },
              });
              this.$router.push({
                path: "/businessCentric/salesmanList",
              });
            }
          } else {
            this.isSubmit = true;
            this.salesManForm.idCardFront = this.list[0].idCardFront;
            this.salesManForm.idCardNumber = this.list[0].idCardNumber;
            this.salesManForm.idCardPastTime =
              this.list[0].idCardPastTime + "T23:59:59";
            this.salesManForm.idCardReverse = this.list[0].idCardReverse;
            let { data } = await addSalesMan(this.salesManForm);
            this.isSubmit = false;
            if (data) {
              this.$message.success("添加成功");
              setContextData("salesmanList_list", {
                current: 1,
                size: 10,
                model: {
                  approvalStatus: "PENDING",
                },
              });
              this.$router.push({
                path: "/businessCentric/salesmanList",
              });
            }
          }
        } else {
          console.log("submit error!");
        }
      });
    },
  },
  // watch: {
  //   address(newl, old) {
  //     if (!newl.provinceId) {
  //       this.salesManForm.area = "";
  //     } else {
  //       this.salesManForm.area = newl;
  //     }
  //   },
  // },
  created() {
    if (this.$route.query.id) {
      this.getItem();
    } else {
      this.showUploadFlag = true;
      let obj = {
        label: "身份证",
        idCardNumber: "",
        idCardPastTime: "",
        idCardFront: "",
        idCardReverse: "",
        isEdit: false,
      };
      this.list.push(obj);
    }
    this.getareas();
  },
  components: {
    selectAddr,
    uploadId,
  },
};
</script>

<style lang="less" scoped>
.salesManDetail {
  border-top: 1px solid #ebecee;
  background-color: #fff;
  .top_title {
    line-height: 56px;
    border-bottom: 1px solid #eeeeee;
    font-size: 18px;
    margin-bottom: 30px;
    .flex_between_center {
      padding: 0 20px;
    }
  }
  .contemt {
    padding: 0px 20px;
    .formItem {
      min-width: 250px;
    }
    .item {
      width: 100%;
      margin-bottom: 30px;
      border-bottom: 1px solid #eeeeee;
      .title {
        padding: 0 0 15px;
        span {
          font-size: 16px;
          padding-left: 10px;
          border-left: 4px solid rgba(64, 158, 255, 1);
        }
        .text{
          font-size: 14px;
          margin-left: 10px;
          font-style: normal;
          color: #ccc;
        }
      }



      // /deep/ .el-upload {
      //   width: 40px;
      //   height: 40px;
      //   position: relative;
      // }
      // /deep/ .el-upload > i {
      //   font-size: 16px;
      //   position: absolute;
      //   left: 50%;
      //   top: 50%;
      //   -webkit-transform: translateX(-50%) translateY(-50%);
      //   transform: translateX(-50%) translateY(-50%);
      // }
      // /deep/ .el-upload-list .el-upload-list__item {
      //   width: 40px;
      //   height: 40px;
      // }
      // /deep/ .hide .el-upload--picture-card {
      //   display: none;
      // }
    }
  }
  /deep/ .el-icon-circle-close{
    color:#fff
  }

}
    /deep/ .avatar-uploader .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }
    /deep/ .avatar-uploader .el-upload:hover {
      border-color: #409eff;
    }
    /deep/ .avatar-uploader-icon {
      font-size: 24px;
      color: #8c939d;
      width: 40px;
      height: 40px;
      line-height: 40px;
      text-align: center;
    }
    /deep/ .avatar {
      width: 50px;
      height: 50px;
      display: block;
    }
</style>
