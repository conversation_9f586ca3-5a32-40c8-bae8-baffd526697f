<template>
    <div :class="$style.container">
        <el-form ref="form" :class="$style.wrap" :model="model">
            <div :class="$style.form">
                <el-row :gutter="8">
                    <el-col :span="8" v-for="(field, index) in fields" :key="field.prop">
                        <el-form-item v-if="shouldVisible(field.omit, index) || isExpanded" :prop="field.prop" :label="field.label"
                            :placeholder="field.placeholder">
                            <template v-if="field.type === 'Select'">
                                <el-select clearable :multiple="field.multiple || false" :placeholder="field.placeholder" v-model="model[field.prop]" filterable>
                                    <el-option v-for="item in field.options" :label="item.text" :value="item.value"
                                        :key="item.value" />
                                </el-select>
                            </template>
                            <template v-else-if="field.type === 'DateRange'">
                                <date-range-picker :class="$style.picker" v-model="model[field.prop]">
                                </date-range-picker>
                            </template>
                            <template v-else-if="field.prop === 'orderNo'">
                                <el-input :placeholder="field.placeholder" v-model.trim="model[field.prop]" @input="model[field.prop] = mixinReplaceStr(model[field.prop])"></el-input>
                            </template>
                            <template v-else-if="field.prop === 'saleMerchantName'">
                              <el-autocomplete
                                style="width: 100%;"
                                v-model="model[field.prop]"
                                :fetch-suggestions="queryMerchantName"
                                placeholder="请输入商家名称"
                              />
                            </template>
                            <template v-else>
                                <el-input :placeholder="field.placeholder" v-model.trim="model[field.prop]"></el-input>
                            </template>
                        </el-form-item>
                    </el-col>
                </el-row>
            </div>
            <div :class="$style.btns">
                <el-button @click="onSearch" type="primary">{{ searchText }}</el-button>
                <el-button @click="onReset">{{ resetText }}</el-button>
                <el-button v-if="omit" @click="onToggle" type="text">{{ isExpanded ? '收起' : '展开' }} <i :class="[isExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down']" />
                </el-button>
            </div>
        </el-form>
    </div>
</template>
<script>
import DateRangePicker from "@/components/RangePicker/index.vue";
import { getMerchantList2Keyword } from '@/api/trade';

export default {
    props: {
        model: {
            type: Object,
            default: () => { }
        },
        fields: {
            type: Array,
            default: () => []
        },
        omit: {
            type: Boolean,
            default: false
        },
        searchText: {
            type: String,
            default: '搜索'
        },
        resetText: {
            type: String,
            default: '重置'
        },
        commerceModel: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            isExpanded: false,
            timeout: null
        }
    },
    methods: {
        // 模糊查询商家名称
        queryMerchantName(query, cb) {
          clearTimeout(this.timeout)
          if (!query) {
            cb([])
            return
          }
          getMerchantList2Keyword(this.commerceModel, query).then(res => {
            this.timeout = setTimeout(() => cb(res), 500)
          })
        },
        shouldVisible(omit) {
            if (omit === undefined || !this.omit) return true;
        },
        onSearch() {
            this.$emit('search')
        },
        onReset() {
            this.$refs.form.resetFields();
            this.$emit('reset')
        },
        onToggle() {
            this.isExpanded = !this.isExpanded
        },
    },
    components: {
        DateRangePicker
    }
}
</script>
<style lang='scss' module>
.container {
    // margin: 16px 0;
}
.wrap {
    display: flex;
    align-items: flex-start;
    padding: 16px 16px 8px 16px;
    background-color: #fff;

    .form {
        flex: 1;
        padding-right: 6px;
    }

    .btns {
        flex: 0 0 auto;
    }

    :global {
        .el-select {
            width: 100%;
        }

        .el-form-item {
            margin-bottom: 8px;
        }
    }

    .picker {
        width: 100%;
    }
}
</style>