<template>
  <div class="list—index">
    <!--搜索Form-->
    <im-search-pad :has-expand="false" :is-expand.sync="isExpand" :model="model" @reset="reload" @search="searchLoad">
      <im-search-pad-item prop="during">
        <el-date-picker :default-time="['00:00:00', '23:59:59']" value-format="yyyy-MM-dd HH:mm:ss" v-model="model.during" type="datetimerange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </im-search-pad-item>
      <im-search-pad-item prop="saleMerchantName">
        <el-input v-model="model.saleMerchantName" @keyup.enter.native="searchLoad" placeholder="请输入商家名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="saleManName">
        <el-input v-model="model.saleManName" @keyup.enter.native="searchLoad" placeholder="请输入执行人姓名" />
      </im-search-pad-item>
      <im-search-pad-item prop="purMerchantName">
        <el-input v-model="model.purMerchantName" @keyup.enter.native="searchLoad" placeholder="请输入客户名称" />
      </im-search-pad-item>
    </im-search-pad>
    <!--  -->
    <div class="tab_bg">
      <!--Tabs布局-->
      <scroll-tabs @change="handleTabsChange" :enable-count="false" v-model="model.approvalStatus" :tabs="tabs"
        style="margin-bottom: 8px;">
        <template slot="append">
          <el-button @click="refresh">刷新</el-button>
          <paged-export v-if="checkPermission(['admin', 'admin-business-training:export'])" :max-usable="total" :before-close="handleExportBeforeClose" controllable></paged-export>
          <el-button v-throttle @click="onBatchAudit" v-if="['PENDING', 'REJECTED'].includes(model.approvalStatus)"
            type="primary">批量审核</el-button>
          <reject-popup @ok="handleRejectOk" :data="selects" v-if="['PENDING'].includes(model.approvalStatus)">
          </reject-popup>
        </template>
      </scroll-tabs>

      <!-- 分页tab -->
      <table-pager ref="pager-table" selection @selectionChangeHandle="handleTableSelectChange" :options="tableColumns" :remote-method="load" :data.sync="tableData"
         :pageSize="pageSize" :operation-width="150">
         <el-table-column label="执行时间" width="160" slot="createTime">
          <template slot-scope="{row}">
            {{row.approvalTime || row.createTime}}
          </template>
        </el-table-column>

        <el-table-column label="作业视频" width="80" align="center" slot="videoIds">
          <template slot-scope="{row}">
            <perview-video v-if="row.videoIds" :poster="row.videoCover" :url="row.videoIds"></perview-video>
          </template>
        </el-table-column>

        <el-table-column label="合同照片" width="160" slot="pictIdS">
          <slot slot-scope="{row}">
            <!-- <span
              v-if="row.pictIdS == null || row.pictIdS == '' || row.pictIdS == undefined || row.pictIdS == 'null'"></span>
            <div v-else>
              <img style="width:50px;height:50px;margin-right:10px;" v-for="(item,index) in row.pictIdS.split(',')"
                :key="index" :src="item" alt="">
            </div> -->
            <div class="img-cell">
              <div v-if="row.pictIdS.split(',').length < 2">
                <img style="width:50px;height:50px;margin-right:5px" v-for="(i, indexs) in row.pictIdS.split(',')"
                  :key="indexs" :src="i" alt="">
              </div>
              <div class="img_box" v-else>
                <img style="width:50px;height:50px;margin-right:5px" :src="row.pictIdS.split(',')[0]" alt="">
                <div class="img-posi">
                  <img style="width:50px;height:50px;margin-right:5px" :src="row.pictIdS.split(',')[1]" alt="">
                  <div class="img_mask"> +{{ row.pictIdS.split(',').length - 1 }}</div>
                </div>
              </div>
            </div>
          </slot>
        </el-table-column>
        <el-table-column slot="approvalStatus" label="合同状态" width="80" align="center">
          <template slot-scope="scope">
            {{ scope.row.approvalStatus && scope.row.approvalStatus.desc }}
          </template>
        </el-table-column>
        <!--操作栏-->
        <div slot-scope="{row}">
          <el-row class="table-edit-row">
            <span class="table-edit-row-item">
              <el-button v-if="checkPermission(['admin', 'admin-business-training:photographPreview'])" type="text" @click="handleBigImage(row)">预览图片</el-button>
              <el-image :ref="`ref${row.id}`" style="width:0;height:0" :src="previewDetail[0]"
                :preview-src-list="previewDetail"></el-image>
            </span>
          </el-row>
        </div>
      </table-pager>
    </div>
  </div>
</template>


<script>
const TableColumns = [{
  label: "商家名称",
  name: "saleMerchantName",
  prop: "saleMerchantName",
  width: "150"
},
{
  label: "执行人姓名",
  name: "saleManName",
  prop: "saleManName",
  width: "150"
},
{
  label: "执行人编码",
  name: "saleManCode",
  prop: "saleManCode",
  width: "150"
},
{
  label: "执行时间",
  name: "createTime",
  prop: 'createTime',
  width: "150",
  slot: true
},
{
  label: "客户名称",
  name: "purMerchantName",
  prop: 'purMerchantName',
  width: "150"
},
{
  label: "联系人",
  name: "ceoName",
  prop: 'ceoName',
  width: "100"
},
{
  label: "联系电话",
  name: "ceoMobile",
  prop: 'ceoMobile',
  width: "140"
},
{
  label: "合同金额",
  name: "contractAmount",
  prop: 'contractAmount',
  width: "140"
},
{
  label: "所在地区",
  name: "region",
  prop: 'region',
  width: "160"
},
{
  label: "注册地址",
  name: "registerAddress",
  prop: 'registerAddress',
  width: "180",
},
{
    label: "合同状态",
    name: "approvalStatus",
    prop: 'approvalStatus',
    width: "80",
    slot: true
  },
  {
    label: "驳回原因",
    name: "rejectReason",
    prop: 'rejectReason',
    width: "180",
  },
{
  label: "备注",
  name: "description",
  prop: 'description',
  width: "140",
},
{
  label: "作业视频",
  name: "videoIds",
  prop: 'videoIds',
  width: "200",
  slot: true
},
{
  label: "合同照片",
  name: "pictIdS",
  prop: 'pictIdS',
  width: "200",
  slot: true
}

];
const TableColumnList = [];
for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({
    key: i,
    ...TableColumns[i]
  });
}
import PerviewVideo from './components/BasicComponents/PerviewVideo.vue';
import RejectPopup from './components/BasicComponents/RejectPopup.vue';
import ScrollTabs from '@/components/ScrollTabs/ScrollTabs.vue';
import PagedExport from '@/components/PagedExport/index.vue';
import { formatDataTime } from '@/utils/index';
import {
  purMerchantTraining,
  updateContractStatusToAccept,
  fetchContractExcelBlob
} from '@/api/salemanCenter/index' // TODO 替换成对应用的列表api
import { exoprtToExcel } from "@/utils/commons";
import checkPermission from '@/utils/permission';
export default {
  //import引入的组件
  components: { 
    PerviewVideo,
    ScrollTabs,
    PagedExport,
    RejectPopup 
  },

  data() {
    return {
      isExpand: false,
      model: {

        during: [],
        approvalStatus: '',
        purMerchantName: '',
        saleManName: '',
        saleMerchantName: ''
      },
      tableData: [],
      tableColumns: TableColumnList,
      pageSize: 10,
      previewDetail: [],
      total: 0,
      tabs: [
        { text: '全部', value: '', hide: !checkPermission(['admin', 'admin-business-training:allView']) },
        { text: '待审核', value: 'PENDING', hide: !checkPermission(['admin', 'admin-business-training:pendingAuditView']) },
        { text: '已审核', value: 'ACCEPTED', hide: !checkPermission(['admin', 'admin-business-training:hadAuditView']) },
        { text: '已驳回', value: 'REJECTED', hide: !checkPermission(['admin', 'admin-business-training:rejectedView']) },
      ],
      selects: [], // 已选择列表
    };
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() { },

  computed: {},

  created() { },

  filters: {},

  //方法集合
  methods: {
    checkPermission,
    /**
     * 批量审核
     */
    async onBatchAudit() {

      if (!this.selects.length) {
        this.$message.warning('至少选择一条数据');
        return
      }
      await this.$confirm('确定要通过些合同吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      const params = {
        'ids[]': this.selects.map(({ id }) => id).join(',')
      }
      const { code } = await updateContractStatusToAccept(params)
      if (code === 0) {
        this.$message.success('审核成功')
        this.resetTableSelected()
        this.refresh()
      }

    },
    /**
     * 合同打包下载
     */
    async onPacketDownload() {
      if (!this.selects.length) {
        this.$message.warning('至少选择一条数据');
        return
      }
      const params = {
        current: 1,
        map: {},
        model: {
          ...this.model,
          contractIdList: this.selects.map(({ id }) => id)
        },
        order: "descending",
        size: 10,
        sort: 'approvalTime'
      }
      const { code } = await addContractPackageDownloadRecords(params)
      if (code === 0) MessageConfirmExport()
    },
    handleRejectOk() {
      this.resetTableSelected()
      this.refresh()
    },

    /**
     * 表格选择
     */
    handleTableSelectChange(selects) {
      this.selects = selects
      console.log(this.handleTableSelectChange.name, selects)
    },
    /**
     * 导出弹窗关闭之前的回调函数
     * start 开始页码  end 结束页码
     * params: {startPage: number, endPage: number}
     */
    async handleExportBeforeClose({startPage, endPage}) {
      const loading = this.$loading({
        lock: true,
        text: '正在导出中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.4)'
      });
      try {
        const { data } = await fetchContractExcelBlob({
          model: {
            ...this.model,
            startPage: `${startPage}`,
            endPage: `${endPage}`,
          }
        })
        exoprtToExcel(data, `合同列表${formatDataTime('yyyyMMDDHHmmss')}.xlsx`)
      } catch (error) {
        console.error('error', error);
      } finally {
        loading.close();
      }

    },

    handleTabsChange() {
      this.resetTableSelected();
      this.$refs['pager-table'].doRefresh();
    },

    resetTableSelected() {
      this.selects = [];
      this.$refs['pager-table'].clearSelection();
    },
    refresh() {
      this.$refs['pager-table'].doRefresh()
    },
    async load(params) {
      const { during, ...model } = this.model;
      const [approvalTime_st, approvalTime_et] = during
      let listQuery = {
        map: { approvalTime_st, approvalTime_et },
        model: {
          ...model
        },
        order: "descending",
        sort: 'approvalTime'
      };
      Object.assign(listQuery, params);
      const result = await purMerchantTraining(listQuery);
      this.total = result.data.total
      return result;
    },
    searchLoad() {
      console.log('search')
      this.handleRefresh({
        page: 1,
        pageSize: this.pageSize
      })
    },
    reload() {
      this.$refs['tabs-layout'].reset()
      this.handleRefresh({
        page: 1,
        pageSize: this.pageSize
      })
    },
    // 预览图片
    async handleBigImage(row) {
      this.previewDetail = [];
      if (row.pictIdS != null && row.pictIdS != undefined && row.pictIdS != 'null') {
        if (row.pictIdS.split(',').length == 0) {
          this.$message.warning('无图片可查看');
          return
        }
        row.pictIdS.split(',').forEach(item => {
          this.previewDetail.push(item);
        });
        this.$refs[`ref${row.id}`].showViewer = true;
      }
      console.log('this.previewDetail', this.previewDetail);
    },
    handleRefresh(pageParams) {
      this.$refs['pager-table'].doRefresh(pageParams)
    },
  },
};

</script>


<style lang='scss' scoped>
.img-cell {
  display: flex;
  align-items: center;

  .img_box {
    display: flex;
    align-items: center;
    position: relative;

    .img-posi {
      width: 50px;
      height: 50px;
    }

    .img_mask {
      position: absolute;
      width: 50px;
      height: 50px;
      top: 0;
      left: 55px;
      line-height: 50px;
      text-align: center;
      background-color: rgba($color: #000000, $alpha: 0.6);
      color: #fff;
    }
  }
}
</style>
