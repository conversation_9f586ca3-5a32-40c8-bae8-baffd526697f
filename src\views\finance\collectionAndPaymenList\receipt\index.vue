<template>
  <div class="archivesPageContent">
    <im-search-pad
      :is-expand.sync="isExpand"
      :model="listQuery"
      @reset="resetForm"
      @search="onSearchSubmitFun"
    >
      <im-search-pad-item prop="id">
        <el-input v-model="listQuery.model.id" placeholder="请输入收款单号" />
      </im-search-pad-item>
      <im-search-pad-item prop="businessNo">
        <el-input v-model="listQuery.model.businessNo" placeholder="请输入业务单号" />
      </im-search-pad-item>
      <im-search-pad-item prop="payerName">
        <el-input v-model="listQuery.model.payerName" placeholder="请输入付款方" />
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="type">
        <el-select v-model="listQuery.model.type" placeholder="请选择收款单">
          <!-- ORDER:0,订单支付;CASH:1,余额充值;SELLER:2,商家保证金缴纳;DEPOSIT:3,品种保证金缴纳;SERVICE:4,平台技术服务费缴纳 -->
          <el-option label="订单支付" value="ORDER"></el-option>
          <el-option label="余额充值" value="CASH"></el-option>
          <el-option label="商家保证金缴纳" value="SELLER"></el-option>
          <el-option label="品种保证金缴纳" value="DEPOSIT"></el-option>
          <el-option label="平台技术服务费缴纳" value="SERVICE"></el-option>
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="payerType">
        <el-select
          v-model="listQuery.model.payerType"
          placeholder="请选择付款方"
        >
          <el-option label="销售商" value="SELLER"></el-option>
          <el-option label="采购商" value="PURCHASERS"></el-option>
          <el-option label="业务员" value="SALE"></el-option>
        </el-select>
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        v-model="listQuery.model.paymentStatus"
        :tabs="tabs"
        @change="chageTabsFun"
      />
      <div class="table">
        <el-table
          ref="table"
          v-if="list"
          @selection-change="selectTableItemFun"
          v-loading="listLoading"
          :data="list"
          row-key="id"
          border
          fit
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column
            align="center"
            width="65"
            :render-header="renderHeader"
            fixed
          >
            <template slot-scope="scope">
              <span>{{ scope.$index + 1 }} </span>
            </template>
          </el-table-column>
          <el-table-column
            type="selection"
            width="55"
            align="center"
          ></el-table-column>
          <el-table-column
            v-for="(item, index) in tableTitle"
            :key="index"
            :min-width="item.width ? item.width : '350px'"
            :label="item.label"
            show-overflow-tooltip
            align="left"
          >
            <template slot-scope="{ row }">
            <span
              v-if="
                item.name == 'payerType' ||
                item.name == 'method' ||
                item.name == 'type'
              "
            >{{ row[item.name].desc }}</span
            >
              <span v-else-if="item.name == 'paymentAmount'">{{
                  row[item.name] | getDecimals
                }}</span>
              <span v-else>{{ row[item.name] }}</span>
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            align="center"
            label="操作"
            width="160"
            class="itemAction"
          >
            <template slot-scope="{ row }">
              <el-row class="table-edit-row">
                <span v-if="listQuery.model.paymentStatus == 'WAIT' && checkPermission(['admin', 'admin-finance-receipts:confirm'])" class="table-edit-row-item">
                  <el-button type="text" @click="acceptedFun(row)">确认收款</el-button>
                </span>
                <span class="table-edit-row-item" v-if="checkPermission(['admin', 'admin-finance-receipts:reHandle'])">
                  <el-button type="text"  @click="detailFun(row.id)">查看详情</el-button>
                </span>
              </el-row>

            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-if="total > 0"
          :pageSizes="[10, 20, 50, 100]"
          :total="total"
          :page.sync="listQuery.current"
          :limit.sync="listQuery.size"
          @pagination="getlist"
        />
      </div>
    </div>
    <el-dialog
      append-to-body
      title="审核收款单"
      :visible.sync="acceptedFlag"
      width="30%"
      @close="close"
      :close-on-click-modal="false"
    >
      <el-form ref="accepted" :model="acceptedForm" label-width="100px">
        <el-form-item class="formItem" prop="businessNo" label="业务单号:">
          {{ row.businessNo }}
        </el-form-item>
        <el-form-item class="formItem" prop="payerName" label="付款方:">
          {{ row.payerName }}
        </el-form-item>
        <el-form-item class="formItem" prop="payAccountName" label="应付金额(元):">
          {{ row.paymentAmount }}
        </el-form-item>
        <el-form-item class="formItem" prop="method" label="支付方式:">
          {{ row.method && row.method.desc }}
        </el-form-item>
        <el-form-item class="formItem" prop="paymentStatus" label="支付状态:">
          {{ row.paymentStatus && row.paymentStatus.desc }}
        </el-form-item>
        <el-form-item class="formItem" prop="remarks" label="付款账号:">
          {{ row.financePaymentFlow.payAccount }}
        </el-form-item>
        <el-form-item class="formItem" prop="remarks" label="付款名称:">
          {{ row.financePaymentFlow.payAccountName }}
        </el-form-item>
        <el-form-item class="formItem" prop="remarks" label="付款时间:">
          {{
            row.financePaymentFlow.paymentTime
              ? row.financePaymentFlow.paymentTime.substr(0, 10)
              : ""
          }}
        </el-form-item>
        <el-form-item class="formItem" prop="remarks" label="付款流水号:">
          {{ row.financePaymentFlow.serialNumber }}
        </el-form-item>
        <el-form-item class="formItem" prop="remarks" label="付款凭证:">
          <el-image
            v-if="row.financePaymentFlow && row.financePaymentFlow.certificatePath"
            style="width: 80px; height: 80px"
            :src="row.financePaymentFlow.certificatePath"
            :preview-src-list="[row.financePaymentFlow.certificatePath]"
          >
          </el-image>
          <div v-if="row.financePaymentFlow && row.financePaymentFlow.certificatePath" style="font-size: 14px; color: #ccc">点击图片可放大预览</div>
        </el-form-item>
        <el-form-item
          class="formItem"
          prop="remarks"
          label="备注信息:"
          :rules="[
            { required: true, message: '请输入备注信息', trigger: 'blur' },
          ]"
        >
          <el-input
            style="width: 90%; min-width: 200px"
            type="textarea"
            rows="3"
            v-model="acceptedForm.remarks"
            placeholder="请输入备注信息"
          >
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="acceptedFormFun">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from "@/components/Pagination";
import tableInfo from "@/views/finance/collectionAndPaymenList/receipt/tableInfo";
import {
  list,
  confirmReceipt,
  detail,
} from "@/api/finance/collectionAndPaymenList/receipt";
import { setContextData, getContextData } from "@/utils/auth";
import TabsLayout from '@/components/TabsLayout'
import checkPermission from "@/utils/permission";

export default {
  data() {
    return {
      isExpand: false,
      listLoading: false,
      list: [],
      // tabType: "WAIT",
      listQuery: {
        current: 1,
        size: 10,
        model: {
          paymentStatus: "WAIT",
        },
      },
      tabs: [
        {
          name: '待收款',
          value: 'WAIT',
          hide: !checkPermission(['admin', 'admin-finance-receipts:unhandledView'])
        },
        {
          name: '收款完成',
          value: 'FINISH',
          hide: !checkPermission(['admin', 'admin-finance-receipts:handledView'])
        },
        {
          name: '收款关闭',
          value: 'COLSE',
          hide: !checkPermission(['admin', 'admin-finance-receipts:closedView'])
        }
      ],
      acceptedFlag: false,
      acceptedForm: {},
      row: {
        financePaymentFlow: {},
      },
      total: 0,
      cityValue: [],
      tableTitle: [],
      tableSelectTitle: [0, 1, 2, 3],
      multipleSelection: [],
      multipleSelectionId: [],
      showSelectTitle: false,
      itemLoading: false,
    };
  },
  methods: {
    checkPermission,
    async getitem(id) {
      this.loading = true;
      let { data } = await detail(id);
      this.loading = false;
      this.row = data;
      this.rowitem = data.financePaymentFlow;
    },
    chageTabsFun() {
      this.listQuery.current = 1;
      this.initTbaleTitle();
      this.list = [];
      this.getlist();
    },
    close() {
      this.acceptedFlag = false;
      this.$refs.accepted.resetFields();
    },
    detailFun(id) {
      setContextData("receipt_detail", this.listQuery);
      this.$router.push({
        path: "/finance/collectionAndPaymen/receipt/detail",
        query: {
          id,
        },
      });
    },
    acceptedFun(row) {
      this.acceptedFlag = true;
      this.getitem(row.id);
    },
    acceptedFormFun() {
      this.$refs.accepted.validate(async (vald) => {
        if (vald) {
          let { data } = await confirmReceipt({
            id: this.row.id,
            remarks: this.acceptedForm.remarks,
          });
          if (data) {
            this.$message.success("已确认收款该收款单");
            this.listQuery = {
              current: 1,
              size: 10,
              model: {
                paymentStatus: "FINISH",
              },
            };
            this.listQuery.model.paymentStatus = "FINISH";
            this.getlist();
            this.acceptedFlag = false;
          }
        }
      });
      return;
    },
    cityChange(e) {
      this.listQuery.model.provinceId = e[0];
      this.listQuery.model.cityId = e[1];
      this.listQuery.model.countyId = e[2];
    },
    resetForm(type) {
      this.listQuery = {
        current: 1,
        size: 10,
        model: {
          paymentStatus: this.listQuery.model.paymentStatus,
        },
      };
      this.list = [];
      this.getlist();
    },
    selectTableItemFun: function (val) {
      // let arr = [];
      // val.forEach((item) => {
      //   arr.push(item.id);
      // });
      // this.multipleSelection = val;
      // this.multipleSelectionId = arr;
    },
    onSearchSubmitFun() {
      this.getlist();
    },
    async getlist() {
      this.listLoading = true;
      let { data } = await list(this.listQuery);
      this.listLoading = false;
      this.total = data.total;
      this.list = data.records;
    },
    initTbaleTitle() {
      this.tableSelectTitle = [];
      console.log(this.listQuery.model.paymentStatus)
      this.tableTitle = tableInfo[this.listQuery.model.paymentStatus];
    },
    renderHeader(h, { column }) {
      var titles = tableInfo[this.listQuery.model.paymentStatus];
      var titlesName = ["显示字段项", "隐藏字段项"];
      return (
        <div style="position:relative">
          <div onClick={this.showHeaer}>
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="640px"
            center
            append-to-body={true}
          >
            <el-transfer
              vModel={this.tableSelectTitle}
              data={titles}
              titles={titlesName}
              onChange={this.setleftTitleFun}
            ></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button onClick={this.closeHeaer}>取消</el-button>
              <el-button type="primary" onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      );
    },
    setleftTitleFun(val) {
      this.tableSelectTitle = val;
    },
    showHeaer: function () {
      this.showSelectTitle = true;
    },
    closeHeaer: function () {
      this.showSelectTitle = false;
      this.tableSelectTitle = [];
    },
    setHeaer: function () {
      var titles = tableInfo[this.listQuery.model.paymentStatus];
      var listinfo = titles.filter((element, index, self) => {
        return !this.tableSelectTitle.includes(element.key);
      });
      this.tableTitle = listinfo;
      this.showSelectTitle = !this.showSelectTitle;
    },
  },
  created() {
    this.initTbaleTitle();
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (from.path == "/finance/collectionAndPaymen/receipt/detail") {
        if (getContextData("receipt_detail") != "") {
          vm.listQuery = getContextData("receipt_detail");
          vm.tabType = vm.listQuery.model.paymentStatus;
        }
      };
      vm.listQuery = {
        current: 1,
        size: 10,
        model: {
          paymentStatus: "WAIT",
        },
      };
      vm.initTbaleTitle();
      vm.getlist();
    });
  },
  components: {
    Pagination,
    TabsLayout
  },
};
</script>


<style lang="less" scoped>
.archivesPageContent {
  padding: 0;
  .temp_searchBox {
    height: 64px;
    overflow: hidden;
    margin-bottom: 0;
  }
  .form-inline {
    height: 60px;
    overflow: hidden;
  }
  .title {
    border-bottom: 2px solid #ebecee;
    margin-bottom: 16px;
    span {
      margin-bottom: -2px;
      padding: 0 15px;
      height: 40px;
      line-height: 30px;
      display: block;
      background: rgba(255, 255, 255, 0);
      border-bottom: 2px solid rgb(64, 158, 255);
      font-size: 16px;
      font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
        "PingFang SC"-400;
      font-weight: 400;
      color: rgb(64, 158, 255);
    }
  }

  .formItem {
    width: 586px;
  }
  .line {
    color: #dfe6ec;
    margin: 0 6px;
  }
  .typeTabs {
    height: 40px;
    margin-bottom: -2px;
    margin-left: 30px;
  }
}
</style>
<style >
.el-icon-circle-close {
  color: #fff;
}
</style>
