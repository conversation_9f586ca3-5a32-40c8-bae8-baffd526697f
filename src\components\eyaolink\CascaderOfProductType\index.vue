<template>
  <div  v-if="show">
      <el-cascader placeholder="请选择产品分类" 
      :props="{
        emitPath:false
      }"
      :show-all-levels="false"
      :style="{width:width}" 
      v-model="modelVal" 
      :options="treeDatas"></el-cascader>
  </div>
</template>
<script>
import { list,getChildren } from "@/api/products/categoryPlatform";
export default {
  data() {
    return {
      show:false,
      listQuery: {
          model:{
            // parentId:0,
            label:""
          },
          current:1,
          size:99999
      },
      treeDatas:[]
    };
  },
  props: {
    width:{
      type:String,
      default:"250px"
    },
    valueProp:{
      type:String,
      default:"id"
    },
    selectId:{
      type:String,
      default:"0",
      required: true,
    }
  },
  computed: {
    modelVal: {
      get() {
        return this.selectId
      },
      set(val) {
        this.$emit('update:selectId', val+"")
      }
    }
  },
  methods: {
    generateOptions(params) {//生成Cascader级联数据
        var result = [];
        for (let param of params) {
            if (param.parentId == "0") {//判断是否为顶层节点
                var parent = {
                    'label': param.label,
                    'id': param.id+"",
                    'value': param[this.valueProp]+""
                }
                // parent.children = this.getchilds(param.id, params);//获取子节点
                if(this.getchilds(param.id, params).length>0){
                  parent.children = this.getchilds(param.id, params);//获取子节点
                }
                result.push(parent);
            }
        }
        return result;
    },
 
    getchilds(id, array) {
        let childs = new Array();
        for (let arr of array) {//循环获取子节点
            if (arr.parentId == id) {
                childs.push({
                    'label': arr.label,
                    'id': arr.id+"",
                    'value': arr[this.valueProp]+""
                });
            }
        }
        for (let child of childs) {//获取子节点的子节点
            let childscopy = this.getchilds(child.id, array);//递归获取子节点
            if (childscopy.length > 0) {
              // console.log('children',childscopy);
                child.children = childscopy;
            }
        }
        return childs;
    },
    async reloadTreeNode(parentId){
        const formData = new FormData();
        formData.append('id', parentId);
        const { data } = await getChildren(formData);
        // data.forEach(element => {
        //   element['level']=0
        //   element['hasChildren']=true
        // });
        this.treeDatas=data
    },
    async listFun(){
        const { data } = await list(this.listQuery);
        if(data!=null) this.treeDatas=this.generateOptions(data.records)
    },
  },
  mounted() {
      this.show=true;
       this.listFun()
  },
  beforeDestroy() {}
};
</script>
<style lang="less" scoped></style>
