<template>
<!-- 头 -->
  <div class="headerBox" >
    <div class="header ">
      <!-- <div class="navTop">
        <div class="navBackFun" >
          <img src="http://eyaolink-dev-bucket.oss-cn-shenzhen.aliyuncs.com/wechat/20210113/images/icon/icon_back.png"/>
        </div>
        <div class="line">|</div>
        <div class="toIndex" >
          <img src="http://eyaolink-dev-bucket.oss-cn-shenzhen.aliyuncs.com/wechat/20210113/images/icon/icon_home.png"/> 
        </div>
      </div> -->
      <div class="searchInput">
        <input value="" disabled placeholder="搜索商家商品"/>
        <div class="search">
          <img  src="http://eyaolink-dev-bucket.oss-cn-shenzhen.aliyuncs.com/wechat/20210113/images/icon/icon_search.png" />
        </div>
      </div>
    </div>
  </div>
  <!-- 头 end -->
</template>
<script>
export default {
data() {
return {};
},
props: {},
methods: {},
mounted() {},
beforeDestroy() {}
};
</script>
<style lang="less" scoped>
.headerBox {
  width: 375px;
  position: relative;
  left: 0;
  top: 0;
  z-index: 999
}

.header {
  height: 88/2px;
  width: 710/2px;
  margin: 37/2px auto 0;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.header .navTop {
  width: 176/2px;
  height: 64/2px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 33/2px;
  border: 1/2px solid rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20/2px;
}

.header .navBackFun {
  height: 44/2px;
  width: 44/2px;
}

.header .navBackFun image {
  height: 44/2px;
  width: 44/2px;
}

.header .toIndex {
  height: 44/2px;
  width: 44/2px;
}

.header .toIndex image {
  height: 44/2px;
  width: 44/2px;
}

.header .line {
  height: 44/2px;
  line-height: 44/2px;
  background: #000;
  opacity: 0.2;
  margin: 0 22/2px;
  width: 1/2px;
}

.headerBox .header .searchInput {
  position: relative;
}

.headerBox .header .searchInput, .headerBox .header .searchInput input {
  width: 686/2px;
  height: 64/2px;
  background: #f1f1f3;
  border-radius: 32/2px;
}

.headerBox .header .searchInput input {
  width: 646/2px;
  margin: 0 0 0 50/2px;
  font-size: 28/2px;
  font-weight: 400;
  color: #2e3a4e;
  background: rgba(255, 255, 255, 0);
}

.headerBox .header .searchInput input[disabled] {
  color: #b5b9c0;
  border: none;
}

.headerBox .header .searchInput input::placeholder {
  color: #b5b9c0;
}

.headerBox .header .searchInput .search {
  width: 26/2px;
  height: 26/2px;
  position: absolute;
  left: 12/2px;
  top: 50%;
  margin-top: -20/2px;
  display: block;
  z-index: 999;
}

.headerBox .header .searchInput .search image {
  width: 26/2px;
  height: 26/2px;
}

</style>