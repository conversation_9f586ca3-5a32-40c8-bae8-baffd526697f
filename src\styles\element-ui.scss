// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    min-width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.el-tooltip__popper.is-dark {
  max-width: 45%;
}

.el-table__header{
  background-color: rgba(250, 250, 250, 1);
  th {
    padding: 12px 0;
    background: rgba(250, 250, 250, 1);
    .cell{
      font-weight:bold;
      color:rgba(0,0,0,0.85);
      font-size: 14px;
      .el-icon-menu{
        font-weight: normal;
      }
    }
  }
}
.el-table td{
  padding: 4px 0;
  height: 40px;
  box-sizing: border-box;
  text-overflow: ellipsis;
  vertical-align: middle;
  &.img-cell{
    .cell{
      line-height: 0;
    }
  }
}
.el-button {
  border-radius: 2px !important;
}
.el-button--small{
  font-size: 14px;
}
.el-button.el-button--small{
  font-size: 14px;
}
.table-edit-row {
  .table-edit-row-item {
    display: inline-block;
    line-height: 1;
    .el-button--small{
      font-size: 14px;
    }

    &:not(:last-child) {
      border-right: 1px solid rgba(0, 0, 0, 0.09);
      margin-right: 5px;
      padding-right: 8px;
    }

    .el-button {
      padding-top: 0;
      padding-bottom: 0;
    }

    .el-dropdown {
      .el-icon--right {
        margin-left: 0;
      }
    }
  }
}

.el-form-item--medium .el-form-item__content, .el-form-item--small .el-form-item__content {
  color: rgba(0, 0, 0, .6) !important;
}

.el-button--default.is-disabled {
  color: rgba(0, 0, 0, 0.3) !important;
}

.varietiesBan-list{
  &-container{
    background-color: #ffffff;
    .varietiesBan-list-tabs-wrapper{
      position: relative;
      .varietiesBan-list-tabs{
        min-height: 42px;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #dcdde0;
        font-size: 14px;
        color: #303133;
        margin-bottom: 16px;
        .tab{
          height: 42px;
          line-height: 40px;
          padding: 0 10px;
          margin-right: 9px;
          position: relative;
          cursor: pointer;
          &:after {
            content: '';
            width: 100%;
            height: 2px;
            background-color: #0056E5;
            position: absolute;
            bottom: -1px;
            left: 0;
            display: none;
          }
          &.active:after {
            display: block;
          }
          &.active {
            color: #0056E5;
          }
        }
      }
      .operations{
        position: absolute;
        right: 0;
        bottom: 8px;
      }
    }
  }
}
