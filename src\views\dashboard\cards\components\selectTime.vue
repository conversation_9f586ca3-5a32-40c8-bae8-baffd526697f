<template>
  <div class="btn">
    <el-select
      style="width: 130px"
      @change="timeTypeChange"
      v-model="selectOptions"
      placeholder="请选择"
    >
      <el-option
        v-for="item in options"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      >
      </el-option>
    </el-select>

    <el-date-picker
      key="1"
      v-if="selectOptions == 1 || selectOptions == 2 || selectOptions == 5"
      :disabled="selectOptions == 1 || selectOptions == 2"
      style="margin: 0 10px; width: 260px"
      v-model="orderTime"
      @change="getTime"
      value-format="yyyy-MM-dd"
      type="daterange"
      range-separator="至"
      start-placeholder="开始日期"
      end-placeholder="结束日期"
      :picker-options="disAbledFun"
    >
    </el-date-picker>
    <span v-if="selectOptions == 3">
      <el-date-picker
        style="margin: 0 10px; width: 260px"
        key="3"

        v-model="orderWeek"
        @change="getWeek"
        type="week"
        format="yyyy 第 WW 周"
        placeholder="选择周"
        :picker-options="disAbledFun"
      >
      </el-date-picker>
    </span>

    <el-date-picker

      style="margin: 0 10px; width: 260px"
      key="4"
      v-if="selectOptions == 4"
      v-model="orderMonth"
      @change="getMonth"
      type="month"
      placeholder="选择月"
      :picker-options="disAbledFun"
    >
    </el-date-picker>

    <el-select
      v-if="orderTypes"
      key="5"
      style="width: 100px"
      @change="orderTypeChange"

      v-model="query.orderStatus"
      placeholder="请选择"
    >
      <el-option
        v-for="item in orderType"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      >
      </el-option>
    </el-select>
  </div>
</template>

<script>
import { parseTime } from "@/utils/index";
export default {
  data() {
    return {
      options: [
        {
          value: 1,
          label: "近七天",
        },
        {
          value: 2,
          label: "近三十天",
        },
        {
          value: 3,
          label: "自然周",
        },
        {
          value: 4,
          label: "自然月",
        },
        {
          value: 5,
          label: "自定义",
        },
      ],
      // PART_DELIVERY:130,部分发货
      orderType: [
        { value: "1", label: "待审核" },
        { value: "100", label: "待付款" },
        { value: "120", label: "待发货" },
        // { value: "4", label: "发货中" },
        { value: "140", label: "已发货" },
        { value: "200", label: "已完成" },
        { value: "210", label: "已取消" },
      ],
      selectOptions: 1,
      orderTime: [],
      orderWeek: "",
      orderMonth: "",
      disAbledFun: {
        firstDayOfWeek: 1,
        disabledDate(date) {
          return date.getTime() > Date.now();
        },
      },
      query: {
        endTime: "",
        naturalWeek: "N",
        startTime: "",
      },
    };
  },
  methods: {
    getTime(val) {
      this.query.startTime = this.orderTime[0];
      this.endTime = this.orderTime[1];
      console.log(this.orderTime);
      console.log(val);
      this.upDate();
    },
    upDate() {
      this.$emit("update:Query", this.query);
      this.$emit("getDetail", this.query);
    },
    getMonth(row) {
      let month = new Date(row).getMonth() + 1;
      var new_year = new Date(row).getFullYear();
      var new_month = month++;
      if (month > 12) {
        new_month -= 12;
        new_year++;
      }
      var new_date = new Date(new_year, new_month, 1);
      this.orderTime = [
        parseTime(new Date(row).getTime(), "{y}-{m}-{d}"),
        parseTime(
          new Date(new_date.getTime() - 1000 * 60 * 60 * 24),
          "{y}-{m}-{d}"
        ),
      ];
      this.query.startTime = parseTime(new Date(row).getTime(), "{y}-{m}-{d}");
      this.query.endTime = parseTime(
        new Date(new_date.getTime() - 1000 * 60 * 60 * 24),
        "{y}-{m}-{d}"
      );
      this.upDate();
    },
    getWeek(row) {
      // this.orderTime = [
      //   parseTime(new Date(row).getTime(), "{y}-{m}-{d}"),
      //   parseTime(
      //     new Date(row).getTime() + 3600 * 1000 * 24 * 7,
      //     "{y}-{m}-{d}"
      //   ),
      // ];
      // this.query.startTime = parseTime(new Date(row).getTime(), "{y}-{m}-{d}");
      // this.query.endTime = parseTime(
      //   new Date(row).getTime() + 3600 * 1000 * 24 * 7,
      //   "{y}-{m}-{d}"
      // );
      let weekFirstDay = new Date(row).getTime() - 3600 * 1000 * 24;
      this.orderTime = [
        parseTime(weekFirstDay, "{y}-{m}-{d}"),
        parseTime(weekFirstDay + 3600 * 1000 * 24 * 6, "{y}-{m}-{d}")
      ];
      this.query.startTime = parseTime(weekFirstDay, "{y}-{m}-{d}");
      this.query.endTime = parseTime(weekFirstDay + 3600 * 1000 * 24 * 6, "{y}-{m}-{d}");
      this.upDate();
    },
    orderTypeChange(val) {
      this.upDate();
    },
    timeTypeChange(row) {
      this.orderTime = [];
      if (row == 1) {
        this.orderTime = [
          parseTime(new Date().getTime() - 3600 * 24 * 6 * 1000, "{y}-{m}-{d}"),
          parseTime(new Date().getTime(), "{y}-{m}-{d}"),
        ];
        this.query.startTime = parseTime(
          new Date().getTime() - 3600 * 24 * 6 * 1000,
          "{y}-{m}-{d}"
        );
        this.query.endTime = parseTime(new Date().getTime(), "{y}-{m}-{d}");
        this.upDate();
      } else if (row == 2) {
        this.orderTime = [
          parseTime(
            new Date().getTime() - 3600 * 24 * 30 * 1000,
            "{y}-{m}-{d}"
          ),
          parseTime(new Date().getTime(), "{y}-{m}-{d}"),
        ];
        this.query.startTime = parseTime(
          new Date().getTime() - 3600 * 24 * 30 * 1000,
          "{y}-{m}-{d}"
        );
        this.query.endTime = parseTime(new Date().getTime(), "{y}-{m}-{d}");
        this.upDate();
      }
      row == 3
        ? (this.query.naturalWeek = "Y")
        : (this.query.naturalWeek = "N");
    },
  },
  created() {
    this.orderTime = [
      parseTime(new Date().getTime() - 3600 * 24 * 6 * 1000, "{y}-{m}-{d}"),
      parseTime(new Date().getTime(), "{y}-{m}-{d}"),
    ];
    this.query.startTime = parseTime(
      new Date().getTime() - 3600 * 24 * 6 * 1000,
      "{y}-{m}-{d}"
    );
    this.query.endTime = parseTime(new Date().getTime(), "{y}-{m}-{d}");
    this.orderTypes ? this.$set(this.query, "orderStatus", "1") : "";
    this.upDate();
  },
  props: {
    orderTypes: {
      type: Boolean,
      default: true,
    },
    Query: {
      type: Object,
      required: true,
    },
  },
};
</script>

<style>
</style>
