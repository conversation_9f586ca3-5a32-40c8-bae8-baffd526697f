<template>
  <im-dialog :title="title" :visible.sync="visibleDialog" :width="width" :append-to-body="true" class="import-image" @confirm="confirm">
    <div class="dialog-content" :class="type">
      <span style="display: none">{{ fileList }}</span>
      <div class="img-content" v-if="fileList.length > 0 && fileList[0].response.data.url">
        <el-image :src="fileList[0].response.data.url" class="avatar"></el-image>
        <div class="remove-img"><i class="icon el-icon-delete" @click="delFile"></i></div>
      </div>
      <el-upload
        :action="'/api/file/file/upload'"
        list-type="picture-card"
        accept=".jpg,.gif,.png"
        :before-upload="beforeUpload"
        :file-list="[]"
        :on-success="uploadSuccess"
        :on-change="handleChange"
        :headers="headersProgram"
        :data="insertProgram"
        :limit="1"
      >
        <el-button >从电脑上传图片</el-button>
        <div slot="tip" class="el-upload__tip">
          <span v-if="type==='logo'">建议尺寸：208*80px，支持JPG、PNG、GIF格式图片，大小不超过2M</span>
          <span v-else>建议尺寸：128*40px，支持JPG、PNG、GIF格式图片，大小不超过2M</span>
        </div>
      </el-upload>
    </div>
  </im-dialog>
</template>

<script>
import { getToken } from '@/utils/auth'
import { advUpdate } from '@/views/fitment/pcmall/components/index'

export default {
  name: 'ImportImage',
  props: {
    title: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '720px'
    }
  },
  data() {
    return {
      dialogImageUrl: '',
      fileList: [],
      dialogVisible: false,
      visibleDialog: false,
      headersProgram: {
        token: getToken(),
        Authorization: 'Basic YWRtaW5fdWk6YWRtaW5fdWlfc2VjcmV0'
      },
      insertProgram: {
        folderId: 0
      }
    }
  },
  watch: {
    visibleDialog(newVal) {
      if (!newVal) this.fileList = []
    }
  },
  methods: {
    init(data) {
      if (data && data.length > 0) {
        this.fileList = [{ name: data[0].linkUrl, response: { data: { url: data[0].picUrl }}}]
      } else {
        this.fileList = []
      }
      this.visibleDialog = true
    },
    beforeUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < 2
      if (!isLt2M) {
        this.$message({
          message: '上传文件大小不能超过 2MB!',
          type: 'warning'
        })
        return false
      }
    },
    handleChange(file, fileList) {
      console.log(fileList)
      this.fileList = fileList
    },
    uploadSuccess(res, file) {
      console.info(res, file)
    },
    confirm() {
      advUpdate([{
        linkUrl: this.fileList.length > 0 ? this.fileList[0].name : null,
        sortValue: 1,
        picUrl: this.fileList.length > 0 ? this.fileList[0].response?.data?.url : null,
        type: this.type === 'logo' ? 'PC_LOGO' : 'PC_APPLETS'
      }]).then(res => {
        this.$emit('confirm')
        this.visibleDialog = false
      })
    },
    delFile() {
      this.fileList = []
    }
  }
}
</script>

<style lang="less" scoped>
.import-image{
  .img-content{
    width: 208px;
    height: 80px;
    position: relative;
    .el-image{
      width: 208px;
      height: 80px;
    }
    .remove-img{
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      background-color: rgba(0,0,0,.7);
      justify-content: center;
      align-items: center;
      font-size: 14px;
      color: #fff;
      display: none;
      i{
        cursor: pointer;
      }
    }
    &:hover .remove-img{
      display: flex;
    }
  }
  .dialog-content{
    display: flex;
    margin-left: 20px;
    min-height: 90px;
    position: relative;

    .el-upload--picture-card{
      width: 130px;
      height: 32px;
      line-height: 0;
      border: 0;
    }
    .el-upload{
      margin-left: 40px;
    }
    &.logo{
      .el-progress-circle{
        width: 78px !important;
        height: 78px !important;
      }
      .el-upload__tip {
        left: 256px;
        // position: absolute;
        top: 48px;
      }
    }
    &.code{
      .el-upload__tip {
        left: 180px;
        // position: absolute;
        top: 48px;
      }
      .img-content{
        width: 90px;
        height: 90px;
        .el-image{
          width: 90px;
          height: 90px;
        }
      }
      .el-upload{
        margin-left: 50px;
      }
    }
    .el-upload-list{
      display: none;
    }
  }
  .el-dialog__body{
    padding: 40px;
  }
}

</style>
