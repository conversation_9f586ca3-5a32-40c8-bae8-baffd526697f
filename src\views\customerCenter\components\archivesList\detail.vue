<template>
  <div class="archivesEditContent" v-if="!isLoading">
    <page-title title="查看采购商档案">
      <template>
        <template v-if="$route.query.tabType == 'PENDING'">
          <el-popover v-if="checkPermission(['admin', 'admin-platform-pur-merchant:reject','admin-saas-pur-merchant:reject'])" v-model="rejectFlag" placement="bottom-end" title="驳回理由" width="300" trigger="click">
            <el-button slot="reference">驳回</el-button>
            <el-form ref="rejectform" :model="rejectText">
              <el-form-item prop="text" :rules="[{required: true, message: '请填写驳回理由',trigger: 'blur'},{required: true,min:5, message: '请至少填写5个字！',trigger: 'blur'}]">
                <el-input type="textarea" :rows="3" placeholder="请输入驳回理由" v-model="rejectText.text">
                </el-input>
              </el-form-item>
            </el-form>
            <div style="text-align: right; margin: 0;padding-top:14px">
              <el-button size="mini" @click="rejectFlag = false">取消</el-button>
              <el-button type="primary" size="mini" @click="rejected">确定</el-button>
            </div>
          </el-popover>
          <el-button v-if="checkPermission(['admin', 'admin-platform-pur-merchant:accept','admin-saas-pur-merchant:accept'])" type="primary" @click="accepted">通 过</el-button>
          <el-button v-if="checkPermission(['admin', 'admin-platform-pur-merchant:edit','admin-saas-pur-merchant:edit'])"  @click="toEdit">编辑</el-button>
        </template>
        <template v-else-if="$route.query.tabType == 'REJECTED'">
          <el-button v-if="checkPermission(['admin', 'admin-platform-pur-merchant:edit','admin-saas-pur-merchant:edit'])"  @click="toEdit">编辑</el-button>
          <el-popover v-model="rejectFlag" placement="bottom-end" title="驳回理由" width="300" trigger="click">
            <el-button slot="reference">驳回理由</el-button>
            {{query.rejectReason}}
            <div style="text-align: right; margin: 0;padding-top:14px">
              <el-button type="primary" size="mini" @click="rejectFlag = false">知道了</el-button>
            </div>
          </el-popover>
        </template>
        <template v-else-if="$route.query.tabType == 'ACCEPTED'">
          <el-button v-if="query.publishStatus.code == 'Y' && checkPermission(['admin', 'admin-platform-platform-pur-merchant:freeze','admin-saas-platform-pur-merchant:freeze'])" @click="frozen">冻结</el-button>
          <el-button v-if="query.publishStatus.code == 'N' && checkPermission(['admin', 'admin-platform-pur-merchant:release', 'admin-saas-pur-merchant:release'])"  @click="enable" >启用</el-button>
          <el-button v-if="checkPermission(['admin', 'admin-platform-pur-merchant:edit','admin-saas-pur-merchant:edit'])" @click="toEdit">编辑</el-button>
        </template>
      </template>
    </page-title>
    <el-form :inline="true" label-width="140px" :model="query" ref="editForm" :rules="rules">
      <div class="item">
        <module-title title="基础信息" />
        <el-row>
          <el-col :span="6">
            <el-form-item class="formItem" label="客户编码:">
              <span class="graytext">{{query.code}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" prop="productNumber" label="客户名称:" :rules="[{ required: true}]">
              <span class="graytext">{{query.name}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" prop="productNumber" label="客户识别码:" :rules="[{ required: true }]">
              <span class="graytext"> {{query.identifyCode}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" prop="productNumber" label="社会统一信用代码:" :rules="[{ required: true }]">
              <span class="graytext"> {{query.socialCreditCode}}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item class="formItem" label="法定代表人:">
              <span class="graytext">{{query.legalPerson}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" prop="productNumber" label="负责人:" >
              <span class="graytext"> {{query.ceoName}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" prop="productNumber" label="负责人手机:">
              <span class="graytext">{{query.ceoMobile}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" prop="productNumber" label="质量负责人:" >
              <span class="graytext"> {{query.qualityPersonInCharge}}</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="6">
            <el-form-item class="formItem" label="所在区域:" :rules="[{required: true}]">
              <span class="graytext">{{query.region}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" prop="productNumber" label="注册资金:" >
              <span class="graytext" v-if="query.registerCapital">{{query.registerCapital}} （万）</span>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" prop="productNumber" label="注册地址:" :rules="[{ required: true, message: '请填写产品名称',trigger: 'blur' }]">
              <span class="graytext">{{query.registerAddress}}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <div class="item">
        <module-title title="账户信息" />
        <el-row>
          <el-col :span="6">
            <el-form-item class="formItem" label="登录账号:" :rules="[{required:true}]">
              <span class="graytext"> {{query.loginAccount}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" prop="userMobile" label="手机号码:" :rules="[{ required: false, message: '请填写产品名称',trigger: 'blur' }]">
              <span class="graytext"> {{query.userMobile}}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <div class="item">
        <module-title title="经营类目" />
        <div style="padding: 0 30px" class="cateGory">
          <el-row :gutter="20" v-if="cateGory" style="width:100%">
            <el-col v-for="(ids, keys) in cateGory" :key="keys" :span="8" :style="'width:'+getFlexNum()+'%;flex:1;display:flex;flex-wrap:wrap;'">
              <span>{{keys + ':'}}</span> <span style="white-space: nowrap;color:#aaaaaa" v-for="ite in ids" :key="ite.id">{{ite.label + '、'}}</span>
            </el-col>
          </el-row>
          <span style="padding-left:30px;color:#a9a9ac" v-else>无</span>
        </div>
      </div>
      <div class="item">
        <module-title title="客户资质" />
        <div>
          <template>
            <el-form-item class="formItem" prop="merchantTypeId" label="企业类型:" :rules="[
                {
                  required: true,
                  message: '请选择企业类型',
                  trigger: 'blur',
                },
              ]">
              <el-select disabled v-model="query.merchantTypeId" placeholder="请选择企业类型">
                <el-option v-for="item in listmerchantType" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <div class="graytext">
              上传材料为复印件加盖企业原印公章且均在有效期内，支持JPG、JPEG、PNG、BMP格式，大小不超过2M
            </div>
            <template>
              <lisence-table v-if="lisenceTableDate.length!=0" :lisenceTableDate.sync="lisenceTableDate" />
            </template>
          </template>
        </div>
      </div>

      <div class="item">
        <module-title title="收货地址" />
        <template>
          <addrTable :addrtableDate="addrtableDate" :id="$route.query.id" />
        </template>
      </div>

      <div class="item">
        <module-title title="发票信息" />
        <template>
          <div>
            <el-form-item class="formItem" prop="fpinvoiceType" label="发票类型:" :rules="[{ required: true, message: '请选择发票类型', trigger: 'blur' },]">
              <el-radio disabled v-if="query.fpinvoiceType=='VATINVOICE'" v-model="query.fpinvoiceType" label="VATINVOICE">普通发票</el-radio>
              <el-radio disabled v-if="query.fpinvoiceType=='SPECIALINVOICE'" v-model="query.fpinvoiceType" label="SPECIALINVOICE">增值税专用发票</el-radio>
            </el-form-item>
          </div>
          <div>
            <el-form-item class="formItem" prop="fpname" label="发票抬头:" :rules="[{ required: true, message: '请填写发票抬头', trigger: 'blur' },]">
              <span class="graytext">{{query.fpname}}</span>
            </el-form-item>
          </div>
          <div>
            <el-form-item class="formItem" prop="fptaxNumber" label="税号:" :rules="[{ required: true, message: '请填写税号', trigger: 'blur' },]">
              <span class="graytext">{{query.fptaxNumber}}</span>
            </el-form-item>
          </div>

           <div v-if="query.fpinvoiceType == 'SPECIALINVOICE'">
            <el-form-item class="formItem" prop="fpregisterAddress" label="注册地址:" :rules="[{ required: true, message: '请填写注册地址', trigger: 'blur' },]">
              <span class="graytext">{{query.fpregisterAddress}}</span>
            </el-form-item>
          </div>

          <div v-if="query.fpinvoiceType == 'SPECIALINVOICE'">
            <el-form-item class="formItem" prop="fpregisterMobile" label="注册电话:" :rules="[{ required: true, message: '请填写注册电话', trigger: 'blur' },]">
              <span class="graytext">{{query.fpregisterMobile}}</span>
            </el-form-item>
          </div>

          <div v-if="query.fpinvoiceType == 'SPECIALINVOICE'">
            <el-form-item class="formItem" prop="fpbankNumber" label="银行账号:" :rules="[{ required: true, message: '请填写银行账号', trigger: 'blur' },]">
              <span class="graytext">{{query.fpbankNumber}}</span>
            </el-form-item>
          </div>

          <div v-if="query.fpinvoiceType == 'SPECIALINVOICE'">
            <el-form-item class="formItem" prop="fpdepositBank" label="开户银行:" :rules="[{ required: true, message: '请填写开户银行', trigger: 'blur' },]">
              <span class="graytext">{{query.fpdepositBank}}</span>
            </el-form-item>
          </div>
        </template>
      </div>
    </el-form>
  </div>
</template>
<script>
import checkPermission from '@/utils/permission'
import { setContextData, getContextData } from "@/utils/auth";
import LisenceTable from "@/views/businessCenter/businessList/lisenceTable";
import addrTable from "@/views/businessCenter/businessList/addrTable";
import PageTitle from '@/components/PageTitle'
import ModuleTitle from '@/components/PageModuleTitle'
import { checkNumPot2 } from "@/utils/rules";
import rule from "@/utils/rules";
import {
  add,
  listByLicenseBaseType,
  getitems,
  enable,
  accepted,
  rejected,
  frozen,
  edititem,
  MerchantTypeDetailsById,
} from "@/api/purchasingAgent/archivesList";
import { findSaleScope, areas } from "@/api/businessCenter/businessList";
export default {
  components: {
    LisenceTable,
    addrTable,
    PageTitle,
    ModuleTitle
  },
  data() {
    let that = this;
    var checkNumPot3 = (rule, value, callback) => {
      const reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
      if (!value) {
        return callback(new Error("请填写数字"));
      } else if (!reg.test(value)) {
        return callback(new Error("请填写数字,最多2位小数"));
      } else if (value <= 0 || value > 100) {
        return callback(new Error("请填写0-100以内的数"));
      } else {
        callback();
      }
    };
    var validatePass2 = (rule, value, callback) => {
      if (value !== this.query.password) {
        callback(new Error("两次输入密码不一致!"));
      }
      callback();
    };
    return {
      isLoading: false,
      rules: {
        password: [
          { required: true, message: "密码不能为空", trigger: "blur" },
          {
            required: true,
            min: 6,
            max: 20,
            message: "长度在 6 到 20 个字符",
            trigger: "blur",
          },
        ],
        confirmPassword: [
          { required: true, message: "确认密码不能为空", trigger: "blur" },
          {
            required: true,
            min: 6,
            max: 20,
            message: "长度在 6 到 20 个字符",
            trigger: "blur",
          },
          { validator: validatePass2, trigger: "blur" },
        ],
        registerCapital: [
          { validator: checkNumPot2, trigger: "blur", required: true },
        ],
        ceoMobile: rule.phone,
        orderAmountRate: [
          { validator: checkNumPot3, trigger: "blur", required: true },
        ],
      },
      checkList: [],
      businessScope: {},
      query: {},
      rejectText: {},
      rejectFlag: false,
      lisenceTableDate: [],
      listmerchantType: [],
      areasTree: [],
      addrtableDate: [],
    };
  },
  methods: {
    checkPermission,
    getFlexNum(){
      let num = 0
      this.cateGory
      for (const key in this.cateGory) {
        num++
      }
      if(num == 1){
        return 100
      } else if(num ==2) {
        return 50
      } else {
        return 33
      }
    },
    async getMerchantTypeDetailsById() {
      let { data } = await MerchantTypeDetailsById(this.query.merchantTypeId);
      let tableDate = data.licenseBases || [];
      let arr = []
      tableDate.forEach((item,index) => {
        let obj = {
          licenseBaseId: item.id,
          licenseEndTime: "",
          filePath: "",
          isForever: "",
          licenseNumber: "",
          label: item.name,
          isEdit: false,
          limit: item.multiple.code == "Y" ? 5 : 1,
        };
        this.query.merchantLicenses.forEach((ids) => {
          if (item.id == ids.licenseBaseId) {
            obj.licenseEndTime = ids.isForever.code === 'Y' ? '' :ids.licenseEndTime;
            obj.filePath = ids.filePath;
            obj.filePathList = this.getsrc(ids.filePath);
            obj.licenseNumber = ids.licenseNumber;
            obj.label = item.name;
            obj.merchantId = ids.merchantId;
            obj.id = ids.id;
            obj.isForever = ids.isForever.code === 'Y'
          }
        });
        this.$set(this.lisenceTableDate, index, obj)
      });
    },
    toEdit() {
      this.$router.push({
        path: "/customerCenter/archivesList/edit",
        query: {
          id: this.$route.query.id,
          isSaas: this.$route.query.isSaas
        },
      });
    },
    back() {
      this.$store.dispatch("tagsView/delView", this.$route);
      this.$router.go(-1);
    },
    async getareas() {
      let { data } = await areas();
      this.areasTree = data;
    },
    getsrc(str) {
      if (!str) {
        return [];
      } else {
        let arr = str.split(",");
        let list = [];
        arr.forEach((item) => {
          let obj = {
            response: {
              data: {
                url: "",
              },
            },
          };
          obj.response.data.url = item;
          obj.url = item;
          list.push(obj);
        });
        return list;
      }
    },
    resetSucss(type, msg, tabType) {
      if (type) {
        this.$message.success(msg);
        let listQuery = {
          current: 1,
          size: 10,
          model: {
            approvalStatus: { code: tabType },
          },
        };
        this.$store.dispatch("tagsView/delView", this.$route);
        setContextData("purchasingAgentlist_detail", listQuery);
        this.$router.go(-1);
      }
    },
    // 通过审核
    async accepted() {
      let { data } = await accepted(this.$route.query.id);
      this.resetSucss(data, "已通过该采购商！", "ACCEPTED");
    },
    // 驳回审核
    async rejected() {
      this.$refs.rejectform.validate(async (valid) => {
        if (valid) {
          let { data } = await rejected({
            id: this.$route.query.id,
            rejectReason: this.rejectText.text,
          });
          this.resetSucss(data, "已驳回该采购商！", "REJECTED");
        } else {
          return false;
        }
      });
    },
    // 冻结
    async frozen() {
      let { data } = await frozen(this.$route.query.id);
      this.$emit("update:row", this.row);
      this.back();
      this.$message.success("已冻结该销售商！");
    },
    // 启用
    async enable() {
      let { data } = await enable(this.$route.query.id);
      this.$emit("update:row", this.row);
      this.back();
      this.$message.success("已启用该销售商！");
    },
    checkListFun(e) {
      this.query.businessCategoryId = e;
    },
    // 获取经营范围
    async getjyfw() {
      let { data } = await findSaleScope();
      let obj = {};
      data.forEach((item) => {
        if (obj[item.parentName + ":"]) {
          obj[item.parentName + ":"].push(item);
        } else {
          obj[item.parentName + ":"] = [];
          obj[item.parentName + ":"].push(item);
        }
      });
      this.businessScope = obj;
    },
    // 获取详情
    async getitem() {
      this.isLoading = true;
      let { data } = await getitems(this.$route.query.id);
      this.isLoading = false;
      if (!data.deliveryAddressDetailDTOList) {
        data.deliveryAddressSaveDTOList = [];
      }
      this.query = data;
      this.query.regionId = [data.provinceId, data.cityId, data.countyId];
      let obj = {};
      if (!data.businessCategoryDetailList) {
        this.cateGory = false;
      } else {
        data.businessCategoryDetailList.forEach((item) => {
          if(item.parentName!=null){
            if (!obj[item.parentName]) {
              obj[item.parentName] = [];
              obj[item.parentName].push(item);
            } else {
             obj[item.parentName].push(item);
            }
          }
        });
        this.cateGory = obj;
      }
      this.listmerchantType = [
        {
          name: data.merchantType,
          id: data.merchantTypeId,
        },
      ];
      if (!!data.invoiceInfo) {
        this.$set(
          this.query,
          "fpinvoiceType",
          data.invoiceInfo.invoiceType.code
        );
        this.$set(this.query, "fpname", data.invoiceInfo.name);
        this.$set(this.query, "fpdepositBank", data.invoiceInfo.depositBank);
        this.$set(this.query, "fptaxNumber", data.invoiceInfo.taxNumber);
        this.$set(
          this.query,
          "fpregisterMobile",
          data.invoiceInfo.registerMobile
        );
        this.$set(this.query, "fpbankNumber", data.invoiceInfo.bankNumber);
        this.$set(this.query, "fpregisterAddress", data.invoiceInfo.registerAddress);
        delete this.query.invoiceInfo;
      }
      if (data.businessCategoryDetailList) {
        data.businessCategoryDetailList.forEach((item) => {
          this.checkList.push(item.id);
        });
      }
      this.getMerchantTypeDetailsById();
    },
  },
  created() {
    this.getareas();
    this.getitem();
    this.getjyfw();
  },
  mounted() {
    if (this.$route.query.id) {
      this.isEdit = true;
    }
  },
  beforeDestroy() {},
};
</script>
<style lang="less" scoped>
.archivesEditContent {
  border-top: 1px solid #ebecee;
  padding: 0px 20px;
  background-color: #fff;
  .item {
    width: 100%;
    margin-bottom: 30px;
    border-bottom: 1px solid #eeeeee;
    .cateGory{
      display: flex;
      .el-col-8{
        word-break:break-all;
        font-size: 14px;
        line-height: 20px;
        padding-bottom: 10px;
        text-overflow: wrap;
      }
    }
  }
  .uploadPic {
    padding-bottom: 100%;
    margin-bottom: -100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    > div {
      min-width: 100%;
      height: 25px;
    }
  }
  .productPicContent .text p {
    font-family: "PingFangSC-Regular", "PingFang SC", sans-serif,
      "PingFangSC-Regular", "PingFang SC", sans-serif-400;
    font-weight: 400;
    color: #aaaaaa;
    line-height: 20px;
    font-size: 13px;
    margin: 0;
  }
  .detailMsg {
    font-family: "PingFangSC-Regular", "PingFang SC", sans-serif,
      "PingFangSC-Regular", "PingFang SC", sans-serif-400;
    font-weight: 400;
    color: #aaaaaa;
    line-height: 20px;
    padding-bottom: 20px;
    font-size: 13px;
  }
  .graytext {
    font-family: "PingFangSC-Regular", "PingFang SC", sans-serif,
    "PingFangSC-Regular", "PingFang SC", sans-serif-400;
    font-weight: 400;
    color: rgba(0, 0, 0, .6);
    line-height: 20px;
    font-size: 13px;
    margin: 0;
    padding-bottom: 20px;
  }
}
/deep/ .el-upload {
  width: 40px;
  height: 40px;
  position: relative;
}
/deep/ .el-upload > i {
  font-size: 16px;
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translateX(-50%) translateY(-50%);
  transform: translateX(-50%) translateY(-50%);
}
/deep/ .el-upload-list .el-upload-list__item {
  width: 40px;
  height: 40px;
}
/deep/ .hide .el-upload--picture-card {
  display: none;
}
</style>
