<template>
  <div>
    <!-- <tabs-layout ref="tabs-layout" :tabs="tabs" v-model="tabCode" @change="handleChangeTab"></tabs-layout> -->
    <!--搜索Form-->
    <im-search-pad style="padding-top: 6px;padding-bottom: 0;" :has-expand="false" :is-expand.sync="isExpand" :model="model" @reset="reload" @search="searchLoad">
      <im-search-pad-item prop="saleMerchantName">
        <el-input v-model="model.saleMerchantName" @keyup.enter.native="searchLoad" placeholder="商家名称" />
      </im-search-pad-item>

      <im-search-pad-item prop="productName">
        <el-input v-model="model.productName" @keyup.enter.native="searchLoad" placeholder="商品名称/ERP编码" />
      </im-search-pad-item>

      <im-search-pad-item prop="manufacturer">
        <el-input v-model="model.manufacturer" @keyup.enter.native="searchLoad" placeholder="生产厂家" />
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <el-table ref="multipleTable" :row-key="getRowKeys" border :data="tableData" tooltip-effect="dark"
        style="width: 100%" @selection-change="handleSelectionChange" @select-all="handleSelectAll" max-height="400">
        <el-table-column type="selection" width="55" fixed :reserve-selection="true"></el-table-column>
        <el-table-column prop="saleMerchantName" label="商家名称" width="160"></el-table-column>
        <el-table-column prop="erpCode" label="ERP商品编码" width="110"></el-table-column>
        <el-table-column prop="productName" label="商品名称" width="160"></el-table-column>
        <el-table-column prop="spec" label="规格" width="120"></el-table-column>
        <el-table-column prop="unit" label="单位" width="50"></el-table-column>
        <el-table-column prop="manufacturer" label="生产厂家" width="180"></el-table-column>

        <el-table-column prop="salePrice" label="销售价" width="130">
          <template slot-scope="{row}">
            <div style="color: #FF6600;">{{row.salePrice}}</div>
          </template>
        </el-table-column>
        <el-table-column prop="costPrice" label="成本价" width="130">
          <template slot-scope="{row}">
            <div style="color: #339900;">{{row.costPrice}}</div>
          </template>
        </el-table-column>
        <el-table-column prop="grossProfitMargin" label="毛利率" width="130"></el-table-column>

        <el-table-column prop="stockQuantity" label="可卖库存" width="130"></el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="page-row">
        <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size.sync="limit"
          layout="total, sizes, prev, pager, next, jumper" :total="totalCount">
        </el-pagination>
      </div>
    </div>
    <!-- <div class="bottom_btn">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="submit">{{ tabCode=="ALREADY" ? "移除" : "添加" }}{{ this.multipleSelection.length==0 ? "" : `(${this.multipleSelection.length})` }}</el-button>
    </div> -->

  </div>
</template>


<script>
import {
  bindingProductGroup,
  noBindingProductGroup,
  noRelevance,
  relevance,
  productGroupNum
} from '@/api/products/productGroup'
export default {
  //import引入的组件
  components: {},
  props: {
    title: {
      type: String,
      default: ''
    },
    currentId: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      // 获取row的key值
      getRowKeys(row) {
        return row.id
      },
      isExpand: false,
      model: {
        manufacturer: '',
        productName: '',
        saleMerchantName: '',
      },
      tabCode: 'ALREADY',
      page: 1,
      limit: 10,
      totalCount: 0,
      tableData: [],
      multipleSelection: [],
      tabNum: {
        bingSum: 0,
        noBingSum: 0
      }
    }
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.load();
    this.tabNumFn();
  },

  computed: {
    tabs() {
      return [{
        name: `已关联商品(${this.tabNum.bingSum})`,
        value: 'ALREADY',
      },
        // {
        //   name: `未关联商品(${this.tabNum.noBingSum})`,
        //   value: 'NOT',
        // }
      ]
    }
  },

  created() { },

  filters: {},

  //方法集合
  methods: {
    load() {
      let params = {
        current: this.page,
        map: {},
        model: {
          ...this.model,
          erpCode: this.model.productName,
          id: this.currentId,
        },
        order: 'descending',
        size: this.limit,
        sort: 'id'
      };
      // console.log('params--->', params, this.tabCode);
      if (this.tabCode == 'ALREADY') {
        // 已关联
        relevance(params).then(res => {

          if (res.code == 0 && res.msg == 'ok') {
            // console.log('已绑定客户', res);
            this.tableData = res.data.records && res.data.records.map(item => {
              item.grossProfitMargin = item.grossProfitMargin && `${item.grossProfitMargin}%` || ''
              return item
            }) || [];
            this.totalCount = res.data.total;
          }
        })
      } else {
        // 未关联
        noRelevance(params).then(res => {
          if (res.code == 0 && res.msg == 'ok') {
            // console.log('未绑定客户', res);
            this.tableData = res.data.records || [];
            this.totalCount = res.data.total;
          }
        })
      }
    },
    tabNumFn() {
      let params = {
        current: this.page,
        map: {},
        model: {
          ...this.model,
          id: this.currentId,
          erpCode: this.model.productName,
        },
        order: 'descending',
        size: this.limit,
        sort: 'id'
      };
      productGroupNum(params).then(res => {
        if (res.code == 0 && res.msg == 'ok') {
          this.tabNum = res.data;
          this.$emit('update:title', `${this.title.replace(/\([0-9]+\)$/g, '')}(${res.data.bingSum || 0})`)
        }
      })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleSelectAll(val) {
      this.multipleSelection = val;
    },
    handleSizeChange(val) {
      this.limit = val;
      this.load();
      this.tabNumFn();
    },
    handleCurrentChange(val) {
      this.page = val;
      this.load();
      this.tabNumFn();
    },
    handleCancel() {
      this.$emit('closeProduct');
    },
    submit() {
      // console.log('this.multipleSelection', this.multipleSelection);
      if (this.multipleSelection.length == 0) {
        this.$emit('closeProduct');
        return;
      }
      let list = this.multipleSelection.map(item => {
        return item.id;
      });
      if (this.tabCode == 'ALREADY') {
        // 批量移除
        this.$confirm("您确定批量移除这些商品吗?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: 'warning',
        }).then(res => {
          this.unbindCustomer(list)
        })
      } else {
        // 批量添加
        this.$confirm("您确定批量添加这些商品吗?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: 'warning',
        }).then(res => {
          this.bindCustomer(list)
        })
      }
    },
    // 绑定商品接口
    bindCustomer(list) {
      let params = {
        productIds: list,
        productGroupId: this.currentId,
      };
      bindingProductGroup(params).then(res => {
        if (res.code == 0 && res.msg == 'ok') {
          this.$message.success('绑定商品成功');
          this.reload();
          this.tabNumFn();
          this.$refs.multipleTable.clearSelection();
          if (list.length > 1) {
            this.$emit('closeProduct');
          }
        }
      })
    },
    // // 解绑
    // handleUnbind(id) {
    //   this.$confirm("您确定解绑此客户吗?", "提示", {
    //     confirmButtonText: "确定",
    //     cancelButtonText: "取消",
    //     type: 'warning',
    //   }).then(res => {
    //     this.unbindCustomer([id]);
    //   })
    // },
    // 解绑客户调用接口
    unbindCustomer(list) {
      let params = {
        productIds: list,
        productGroupId: this.currentId,
      };
      noBindingProductGroup(params).then(res => {
        if (res.code == 0 && res.msg == 'ok') {
          this.$message.success('移除商品成功');
          this.reload();
          this.tabNumFn();
          this.$refs.multipleTable.clearSelection();
          if (list.length > 1) {
            this.$emit('closeProduct');
          }
        }
      })
    },

    //   刷新
    reload() {
      this.model = {
        manufacturer: '',
        productName: '',
        saleMerchantName: '',
      }
      this.handleRefresh()

    },
    searchLoad() {
      this.handleRefresh()
      // this.tabNumFn();
    },
    handleRefresh(pageParams) {
      this.page = 1;
      this.load();
      this.tabNumFn();
    },
    // tab切换
    handleChangeTab(tab) {
      this.$refs.multipleTable.clearSelection();
      this.tabCode = tab.value;
      this.handleRefresh()
    },
  },

}

</script>


<style lang='scss' scoped>
.page-row {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #505465;
  font-size: 13px;
  margin-top: 16px;
}

.bottom_btn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
