const viewGenerator = require('./plop-templates/view/prompt')
const componentGenerator = require('./plop-templates/component/prompt')
const storeGenerator = require('./plop-templates/store/prompt.js')
const listViewGenerator = require('./plop-templates/view/list/prompt')
const detailViewGenerator = require('./plop-templates/view/detail/prompt')

module.exports = function(plop) {
  plop.setGenerator('view', viewGenerator)
  plop.setGenerator('component', componentGenerator)
  plop.setGenerator('store', storeGenerator)
  plop.setGenerator('list-view', listViewGenerator)
  plop.setGenerator('detail-view', detailViewGenerator)
}
