<template>
  <div>
    <im-search-pad
      :is-expand.sync="isExpand"
      :model="model"
      @reset="reload"
      @search="searchLoad"
    >
      <im-search-pad-item prop="settlementOrderId">
        <el-input v-model="model.settlementOrderId" placeholder="请输入结算单编号" />
      </im-search-pad-item>
      <im-search-pad-item prop="id">
        <el-input v-model="model.id" placeholder="请输入账单编号" />
      </im-search-pad-item>
      <im-search-pad-item prop="orderNumber">
        <el-input v-model="model.orderNumber" placeholder="请输入订单/退款单编号" />
      </im-search-pad-item>
      <im-search-pad-item prop="type">
        <el-select placeholder="账单类型" v-model="model.type" clearable>
          <el-option label="销售账单" value="SALE"></el-option>
          <el-option label="退款账单" value="REFUND"></el-option>
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="type">
        <el-date-picker
          type="daterange"
          range-separator="至"
          v-model="during"
          value-format="yyyy-MM-dd"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <div class="varietiesBan-list-container">
        <div class="varietiesBan-list-tabs-wrapper">
          <div class="varietiesBan-list-tabs">
            <template v-for="(tab, index) in tabs">
              <div v-if="!tab.hide" class="tab" :class="{'active': currentTab == index}" 
                 :key="index"
                 @click="handleChangeTab(index,tab.value)">
                {{ tab.name }}（{{tab.count}}）
              </div>
            </template>
          </div>
          <div class="operations">
            <el-button  @click="$router.push({path: '/finance/businessStatement/business/list'})">返回</el-button>
            <!-- <el-button  v-if="checkPermission(['admin','bussinessDetail:export'])">导出</el-button> -->
            <el-button  @click="reload">刷新</el-button>
            <el-button  v-if="checkPermission(['admin', 'admin-finance-merchantBilling:application'])" type="primary" @click="handleBatch">+批量申请结算</el-button>
          </div>
        </div>
      </div>
      <div class="todo-settlement" v-if="model.merchantsStatus==='WAIT'&&isShow===true">
        {{todoName}}的账单列表：已选<span class="text-primary">{{selectNumber}}</span>条账单，结算金额：<span class="text-warning">{{settleMoney}}</span>元，该商家还有<span class="text-primary">{{toSelect}}</span>条账单可结算
      </div>

      <table-pager ref="bussinessTabel" :options="tableTitle" :remote-method="load" :data.sync="tableData" :pageSize="pageSize" :selection="true" :isNeedButton="false"  @selection-change="handleSelectionChange" @selection-all="handleSelectAll" @size-change="handleSizeChange">
        <template slot="settlementOrderId" v-if="model.merchantsStatus==='FINISH'||model.merchantsStatus==='PROCESS'">
          <el-table-column label="结算单号" width="160">
            <slot slot-scope="{row}">
              <span class="text-primary">{{row.settlementOrderId}}</span>
            </slot>
          </el-table-column>
        </template>
        <template slot="settlementSerialNumber" v-if="model.merchantsStatus==='FINISH'">
          <el-table-column label="结款流水编号" width="160">
            <slot slot-scope="{row}">
              <span class="text-primary">{{row.settlementSerialNumber}}</span>
            </slot>
          </el-table-column>
        </template>
        <template slot="settlementTime" v-if="model.merchantsStatus==='FINISH'">
          <el-table-column label="结算时间" width="170">
            <slot slot-scope="{row}">
              <span class="text-primary">{{row.settlementTime}}</span>
            </slot>
          </el-table-column>
        </template>
        <template slot="orderAmount">
          <el-table-column label="订单/退款单金额（元）" width="170">
            <slot slot-scope="{row}">
              {{row.orderAmount|getDecimals}}
            </slot>
          </el-table-column>
        </template>
        <template slot="realAmount">
          <el-table-column label="实收/退金额（元）" width="140">
            <slot slot-scope="{row}">
              {{row.realAmount|getDecimals}}
            </slot>
          </el-table-column>
        </template>
        <template slot="settlementAmount">
          <el-table-column label="应结货款（元）" width="135">
            <slot slot-scope="{row}">
              {{row.settlementAmount|getDecimals}}
            </slot>
          </el-table-column>
        </template>
        <template slot="settlementServiceAmount">
          <el-table-column label="交易服务费（元）" width="140">
            <slot slot-scope="{row}">
              {{row.settlementServiceAmount|getDecimals}}
            </slot>
          </el-table-column>
        </template>
        <template slot="settlementCommissionAmount">
          <el-table-column label="业务员品种推广佣金（元）" width="190">
            <slot slot-scope="{row}">
              {{row.settlementCommissionAmount|getDecimals}}
            </slot>
          </el-table-column>
        </template>
        <template slot="settlementChargedAmount">
          <el-table-column label="交易手续费（元）" width="170">
            <slot slot-scope="{row}">
              {{row.settlementChargedAmount|getDecimals}}
            </slot>
          </el-table-column>
        </template>
      </table-pager>
    </div>
  </div>
</template>

<script>
  const TableColumns = [
    { label: "账单类型", name: "type.desc",prop: "type.desc",width: "150"},
    { label: "账单编号", name: "id", prop:"id",width: "170" },
    { label: "订单/退款单编号", name: "orderNumber", prop:"orderNumber",width: "180" },
    { label: "结算单号", name: "settlementOrderId", prop:"settlementOrderId",slot: true },
    { label: "结款流水编号", name: "settlementSerialNumber", prop:"settlementSerialNumber",slot: true },
    { label: "结算时间", name: "settlementTime", prop:"settlementTime",slot: true },
    { label: "商家编码", name: "merchantSn", prop:"merchantSn",width: "150" },
    { label: "商家名称", name: "merchantName", prop:"merchantName",width: "150" },
    { label: "客户名称", name: "customerName", prop:"customerName",width: "150" },
    { label: "订单支付时间", name: "orderPaymentTime", prop:"orderPaymentTime",width: "170" },
    { label: "订单/退款单金额（元）", name: "orderAmount", prop:"orderAmount",slot: true, width: "170" },
    { label: "实收/退金额（元）", name: "realAmount",prop: 'realAmount',width: '140',slot: true },
    { label: "应结货款（元）", name: "settlementAmount", prop:'settlementAmount',width: '135',slot: true },
    { label: "交易服务费（元）", name: "settlementServiceAmount", prop:'settlementServiceAmount',width: '140',slot: true},
    { label: "业务员品种推广佣金（元）", name: "settlementCommissionAmount",prop: 'settlementCommissionAmount',width: '190',slot: true},
    { label: "交易手续费（元）", name: "settlementChargedAmount",prop:'settlementChargedAmount',width: "180",slot: true },
    { label: "制单人", name: "createUserName",prop:'createUserName',width: "100" },
    { label: "制单时间", name: "createTime",prop:'createTime',width: "170" }
  ];
  const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({ key: i, ...TableColumns[i] });
  }
  import checkPermission from "../../../utils/permission";
  import { fboDetailBatchSettlement,financeBillOrderDetail,fboDetailCountStatus,countSettlement,fboCount } from '@/api/finance'
  import SearchPad from '@/components/searchPad'
  export default {
    components: {
      SearchPad
    },
    data () {
      return {
        isExpand: false,
        loading: '',
        currentTab: 0,
        tabs: [
          { name: '可结算', value: 'WAIT',count: 0,hide: !checkPermission(['admin', 'admin-finance-merchantBilling:canHandledView']) },
          { name: '结算中', value: 'PROCESS',count: 0,hide: !checkPermission(['admin', 'admin-finance-merchantBilling:handlingView'])  },
          { name: '已结算', value: 'FINISH',count: 0,hide: !checkPermission(['admin', 'admin-finance-merchantBilling:handledView'])  },
          { name: '未结算', value: 'UNSETTLED',count: 0,hide: !checkPermission(['admin', 'admin-finance-merchantBilling:unhandledView'])  }
        ],
        tableData: [],
        page: 1,
        pageSize: 10,
        totalPage: 0,
        total: 0,
        tableTitle: TableColumnList,
        ids: [],
        during: '',
        model: {
          id: '',
          settlementOrderId:'',
          type: '',
          orderNumber: '',
          merchantsStatus: 'WAIT'
        },
        todoName: '',
        selectNumber: 0,
        settleMoney: 0,
        toSelect: 0,
        isShow: false,
        isCheckAll: false,
        sizeChange: false
      }
    },
    mounted() {
      this.getCount()
      this.$nextTick(()=> {
        if (this.$route.query.currentTab) {
          this.currentTab = this.$route.query.currentTab
          if(this.$route.query.settlementOrderId!=null&&this.$route.query.settlementOrderId!=undefined){
            this.model.settlementOrderId = this.$route.query.settlementOrderId
          }
          this.handleChangeTab(this.currentTab,'FINISH')
        }
      })
    },
    methods: {
      checkPermission,
      //获取退款账单数
      async getFbCount() {
        const query = {
          merchantsId: this.$route.query.merchantsId
        }
        const {data} = await fboCount(query)
        this.selectNumber = data.num
        this.settleMoney = data.amount
        this.toSelect = this.total - data.num
      },
      async getCount() {
        const query = {
          merchantsId: this.$route.query.merchantsId
        }
         if(this.$route.query.settlementOrderId!=null&&this.$route.query.settlementOrderId!=undefined){
            query.settlementOrderId = this.$route.query.settlementOrderId
          }
        const {data} = await fboDetailCountStatus(query)
        this.tabs.forEach(item=>{
          item.count = data[item.value.toLowerCase()]
        })
      },
      handleSelectAll(val) {
        this.sizeChange = false
        this.settleMoney = 0
        this.ids = []
        if (!this.isCheckAll) {
          this.tableData.map((item, index) => {
            this.ids.push(item.id)
          })
          this.isCheckAll = true
        } else {
          val.map((item, index) => {
            this.ids.push(item.id)
          })
          this.isCheckAll = false
        }
        this.selectNumber = Array.from(new Set(this.ids)).length
        this.toSelect = this.tabs[0].count - this.selectNumber
        if (this.ids.length > 0) {
          this.getSettleAmount()
        }
      },
      handleSelectionChange(val) {
        this.settleMoney = 0
        this.ids = []
        val.map((item, index) => {
          this.ids.push(item.id)
          this.settleMoney += item.settlementAmount
          })
        this.selectNumber = Array.from(new Set(this.ids)).length
        this.toSelect = this.tabs[0].count - this.selectNumber
        if (this.ids.length > 0) {
          this.getSettleAmount()
        }
      },
      async getSettleAmount() {
        const data = await countSettlement({
          ids: Array.from(new Set(this.ids)),
          merchantsId: this.$route.query.merchantsId
        })
        if(data.code === 0) {
          this.settleMoney = data.data
        }
      },
      handleSizeChange(val) {
      },
      handleBatch() {
        if(this.ids.length === 0) {
          this.$message.warning('请至少选择一项进行操作！')
          return
        }
        this.$confirm('确定结算所选账单？','提示').then(_ => {
          // fboDetailBatchSettlement({
          //   ids: this.ids
          // }).then(res=>{
          //   if (res.code === 0) {
          //     this.$message.success('操作成功！')
          //     this.ids = []
          //     this.currentTab = index
          //     this.handleRefresh({
          //       page: 1,
          //       pageSize: 10
          //     })
          //   }
          // })
          this.handleChangeTab(2,'FINISH')
        }).catch(_ => {});
      },
      async load(params) {
        const listQuery = {
          model: {
            ...this.model,
            merchantsId: this.$route.query.merchantsId,
            startTime: this.during[0],
            endTime: this.during[1]
          }
        }
        Object.assign(listQuery, params)
        this.loading = true
        const {data} = await financeBillOrderDetail(listQuery)
        if(data.records.length > 0) {
          this.todoName = data.records[0].merchantName
          this.isShow = true
        }
        data.records.forEach((item,index)=>{
          if (item.type.code ==='REFUND' ||item.merchantsStatus.code!=='WAIT') {
            item.selectable = true
            if (item.type.code ==='REFUND') {
              this.$nextTick(()=> {
                this.$refs.bussinessTabel.$refs.table.toggleRowSelection(item,true)
                this.ids.push(item.id)
              })
            }
          } else {
            item.selectable = false
          }
        })
        this.total = data.total
        this.getFbCount()
        this.tableData = data.records
        this.loading = false
        return { data }
      },
      searchLoad() {
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      reload() {
        this.model={
          ...this.model,
          ...{
            id: '',
            settlementOrderId:'',
            type: '',
            orderNumber: ''
          }
        }
        this.during = ''
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      handleChangeTab(index,value) {
        this.currentTab = index
        this.model.merchantsStatus = value
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      handleRefresh(pageParams) {
        this.$refs.bussinessTabel.doRefresh(pageParams)
      }
    }
  }
</script>

<style lang="less" scoped>
  .todo-settlement {
    width: 100%;
    height: 42px;
    margin-bottom: 20px;
    background-color: #effaff;
    border: 1px solid #81d3f8;
    padding: 0 12px;
    line-height: 42px;
    font-size: 14px;
  }
</style>
