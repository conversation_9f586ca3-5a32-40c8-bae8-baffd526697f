<template>
  <div class="businessTypeEditContent">
    <div class="temptop_title flex_between_center">
      <div>{{this.$route.query.id ==null?'新增':'编辑'}}短信模板</div>
      <div>
        <div>
          <el-button   @click="clearFun()">取 消</el-button>
          <el-button   type="primary" @click="submitFun('ruleForm')">确 定</el-button>
        </div>
      </div>
    </div>
    <el-form class="form" :model="query" ref="ruleForm" label-width="130px">
      <el-row :gutter="10">
        <el-col :span="6">
          <el-form-item
            class="formItem"
            label="供应商类型:"
            prop="providerType"
            :rules="[{ required: true, message: '请选择供应商类型',trigger: 'blur' }]"
          >
            <el-select
              style="width:100%;"
              @change="$forceUpdate()"
              v-model="query.providerType"
              placeholder="请选择供应商类型"
            >
              <el-option key="ALI" value="ALI" label="阿里云短信" :disabled="true"></el-option>
              <el-option key="TENCENT"  value="TENCENT" label="腾讯云短信"></el-option>
              <el-option key="BAIDU"  value="BAIDU" label="百度云短信"></el-option>

            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            class="formItem"
            label="应用ID:"
            prop="appId"
            :rules="[{ required: true, message: '请填写应用ID',trigger: 'blur' }]"
          >
            <el-input
              clearable
              autocomplete="off"
              :auto-complete="false"
              v-model="query.appId"
              placeholder="请填写应用ID"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            class="formItem"
            label="应用密码:"
            prop="appSecret"
            :rules="[{ required: true, message: '请填写应用密码',trigger: 'blur' }]"
          >
            <el-input
              show-password
              autocomplete="new-password"
              clearable
              type="password"
              v-model="query.appSecret"
              placeholder="请填写应用密码"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            class="formItem"
            label="模板名称:"
            prop="name"
            :rules="[{ required: true, message: '请填写模板名称',trigger: 'blur' }]"
          >
            <el-input
              clearable
              v-model="query.name"
              placeholder="请填写模板名称"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            class="formItem"
            label="模板编码:"
            prop="customCode"
            :rules="[{ required: true, message: '请填写模板编码',trigger: 'blur' }]"
          >
            <el-input
              clearable
              :disabled="query.id>0?true:false"
              v-model="query.customCode"
              placeholder="请填写模板编码"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            class="formItem"
            label="模板签名名称:"
            prop="signName"
            :rules="[{ required: true, message: '请填写模板签名名称',trigger: 'blur' }]"
          >
            <el-input
              clearable
              v-model="query.signName"
              placeholder="请填写模板签名名称"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            class="formItem"
            label="模板CODE:"
            prop="templateCode"
            :rules="[{ required: true, message: '请填写模板CODE',trigger: 'blur' }]"
          >
            <el-input
              clearable
              :disabled="query.id>0?true:false"
              v-model="query.templateCode"
              placeholder="请填写模板CODE"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            class="formItem"
            label="SMS服务域名:"
            prop="url"
            :rules="[{ required: true, message: '请填写SMS服务域名',trigger: 'blur' }]"
          >
            <el-input
              clearable
              v-model="query.url"
              placeholder="请填写SMS服务域名"
            ></el-input>
          </el-form-item>

        </el-col>
      </el-row>
      <el-form-item class="formItem" label="模板内容:" prop="content"
        :rules="[{ required: true, message: '请填写模板内容',trigger: 'blur' }]">
           <el-input  class="textarea" type="textarea" rows="6"  maxlength="200" resize="none"  v-model="query.content" placeholder="请填写模板内容"></el-input>
      </el-form-item>
      <el-form-item class="formItem" label=" " >
            <aside  style="text-align:left;" >
                百度云：使用 ${xx} 作为占位符<br/>
                阿里云：使用 ${xx} 作为占位符<br/>
                腾讯云：使用 {xx} 作为占位符
            </aside>
      </el-form-item>
      <el-form-item class="formItem" label="模板描述:" prop="templateDescribe"
        :rules="[{ required: true, message: '请填写模板描述',trigger: 'blur' }]">
           <el-input  class="textarea"  type="textarea" resize="none"  rows="2" v-model="query.templateDescribe" placeholder="请填写模板描述"></el-input>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
// import rules from '@/utils/rules'

import { editSmsTemplate,getApi } from '@/api/setting/sms/smsTemplate'
export default {
  data() {
    return {
      query: {
        id:0,
        providerType:"TENCENT"
      }
    };
  },
  methods: {
     goBackFun(){
      this.$router.go(-1)
      this.$store.dispatch("tagsView/delView", this.$route);
    },
    clearFun: function() {
      let _this=this;
      _this.goBackFun()
    },
    async submitFun(ruleForm) {
      var _this=this;
      _this.$refs[ruleForm].validate(async (valid) => {
        if (valid) {
          var data= await editSmsTemplate(this.query);
          if(data.code==0){
             _this.goBackFun()
          }
        } else {
          return false;
        }
      });
    },
    async getFun() {
      const { data } = await getApi(this.$route.query.id);
      this.query = data;
      this.query.providerType=this.query.providerType.code
    }
  },
  mounted() {
    if(this.$route.query.id==undefined){
      this.query.id=0;
      this.query.providerType="TENCENT"
    }else{
      this.getFun()
    }
  },
  beforeDestroy() {}
};
</script>
<style lang="less" scoped>
.businessTypeEditContent {
  // margin: -30px -20px;
  border-top: 1px solid #ebecee;
  padding: 0 20px 20px;
  background-color: #fff;

  aside{
    background: #eef1f6;
    padding: 8px 24px;
    margin-bottom: 20px;
    border-radius: 2px;
    display: block;
    line-height: 32px;
    font-size: 14px;
  }

}
</style>
