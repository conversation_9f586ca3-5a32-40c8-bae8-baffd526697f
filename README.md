 # **-----药链开发工程师入项必读手册-----**

# 一、基本理念与素养
## 1、尊重前人的劳动成果

   无论代码怎样，都是别人的辛苦成果，值得学习与优化。尤其是在践行组件化开发的时候，尽量不要重复造轮子，别人写的东西只要不是太过分，尽量去使用并加以优化，另外尽量不要去私自修改基础公共组件。

## 2、效率优先同时保证质量

   大家思维方式可能都不一样，过完擅长的开发模式也不同，习惯上可以不拘一格，怎么爽可以怎么来，但是尽量在追求快的情况下保证质量，不求多较真，但求比当下的好，或者说不要比当下的差。

## 3、有问题及时抛出，自认为是人才就不需要照顾所谓的自尊

   团队协同，风险是时刻是存在的，存在问题的可能是任何人，因此有问题就及时抛出，不要等到出问题了，大家互相扯皮。有些棘手的问题，可以及时向上求助。

## 4、多交流共同成长
   
   不要只做一个会复制粘贴，复用过往经验与积累的机器

## 5、深刻理解二八定律

   多思考，多沟通，再下手，少BUG

# 二、协同理念与素养
远程协同是对大家都是一个挑战，为了方便大家推进项目，有几个关键节点需要一起沟通，达成一致。

## 1、前端/后端代码提交频率问题

建议某个小功能或者模块完成大部分就提交，目的是方便核心负责人进行代码审核，项目经理及时了解进度。另外一个很重要的目的是，一旦项目进度出现问题，可以基于代码进度客观公正的评定，避免出现扯皮现象。

## 2、“事不过三”原则

大家都是一个协同关系，很多问题无论本质属于谁的，都尽量互相辅助一起解决，出问题责任一起抗，以及遵循“事不过三”原则：尽量不要让某一方请求协助三次以上还不及时响应，问题尽量在3天以内解决。一旦违背这个原则，导致的出现任何问题，会以不响应一方担负更多责任。所有“过三”的事，相关团队负责人都要都要主动介入辅助推动，也背负责任。
## 3、对真正解决问题以及完成某个功能的定义
真正的解决问题或者完成某个功能，以产品验证为准，最基础的指标就是前后端完成接口对接，无BUG，任何一方没完成都不算整体的完成，另外代码质量也需要过评审。
# 三、工时评定标准
在已有项目基础上开发项目工时评定参考标准，若是原有项目代码质量不是特别差的前提下参考以下标准

## 1、整个项目给出2-3天的学习工时

大家理解能力学习能力应该不差，如果不够可以再提，熟悉之后简单的调整是不困难的，困难的可以单独提出。

### 2、整体架构层面如果牵扯到微调整给予1个工时

### 3、简单细节调整的定义以及工时分配

暂时定义以下为简单细节调整（衡量标准为没有新增变量以及特殊逻辑），文案调整、样式微调，某个内容的隐藏或者删除

如果一个小调整，熟悉项目之后，只需要1、2个小时，市面上几乎是不算工时的，考虑到是新人，我们给予0.25个工时。

### 4、新增内容工时评定标准

联调：简单页面（功能），联调时间不超过0.5个工时，复杂页面（功能）不超过1个工时。

界面：一个小颗粒度组件不超过0.25个工时，比如列表，比如弹窗，功能模块整体不超过2个工时。复杂模块可以单独提出，最多不超过3个工时。

以上是相对合理的评定，关于工时这个问题，制定后若有异议，需联系对方线下一起确认，有异议的地方现场互相陈述事实，最后得出双方认可的工时，尽量保证工时合理，同时不伤害对方积极性。合不合理的判定，可有现场（一位初级+一位高级各评估工时）/2

# 四、代码与分支管理规范

## （一）现状描述
产品代码分支与其他项目分支共同存在一个git项目中，导致分支无限膨胀，给后续使用者带来严重心智负担，如果管理不善很容易造成代码互相污染。

#### 解决建议：
如果不能细颗粒度的进行产品代码与项目代码管理，比如红海项目基于产品代码二开的，发现一个bug，然后单独切分支解决bug，合并到项目与产品。又或者某个二开项目有个新特性，未来自己产品也可能需要，那么切一个feature出来用作共享。那么，强烈建议二开的项目分支移除产品线，独立成一个单独的git项目进行管理。

产品基版已经逐渐稳定，新项目必须要求独立成项，旧分支项目，等交付后，渐进迁走独立成项目

## （二）git分支管理规范
 
### （1）分支与角色说明
Git 分支类型

master 分支（主分支） 稳定版本
develop 分支（开发分支） 最新版本
release 分支（发布分支） 发布新版本
hotfix 分支（热修复分支） 修复线上Bug
feature 分支（特性分支） 实现新特性
Gitlab 角色与项目角色对应关系
Owner（拥有者） Git 管理员
Master（管理员） 开发主管
Developer（开发者） 开发人员
Reporter（报告者） 测试人员
Guest（观察者） 其他人员
 
### （2）分支简明使用流程
1、每开发一个新功能，创建一个 feature 分支，多人在此分支上开发；
2、提测时，将 master 分支和需要提测的分支汇总到一个 release 分支，发布测试环境；
3、发现bug时，在feature分支上debug后，再次回到2；
4、发布生产环境后，将 release 分支合并到 master 分支，删除release分支；
 
### （3）创建新项目（master分支）
开发主管提交代码初始版本到master 分支，并推送至Gitlab系统；
开发主管在Gitlab 系统中设置master 分支为Protectd 分支（保护分支）；
Protected 分支不允许Developer 角色推送代码，但Master 角色可以推送代码；
 
### （4）进行项目开发（develop分支）
开发主管在master 分支上创建develop 分支（开发分支），并推送至Gitlab系统；
master 分支与develop 分支一样，有且仅有一个；
对于非并行项目可以使用develop分支开发方式，对于多人并行开发项目，使用feature分支开发方式，但develop和feature开发方式不应同时使用；
 
### （5）开发新特性（feature分支）
每个新需求或新的研究创建一个feature 分支；
命名规范：
feature-产品名称-新特性关键字，例如：此次的平台站点模块迭代，feature-platform-region-manage
推荐使用feature 分支，但feature 分支的生命周期不能跨一次迭代；
 
### （6）发布测试环境（release分支）
开发负责人需完成以下任务：
1. 确认要发布的feature 分支上的功能是否开发完毕并提交；
2. 创建release 分支（发布分支），将所有要发布的分支逐个合并到release分支，有如下情况：
①.feature分支（可能有多个）
②.master分支（期间可能有其他release版本更新到了master）
3. 命名规则：release-产品名-新特性和待发布版本号-release，例如：release-platform-region-managev1.0.0版本可根据需要添加;
4. 删除本次发布的所有feature分支;
5. 发布到测试环境，通知测试;
 
### （7）修复待发布版本中的Bug（feature分支）
如果发现bug，开发人员在feature 分支上修复测试人员提交给自己的bug，修复完成后，由负责人再次创建 release 分支，发布测试环境。
 
### （8）发布正式环境
开发负责人需完成以下任务：
1. 根据修复后的release分支再次将master合并，打包发布生产环境;
2. 确认发布成功，并线上验收通过后，将release分支合并到master分支;
3. 在master分支上创建标签，命名规则：tag-日期-新特性和版本号，例如：tag-20210720-region-managev1.0.0，版本可根据需要添加，作为发版里程碑标记;
4. 删除对应release 分支;
 
### （9）修复线上Bug（hotfix分支）
线上的不同版本出现了bug怎么办？开发负责人需完成以下任务：
1. 从master 分支某个tag 上创建一个hotfix 分支（热修复分支），一般是最新的tag应该和当前生产环境对应；
命名规则：hotfix-分支创建日期-bug名称和待发布版本号，例如：hotfix-20210721-购物车点击没反应v1.0.1;
2. 开发人员完成Bug 修复，提交hotfix分支到测试环境验收通过;
3. 再次发布正式环境流程;
4. 将hotfix 分支合并到master分支;
5. 在master分支上创建标签，命名规则：tag-日期-新特性和版本号，例如：tag-20210721-region-managev1.0.0，版本可根据需要添加，作为发版里程碑标记;
6. 删除hotfix 分支;

# 五、前端项目与开发规范
##** 一、前端项目基本说明**
## （1）开发相关技术栈
- [Vue](https://cn.vuejs.org/index.html) 版本：2.6.10
- [Vuex](https://vuex.vuejs.org/zh/guide/) 版本：3.1.2
- [Vue Router](https://router.vuejs.org/zh/) 版本：3.1.6
- [Element UI](https://element.eleme.cn/#/zh-CN) 版本： 2.13.0
- [axios](https://github.com/axios/axios) 版本：0.19.2
- [Sass](https://sass-lang.com/)


## （2）相关项目

- [vue-element-admin](https://github.com/PanJiaChen/vue-element-admin)

- [electron-vue-admin](https://github.com/PanJiaChen/electron-vue-admin)

- [vue-typescript-admin-template](https://github.com/Armour/vue-typescript-admin-template)

- [awesome-project](https://github.com/PanJiaChen/vue-element-admin/issues/2312)

## （3）Build Setup

```bash
# 安装依赖
npm install

# 建议不要直接使用 cnpm 安装以来，会有各种诡异的 bug。可以通过如下操作解决 npm 下载速度慢的问题
npm install --registry=https://registry.npm.taobao.org

# 启动服务
npm run dev
```

浏览器访问 [http://localhost:9528](http://localhost:9528)

## （4）发布

```bash
# 构建测试环境
npm run build:stage

# 构建生产环境
npm run build:prod
```

## （5）其它

```bash
# 预览发布环境效果
npm run preview

# 预览发布环境效果 + 静态资源分析
npm run preview -- --report

# 代码格式检查
npm run lint

# 代码格式检查并自动修复
npm run lint -- --fix
```

更多信息请参考 [使用文档](https://panjiachen.github.io/vue-element-admin-site/zh/)

## （二）前端开发规范
### （1）命名规范
1. 文件夹命名  
  用短横线命名 (kebab-case)。有复数结构采用复数命名法。例如：my-components
   - 模块独立新建一个文件夹
   - 模块内部的组件放在对应的components内
   - 模块内部的功能方法放在对应的utils内
2. 文件命名
    *文件的命名统一用小写，使用短横线命名 (kebab-case)，包括js、css、html文件*
   - 入口文件统一为index.vue
3. 组件命名
   - 声明约定：PascalCase（大驼峰，单词首字母大写命名）
   - 使用约定：kebab-case（短横线分割命名）
   - 组件名应该始终是多个单词的，根组件APP以及<transition>、<component>之类的Vue内置组件除外
   - 导入及注册组件的时候，遵循PascalCase约定。例如：import MyComponent from '@/components/MyComponent'
4. 普通变量命名规范  
    *小驼峰命名*，有复数结构采用复数命名法。例如：myData
5. 常量  
   全部大写，下划线分割单词。例如：MY_DATA
6. Props（组件参数）命名
   在声明prop的时候，始终使用camelCase， 在模板中使用的时候始终使用kebab-case
7. Method方法命名规范
   - 驼峰式命名，统一使用动词或者动词+名词形式
   - 请求数据方法，以data结尾
   - 尽量使用常用单词开头（set，get，go，can, has, is）

### （2）结构化规范
1. [目录结构](https://panjiachen.github.io/vue-element-admin-site/zh/guide/#%E7%9B%AE%E5%BD%95%E7%BB%93%E6%9E%84)
2. vue文件基本结构
   ```
    <template>
        <div>
            <!-- template下只能有一个节点 -->
        </div>
    </template>
    <script>
        export default {
            // 必须带上唯一性的name，跟文件名一致
        name: 'myDemo',
        data () {
                return {
                }
            },
        mounted() {
            },
            methods: {
            },
        }
    </script>
    <!-- 声明语言，并且添加scoped，统一用scss -->
    <style lang="scss" scoped>
    </style>

   ```
3. 多种特性的元素规范
   ```
   // 多个特性的元素应该分多行撰写，增强易读性。
    <my-component
        foo=”a”
        bar=”b”
    >
    </my-component>

   ```
4. 元素特性的顺序原生属性放前面，指令放后面
5. 组件选项顺序(可参考)
    - 副作用 (触发组件外的影响)
    - el
    - 全局感知 (要求组件以外的知识)
    - name
    - parent
    - 组件类型 (更改组件的类型)
    - functional
    - 模板修改器 (改变模板的编译方式)
    - delimiters
    - comments
    - 模板依赖 (模板内使用的资源)
    - components
    - directives
    - filters
    - 组合 (向选项里合并属性)
    - extends
    - mixins
    - 接口 (组件的接口)
    - inheritAttrs
    - model
    - props/propsData
    - 本地状态 (本地的响应式属性)
    - data
    - computed
    - 事件 (通过响应式事件触发的回调)
    - watch
    - 生命周期钩子 (按照它们被调用的顺序)
    - beforeCreate
    - created
    - beforeMount
    - mounted
    - beforeUpdate
    - updated
    - activated
    - deactivated
    - beforeDestroy
    - destroyed
    - 非响应式的属性 (不依赖响应系统的实例属性)
    - methods
    - 渲染 (组件输出的声明式描述)
    - template/render
    - renderError

### （3）注释规范
1. 务必添加注释列表
   1. 公共组件使用说明
   2. 各组件中重要函数或者类说明
   3. 复杂的业务逻辑处理说明
   4. 特殊情况的代码处理说明，对于代码中特殊用途的变量、存在临界值、函数中使用的hack、使用了某种算法或思路等需要进行注释描述
   5. 多重if判断语句
   6. 注释块必须以/** （至少两个星号）开头 **/
   7. 单行注释使用//
2. 单行注释：注释单独一行
3. 多行注释
   组件使用说明，和调用说明
   ```
   <!--公用组件：导出模板
    /** 
    * 组件名称 
    * @module 组件存放位置 
    * @desc 组件描述 
    * <AUTHOR> 
    * @date 2017年12月05日17:22:43 
    * @param {Object} [title] - 参数说明 
    * @param {String} [columns] - 参数说明 
    * @example 调用示例 
    * <hb-table :title="title" :columns="columns" :tableData="tableData"></hb-table>
    * /
   ```