<template>
  <div class="archivesPageContent">
    <div class="accountDetail" v-loading="statisticsFlag">
      <div class="left">
        <div class="total">
          <div>
            <p>资金账户总额（元）</p>
            <p>{{ statistics.amount|getDecimals  }}</p>
          </div>
        </div>
        <div class="detail">
          <div class="top">
            <div>
              <p>货款账户（元）</p>
              <p>{{ statistics.purchasesAmount|getDecimals }}</p>
            </div>
            <div>
              <p>交易服务费账户（元）</p>
              <p>{{ statistics.serviceAmount|getDecimals }}</p>
            </div>
            <div>
              <p>商家总金额（元）<el-button type="text" @click="$router.push('/finance/merchantsBalance/index')">明细</el-button></p>
              <p style="color: #333">{{ statistics.merchantsAmount|getDecimals }}</p>
            </div>
          </div>
          <div class="top">
            <div>
              <p>业务员品种推广账户（元）</p>
              <p>{{ statistics.promotionAmount|getDecimals }}</p>
            </div>
            <div>
              <p>交易手续费账户（元）</p>
              <p>{{ statistics.chargedAmount|getDecimals }}</p>
            </div>
            <div>
              <p>业务员总余额（元）<el-button v-if="false" type="text" @click="$router.push('/finance/salesmanBalanc/index')">明细</el-button></p>
              <p style="color: #333">{{ statistics.salesmanAmount|getDecimals }}</p>
            </div>
          </div>
        </div>
      </div>
      <div class="right">
        <div>
          <p>已收商家保证金（元）</p>
          <p>{{ statistics.merchantDepositAmount|getDecimals }}</p>
        </div>
        <div>
          <p>已收品种保证金（元）</p>
          <p>{{ statistics.varietyDepositAmount|getDecimals }}</p>
        </div>
        <div>
          <p>已收平台技术服务费总额（元）</p>
          <p>{{ statistics.platformAmount|getDecimals }}</p>
        </div>
      </div>
    </div>
    <im-search-pad
      :has-expand="false"
      :model="listQuery"
      @reset="resetForm"
      @search="onSearchSubmitFun"
    >
      <im-search-pad-item prop="businessNo">
        <el-input v-model="listQuery.model.businessNo" placeholder="请输入业务单号" />
      </im-search-pad-item>
      <im-search-pad-item prop="detailType">
        <el-select
          v-model="listQuery.model.detailType"
          placeholder="明细类型"
        >
          <el-option
            v-for="item in detailType"
            :label="item.label"
            :value="item.value"
            :key="item.value"
          ></el-option>
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item prop="paymentsType">
        <el-select
          v-model="listQuery.model.paymentsType"
          placeholder="收支类型"
        >
          <el-option
            v-for="item in paymentsType"
            :label="item.label"
            :value="item.value"
            :key="item.value"
          ></el-option>
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item prop="accountsType">
        <el-select
          v-model="listQuery.model.accountsType"
          placeholder="账户类型"
        >
          <el-option
            v-for="item in accountsType"
            :label="item.label"
            :value="item.value"
            :key="item.value"
          ></el-option>
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item prop="timePicker">
        <el-date-picker
          v-model="timePicker"
          type="datetimerange"
          align="right"
          unlink-panels
          range-separator="至"
          start-placeholder="查询起始日期"
          end-placeholder="查询结束日期"
          :picker-options="pickerOptions"
          value-format="yyyy-MM-dd hh:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          @change="selectTime"
        >
        </el-date-picker>
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        :tabs="[{ name: '资金账户收支明细' }]"
      />
      <div class="table">
        <el-table
          ref="table"
          v-if="list"
          v-loading="listLoading"
          :data="list"
          row-key="id"
          border
          fit
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column
            align="center"
            width="65"
            :render-header="renderHeader"
            fixed
          >
            <template slot-scope="scope">
              <span>{{ scope.$index + 1 }} </span>
            </template>
          </el-table-column>
          <el-table-column
            v-for="(item, index) in tableTitle"
            :key="index"
            :min-width="item.width ? item.width : '350px'"
            :label="item.label"
            show-overflow-tooltip
            align="left"
          >
            <template slot-scope="{ row }">
            <span
              v-if="
                [item.name] == 'paymentsType' ||
                [item.name] == 'accountsType' ||
                [item.name] == 'detailType'
              "
            >
              {{ row[item.name].desc }}
            </span>
              <span v-else-if="item.name == 'amount'">
              <span v-if="row.paymentsType.code == 'COLLECT'"  style="color:#70B603">{{ (row[item.name])|getDecimals }} </span>
              <span v-else style="color:#F59A23">{{(row[item.name])|getDecimals}}</span>
            </span>
              <span v-else>{{ row[item.name] }}</span>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-if="total > 0"
          :pageSizes="[10, 20, 50, 100]"
          :total="total"
          :page.sync="listQuery.current"
          :limit.sync="listQuery.size"
          @pagination="getlist"
        />
      </div>
    </div>
  </div>
</template>

<script>
import Pagination from "@/components/Pagination";
import { list, statistics } from "@/api/finance/capitalAccount/index";
import { areas } from "@/api/enterprise";
import tableInfo from "@/views/finance/capitalAccount/tableInfo";
import TabsLayout from '@/components/TabsLayout'
export default {
  name: "capitalAccount",
  data() {
    return {
      listLoading: false,
      list: [],
      tabType: "list",
      accountsType: [
        {
          index:9,
          value:'',
          label: "全部"
        },
        {
          index: 0,
          value: "HUOKUAN",
          label: "货款账户",
        },
        {
          index: 1,
          value: "COMMISSION",
          label: "业务员品种推广账户",
        },
        {
          index: 2,
          value: "SERVICE",
          label: "交易服务费账户",
        },
        {
          index: 3,
          value: "PROCEDURES",
          label: "交易手续费账户",
        },
        {
          index: 4,
          value: "SHOP",
          label: "商家余额",
        },
        {
          index: 5,
          value: "SALE",
          label: "业务员余额",
        },
        {
          index: 6,
          value: "TIXIAN",
          label: "平台提现账户",
        },
        {
          index: 7,
          value: "CHONGZHI",
          label: "平台充值账户",
        },
        {
          index: 8,
          value: "BANK",
          label: "银行卡",
        },
      ],
      detailType: [
        {
          index:10,
          value:'',
          label:'全部'
        },
        {
          index: 0,
          value: "ORDER",
          label: "商品销售",
        },
        {
          index: 1,
          value: "REFUND",
          label: "退货退款",
        },
        {
          index: 2,
          value: "RECHARGE",
          label: "余额充值",
        },
        {
          index: 3,
          value: "CASH",
          label: "余额提现",
        },
        {
          index: 4,
          value: "CASHCHONGZHI",
          label: "提现冲正",
        },
        {
          index: 5,
          value: "SERVICE",
          label: "交易服务费",
        },
        {
          index: 6,
          value: "PROCEDURES",
          label: "交易手续费",
        },
        {
          index: 7,
          value: "COMMISSION",
          label: "品种推广佣金",
        },
         {
          index: 8,
          value: "SELLER",
          label: "商家保证金",
        },
        {
          index: 9,
          value: "DEPOSIT",
          label: "品种保证金",
        },
      ],
      paymentsType: [
        {
          index: 2,
          value:'',
          label: "全部"
        },
        {
          index: 0,
          value: "COLLECT",
          label: "收入(收款)",
        },
        {
          index: 1,
          value: "PAY",
          label: "支出(付款)",
        },
      ],
      timePicker: [],
      listQuery: {
        current: 1,
        size: 10,
        model: {},
      },
      total: 0,
      statistics: {},
      statisticsFlag: false,
      cityValue: [],
      tableTitle: [],
      tableSelectTitle: [0, 1, 2, 3],
      multipleSelection: [],
      multipleSelectionId: [],
      showSelectTitle: false,
      pickerOptions: {
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      props: {
        lazy: true,
        async lazyLoad(node, resolve) {
          const { level } = node;
          let id = node.data ? node.data.id : "";
          let res = await areas({ parentId: id });
          let list = res.data;
          list.forEach((item) => {
            item.value = item.id;
            item.leaf = level >= 2;
          });
          resolve(list);
        },
      },
    };
  },
  methods: {
    selectTime(e) {
      this.listQuery.model.startTime = e[0];
      this.listQuery.model.endTime = e[1];
    },
    cityChange(e) {
      this.listQuery.model.provinceId = e[0];
      this.listQuery.model.cityId = e[1];
      this.listQuery.model.countyId = e[2];
    },
    resetForm(type) {
      this.listQuery = {
        current: 1,
        size: 10,
        model: {},
      }
      this.timePicker = []
      this.list = []
      this.getlist()
    },
    onSearchSubmitFun() {
      this.getlist();
    },
    async getlist() {
      this.listLoading = true
      let { data } = await list(this.listQuery);
      this.listLoading = false
      this.list = data.records;
      this.total = data.total;
    },
    async getStatistics() {
      this.statisticsFlag = true;
      let { data } = await statistics();
      this.statisticsFlag = false;
      this.statistics = data;
    },
    initTbaleTitle() {
      this.tableSelectTitle = [];
      this.tableTitle = tableInfo[this.tabType];
    },
    renderHeader(h, { column }) {
      var titles = tableInfo[this.tabType];
      var titlesName = ["显示字段项", "隐藏字段项"];
      return (
        <div style="position:relative">
          <div onClick={this.showHeaer}>
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="640px"
            center
            append-to-body={true}
          >
            <el-transfer
              vModel={this.tableSelectTitle}
              data={titles}
              titles={titlesName}
              onChange={this.setleftTitleFun}
            ></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button onClick={this.closeHeaer}>取消</el-button>
              <el-button type="primary" onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      );
    },
    setleftTitleFun(val) {
      this.tableSelectTitle = val;
    },
    showHeaer: function () {
      this.showSelectTitle = true;
    },
    closeHeaer: function () {
      this.showSelectTitle = false;
      this.tableSelectTitle = [];
    },
    setHeaer: function () {
      var titles = tableInfo[this.tabType];
      var listinfo = titles.filter((element, index, self) => {
        return !this.tableSelectTitle.includes(element.key);
      });
      this.tableTitle = listinfo;
      this.showSelectTitle = !this.showSelectTitle;
    },
  },
  created() {
    this.initTbaleTitle();
    this.getlist();
    this.getStatistics();
  },
  components: {
    Pagination,
    TabsLayout
  }
};
</script>


<style lang="scss" scoped>
.archivesPageContent {
  padding: 0;
  .accountDetail {
    padding: 22px;
    border-bottom: 16px solid #f2f3f4;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
    flex-flow: wrap;
    p {
      font-size: 14px;
      color: #929292;
      line-height: 36px;
      &:nth-of-type(2) {
        color: #86c12a;
      }
    }
    .total {
      display: flex;
      align-items: center;
      justify-items: center;
      width: 250px;
      p {
        margin: 10px 0;
      }
      p:nth-of-type(2) {
        font-size: 32px;
        margin: 0;
      }
      div {
        width: 100%;
      }
    }
    .left {
      display: flex;
      justify-content: space-around;
      align-content: center;
      .top {
        display: flex;
        justify-items: center;
        border-bottom: 1px solid #ccc;
        div {
          width: 210px;
          padding: 0 10px;
          p:nth-of-type(1) {
            margin-bottom: 0;
          }
          p:nth-of-type(2) {
            font-size: 22px;
            margin-top: 0;
            color: #86c12a;
          }
        }
      }
      .top:last-child {
        border-bottom: none;
      }
    }
    .right {
      display: flex;
      div {
        width: 220px;
        padding: 0 10px;
        p:nth-of-type(2) {
          font-size: 28px;
          color: #f7a945;
          margin-top: 0;
        }
      }
    }
  }
  .temp_searchBox {
    height: 64px;
    overflow: hidden;
    margin-bottom: 0;
  }
  .form-inline {
    height: 60px;
    overflow: hidden;
  }
  .title {
    border-bottom: 2px solid #ebecee;
    margin-bottom: 16px;
    span {
      margin-bottom: -2px;
      padding: 0 15px;
      height: 40px;
      line-height: 30px;
      display: block;
      background: rgba(255, 255, 255, 0);
      border-bottom: 2px solid rgb(64, 158, 255);
      font-size: 16px;
      font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
        "PingFang SC"-400;
      font-weight: 400;
      color: rgb(64, 158, 255);
    }
  }

  .formItem {
    width: 586px;
  }
  .line {
    color: #dfe6ec;
    margin: 0 6px;
  }
  .typeTabs {
    height: 40px;
    margin-bottom: -2px;
    margin-left: 30px;
  }
}
</style>
