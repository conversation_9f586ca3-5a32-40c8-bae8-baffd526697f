import request from '@/utils/request'
import requestAxios from '@/utils/requestAxios'

// 分页列表
export  function list(data) {
  return requestAxios({
    url: '/api/authority/user/page',
    method: 'post',
    data
  })
}

// 重置密码
export  function resetPwd(data) {
  return requestAxios({
    url: '/api/authority/user/reset',
    method: 'post',
    data
  })
}

// 批量启用
export  function batchEnable(data) {
  return requestAxios({
    url: '/api/authority/user/batchEnable',
    method: 'post',
    params: data
  })
}

// 批量冻结
export  function batchFreeze(data) {
  return requestAxios({
    url: '/api/authority/user/batchFreeze',
    method: 'post',
    params: data
  })
} 

// 冻结用户
export  function freeze(data) {
  return requestAxios({
    url: '/api/authority/user/freeze',
    method: 'post',
    params: data
  })
}

// 启用用户
export  function enable(data) {
  return requestAxios({
    url: '/api/authority/user/enable',
    method: 'post',
    params: data
  })
} 

// 添加用户
export  function addUser(data) {
  return requestAxios({
    url: '/api/authority/user',
    method: 'post',
    data
  })
}

// 修改用户
export  function editUser(data) {
  return requestAxios({
    url: '/api/authority/user',
    method: 'put',
    data
  })
}

// 获取用户类型
export  function getUserType() {
  return new Promise((resolve, reject) => {
    requestAxios({
      url: '/api/authority/user/getUserParamType',
      method: 'get',
    }).then(res => {
      if (res.isSuccess) {
        resolve(res.data)
      } else {
        reject(res)
      }
    }).catch(err => {
      reject(err)
    })
  })
}
