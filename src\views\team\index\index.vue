<template>
  <main class="container">
    <!--小卫科技  -->
    <section class="top flex_between_center">
      <div v-if="organizationData == null">请先创建组织</div>
      <el-dropdown
        v-if="organizationData!=null"
        trigger="click"
        size="medium"
        class="dropdown"
        @command="handleSelectEnterprise"
      >
        <span class="el-dropdown-link"
          >{{selectedIndex==null?"请选择":organizationData[selectedIndex].name}}<i class="el-icon-arrow-down el-icon--right"></i
        ></span>
        <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="(item, index) in organizationData" :key="index" :command="index">{{
              item.name
            }}</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <div class="btns">
        <el-button size="small" type="primary" plain @click="handlePromote" v-if="checkPermission(['admin', 'admin-team-orgaStructure:setPromotionFee'])"
          >推广费设置</el-button
        >
        <del-el-button 
          v-if="checkPermission(['admin', 'admin-team-orgaStructure:dissolve'])"
            style="margin: 0 10px;"
            :sizeTyep="'small'"
            :showType="''" 
            :btnTxt="'解散组织'" 
            :text="'确认解散当前组织吗?'" 
            @handleDel="deleteOrganizationFun">
        </del-el-button>
        <el-button v-if="checkPermission(['admin', 'admin-team-orgaStructure:create'])" size="small" type="primary" @click="showEditOrganizationFun">创建组织</el-button
        >
      </div>
    </section>
    <!--小卫科技  -->
    <!--内容  -->
    <div class="content flex_between_start" v-if="organizationInfo" >
      <!-- 左侧 -->
      <aside class="leftNav">
        <div class="leftbtns">
            <el-radio-group v-model="tabPosition" class="radioBox" @change="tabPositionChange">
                <el-radio-button label="organization">组织架构</el-radio-button>
                <el-radio-button label="role">角色</el-radio-button>
            </el-radio-group>
            <template v-if="tabPosition == 'organization'">
              <div class="addBtn">
                <el-link type="primary" v-if="checkPermission(['admin', 'admin-team-orgaStructure:addDepartment'])" :underline="false" @click="showEditSubDepartmentFun()">+添加部门</el-link>
              </div>
              <div>
                <el-tree 
                  ref="tree"
                  class="tree"
                  default-expand-all
                  highlight-current	
                  :data="treeData"
                  node-key="id"
                  :current-node-key="departmentId"
                  @node-click="handleNodeClick"
                  :expand-on-click-node="false"
                  icon-class=" "
                >
                  <template #default="{ node, data }">
                      <el-row type="flex" justify="space-between" align="center"  :style="data.parentId=='0'?'width:100%':''">
                          <el-col :span="data.parentId=='0'?24:20">
                              <p v-if="data.parentId=='0'" :class="data.id ==departmentId?'defaultColor onColor': 'defaultColor' " >
                                  <template v-if="data.children!=undefined">
                                      <i  @click.stop="expandFun(node)" :class="node.expanded?'el-icon-caret-bottom':'el-icon-caret-right'"></i>
                                  </template>
                                  <template v-else>
                                    <i class="el-icon-caret-bottom none"> &nbsp;&nbsp;</i>
                                  </template>
                                  <svg-icon style="margin:0 5px;" icon-class="tree-table" />
                                  <span class="treefontColor"><i class="el-icon-folder-opened iconColor"></i>{{data.name|ellipsis}}({{data.totalCount}}人)</span>
                                  <i class="el-icon-refresh" @click.stop="refreshFun()"></i>
                              </p>
                              <p v-else  :class="data.id ==departmentId?'defaultColor onColor': 'defaultColor' " >
                                  <template v-if="data.children!=undefined">
                                      <i  @click.stop="expandFun(node)"  :class="node.expanded?'el-icon-caret-bottom':'el-icon-caret-right'"></i>
                                  </template>
                                  <template v-else> <i class="el-icon-caret-bottom none"> &nbsp;&nbsp;</i></template>
                                  <span class="treefontColor"><i class="el-icon-folder-opened iconColor"></i>{{data.name|ellipsis}}({{data.totalCount}}人)</span>
                              </p>
                          </el-col>
                      </el-row>
                  </template>
                </el-tree>
              </div>
            </template>
            <template v-if="tabPosition == 'role'">
              <!-- 角色 -->
              <div style="margin-top:10px" class="roleTree">
                <div :class="[defaultCheckedKeys== item.id ? 'custom-tree-node currentCheck' : 'custom-tree-node']" v-for="(item,index) in treeDataRole" :key="index" @click="handleNodeRole(item)">
                  <div>{{ item.name }}</div>
                  <div v-if="checkPermission(['admin', 'admin-team-role:edit'])">
                      <i class="el-icon-edit" style="cursor:pointer;" @click.stop="editRole(item)"></i>
                  </div>
                </div>
              </div>
            </template>
        </div>  
      </aside>
      <!-- 左侧 -->
      <!-- 右侧 -->
    
      <section class="rightContent">
          <template v-if="tabPosition == 'organization'" >
            <Organization  ref="organization" :node="organizationNavNode" ></Organization> 
              
              <!-- 新增部门 -->
              <template v-if="organizationInfo"  >
                <EditDepartmentDialog :editForm="editSubDepartmentForm" :crateSource="'content'" v-if="visibleEditDepartmentDialog"  :visible.sync="visibleEditDepartmentDialog"  @confirm="confirmDepartmentSubmitFun"  ></EditDepartmentDialog>
              </template>
          </template>
          <!-- 新增部门 -->
          <template v-if="tabPosition == 'role'" >
            <Role ref="roleRef"></Role>
          </template >
      </section>
      <!-- 右侧 -->
    </div>
    <!--内容  -->

  <amendDia ref="amendDiaRef" @submitRoleEdit="submitRoleEdit"></amendDia>
  <!-- 新增组织 -->
  <EditOrganizationDialog v-if="visibleEditOrganizationDialog"  :visible.sync="visibleEditOrganizationDialog"  @confirm="confirmOrganizationSubmitFun" ></EditOrganizationDialog>
  <!-- 新增组织 -->
  </main>
</template>
<script>
// import { query } from '@/api/';
import Cookies from 'js-cookie'
import { mapState,mapGetters,mapActions} from 'vuex'
import { getOrganizationList,getOrganizationTreeById,orgRoleList,deleteOrga } from '@/api/organization/index';
import { orgRole } from '@/api/organization/role'
import { getFathersByParemId } from '@/utils/index';
import Role from '@/views/team/index/temp/role';
import amendDia from "@/views/team/index/temp/role/components/amendDia"
import Organization from '@/views/team/index/temp/organization';
import EditOrganizationDialog from '@/views/team/index/temp/organization/editOrganizationDialog';
import EditDepartmentDialog from '@/views/team/index/temp/organization/editDepartmentDialog';
import delElButton from "@/components/eyaolink/delElButton";
import checkPermission from '@/utils/permission';
export default {
  data() {
    return {
      visibleEditOrganizationDialog:false,
      visibleEditDepartmentDialog:false,
      // rootNode:[],
      // currentNode:1,
      selectedIndex:null,
      organizationData:null,
      activeIndex: 0,
      // 左侧导航
      tabPosition: "organization",
      treeData:null,
      departmentId:"",
      treeDataRole:[],
      defaultCheckedKeys: '',
    };
  },
  components: {
    // 页面
    Role,
    // 组件
    amendDia,
    Organization,
    EditOrganizationDialog,
    EditDepartmentDialog,
    delElButton
  },
  computed: {
    // 计算属性computed :
    // 1. 支持缓存，只有依赖数据发生改变，才会重新进行计算
    // 2. 不支持异步，当computed内有异步操作时无效，无法监听数据的变化
    // 3. computed 属性值会默认走缓存，计算属性是基于它们的响应式依赖进行缓存的，也就是基于data中声明过或者父组件传递的props中的数据通过计算得到的值
    // 4. 如果一个属性是由其他属性计算而来的，这个属性依赖其他属性，是一个多对一或者一对一，一般用computed
    // 5.如果computed属性属性值是函数，那么默认会走get方法；函数的返回值就是属性的属性值；在computed中的，属性都有一个get和一个set方法，当数据变化时，调用set方法。
    ...mapGetters([
      'organizationNavNode',
      "organizationInfo"
    ]),

  },
  filters: {
    ellipsis (value) {
      if (!value) return ''
      if (value.length > 8) {
        return value.slice(0,8) + '...'
      }
      return value
    }
  },
  watch: {
    // 监听属性watch：
    // 1. 不支持缓存，数据变，直接会触发相应的操作；
    // 2. watch支持异步；
    // 3. 监听的函数接收两个参数，第一个参数是最新的值；第二个参数是输入之前的值；
    // 4. 当一个属性发生变化时，需要执行对应的操作；一对多；
    // 5. 监听数据必须是data中声明过或者父组件传递过来的props中的数据，当数据变化时，触发其他操作，函数有两个参数，
    // 　　immediate：组件加载立即触发回调函数执行，
    // 　　deep: 深度监听，为了发现对象内部值的变化，复杂类型的数据时使用，例如数组中的对象内容的改变，注意监听数组的变动不需要这么做。注意：deep无法监听到数组的变动和对象的新增，参考vue数组变异,只有以响应式的方式触发才会被监听到。
    selectedIndex:function(newValue, oldValue){
      if(newValue!=oldValue){
        if(this.tabPosition == 'organization'){
          this.setOrganizationNavNode(null)
          this.getOrganizationListFun();
        } else {
          this.initData();
        }
      }
    }
  
  },
  methods: {
    checkPermission,
    ...{getFathersByParemId},
    //方法集合
    ...mapActions({
      setOrganizationNavNode: "organization/setOrganizationNavNode"
    }),
    // 组织切换
    handleSelectEnterprise(selectedIndex) {
      console.log('--selectedIndex--->',selectedIndex);
      this.selectedIndex =selectedIndex
      this.setOrganizationNavNode([this.organizationData[selectedIndex]]);
    },
    
    // 部门树
    expandFun(node){
      // 展开空值
      node.expanded = !node.expanded;
    },
    refreshFun(){
        // 刷新树
        if(this.organizationData!=null){
          this.setOrganizationNavNode([this.organizationData[this.selectedIndex||0]])
          this.getOrganizationTreeByIdFun(this.organizationData[this.selectedIndex||0].id)
        }
        console.info("refreshFun")
    },
    handleNodeClick(node){
      console.info(node)
      this.departmentId=node.id
      this.setOrganizationNavNode(this.getFathersByParemId(node.id,this.treeData))
    },

    // 子組件设置选中树
    setNodeByComponent(id){
      console.info("setNodeByComponent",id)
      this.departmentId=id;
      this.$nextTick(() => {
        if(this.$refs.tree) {
          this.$refs.tree.setCurrentKey(id)
        }
      })
      this.setOrganizationNavNode(this.getFathersByParemId(id,this.treeData))
    },



    // 组织新增
    showEditOrganizationFun(){
      this.visibleEditOrganizationDialog =true;
      // this.$refs.EditOrganizationDialog.show()
    },
    confirmOrganizationSubmitFun(data){
      console.info("confirmOrganizationSubmitFun",data)
      // this.$refs.EditOrganizationDialog.close()
      this.load("UpdateOrgan")
    },
      // 组织新增  end

    // 组织解散
    deleteOrganizationFun(id){
        if(this.organizationNavNode.length > 0){
          deleteOrga(this.organizationNavNode[0].id).then(res=>{
            let {code,data,msg} =res;
            if(code==0){
              this.$message.success("解散成功!")
              this.$router.go(0)
            }
          })
        }
    },
    // 组织解散end




    // 部门新增
    showEditSubDepartmentFun(){
      let row = {
          id:null,
          name: '',
          orgaAreaDto: {
            commonAreaIds: []
          },
          parentId: 0,
          salesmanId: ''
        }
      row.parentId = this.organizationNavNode[this.organizationNavNode.length-1].id;
      this.editSubDepartmentForm = row;
      this.visibleEditDepartmentDialog=true;
      // this.$refs.EditDepartmentDialog.show()
    },
    confirmDepartmentSubmitFun(data){
      // console.info("confirmDepartmentSubmitFun",data)
      // this.$refs.EditDepartmentDialog.close()
      this.visibleEditDepartmentDialog=false;
      this.load("Department")
    },
    // 部门新增 end


    // 获取所有组织
    getOrganizationListFun(){
      getOrganizationList().then(res=>{
        let {code,data,error} = res;
        if(code == 0 ){
          if(data!=null&& data.length > 0){
            this.selectedIndex =  this.selectedIndex|| 0;
            this.organizationData = data||null;
            if(this.organizationData!=null){
              this.departmentId=this.organizationData[this.selectedIndex||0].id
              this.setOrganizationNavNode([this.organizationData[this.selectedIndex||0]])
              this.getOrganizationTreeByIdFun(data[this.selectedIndex||0].id)
            }
          }
        }
      })
    },
    // 获取当前组织树组织
    getOrganizationTreeByIdFun(id){
      getOrganizationTreeById(id).then(res=>{
        let {code,data,msg} = res;
        if(code == 0 ){
          this.treeData = data;
          if(this.departmentId!=""){
            this.$nextTick(() => {
              if(this.$refs.tree) {
                this.$refs.tree.setCurrentKey(this.departmentId)
              }
              
            });
          }
        }
      })
    },

    // 获取内容
    load(data = null) {
      if(data == 'Department'){
        // 更新部门相关信息
        this.getOrganizationTreeByIdFun(this.organizationNavNode[0].id)
        // this.getOrganizationListFun();
        this.$refs.organization.getDepartmentInfo()
      }else  if(data!=='ALL'){
        // 更新 整个页面的
        this.getOrganizationListFun();
      }else{
        
      }
     
    },
    handlePromote(){
      Cookies.set('selectedIndex',this.selectedIndex)
      this.$router.push({
        path:'/teamManager/promote'
      });
    },
    tabPositionChange(val){
      console.log('val-->',val);
      if(val=='role'){
        this.initData();
      } else {
        this.getOrganizationTreeByIdFun(this.organizationData[this.selectedIndex||0].id);
      }
    },
    // 获取角色
    async initData() {
      let params = {
        organizationId:this.organizationData[this.selectedIndex].id
      }
      let result = await orgRoleList(params);
      if (result.code != 0 && result.msg != 'ok') {
        return
      };
      console.log('result',result);
      this.treeDataRole = result.data || [];
      this.$refs.roleRef.switchover(result.data[0]);
      this.defaultCheckedKeys = result.data[0].id;
    },
    handleNodeRole(val){
      console.log('val---->',val);
      this.defaultCheckedKeys = val.id;
      this.$refs.roleRef.switchover(val);
    },
    // 编辑角色
    editRole(item) {
      console.log('item---->',item);
      this.$refs.amendDiaRef.openDia(item);
      // orgRole()
    },
    submitRoleEdit(result){
      console.log('result,',result);
      let params = {
        id:result.id,
        name:result.name
      };
      orgRole(params).then(res=>{
        if(res.code == 0 && res.msg == 'ok') {
          this.$message.success('修改角色成功');
          this.initData();
        }
      })
    },
  },
  async mounted() {
    // 方法调用
    console.info("mounted")
    await  this.getOrganizationListFun();
  },
  beforeRouteEnter (to, from, next) {
    // ...do something
    console.info("beforeRouteEnter")
    if(from.path != "/teamManager/promote"){
      next((_this) => {
        // 可以通过这里访问~
        _this.setOrganizationNavNode(null)
      });
    }else{
       next((_this) => {
        // 可以通过这里访问~
        // _this.setOrganizationNavNode(null)
        _this.selectedIndex= Cookies.get('selectedIndex')||0;
       
      });
    }
   
  },
  beforeDestroy() {},
};
</script>

<style lang="scss" scoped>

.text_overflow {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}
.el-icon-caret-bottom.none:before{
  content:" ";
  display:inline-block;
  width:15px;
}
.container {
  // background-color: #f2f3f4;
  .top {
    margin-bottom: 16px;
    background-color: #fff;
    padding: 16px;

    .dropdown {
      cursor: pointer;
    }
  }
  
  .content{
    align-items: stretch;
    .leftNav {
        width: 280px;
        margin: 0;
        padding: 16px 0;
        background:#fff;

        .radioBox{width: 100%; display: flex;}  
        ::v-deep .treeitem{ line-height: 45px;}
        ::v-deep .el-radio-button{ flex: 1}
        ::v-deep .el-radio-button__inner{width: 100%; flex: 1}
        
        .addBtn{padding: 0 0 10px;}
        .defaultColor{padding: 0; }
        .defaultColor .treefontColor{ color:#000; font-size:14px; min-width: 75%;display: inline-block;
          i{margin-right: 5px;font-size:14px;}
        }
        .defaultColor.onColor .treefontColor{ color:#fff; font-size:14px;
          i{margin-right: 5px;font-size:14px;}
        }
        .iconColor{color: #000;}
        ::v-deep .el-tree-node__expand-icon{display: none}

        .level0{
          font-size: 14px;
          // background:  #0056E5;
          // color:#fff;
          padding: 0;
          padding-left:5px;
          .el-icon-refresh{float: right; margin-right: 5px;margin-top: 8px;}
          .treefontColor{
            margin-left: 5px;color:#000;
          }
        }
        .leftbtns{padding: 0 16px;}
        
    }

    .rightContent{
      margin-left: 16px;flex: 1;    width: calc(100% - 300px);

      
    }
  }




}
.roleTree {
  .custom-tree-node {
    cursor: pointer;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 5px;
  }
 ::v-deep .el-tree-node__content {
    height: 36px;
 }
 .currentCheck {
   background-color: #0056E5;
   color: #fff;
   border-radius: 3px;
 }
}


 ::v-deep .tree .el-tree-node__content {
  line-height: 35px;
  height: 35px;
}
 ::v-deep .tree .is-current > .el-tree-node__content {
  background: #0056E5;
  color:#fff;
}
 ::v-deep .tree .is-current > .el-tree-node__content .iconColor{color: #fff;}
 

</style>
