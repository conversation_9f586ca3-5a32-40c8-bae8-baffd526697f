<template>
  <div class="goods">
    <!-- <div>
      <el-form>
        <el-form-item>
          <el-input placeholder="请输入商品名称/通用名/商品编码" style="width:250px;padding-right:10px"></el-input>
          <el-button type="primary">搜索</el-button>
          <el-button>重置</el-button>
        </el-form-item>
      </el-form>
    </div> -->
    <el-table v-loading="isLoading" border fit :data="list" style="width: 100%" max-height="450px" class="table">
      <el-table-column align="center" width="80" fixed="left" :render-header="renderHeader">
        <template slot-scope="scope">
          <span>{{scope.$index + 1}} </span>
        </template>
      </el-table-column>
      <el-table-column v-for="(item, index) in tableTitle" :key="index" :min-width="(item.width?item.width:'350px')" :fixed="(item.name == 'publishStatus'||item.name == 'pictIdS' ) ? 'left' : false"
        :label="item.label" :align="item.name == 'pictIdS'? 'center': 'left'">
        <template slot-scope="{row}">
          <span>{{ row[item.name] }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-if="total>0" :pageSizes="[2, 10, 20, 50]" :total="total" :page.sync="listQuery.current" :limit.sync="listQuery.size" @pagination="getlist" />
    <!-- <div class="dialog-footer">
      <el-button @click="cancelFun">取 消</el-button>
      <el-button type="primary" @click="confirmFun">确 定</el-button>
    </div> -->
  </div>
</template>

<script>
const tableInfo = [
  {
    key: 0,
    name: "provinceName",
    label: "省份",
    width: "170px",
    disabled: true
  },
  {
    key: 1,
    name: "cityNames",
    label: "城市",
    width: "170px"
  },
  {
    key: 2,
    name: "districtNames",
    label: "区县",
    width: "200px"
  },
  {
    key: 3,
    name: "promotionExpenses",
    label: "推广费(元)",
    width: "140px"
  },
  {
    key: 4,
    name: "expectSale",
    label: "期望月销售额(元)",
    width: "140px"
  },
];
import { getAreas } from "@/api/businessCentric/promotionGoods";
import Pagination from "@/components/Pagination";
import checkPermission from "@/utils/permission";
export default {
  data() {
    return {
      isLoading: false,
      total: 0,
      list: [],
      listQuery: {
        current: 1,
        size: 10,
        model: {}
      },
      tableSelectTitle: [],
      showSelectTitle: false,
      tableTitle: [],
    };
  },
  methods: {
    async getlist() {
      this.isLoading = true;
      let {data} = await getAreas(this.row.id)
      this.isLoading = false
      this.list = data
      this.$emit('update:total', data)
    },
    confirmFun() {},
    cancelFun() {
      this.$emit("update:visible", false);
    },
    initTbaleTitle() {
      this.tableTitle = tableInfo;
       this.tableSelectTitle = [];
    },
    renderHeader(h, { column }) {
      var titles = tableInfo;
      var titlesName = ["显示字段项", "隐藏字段项"];
      return (
        <div style="position:relative">
          <div onClick={this.showHeaer}>
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="640px"
            center
            append-to-body={true}
          >
            <el-transfer
              vModel={this.tableSelectTitle}
              data={titles}
              titles={titlesName}
              onChange={this.setleftTitleFun}
            ></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button onClick={this.closeHeaer}>取消</el-button>
              <el-button type="primary" onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      );
    },
    setleftTitleFun(val) {
      this.tableSelectTitle = val;
    },
    showHeaer: function () {
      this.showSelectTitle = true;
    },
    closeHeaer: function () {
      this.showSelectTitle = false;
      this.tableSelectTitle = [];
    },
    setHeaer: function () {
      var titles = tableInfo;
      var listinfo = titles.filter((element, index, self) => {
        return !this.tableSelectTitle.includes(element.key);
      });
      this.tableTitle = listinfo;
      this.showSelectTitle = !this.showSelectTitle;
    },
    checkPermission,
  },
  created() {
    this.initTbaleTitle();
    this.getlist();
  },
  components: {
    Pagination,
  },
  props: {
    row: {
      type: Object,
    },
    visible: {
      type: Boolean,
      default: false,
      required: true,
    },
  },
};
</script>

<style lang="less" scoped>
.goods {
  margin: -30px -20px;
  border-top: 1px solid #ebecee;
  padding: 30px 20px;
  .table{
    padding-bottom: 400px;
  }
  .dialog-footer {
    border-top: 1px solid #efefef;
    margin: -30px -20px;
    margin-top: 30px;
    padding: 20px;
    padding-top: 10px;
    text-align: right;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
  }
}
</style>