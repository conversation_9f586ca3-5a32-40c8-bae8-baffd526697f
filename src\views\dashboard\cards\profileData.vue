<template>
  <div class="profileData">
    <div class="btn" style="height: 100px; width: 100px">
      <el-select
        @change="timeTypeChange"

        v-model="timeType"
        placeholder="请选择"
      >
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
    </div>
    <div class="left">
      <div>
        <div class="item" v-loading="loading.pay">
          <div class="title">支付金额（元）</div>
          <div class="number">{{ profileData.paymentAmount }}</div>
          <div class="compare">
            {{ timeType == 1 ? "较昨日" : timeType == 2 ? "较上周" : "较上月" }}
            <img
              v-if="profileData.paymentAmountUpOrDown == 1"
              src="@/assets/home/<USER>"
              alt=""
            />
            <img
              v-else-if="profileData.paymentAmountUpOrDown == -1"
              src="@/assets/home/<USER>"
              alt=""
            />
            <span>{{
              profileData.paymentAmountCompare == 0
                ? "-"
                : profileData.paymentAmountCompare
            }}</span>
          </div>
        </div>
        <div class="item" v-loading="loading.product">
          <div class="title">新增商品数</div>
          <div class="number">{{ profileData.newProductNumber }}</div>
          <div class="compare">
            {{ timeType == 1 ? "较昨日" : timeType == 2 ? "较上周" : "较上月" }}
            <img
              v-if="profileData.newProductNumberUpOrDown == 1"
              src="@/assets/home/<USER>"
              alt=""
            />
            <img
              v-else-if="profileData.newProductNumberUpOrDown == -1"
              src="@/assets/home/<USER>"
              alt=""
            />
            <span>{{
              profileData.newProductNumberCompare == 0
                ? "-"
                : profileData.newProductNumberCompare
            }}</span>
          </div>
        </div>
        <div class="item" v-loading="loading.purMerchant">
          <div class="title">新增客户数</div>
          <div class="number">{{ profileData.newPurMerchantNumber }}</div>
          <div class="compare">
            {{ timeType == 1 ? "较昨日" : timeType == 2 ? "较上周" : "较上月" }}
            <img
              v-if="profileData.newPurMerchantNumberUpOrDown == 1"
              src="@/assets/home/<USER>"
              alt=""
            />
            <img
              v-else-if="profileData.newPurMerchantNumberUpOrDown == -1"
              src="@/assets/home/<USER>"
              alt=""
            />
            <span>{{
              profileData.newPurMerchantNumberCompare == 0
                ? "-"
                : profileData.newPurMerchantNumberCompare
            }}</span>
          </div>
        </div>
      </div>
      <div>
        <div class="item" v-loading="loading.pay">
          <div class="title">支付订单数（笔）</div>
          <div class="number">{{ profileData.paymentNumber }}</div>
          <div class="compare">
            {{ timeType == 1 ? "较昨日" : timeType == 2 ? "较上周" : "较上月" }}
            <img
              v-if="profileData.paymentNumberUpOrDown == 1"
              src="@/assets/home/<USER>"
              alt=""
            />
            <img
              v-else-if="profileData.paymentNumberUpOrDown == -1"
              src="@/assets/home/<USER>"
              alt=""
            />
            <span>{{
              profileData.paymentNumberCompare == 0
                ? "-"
                : profileData.paymentNumberCompare
            }}</span>
          </div>
        </div>

        <div class="item" v-loading="loading.product">
          <div class="title">已上架商品数</div>
          <div class="number">{{ profileData.putAwayProductNumber }}</div>
          <div class="compare">
            {{ timeType == 1 ? "较昨日" : timeType == 2 ? "较上周" : "较上月" }}
            <img
              v-if="profileData.putAwayProductNumberUpOrDown == 1"
              src="@/assets/home/<USER>"
              alt=""
            />
            <img
              v-else-if="profileData.putAwayProductNumberUpOrDown == -1"
              src="@/assets/home/<USER>"
              alt=""
            />
            <span>{{
              profileData.putAwayProductNumberCompare == 0
                ? "-"
                : profileData.putAwayProductNumberCompare
            }}</span>
          </div>
        </div>

        <div class="item" v-loading="loading.purMerchant">
          <div class="title">累计客户数</div>
          <div class="number">{{ profileData.totalPurMerchantNumber }}</div>
          <div class="compare">
            {{ timeType == 1 ? "较昨日" : timeType == 2 ? "较上周" : "较上月" }}
            <img
              v-if="profileData.totalPurMerchantNumberUpOrDown == 1"
              src="@/assets/home/<USER>"
              alt=""
            />
            <img
              v-else-if="profileData.totalPurMerchantNumberUpOrDown == -1"
              src="@/assets/home/<USER>"
              alt=""
            />
            <span>{{
              profileData.totalPurMerchantNumberCompare == 0
                ? "-"
                : profileData.totalPurMerchantNumberCompare
            }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="right" v-loading="loading.lineChart">
      <div
        ref="macarons"
        class="macarons"
        :style="{ height: '100%', width: '100%' }"
      />
    </div>
  </div>
</template>

<script>
import echarts from "echarts";
require("echarts/theme/macarons"); // echarts theme
import resize from "../admin/components/mixins/resize";
import { dataProfilePay, dataProfileProduct, dataProfilePurMerchant, dataProfileLineChart } from "@/api/dashboard/index";
export default {
  name: 'profileData',
  mixins: [resize],
  props: {
    commerceModelEnum: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      timeType: 1,
      options: [
        {
          value: 1,
          label: "今日",
        },
        {
          value: 2,
          label: "本周",
        },
        {
          value: 3,
          label: "本月",
        },
      ],
      profileData: {},
      chartDatas: {},
      //
      chart: null,
      chartData: {
        expectedData: [100, 120, 161, 134, 105, 160, 165],
        actualData: [120, 82, 91, 154, 162, 140, 145],
      },
      newProductNumber: [],
      newPurMerchantNumber: [],
      paymentAmount: [],
      paymentNumber: [],
      time: [],
      loading: {
        pay: false,
        product: false,
        purMerchant: false,
        lineChart: false
      }
    };
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val);
      },
    },
    commerceModelEnum: {
      immediate: true,
      handler: function(val) {
        if (!val) return
        this.getDetail();
      },
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    timeTypeChange(val) {
      this.getDetail();
    },
    // 支付金额
    setDataProfilePay() {
      this.loading.pay = true
      dataProfilePay(this.timeType, this.commerceModelEnum).then((data)=>{
        Object.keys(data).forEach(key => {
          this.profileData[key] = data[key]
        })
      }).finally(()=>{
        this.loading.pay = false
      })
    },
    // 商品数
    setDataProfileProduct() {
      this.loading.product = true
      dataProfileProduct(this.timeType, this.commerceModelEnum).then((data)=>{
        Object.keys(data).forEach(key => {
          this.profileData[key] = data[key]
        })
      }).finally(()=>{
        this.loading.product = false
      })
    },
    // 采购商
    setDataProfilePurMerchant() {
      this.loading.purMerchant  = true
      dataProfilePurMerchant(this.timeType, this.commerceModelEnum).then((data)=>{
        Object.keys(data).forEach(key => {
          this.profileData[key] = data[key]
        })
      }).finally(()=>{
        this.loading.purMerchant = false
      })
    },
    // 折线图
    setDataProfileLineChart() {
      this.loading.lineChart  = true
      dataProfileLineChart(this.timeType, this.commerceModelEnum).then((data)=>{
        this.chartData = data.data;
        this.initChart();
      }).finally(()=>{
        this.loading.lineChart = false
      })
    },
    getDetail() {
      this.$nextTick(()=>{
        this.setDataProfilePay()
        this.setDataProfileProduct()
        this.setDataProfilePurMerchant()
        this.setDataProfileLineChart()
      })
    },
    initChart() {
      this.chart = echarts.init(this.$refs.macarons, "macarons");
      this.setOptions(this.chartData);
    },
    setOptions({
      newProductNumber,
      newPurMerchantNumber,
      paymentAmount,
      paymentNumber,
      time,
    } = {}) {
      let that = this;
      this.chart.setOption({
        tooltip: {
          trigger: "axis",
        },
        legend: {
          data: ["支付金额", "支付笔数", "新增商品", "新增客户"],
          left: "3%",
        },
        color: ["#FF6E1B", "#0056E5", "#2DAC0C", "#9360E2"],
        icon: "circle", //  这个字段控制形状  类型包括 circle，rect ，roundRect，triangle，diamond，pin，arrow，none
        grid: {
          left: "3%",
          right: "4%",
          bottom: "0%",
          top: "23%",
          containLabel: true,
        },
        toolbox: {
          feature: {
            saveAsImage: {
              show: false, //控制保存按钮显示隐藏
            },
          },
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: time,
        },
        yAxis: [
          {
            type: "value",
            name: "金额",
            min: 0,
            position: "left", // y轴在左侧
            // y轴的颜色和按y轴刻度画的曲线的颜色
            axisLine: {
              show: false,
              lineStyle: {
                color: "#7C8492",
              },
            },
            axisTick: { show: false }, // 设置y轴上的刻度
            splitLine: { show: false }, // 设置坐标中的虚线
            axisLabel: {
              formatter: "{value}",
            },
          },
          {
            type: "value",
            name: "订单数",
            min: 0,
            position: "right",
            axisTick: { show: false },
            // y轴的颜色和按y轴刻度画的曲线的颜色
            axisLine: {
              show: false,
              lineStyle: {
                color: "#7C8492",
              },
            },
            axisLabel: {
              color: "#7C8492",
              fontSize: 12,
              formatter: function (value, index) {
                var value;
                if (value % 1 == 0) {
                  return value;
                } else {
                  return "";
                }
              },
            },
          },
        ],
        series: [
          {
            name: "支付金额",
            type: "line",
            smooth: false,
            data: paymentAmount,
            yAxisIndex: 0,
            yAxis: "金额",
          },
          {
            name: "支付笔数",
            type: "line",
            yAxisIndex: 1,
            smooth: false,
            data: paymentNumber,
          },
          {
            name: "新增商品",
            type: "line",
            yAxisIndex: 1,
            smooth: false,
            data: newProductNumber,
          },
          {
            name: "新增客户",
            yAxisIndex: 1,
            type: "line",
            smooth: false,
            data: newPurMerchantNumber,
          },
        ],
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.profileData {
  display: flex;
  justify-content: flex-start;
  position: relative;
  .btn {
    position: absolute;
    top: -50px;
    right: 0;
  }
  .left {
    width: 600px;
    height: 254px;
    & > div {
      width: 600px;
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
      .item {
        padding-left: 30px;
        min-width: 200px;
        margin-bottom: 24px;
        padding-bottom: 14px;
        box-sizing: border-box;
        div {
          margin-top: 4px;
        }
        .title {
          font-size: 14px;
          color: #abadb4;
        }
        .number {
          font-size: 32px;
          color: #0f1831;
          font-weight: 600;
          line-height: 45px;
        }
        .compare {
          font-size: 14px;
          color: #7c8492;
          img {
            width: 10px;
            margin-right: 4px;
          }
        }
      }
    }
  }
  .right {
    flex: 1;
  }
}
</style>
