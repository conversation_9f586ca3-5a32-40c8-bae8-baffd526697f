<template>
  <div class="archivesPageContent">
    <im-search-pad
      :is-expand.sync="isExpand"
      :model="listQuery"
      @reset="resetForm"
      @search="onSearchSubmitFun"
    >
      <im-search-pad-item prop="id">
        <el-input v-model="listQuery.model.id" placeholder="请输入付款单单号" />
      </im-search-pad-item>
      <im-search-pad-item prop="type">
        <el-select
          v-model="listQuery.model.type"
          placeholder="请选择付款单类型"
          style="width: 150px"
        >
          <el-option label="全部" value=""></el-option>
          <el-option label="提现" value="CASH"></el-option>
          <el-option label="商家保证金退还" value="SELLER"></el-option>
          <el-option label="品种保证金退还" value="DEPOSIT"></el-option>
          <el-option label="销售订单" value="ORDER"></el-option>
          <el-option label="退货退款" value="REFUND"></el-option>
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item prop="businessNo">
        <el-input v-model="listQuery.model.businessNo" placeholder="请输入关联业务单号" >
         
        </el-input>
      </im-search-pad-item>
      <im-search-pad-item prop="payerType">
        <el-select
          v-model="listQuery.model.payerType"
          placeholder="请选择收款方类型"
          style="width: 160px"
        >
          <el-option label="全部" value=""></el-option>
          <el-option label="销售商" value="SELLER"></el-option>
          <el-option label="采购商" value="PURCHASERS"></el-option>
          <el-option label="业务员" value="SALE"></el-option>
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="timePicker">
        <el-date-picker
          style="width: 260px"
          v-model="timePicker"
          type="datetimerange"
          align="right"
          unlink-panels
          range-separator="至"
          start-placeholder="查询起始日期"
          end-placeholder="查询结束日期"
          :picker-options="pickerOptions"
          value-format="yyyy-MM-dd hh:mm:ss"
          @change="selectTime"
        >
        </el-date-picker>
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        v-model="tabType"
        :tabs="tabs"
        @change="chageTabsFun"
      >
        <template slot="button">
          <!-- <el-button :disabled="multipleSelection.length == 0" @click="outExcel">导 出</el-button> -->
          <el-button @click="resetForm">刷 新</el-button>
        </template>
      </tabs-layout>
      <div class="table">
        <el-table
          ref="table"
          v-if="list"
          @selection-change="selectTableItemFun"
          v-loading="listLoading"
          :data="list"
          row-key="id"
          border
          fit
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column
            align="center"
            width="65"
            :render-header="renderHeader"
            fixed
          >
            <template slot-scope="scope">
              <span>{{ scope.$index + 1 }} </span>
            </template>
          </el-table-column>
          <el-table-column
            type="selection"
            width="55"
            align="center"
          ></el-table-column>
          <el-table-column
            v-for="(item, index) in tableTitle"
            :key="index"
            :min-width="item.width ? item.width : '350px'"
            :label="item.label"
            show-overflow-tooltip
            align="left"
          >
            <template slot-scope="{ row }">
            <span
              v-if="
                item.name == 'payerType' ||
                item.name == 'method' ||
                item.name == 'type' ||
                item.name == 'flowOnline'
              "
            >{{ row[item.name].desc }}</span
            >
              <span v-else-if="item.name == 'paymentAmount'">
              <span v-if="row[item.name] >= 0" style="color: #70b603">{{
                  row[item.name]|getDecimals
                }}</span>
              <span v-else style="color: #f59a23">{{ row[item.name]|getDecimals }}</span>
            </span>
              <span v-else>{{ row[item.name] }}</span>
            </template>
          </el-table-column>

          <el-table-column
            fixed="right"
            align="center"
            label="操作"
            width="160"
            class="itemAction"
            v-if="tabType != 'PROCESS'"
          >
            <template slot-scope="{ row }">
              <el-row class="table-edit-row">
                <span v-if="tabType == 'WAIT' && checkPermission(['admin', 'admin-finance-payments:confirm'])" class="table-edit-row-item">
                  <el-button type="text"  @click="acceptedFun(row)">确认支付</el-button>
                </span>
                <span v-if="tabType == 'ERROR' && checkPermission(['admin', 'admin-finance-payments:rehandle'])" class="table-edit-row-item">
                  <el-button type="text"  @click="rePay(row)">重新支付</el-button>
                </span>
                <span class="table-edit-row-item" v-if="checkPermission(['admin', 'admin-finance-payments:detail'])">
                  <el-button type="text"  @click="detailFun(row.id)">查看详情</el-button>
                </span>
              </el-row>

            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-if="total > 0"
          :pageSizes="[10, 20, 50, 100]"
          :total="total"
          :page.sync="listQuery.current"
          :limit.sync="listQuery.size"
          @pagination="getlist"
        />
      </div>
    </div>
    <el-dialog
      append-to-body
      title="确认支付"
      :visible.sync="acceptedFlag"
      width="30%"
      @close="close"
      :close-on-click-modal="false"
    >
      <el-form ref="accepted" :model="acceptedForm" label-width="100px">
        <el-form-item
          class="formItem"
          prop="serialNumber"
          label="银行流水号:"
          :rules="[
            { required: true, message: '请输入银行流水号', trigger: 'blur' },
          ]"
        >
          <el-input
            style="min-width: 200px"
            type="number"
            v-model="acceptedForm.serialNumber"
            placeholder="请输银行流水号"
          >
          </el-input>
        </el-form-item>
        <!-- <el-form-item
          class="formItem"
          prop="remarks"
          label="备注信息:"
          :rules="[
            { required: true, message: '请输入备注信息', trigger: 'blur' },
          ]"
        >
          <el-input
            style="width: 90%; min-width: 200px"
            type="textarea"
            rows="3"
            v-model="acceptedForm.remarks"
            placeholder="请输入备注信息"
          >
          </el-input>
        </el-form-item> -->
        <el-form-item
          class="formItem"
          prop="certificatePath"
          label="付款凭证:"
          :rules="[
            { required: true, message: '请输上传付款凭证', trigger: 'blur' },
          ]"
        >
          <el-upload
            v-if="acceptedForm.id"
            ref="uploadlisence"
            :class="{ hide: !!acceptedForm.certificatePath }"
            :action="$uploadUrl"
            :data="insertProgram"
            :headers="headersProgram"
            list-type="picture-card"
            :on-remove="handleRemove"
            :on-success="uploadSuccess"
            :before-upload="beforeUpload"
          >
            <i class="el-icon-plus"></i>
          </el-upload>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="acceptedFormFun">确认支付</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from "@/components/Pagination";
import { areas } from "@/api/enterprise";
import { downloadFile } from "@/utils/commons";
import tableInfo from "@/views/finance/collectionAndPaymenList/paymentOrder/tableInfo";
import {
  list,
  confirmPay,
  rePay
} from "@/api/finance/collectionAndPaymenList/paymentOrder";
import qs from "qs";
import { getToken } from "@/utils/auth";
import { setContextData, getContextData } from "@/utils/auth";
import TabsLayout from '@/components/TabsLayout'
import checkPermission from "@/utils/permission";
export default {
  name: "paymenListlist",
  data() {
    return {
      isExpand: false,
      listLoading: false,
      list: [],
      tabType: "WAIT",
      listQuery: {
        current: 1,
        size: 10,
        model: {
          paymentStatus: "WAIT",
        },
      },
      tabs: [
        {
          name: '待付款',
          value: 'WAIT',
          hide: !checkPermission(['admin', 'admin-finance-payments:unhandledView'])
        },
        // {
        //   name: '付款中',
        //   value: 'PROCESS'
        // },
        {
          name: '付款完成',
          value: 'FINISH',
          hide: !checkPermission(['admin', 'admin-finance-payments:finishedView'])
        },
        {
          name: '付款异常',
          value: 'ERROR',
          hide: !checkPermission(['admin', 'admin-finance-payments:errorView'])
        }
      ],
      timePicker: [],
      headersProgram: {
        token: getToken(),
        Authorization: "Basic YWRtaW5fdWk6YWRtaW5fdWlfc2VjcmV0",
      },
      insertProgram: {
        folderId: 0,
      },
      acceptedFlag: false,
      acceptedForm: {},
      row: {},
      total: 0,
      cityValue: [],
      tableTitle: [],
      tableSelectTitle: [0, 1, 2, 3],
      multipleSelection: [],
      multipleSelectionId: [],
      showSelectTitle: false,
      props: {
        lazy: true,
        async lazyLoad(node, resolve) {
          const { level } = node;
          let id = node.data ? node.data.id : "";
          let res = await areas({ parentId: id });
          let list = res.data;
          list.forEach((item) => {
            item.value = item.id;
            item.leaf = level >= 2;
          });
          resolve(list);
        },
      },
      pickerOptions: {
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
    };
  },
  methods: {
    checkPermission,
    async rePay(row) {
      let {data} = await rePay(row.id)
      if(data) {
        this.list = []
        this.listQuery = {
        current: 1,
        size: 10,
        model: {
          paymentStatus: 'WAIT',
        },
      };
      this.initTbaleTitle()
      this.getlist()
      }
    },
    reloadFun() {
      this.list = [];
      this.total = 0;
      this.tabType = "WAIT";
      this.listQuery = {
        current: 1,
        size: 10,
        model: {
          paymentStatus: "WAIT",
        },
      };
      this.getlist();
    },
    selectTime(e) {
      this.listQuery.model.startTime = e[0];
      this.listQuery.model.endTime = e[1];
    },
    handleRemove(file, fileList) {
      this.acceptedForm.fileList = fileList;
      this.$set(this.acceptedForm, "certificatePath", "");
    },
    uploadSuccess(res, file, fileList) {
      this.$set(this.acceptedForm, "certificatePath", file.response.data.url);
    },
    beforeUpload(file) {
      let fileTypeList = [
        "image/png",
        "image/pjpeg",
        "image/jpeg",
        "image/bmp",
      ];
      const isJPG = fileTypeList.indexOf(file.type) > -1;
      const isLt2M = file.size / 1024 / 1024 < 5;

      if (!isJPG) {
        this.$message.error("上传图片格式错误!");
      }
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 2MB!");
      }
      return isJPG && isLt2M;
    },
    chageTabsFun() {
      this.listQuery = {
        current: 1,
        size: 10,
        model: {
          paymentStatus: this.tabType,
        },
      };
      this.initTbaleTitle();
      this.list = [];
      this.getlist();
    },
    close() {
      this.acceptedFlag = false;
      this.$refs.accepted.resetFields();
      this.acceptedForm.fileList = [];
      this.$set(this.acceptedForm, "fileList", []);
      this.acceptedForm = {};
    },
    detailFun(id) {
      setContextData("paymentOrder_detail", this.listQuery);
      this.$router.push({
        path: "/finance/collectionAndPaymen/paymentOrder/detail",
        query: {
          id,
        },
      });
    },
    acceptedFun(row) {
      this.acceptedFlag = true;
      this.acceptedForm.id = row.id;
    },
    acceptedFormFun() {
      console.log(this.acceptedForm);
      this.$refs.accepted.validate(async (valid) => {
        if (valid) {
          let { data } = await confirmPay(qs.stringify(this.acceptedForm));
          console.log(data);
          if (data) {
            this.$message.success("已确认收款该收款单");
            this.tabType = "FINISH";
            this.resetForm();
            this.close();
          }
        } else {
          console.log("sssss");
        }
      });
    },
    cityChange(e) {
      this.listQuery.model.provinceId = e[0];
      this.listQuery.model.cityId = e[1];
      this.listQuery.model.countyId = e[2];
    },
    resetForm(type) {
      this.$refs['tabs-layout'].reset()
      this.listQuery = {
        current: 1,
        size: 10,
        model: {
          paymentStatus: 'WAIT',
        },
      };
      this.timePicker = [];
      this.list = [];
      this.getlist();
    },
    selectTableItemFun: function (val) {
      let arr = [];
      val.forEach((item) => {
        arr.push(item.id);
      });
      this.multipleSelection = val;
      this.multipleSelectionId = arr;
    },
    onSearchSubmitFun() {
      this.getlist();
    },
    async getlist() {
      this.listLoading = true;
      let { data } = await list(this.listQuery);
      this.listLoading = false;
      this.total = data.total;
      this.list = data.records;
    },
    initTbaleTitle() {
      this.tableSelectTitle = [];
      this.tableTitle = tableInfo[this.tabType];
    },
    renderHeader(h, { column }) {
      var titles = tableInfo[this.tabType];
      var titlesName = ["显示字段项", "隐藏字段项"];
      return (
        <div style="position:relative">
          <div onClick={this.showHeaer}>
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="640px"
            center
            append-to-body={true}
          >
            <el-transfer
              vModel={this.tableSelectTitle}
              data={titles}
              titles={titlesName}
              onChange={this.setleftTitleFun}
            ></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button onClick={this.closeHeaer}>取消</el-button>
              <el-button type="primary" onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      );
    },
    setleftTitleFun(val) {
      this.tableSelectTitle = val;
    },
    showHeaer: function () {
      this.showSelectTitle = true;
    },
    closeHeaer: function () {
      this.showSelectTitle = false;
      this.tableSelectTitle = [];
    },
    setHeaer: function () {
      var titles = tableInfo[this.tabType];
      var listinfo = titles.filter((element, index, self) => {
        return !this.tableSelectTitle.includes(element.key);
      });
      this.tableTitle = listinfo;
      this.showSelectTitle = !this.showSelectTitle;
    },
    async outExcel() {
      if (this.multipleSelection.length > 0) {
        const tHeader = ["id"];
        const filterVal = ["id"];
        this.tableTitle.forEach(function (item) {
          tHeader.push(item.label);
          filterVal.push(item.name);
        });
        let exportData = this.formatJson(this.multipleSelection, filterVal);
        downloadFile({
          tHeader: tHeader,
          fileName: "待付款订单列表",
          exportData: exportData,
        });
      } else {
        this.$message.error("请在商家列表中勾选需要商家");
      }
    },
    formatJson(dataList, filterVal) {
      console.log(dataList);
      return dataList.map((v) =>
        filterVal.map((j) => {
          if (j === "publishStatus") {
            return v[j].code == "Y" ? "已启用" : "已冻结";
          } else {
            return v[j];
          }
        })
      );
    },
  },
  created() {
    this.initTbaleTitle();
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (from.path == "/finance/collectionAndPaymen/paymentOrder/detail") {
        if (getContextData("paymentOrder_detail") != "") {
          vm.listQuery = getContextData("paymentOrder_detail");
          vm.tabType = vm.listQuery.model.paymentStatus;
        }
      }
      vm.initTbaleTitle();
      vm.resetForm();
    });
  },
  components: {
    Pagination,
    TabsLayout
  },
};
</script>


<style lang="less" scoped>
.archivesPageContent {
  padding: 0;
  .temp_searchBox {
    height: 64px;
    overflow: hidden;
    margin-bottom: 0;
  }
  .form-inline {
    height: 60px;
    overflow: hidden;
  }
  .title {
    border-bottom: 2px solid #ebecee;
    margin-bottom: 16px;
    span {
      margin-bottom: -2px;
      padding: 0 15px;
      height: 40px;
      line-height: 30px;
      display: block;
      background: rgba(255, 255, 255, 0);
      border-bottom: 2px solid rgb(64, 158, 255);
      font-size: 16px;
      font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
        "PingFang SC"-400;
      font-weight: 400;
      color: rgb(64, 158, 255);
    }
  }
  .formItem {
    width: 586px;
  }
  .line {
    color: #dfe6ec;
    margin: 0 6px;
  }
  .typeTabs {
    height: 40px;
    margin-bottom: -2px;
  }
}
/deep/ .hide .el-upload--picture-card {
  display: none !important;
}
</style>
