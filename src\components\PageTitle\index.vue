<template>
  <div class="page-title-top flex-between-center">
    <div>
      <span>{{ title }}</span>
      <slot name="icon"></slot>
    </div>
    <div class="btn-group">
      <el-button @click="back" v-if="showBack">返回</el-button>
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PageTitle',
  props: {
    title: {
      type: String,
      default: ''
    },
    showBack: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
    }
  },
  mounted() {
  },
  methods: {
    back() {
      this.$store.dispatch("tagsView/delView", this.$route);
      this.$router.go(-1);
    }
  }
}
</script>

<style lang="scss" scoped>
  .page-title-top {
    height: 56px;
    line-height: 56px;
    font-family: "PingFangSC-Regular", "PingFang SC", sans-serif;
    font-size: 18px;
    text-align: left;
    border-bottom: 1px solid #eeeeee;
    margin-bottom: 20px;
    .btn-group {
      .el-button, >span {
        margin-left: 10px;
      }
    }
  }
  .flex-between-center {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
</style>
