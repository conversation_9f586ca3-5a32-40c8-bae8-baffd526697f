<template>
  <div class="container">
    <!-- 部门成员 -->
    <div>
      <div class="tab_bg">
        <tabs-layout :tabs="[{ name: '部门成员' }]">
          <template slot="button">
            <div class="flex_between_center">
              <el-input
                size="small"
                v-model.trim="peopleListQuery.mbOrDepName"
                placeholder="请输入姓名或部门"
                class="memberSearchInput"
              >
                <el-button @click="peopleListFun()" slot="append" icon="el-icon-search"></el-button>
              </el-input>
              <el-button v-if="checkPermission(['admin', 'admin-team-orgaStructure:movOfficeClerk'])" :disabled="multipleSelection.length==0" size="small" @click="showChangeFun()">批量移动</el-button>
              <del-el-button 
                v-if="checkPermission(['admin', 'admin-team-orgaStructure:delOfficeClerk'])" 
                  style="margin: 0 10px;"
                  :sizeTyep="'small'"
                  :showType="'primary'" 
                  :btnTxt="'批量移除'" 
                  :text="delText" 
                  @handleDel="deleteDepartmentSalesmansFun">
              </del-el-button>
              <!-- <el-button size="small" @click="">批量移除</el-button> -->
              <el-button
                v-if="checkPermission(['admin', 'admin-team-orgaStructure:addOfficeClerk'])"
                size="small"
                type="primary"
                @click="showEditMemberFun()"
                >+新增成员</el-button
              >
            </div>
          </template>
        </tabs-layout>
          <!-- :remote-method="loadMember" -->
        <table-pager
          ref="table"
          :height="450"
           :paging="false"
          :options="memberTableColumnList"
          :data.sync="peopleList"
          
          :selection="true" @selection-change="onSelect" @selection-all="onAllSelect" 
        >
          <div slot-scope="props" v-if="checkPermission(['admin', 'admin-team-orgaStructure:singleDelOfficeClerk'])">
            <el-row class="table-edit-row">
              <span class="table-edit-row-item">
                <del-el-button style="margin-left:5px" 
                  :targetId="props.row.id" 
                  :btnTxt="delbtnTxt" 
                  :text="delText" 
                  @handleDel="deleteDepartmentSalesmanFun">
                </del-el-button>
              </span>
            </el-row>
          </div>
        </table-pager>
      </div>
    </div>
    <!-- 部门成员 end -->

    <!-- 新增部门成员  -->
    <el-dialog
      v-if="showEditMember"
      title="新增部门成员"
      :visible.sync="showEditMember"
      width="30%"
      class="editMemberDialog"
      :show-close="true"
      :close-on-click-modal="false"
    >
      <div class="editMember">
        <el-form
          ref="editMemberForm"
          :model="editMemberForm"
          label-width="90px"
        >
          <el-form-item
            label="成员名字:"
            prop="director"
            :rules="[
              {
                required: true,
                message: '请选择',
                trigger: 'blur',
              },
            ]"
          >
            <el-select
              filterable
              v-model="editMemberForm.salesmanId"
              placeholder="请选择"
            >
              <!-- <el-option
                v-for="item in salesmanList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
                <span style="float: left">{{ item.label }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{
                  item.department
                }}</span>
              </el-option> -->
              <el-option
                  :key="0"
                  :label="`请选择`"
                  :value="0">
                  <span style="float: left">请选择</span>
                </el-option>
                <el-option
                v-for="item in salesmanList"
                :key="item.id"
                :label="`${item.salesmanName}(${item.mobile})`"
                :value="item.id"
              >
                <span style="float: left">{{ item.salesmanName }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{item.mobile}}</span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button
          @click="
            () => {
              this.showEditMember = false;
        }">取 消</el-button>
        <el-button type="primary" @click="submitEditFun" v-throttle>确 定</el-button>
      </span>
    </el-dialog>
    <!-- 新增部门成员 end -->


    <!-- 移动部门树 -->
    <organizationTree v-if="visible" :visible.sync="visible" @confirm="submitOrganizationChangeFun" ></organizationTree>
    <!-- 移动部门树 -->



  </div>
</template>
<script>
// import { query } from '@/api/';

// 成员
const memberTableColumns = [
  {
    label: "所在部门",
    name: "name",
    prop: "name",
    width: "150px",
  },
  {
    label: "姓名",
    name: "salesmanName",
    prop: "salesmanName",
    width: "150px",
    selectable: true,
  },
  {
    label: "角色 ",
    name: "roleName",
    prop: "roleName",
    width: "150px",
  },
  // { label: "销售区域", name: "productNo", prop: "productNo", width: "250px" },
  // {
  //   label: "销售品种",
  //   name: "salesQuantity",
  //   prop: "salesQuantity",
  //   width: "100px",
  // },
  {
    label: "联系手机",
    name: "mobile",
    prop: "mobile",
    width: "100px",
  },
];
const memberTableColumnList = [];
for (let i = 0; i < memberTableColumns.length; i++) {
  memberTableColumnList.push({ key: i, ...memberTableColumns[i] });
}

import { mapState,mapGetters,mapActions} from 'vuex'
import { peopleList,getSalesManList,addOrgaSalesman ,deleteDepartmentSalesman ,deleteDepartmentSalesmans,batchMove} from '@/api/organization/index';
import delElButton from "@/components/eyaolink/delElButton";
import organizationTree from '@/views/team/index/temp/organization/organization-tree';
import checkPermission from '@/utils/permission';

export default {
  data() {
    return {
      //移动部门
      visible:false,



      delText:"确认移除选中信息吗？",
      delbtnTxt:"移除",
      // 部门成员内容
      memberTableColumnList: memberTableColumnList,
      selectMemberTableItemIds: null,
      peopleList: [],
      peopleListQuery:{
        "id": 0,
        "mbOrDepName": ""
      },
      // 新增 部门部门成员
      showEditMember: false,
      editMemberForm: {
        salesmanId: 0,
      },
      // 业务员内容
      multipleSelection:[],
      salesmanList: [],
    };
  },
  props: {
     /**
     * @param {string} departmentId  父节点ID
     */
    departmentId: {
        type: String,
        default: "",
        required: true,
    }
  },
  components: {
    // 组件
    organizationTree,
    delElButton
  },
  computed: {
    // 计算属性computed :
    // 1. 支持缓存，只有依赖数据发生改变，才会重新进行计算
    // 2. 不支持异步，当computed内有异步操作时无效，无法监听数据的变化
    // 3. computed 属性值会默认走缓存，计算属性是基于它们的响应式依赖进行缓存的，也就是基于data中声明过或者父组件传递的props中的数据通过计算得到的值
    // 4. 如果一个属性是由其他属性计算而来的，这个属性依赖其他属性，是一个多对一或者一对一，一般用computed
    // 5.如果computed属性属性值是函数，那么默认会走get方法；函数的返回值就是属性的属性值；在computed中的，属性都有一个get和一个set方法，当数据变化时，调用set方法。
    ...mapGetters([
      'organizationNavNode',
      "organizationInfo",
      "paremDepartmentInfo",
      "departmentInfo",
    ]),
  },
  filters: {},
  watch: {
    // 监听属性watch：
    // 1. 不支持缓存，数据变，直接会触发相应的操作；
    // 2. watch支持异步；
    // 3. 监听的函数接收两个参数，第一个参数是最新的值；第二个参数是输入之前的值；
    // 4. 当一个属性发生变化时，需要执行对应的操作；一对多；
    // 5. 监听数据必须是data中声明过或者父组件传递过来的props中的数据，当数据变化时，触发其他操作，函数有两个参数，
    // 　　immediate：组件加载立即触发回调函数执行，
    // 　　deep: 深度监听，为了发现对象内部值的变化，复杂类型的数据时使用，例如数组中的对象内容的改变，注意监听数组的变动不需要这么做。注意：deep无法监听到数组的变动和对象的新增，参考vue数组变异,只有以响应式的方式触发才会被监听到。
    // 　　deepdemo：
    //      obj:{
    //          handler(){
    //              console.log('obj 变了')
    //          },
    //          deep:true
    //      }
    
    departmentId:{
        handler(val){
          let peopleListQuery = this.peopleListQuery;
          let editMemberForm = this.editMemberForm;
          this.peopleListQuery = Object.assign(peopleListQuery,{id:val})
          this.editMemberForm = Object.assign(editMemberForm,{departmentId:val,salesmanId:0})
          this.peopleListFun()
        },
        deep:true
    }
  },
  methods: {
    checkPermission,
    //方法集合
    // 部门成员
   // table 选中
    onAllSelect(selection) {
      this.onSelect(selection);
      console.info(this.multipleSelection)
    },
    onSelect(val) {
      this.multipleSelection = val;
    },
    selectMemberTableItemFun(row) {
      this.selectMemberTableItemIds = row;
    },
    removeMemberFun(row) {},
    memberLoad() {},
    // 部门成员
    
    // 新增部门成员
    showEditMemberFun() {
      this.showEditMember = !this.showEditMember;
    },
    // 查询业务员列表
    getSalesManListFun(){
      getSalesManList(null).then().then(res => {
        let {code,data,msg} = res;
          if(code == 0 ){
            this.salesmanList=data;
          } 
      })
    },
    // 显示批量移动
    showChangeFun(){
      this.visible=true
    },
    // 更新所有信息
    submitOrganizationChangeFun(data){
      console.info("submitOrganizationChangeFun")
      const ids = this.multipleSelection.map(i=>i.id);
      batchMove({
          depId:data,
          'ids[]':ids.toString()
      }).then(res=>{
        let {code,data,msg} =res;
        if(code==0){
         
          this.updateComponentsFun()
        }
      })
    },


    // 查询业务员列表
    peopleListFun(){
      peopleList(this.peopleListQuery).then(res => {
        let {code,data,msg} = res;
          if(code == 0 ){
            this.peopleList=data;
          } 
      })
    },
    submitEditFun(){
      let editMemberForm = this.editMemberForm;
    
      // 新增提交
      addOrgaSalesman(editMemberForm).then(res=>{
        let {code, data,msg} = res;
        if(code==0){
            this.peopleListFun()
            this.editMemberForm = Object.assign(editMemberForm,{salesmanId:0});
            this.showEditMember=false;
            this.updateComponentsFun()
        }
      })
    },
    // 批量移除部门成员
    deleteDepartmentSalesmansFun(){
      if(this.multipleSelection.length==0){
        this.$message.error("请选择移除成员")
        return;
      }
      const ids = this.multipleSelection.map(i=>i.id);
      deleteDepartmentSalesmans(ids.toString()).then(res=>{
        let {code, data,msg} = res;
        if(code==0){
            this.peopleListFun()
            this.updateComponentsFun()
        }
      })
    },
    // 移除部门成员
    deleteDepartmentSalesmanFun(id){
      console.info(id)
      // const ids = this.multipleSelection.map(i=>i.id);
      deleteDepartmentSalesman(id).then(res=>{
        let {code, data,msg} = res;
        if(code==0){
            this.peopleListFun()
            this.updateComponentsFun()
        }
      })
    },
    // 回调更新信息
    updateComponentsFun() {
      this.$parent.$parent.load('Department')
      this.$parent.getDepartmentInfo()
    },
  },
  mounted() {
    // 方法调用
    this.getSalesManListFun()
  },
  beforeDestroy() {},
};
</script>

<style lang="scss" scoped>

.department {
  font-size: 16px;
  padding: 16px 0 0 16px;
}
.departmentBreadcrumb {
  padding: 16px 0 0 16px;

  ::v-deep .breadcrumbItem {
    & > span {
      color: #0056e5;
    }
    & > i {
      color: #0056e5;
    }
    &:last-child > span {
      color: #303133;
    }
  }
}

::v-deep .es-table-pager .page-row {
  justify-content: flex-end;
}
.memberSearchInput {
  margin-right: 10px;
}


::v-deep .editMemberDialog .el-dialog{
  min-width: 400px;
}

::v-deep .editMemberDialog .el-dialog__body{
  padding: 30px 20px !important;
}

.editMember{
  ::v-deep .el-select{width: 100%;}
  ::v-deep .sellAreaClass .el-radio{ padding-top: 10px;}
  ::v-deep .sellTypeClass .el-radio{ padding-top: 10px;}

}
</style>
