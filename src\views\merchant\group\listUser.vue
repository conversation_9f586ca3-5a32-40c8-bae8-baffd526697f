<template>
  <div class="app-container">
    <el-dialog
      :title="title"
      showClose
      :visible.sync="listvisible"
      width="45%"
      @close="handleClose"
    >
      <search-pad @search="search" @reset="reset" style="margin-bottom: 20px;">
        <el-form-item>
          <el-input placeholder="请输入客户编码" v-model="listQuery.model.purMerchantCode" class="input-with-select" />
        </el-form-item>
        <el-form-item>
          <el-input placeholder="请输入名称" v-model="listQuery.model.purMerchantName" class="input-with-select" />
        </el-form-item>
          <<!--el-input placeholder="请输入内容" v-model="input" class="input-with-select">
            <el-select v-model="select" slot="prepend" placeholder="请选择" @change="getSelect" style="width: 120px;">
              <el-option label="客户编码" value="1"></el-option>
              <el-option label="名称" value="2"></el-option>
            </el-select>
          </el-input>-->
      </search-pad>
      <div class="fr" style="margin-top: -55px;">
        <el-button type="primary" @click="addUser">+添加客户</el-button>
      </div>
      <!--<el-table
        ref="dragTable"
        v-loading="listLoading"
        :data="list"
        row-key="id"
        border
        fit
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column
          v-for="(item, index) in tableTitle"
          :key="index"
          min-width="100px"
          :label="item.label"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            <span>{{ row[item.name] }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="{ row }">
            <el-button v-on:click="del(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>-->
      <table-pager ref="todoTable" :options="tableTitle" :data.sync="tableData" :remote-method="load">
      <div slot-scope="props">
        <!--<el-link @click="$router.push({ name: 'clientDetail', params: { id: props.row.id } })">查看</el-link>-->
        <el-link @click="del(props.row)">删除</el-link>
      </div>

    </table-pager>

      <div slot="footer" class="dialog-footer">
        <el-button @click="listvisible = false">取 消</el-button>
        <el-button type="primary" @click="listvisible = false">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { queryPageSaleMerchantGroupCustomerListDTO,delGroup } from "@/api/group";
import Sortable from "sortablejs";
const TableColumns = [
  { label: "客户编码", prop: "code" },
  { label: "客户名称", prop: "name" },
  { label: "企业类型", prop: "merchantType" },
  { label: "联系人", prop: "ceoName" },
  { label: "联系电话", prop: "ceoMobile" },
  { label: "所在区域", prop: "region" },
  { label: "创建时间", prop: "createTime" },
];
const TableColumnList = [];
for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({ key: i, ...TableColumns[i] });
}

export default {
  name: "DragTable",
  props: ["visible",'row','saleMerchantId'],
  data() {
    return {
      tableTitle: TableColumnList,
      list: null,
      total: null,
      listLoading: false,
      tableData: [],
      listQuery: {
        model:{
          "purMerchantCode": '',
          "merchantGroupId": '',
          "purMerchantName": '',
          "saleMerchantId": ''
        }
      },
      form: {},
      sortable: null,
      oldList: [],
      newList: [],
      select: '1',
      input: '',
      title: '',
      listvisible: false
    };
  },
  created() {
    //this.getList();
  },
  methods: {
    handleClose(){
      // 子组件调用父组件方法，并传递参数
      this.$emit('changeShow','false')
    },
    addUser() {
      this.$emit('addUser')
    },
    async del(row){
      await delGroup(row.purMerchantId,this.listQuery.model.merchantGroupId)
      this.$message.success('删除成功！')
      this.reset()
    },
    search() {
      /*if (this.select === '1') {
        this.listQuery.model.purMerchantCode = this.input
        this.listQuery.model.purMerchantName = ''
      } else {
        this.listQuery.model.purMerchantName = this.input
        this.listQuery.model.purMerchantCode = ''

      }*/
      this.handleRefresh({
        page: 1,
        pageSize: 10
      })
      this.load()
    },
    reset() {
      this.handleRefresh({
        page: 1,
        pageSize: 10
      })
      this.listQuery.model.purMerchantCode = ''
      this.listQuery.model.purMerchantName = ''
      this.load()
    },
    handleRefresh(pageParams) {
      this.$refs.todoTable.doRefresh(pageParams)
    },
    async load(params) {
      this.listQuery.model.merchantGroupId = this.row.id
      /*this.listQuery.model.saleMerchantId = this.saleMerchantId*/
      this.listLoading = true;
      Object.assign(this.listQuery, params)
      const { data } = await queryPageSaleMerchantGroupCustomerListDTO(this.listQuery);
      this.title = this.row.name + '（' + data.total + '）'
      return { data }
    },
    setSort() {
      const el = this.$refs.dragTable.$el.querySelectorAll(
        ".el-table__body-wrapper > table > tbody"
      )[0];
      this.sortable = Sortable.create(el, {
        ghostClass: "sortable-ghost", // Class name for the drop placeholder,
        setData: function (dataTransfer) {
          // to avoid Firefox bug
          // Detail see : https://github.com/RubaXa/Sortable/issues/1012
          dataTransfer.setData("Text", "");
        },
        onEnd: (evt) => {
          const targetRow = this.list.splice(evt.oldIndex, 1)[0];
          this.list.splice(evt.newIndex, 0, targetRow);

          // for show the changes, you can delete in you code
          const tempIndex = this.newList.splice(evt.oldIndex, 1)[0];
          this.newList.splice(evt.newIndex, 0, tempIndex);
        },
      });
    },
  },
  watch: {
    visible() {
      this.listvisible = this.visible
    }
  }
};
</script>
