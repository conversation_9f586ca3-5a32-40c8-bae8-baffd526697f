<template>
  <div>
    <el-dialog title="新增成员" :visible.sync="dialogVisible" v-if="dialogVisible" :close-on-click-modal="false" width="70%" :before-close="handleClose">
      <div>
        <!-- 搜索form -->
        <im-search-pad :has-expand="false" :is-expand.sync="isExpand" :model="model" @reset="reload"
          @search="searchLoad">
          <im-search-pad-item prop="name">
            <el-input v-model="model.name" @keyup.enter.native="searchLoad" placeholder="请输入姓名" />
          </im-search-pad-item>
          <im-search-pad-item prop="mobile">
            <el-input v-model="model.mobile" @keyup.enter.native="searchLoad" placeholder="请输入手机号码" />
          </im-search-pad-item>
        </im-search-pad>
        <!-- 分页table -->
        <!-- 分页tab -->
        <table-pager ref="pager-table" :options="tableColumns" :remote-method="initData" :data.sync="tableData"
          :selection="true" @selection-change="onSelect" @selection-all="onAllSelect" :pageSize="pageSize"
          :minHeight="450" :height="500" :isNeedButton="false" :reserveSelection="reserveSelection" :rowKey="rowKey">

        </table-pager>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submitAdd">确
          定{{ multipleSelection.length > 0 ? `(${multipleSelection.length})` : '' }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>


<script>
import {
    mapGetters
  } from 'vuex'
  import {
    pageSalesMan,
    addSalesMan
  } from '@/api/organization/role';
  const TableColumns = [{
      label: "业务员编码",
      name: "code",
      prop: "code",
      width: "150"
    },
    {
      label: "真实姓名",
      name: "salesmanName",
      prop: "salesmanName",
      width: "120"
    },
    {
      label: "手机号码",
      name: "mobile",
      prop: 'mobile',
      width: "150",
    },
    {
      label: "所属角色",
      name: "roleName",
      prop: 'roleName',
      width: '150',
      selectable:true,
    },
    {
      label: "所在部门",
      name: "departmentName",
      prop: 'departmentName',
      width: "150"
    }
  ];
  const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({
      key: i,
      ...TableColumns[i]
    });
  }
  export default {
    //import引入的组件
    components: {},

    data() {
      return {
        rowKey: "id",
        dialogVisible: false,
        isExpand: false,
        multipleSelection: [],
        pageSize: 10,
        tableData: [],
        tableColumns: TableColumnList,
        reserveSelection: true,
        model: {
          name: "",
          mobile: ""
        },
        currentRow: {}
      }
    },
    //生命周期 - 挂载完成（可以访问DOM元素）
    mounted() {},

    computed: {
      ...mapGetters([
        'organizationInfo'
      ]),
    },

    created() {
      // this.initData();
    },

    filters: {},

    //方法集合
    methods: {
      handleClose() {
        this.$refs['pager-table'].clearSelection();
        this.multipleSelection = [];
        this.dialogVisible = false;
      },
      handleOpen(row) {
        this.currentRow = row;
        this.dialogVisible = true;
        // this.initData();
      },
      async initData(params) {
        let listQuery = {
          model: {
            ...this.model
          }
        };
        Object.assign(listQuery, params);
        const { data } = await pageSalesMan(listQuery);
        data.records.forEach((item,index)=>{
          if (item.roleName != null) {
            item.selectable = true
          } else {
            item.selectable = false
          }
        })
        return { data }
      },
      reload() {
        this.handleRefresh({
          page: 1,
          pageSize: this.pageSize
        })
      },
      handleRefresh(pageParams) {
        this.$refs['pager-table'].doRefresh(pageParams)
      },
      searchLoad() {
        this.handleRefresh({
          page: 1,
          pageSize: this.pageSize
        })
      },
      // table 选中
      onAllSelect(selection) {
        this.onSelect(selection);
      },
      onSelect(val) {
        this.multipleSelection = val;
      // this.multipleSelection = val.map(function(item,index) {
      //     return item.roleName;
      //   })
      },
      // 新增角色的成员
      submitAdd() {
        if (this.multipleSelection.length < 1) {
          this.$message.warning('请先选择需要绑定的业务员');
          return
        }
        let salesManIds = [];
        this.multipleSelection.forEach(element => {
          salesManIds.push(element.id);
        })
        let params = {
          orgRoleId: this.currentRow.id,
          salesManIds,
          organizationId:this.organizationInfo.id
        };
        addSalesMan(params).then(res => {
          if (res.code == 0 && res.msg == 'ok') {
            this.$message.success('绑定业务员成功');
            this.handleClose();
            this.$emit('handleReload');
          }
        })
      }
    },

  }

</script>


<style lang='scss' scoped>

</style>
