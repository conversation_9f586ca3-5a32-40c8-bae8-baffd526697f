<template>
<div>
  <el-dialog :title="title" :visible.sync="addVisible" width="25%" @close="resetForm('groupForm')">
    <el-form ref="groupForm" :model="groupForm" :rules="rules"  label-width="80px">
      <el-form-item label="分组编码">
        <el-input v-model="groupForm.code" placeholder="请输入分组编码"></el-input>
      </el-form-item>
      <el-form-item label="分组名称" prop="name">
        <el-input v-model="groupForm.name" placeholder="请输入分组名称"></el-input>
      </el-form-item>
      <el-form-item label="价格系数" prop="priceModulus">
        <el-input-number v-model="groupForm.priceModulus" controls-position="right" :min="0" :max="100"></el-input-number>
        <span>%</span>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="resetForm('groupForm')">取 消</el-button>
      <el-button type="primary" @click="onSubmit('groupForm')">确定</el-button>
    </div>
  </el-dialog>
</div>
</template>

<script>
  import { addGroup } from '@/api/group'
export default {
  name: "addGroup",
  props:['groupVisible','title','saleMerchantId','detail'],
  data() {
    return {
      addVisible: false,
      groupForm: {
        code: '',
        name: '',
        priceModulus: ''
      },
      rules: {
        name: [
          {required: true, message: '请输入分组名称', trigger: 'blur'},
          {min: 1,max: 20,message: '分组名称不超过20个字符',trigger: 'blur'}
        ],
        priceModulus: [{
          required: true, message: '请输入价格系数', trigger: 'blur'
        }]
      },
    }
  },
  methods: {
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.addVisible = false
      this.$emit('changeShow','false')
    },
    onSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let query = {
            code: this.groupForm.code,
            name: this.groupForm.name,
            priceModulus: this.groupForm.priceModulus,
            saleMerchantId: this.saleMerchantId
          }
          addGroup(query).then(res=>{
            this.$message.success('新增分组成功!')
            this.addVisible = false
            this.$emit('load',false)
          })
        } else {
          return false;
        }
      });
    }
  },
  watch: {
    detail() {
      this.groupForm = this.detail
    },
    groupVisible() {
      this.addVisible = this.groupVisible
    }
  }
}
</script>

<style scoped>

</style>
