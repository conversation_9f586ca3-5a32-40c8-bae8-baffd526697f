<template>
  <div class="archivesPageContent">
    <im-search-pad
      :has-expand="false"
      :model="model"
      :is-expand.sync="isExpand"
      @reset="resetForm('searchForm')"
      @search="onSearchSubmitFun"
    >
      <im-search-pad-item prop="name">
        <el-input v-model="model.name" placeholder="请输入企业名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="cityValue">
        <el-cascader
          placeholder="请选择所在区域"
          v-model="cityValue"
          :props="props"
          @change="cityChange"
          clearable
        />
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout ref="tabs-layout" :tabs="[{ name: '企业档案' }]">
        <template slot="button">
          <el-button @click="resetForm">刷新</el-button>
          <el-button v-if="checkPermission(['admin', 'admin-enterprise:add'])" type="primary" @click="handleAdd">+ 企业档案</el-button>
        </template>
      </tabs-layout>
      
      <div class="table">
        <!-- 分页table -->
        <table-pager
          ref="pager-table"
          :options="tableColumns"
          :remote-method="getlist"
          :data.sync="tableData"
          :pageSize="pageSize"
          :operation-width="120"
        >
        <!--操作栏-->
        <div slot-scope="{row}">
          <el-row class="table-edit-row">
            <span class="table-edit-row-item">
              <el-button type="text" v-if="checkPermission(['admin', 'admin-enterprise:edit'])" @click="handleEdit(row)">编辑档案</el-button>
            </span>
          </el-row>
        </div>
        </table-pager>
      </div>
    </div>
  </div>
</template>

<script>
import { list, areas } from "@/api/enterprise";
import checkPermission from '@/utils/permission';

const TableColumns = [
  { label: "企业名称", name: "name", width: "248px", disabled: true },
  {
    label: "社会统一信用代码",
    name: "creditCode",
    width: "208px",
    prop: "creditCode",
    disabled: true,
  },
  {
    label: "法定代表",
    name: "legalPerson",
    prop: "legalPerson",
    width: "168px",
    disabled: true,
  },
  {
    label: "注册资本（万元）",
    name: "registerCapital",
    prop: "registerCapital",
    width: "168px",
    disabled: true,
  },
  {
    label: "负责人",
    name: "ceoName",
    prop: "ceoName",
    width: "168px",
  },
  {
    label: "负责人手机",
    name: "enterPhone",
    prop: "enterPhone",
    width: "168px",
  },
  {
    label: "所在地区",
    name: "region",
    prop: "region",
    width: "168px",
  },
  {
    label: "注册地址",
    name: "registerAddress",
    prop: "registerAddress",
    width: "320px",
  },
  {
    label: "地图位置",
    name: 'detailLocation',
    prop: 'detailLocation',
    width: '240px'
  },
  {
    label: "来源",
    name: 'source',
    prop: 'source',
    width: '320px'
  }
];
const TableColumnList = [];
for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({
    key: i,
    ...TableColumns[i],
  });
}
export default {
  data() {
    return {
      arealist: [],
      props: {
        lazy: true,
        checkStrictly: true,
        async lazyLoad(node, resolve) {
          const { level } = node;
          let id = node.data ? node.data.id : "";
          let res = await areas({ parentId: id });
          let list = res.data;
          list.forEach((item) => {
            item.value = item.id;
            item.leaf = level >= 2;
          });
          resolve(list);
        },
      },
      cityValue: [],
      isExpand: false,
      list: [],
      listLoading: false,
      total: 0,
      model:{
        name:'',
      },
      
      tableData: [],
      tableColumns: TableColumnList,
      pageSize: 10,
      tableSelectTitle: [0, 1, 2, 3],
      showSelectTitle: false,
      
    };
  },
  methods: {
    checkPermission,
    cityChange(e) {
      this.model.provinceId = e[0];
      this.model.cityId = e[1];
      this.model.countyId = e[2];
    },
    onSearchSubmitFun() {
      this.handleRefresh({
        page: 1,
        pageSize: this.pageSize
      })
    },
    handleRefresh(pageParams) {
      this.$refs['pager-table'].doRefresh(pageParams)
    },
    resetForm() {
      this.addr = {};
      this.cityValue = [];
      this.model = {
        name:''
      };
      this.$refs['tabs-layout'].reset()
      this.handleRefresh({
        page: 1,
        pageSize: this.pageSize
      });
    },
    async getlist(params) {
      let listQuery = {
        model:{
          ...this.model,
        }
      };
      Object.assign(listQuery, params);
      return await list(listQuery);
      
    },
    // 新增企业档案
    handleAdd(){
      this.$router.push({
        path:'/enterprise/detail'
      })
    },
    handleEdit(row) {
      this.$router.push({
        path: '/enterprise/detail',
        query:{
          id:row.id
        }
      })
    },
  },
  created() {
    
  },
  mounted() {
    
  },
  
  components: {
    
  },
};
</script>
<style lang="scss" scoped>
.archivesPageContent {
  padding: 0;
  .temp_searchBox {
    height: 64px;
    overflow: hidden;
    margin-bottom: 0;
  }
  .form-inline {
    height: 60px;
    overflow: hidden;
  }
  .title {
    margin-bottom: 16px;
    span {
      margin-bottom: -2px;
      padding: 0 15px;
      height: 40px;
      line-height: 30px;
      display: block;
      background: rgba(255, 255, 255, 0);
      border-bottom: 2px solid rgb(64, 158, 255);
      font-size: 16px;
      font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
        "PingFang SC"-400;
      font-weight: 400;
      color: rgb(64, 158, 255);
    }
  }
  .cont {
    border-top: 16px solid #f2f3f4;
  }
}
</style>
