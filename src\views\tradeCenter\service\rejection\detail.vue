<template>
  <div class="detail-wrapper">
    <p class="detail-title">拒收单详情</p>
    <div class="detail-items">
      <ul class="orders"><li>订单编号：{{detail.orderNo}}</li><li>下单时间：{{detail.orderCreateTime}}</li><li>下单渠道：{{detail.orderChannel}}</li></ul>
      <div class="clearfix"></div>
      <el-steps :active="active" align-center>
        <el-step title="待审核" :description="detail.pendingTime"></el-step>
        <el-step title="待入库" :description="detail.warehousingTime"></el-step>
        <el-step title="已完成" :description="detail.acceptedTime"></el-step>
      </el-steps>
      <div class="order-status" v-if="detail.deliveryStatus">
        <h3><img src="../../../../assets/imgs/order_icon.png">{{detail.deliveryStatus.desc}}
          <span style="margin-left: 20px;">拒收总金额：<span class="text-warning">￥<span class="text-warning" v-text="detail.rejectionMoney"></span></span></span>
          <span style="margin-left: 20px;" v-if="detail.deliveryStatus.code==='WAREHOUSING'||detail.deliveryStatus.code==='ACCEPTED'">总退款金额：
            <span class="text-warning">￥{{detail.totalRefundMoney}}</span>
            <small style="color: #666;">（含运费¥<span style="font-weight: 500;" v-text="detail.freight"></span>）
              <span v-if="detail.deliveryStatus.code==='WAREHOUSING'" class="text-warning">需退款</span>
              <span v-if="detail.deliveryStatus.code==='ACCEPTED'" class="text-warning">退款待审核</span>
            </small>
          </span>
        </h3>
        <p v-if="detail.deliveryStatus.code === 'WAREHOUSING'">已同意拒收申请，正在拦截物流</p>
        <p v-if="detail.deliveryStatus.code === 'PENDING'">订单包裹已提交拒收申请，等待管理员进行审核</p>
        <p v-if="detail.deliveryStatus.code === 'ACCEPTED'">已同意拒收申请，正在拦截物流</p>
        <div class="fr" style="margin-top: -65px;">
          <!--待审核：导出单据、审核；待入库：导出单据、确认收货；-->
          <!-- <el-button v-if="checkPermission(['admin','rejectionDetail:export'])">导出单据</el-button> -->
          <el-button type="primary" v-if="detail.deliveryStatus.code === 'WAREHOUSING'&&checkPermission(['admin','rejectionDetail:confirm'])" @click="handleComfirm">确认收货</el-button>
          <el-button type="primary" v-if="detail.deliveryStatus.code === 'PENDING'&&checkPermission(['admin','rejectionDetail:verify'])" @click="verifyVisible = true">审核</el-button>
        </div>
      </div>
      <detail-item itemName="拒收信息">
        <ul class="client-info" v-if="detail.purMerchant">
          <li>拒收单号：{{detail.rejectionNo}}</li>
          <li>客户编码：{{detail.purMerchant.code}}</li>
          <li>物流公司：{{detail.logisName}}</li>
          <li>申请人：{{detail.purMerchant.code}}</li>
          <li>客户名称：{{detail.purMerchant.name}}</li>
          <li>物流单号：{{detail.logisticsNo}}</li>
          <li>拒收时间：{{detail.rejectionTime}}</li>
          <li style="width:100%;">拒收原因：{{detail.rejection}}</li>
        </ul>
      </detail-item>
      <detail-item itemName="拒收商品信息">
        <el-table border :data="detail.orderDeliveryInfoVoList" :span-method="arraySpanMethod" :default-expand-all="true" class="expand-table">
          <el-table-column type="expand" align="left">
            <template slot-scope="scope">
              <el-table :data="scope.row.orderItemVoList" :show-header="false">
                <el-table-column type="index"></el-table-column>
                <el-table-column prop="picturePath">
                  <template slot-scope="scope">
                    <img :src="scope.row.picturePath | imgFilter" class="productImg">
                  </template>
                </el-table-column>
                <el-table-column prop="productCode"></el-table-column>
                <el-table-column prop="spec"></el-table-column>
                <el-table-column prop="saleMerchantName"></el-table-column>
                <el-table-column prop="skuId"></el-table-column>
                <el-table-column prop="expireTime"></el-table-column>
                <el-table-column prop="unitMoney"></el-table-column>
                <el-table-column prop="totalNum"></el-table-column>
                <el-table-column prop="totalNum"></el-table-column>
              </el-table>
            </template>
          </el-table-column>
          <el-table-column label="商品主图">
            <template slot-scope="scope">
             <span>[包裹{{scope.$index + 1}}]</span>
             <span>物流公司：{{scope.row.logisName}}</span>
             <span>运单号：{{scope.row.shippingNo}}</span>
             <span>状态：{{scope.row.deliveryStatus.desc}}</span>
            </template>
          </el-table-column>
          <el-table-column label="商品编码"></el-table-column>
          <el-table-column label="规格"></el-table-column>
          <el-table-column label="生产厂家"></el-table-column>
          <el-table-column label="生产批号"></el-table-column>
          <!--<el-table-column label="有效期至"></el-table-column>-->
          <el-table-column label="单价"></el-table-column>
          <el-table-column label="发货批号"></el-table-column>
          <el-table-column label="发货数量"></el-table-column>
        </el-table>
        <el-tabs v-model="activeName">
          <el-tab-pane label="退货日志" name="first">
            <el-timeline>
              <el-timeline-item
                hide-timestamp
                v-for="(activity, index) in activities"
                :key="index"
                :icon="activity.icon"
                :type="activity.type"
                :color="activity.color"
                :size="activity.size">
                {{activity.time}}
                <span class="active-content">{{activity.content}}</span>
              </el-timeline-item>
            </el-timeline>
          </el-tab-pane>
        </el-tabs>
      </detail-item>
    </div>
    <verify  v-bind:title="title" :verifyVisible="verifyVisible" @changeShow="changeVerify" @verifyRefuse="verifyRefuse" @verifyAgree="verifyAgree"></verify>
  </div>
</template>

<script>
  import detailItem from '@/views/merchant/list/detail-item'
  import { rejectionDetail,rejectionComfirm,refuseRejection,agreeRejection } from "@/api/trade";
  import verify from "../dialogs/verify";
  import checkPermission from '@/utils/permission';
  export default {
    name: "detail",
    components: {
      detailItem,
      verify
    },
    data(){
      return {
        activeName: 'first',
        title: '该拒收单是否通过申请',
        verifyVisible: false,//审核弹出框
        remarkVisible: false,
        remarks: '',
        innerVisible: false,
        agreeVisible: false,//同意申请
        reason: '',
        cancelVisible: false,
        currentComponent: '',
        detail: {},
        productData: [],
        activities: [],
        agreeForm: {},
        active: 1
      }
    },
    mounted() {
      this.getDetail()
    },
    methods: {
      checkPermission,
      changeVerify(data) {
        if (data === 'false') {
          this.verifyVisible = false
        } else {
          this.verifyVisible = true
        }
      },
      //确认收货
      handleComfirm() {
        rejectionComfirm(this.$route.query.id).then(res=>{
          this.$message.success('确认收货成功！')
          this.getDetail()
        })
      },
      //审核拒绝
      verifyRefuse(reason) {
        refuseRejection(this.$route.query.id,reason).then(res=>{
          this.$message.success('审核已拒绝！')
          this.verifyVisible = false
          this.getDetail()
        })
      },
      //审核通过
      verifyAgree(agreeReason,freight) {
        agreeRejection({
          agreeReason: agreeReason,
          freight: freight,
          id: this.$route.query.id}).then(res=>{
          this.$message.success('审核已通过！')
          this.verifyVisible = false
          this.getDetail()
        })
      },
      arraySpanMethod({ row, column, rowIndex, columnIndex }) {
        if(columnIndex === 1) {
          return [1, 10]
        }
      },
      async getDetail() {
        let id = this.$route.query.id
        const {data} = await rejectionDetail(id)
        this.detail = data
        this.activities = data.rejectionJournal
        this.productData = data.orderDeliveryInfoVoList[0].orderItemVoList
        if(data.deliveryStatus.code === 'PENDING') {
          this.active = 0
        } else if(data.deliveryStatus.code === 'WAREHOUSING') {
          this.active = 1
        } else {
          this.active = 2
        }
      },
      handleClick() {},
      componentResult() {

      },
      handleVeriry() {

      },
      handleRefuse() {

      },

    }
  }
</script>

<style lang="scss">


</style>
