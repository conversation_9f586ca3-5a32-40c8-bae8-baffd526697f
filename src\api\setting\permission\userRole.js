import requestAxios from '@/utils/requestAxios'

export function list(data) {
    return requestAxios({
        url: '/api/authority/role/page',
        method: 'post',
        data
    })
}
export function query(data) {
    return requestAxios({
        url: '/api/authority/role/query',
        method: 'post',
        data
    })
}
export function getApi(id) {
    return requestAxios({
        url: '/api/authority/role/' + id,
        method: "get"
    })
}


export function editApi(data) {
    return requestAxios({
        url: '/api/authority/role',
        method: data.id > 0 ? 'put' : 'post',
        data
    })
}


export function editRoleAndAuthority(data) {
    return requestAxios({
        url: '/api/authority/role/roleAndAuthority',
        method: data.role.id == undefined ? 'post' : 'put',
        data
    })
}

export function adminSite(data) {
    return requestAxios({
        url: '/api/merchant/admin/site',
        method: data.id == undefined ? 'post' : 'put',
        data
    })
}


export function deleteApi(id) {
    return requestAxios({
        url: '/api/authority/role?ids[]=' + id,
        method:'delete'
    })
}

export function findAuthorityIdByRoleId(roleId) {
    return requestAxios({
        url: `/api/authority/role/authority/${roleId}`,
        method: 'GET'
    })
}

// 根据Id查询详情
export function feignListByUserId(id) {
    return requestAxios({
        url: `/api/merchant/admin/site/${id}`,
        method: 'GET'
    })
}



export function roleUserApi(data) {
    return requestAxios({
        url: '/api/authority/role/user',
        method: 'post',
        data
    })
}


export function setUserRoleApi(data) {
    return requestAxios({
        url: '/api/authority/role/user/assign',
        method: 'post',
        data
    })
}

export function checkUserRoleCodeApi(code) {
    return requestAxios({
        url: `/api/authority/role/check/${code}`,
        method: 'get'
    })
}
