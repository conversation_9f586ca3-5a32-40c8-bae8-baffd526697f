<template>
    <el-dialog title="添加副主管" width="30%" v-bind="$attrs" v-on="$listeners" @opened="handleOpen"  :close-on-click-modal="false" @close="clearData"
    >
    <el-form ref="form" :model="form" label-width="50px" :rules="rules" class="je-pr30">
		  <el-form-item label="姓名" prop="salesmanId" :rules="[
              {
                required: true,
                message: '请选择主管',
                trigger: 'change',
              },
              { validator: checkSalesManByIdFun, trigger: 'change', required: true }
            ]">
        <el-select v-model="form.salesmanId" style="width: 95%;" filterable placeholder="请选择姓名">
          <el-option
                v-for="(item, index) in salesmanList"
                :key="index"
                :label="`${item.salesmanName}(${item.mobile})`"
                :value="item.id"
              >
                <span style="float: left">{{ item.salesmanName }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{item.mobile}}</span>
              </el-option>
        </el-select>
      </el-form-item>
		</el-form>
		<div slot="footer" class="dialog-footer">
      <el-button @click="$emit('update:visible', false)">关 闭</el-button>
      <el-button type="primary" @click="handlePrimary">提 交</el-button>
    </div>
    </el-dialog>
</template>
<script>
    import {getSalesManList,addDeputyDirector,checkSalesManById} from '@/api/organization/index';
    import { mapGetters} from 'vuex'
    export default {
      name: 'AddSecondCommand',
      props: {
        organizationId: {
          type: String,
          default: () => {
            return ''
          }
        },
        departmentId: {
          type: String,
          default: () => {
            return ''
          }
        }
      },
			data() {
				return {
					form: {
            organizationId: '',
            departmentId: '',
            salesmanId: ''
          },
          rules: {
          salesmanId: [{ required: true, message: '请选择姓名', trigger: 'change'}]
          },
          salesmanList: []
				}
			},
      computed: {
      ...mapGetters([
          'organizationInfo',
      ]), 
    
    
    },
      mounted() {
        this.getSalesManListFun()
      },
			methods: {
            //方法集合
    checkSalesManByIdFun(rule, value, callback){
      checkSalesManById(value).then(res=>{
        let {code,data,msg} = res;
        if (code == 0 && data != null&& data.organizationId != this.organizationInfo.id&&data.isLeader.code=='Y') {
          this.$message.warning('当前业务员已是其他组织主管')
          // return callback(new Error('当前业务员已是其他组织主管'))
        } else if (code == 0 && data != null&& data.organizationId != this.organizationInfo.id) {
          this.$message.warning('当前业务员是其他组织成员')
          // return callback(new Error('当前业务员是其他组织成员'))
        } else if (code == 0 && data != null&& data.organizationId == this.organizationInfo.id&&data.isLeader.code=='Y') {
          this.$message.warning('当前业务员是其他部门主管')
          // return callback(new Error('当前业务员是其他部门主管'))
        } else if (code == 0 && data != null&& data.organizationId == this.organizationInfo.id) {
          this.$message.warning('当前业务员是其他部门成员')
          // return callback(new Error('当前业务员是其他部门成员'))
        } 
        callback()
      })
    },
        // 查询业务员列表
        getSalesManListFun(){
          getSalesManList(null).then().then(res => {
            let {code,data,msg} = res;
            if(code == 0 ){
              this.salesmanList=data;
            } 
          })
        },
				handleOpen() {
          this.form = {...this.form, organizationId: this.organizationId,departmentId: this.departmentId }
        },
				clearData() {
          this.$refs.form.resetFields()
          this.form = {
            organizationId: '',
            departmentId: '',
            salesmanId: ''
          };
        },
				// 提交
				handlePrimary() {
          this.$refs.form.validate(valid => {
              if(valid) {
                const query = {...this.form}
                addDeputyDirector({...query}).then(res => {
                  if(res.code === 0) {
                    this.$message.success('添加成功!');
                    this.$emit('onsuccess');
                    this.$emit('update:visible', false)
                  }
                })
              }
          })
        }
			}
    }
</script>