<template>
  <div>
    <!--搜索Form-->
    <im-search-pad :has-expand="false" :is-expand.sync="isExpand" :model="model" @reset="reload" @search="searchLoad">
      <im-search-pad-item prop="keyword">
        <el-input v-model="model.keyword"  @keyup.enter.native="searchLoad"  placeholder="企业名称" />
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <el-table ref="multipleTable" border :data="tableData" tooltip-effect="dark" style="width: 100%"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" fixed></el-table-column>
        <el-table-column prop="code" label="企业编码" width="200"></el-table-column>
        <el-table-column prop="name" label="企业名称"></el-table-column>
        <el-table-column prop="ceoName" label="负责人" width="180"></el-table-column>
        <el-table-column prop="ceoMobile" label="负责人手机" width="180"></el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="page-row">
        <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size.sync="limit"
          layout="total, sizes, prev, pager, next, jumper" :total="totalCount">
        </el-pagination>
      </div>
    </div>
    <div class="bottom_btn">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="submit">确定</el-button>
    </div>
  </div>
</template>


<script>
import {
    unboundSaleMerchants,
    bindingSaleMerchant
  } from '@/api/salemanCenter/index'
  export default {
    //import引入的组件
    components: {},
    props:{
      salesmanId:{
        type:String,
        default:''
      }
    },

    data() {
      return {
        isExpand: false,
        model: {
          keyword: '',
        },
        page: 1,
        limit: 10,
        totalCount: 0,
        tableData:[],
        multipleSelection: []
      }
    },
    //生命周期 - 挂载完成（可以访问DOM元素）
    mounted() {
      this.load();
    },

    computed: {
    },

    created() {},

    filters: {},

    //方法集合
    methods: {
      load(){
        let params = {
          current: this.page,
          map: {},
          model: {
            ...this.model,
            salesmanId:this.salesmanId
          },
          order: 'descending',
          size: this.limit,
          sort: 'id'
        };
        unboundSaleMerchants(params).then(res=>{
          if(res.code == 0 && res.msg == 'ok') {
            console.log('未绑定企业的',res);
            this.tableData = res.data.records || [];
            this.totalCount = res.data.total;
          }
        })
      },
      handleSelectionChange(val) {
        this.multipleSelection = val;
      },
       handleSizeChange(val) {
        this.limit = val;
        this.load();
      },
      handleCurrentChange(val) {
        this.page = val;
        this.load();
      },
      handleCancel(){
        this.$emit('closeDia');
      },
      submit(){
        console.log('this.multipleSelection',this.multipleSelection);
        let list = this.multipleSelection.map(item=>{
          return item.id;
        })
        let params = {
          saleMerchantIds:list,
          salesmanId:this.salesmanId
        };
        bindingSaleMerchant(params).then(res=>{
          if(res.code == 0 && res.msg == 'ok') {
            this.$message.success('绑定企业成功');
            this.$emit('closeDia');
          }
        })
      },
      //   刷新
      reload() {
        this.model = {
          keyword: '',
        }
        this.handleRefresh()
      },
      searchLoad() {
        this.handleRefresh()
      },
      handleRefresh(pageParams) {
        this.page = 1;
        this.load();
      },
      // tab切换
      handleChangeTab(tab) {
        this.model.tabCode = tab.value
        this.handleRefresh()
      },
    },

  }

</script>


<style lang='scss' scoped>
.page-row {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #505465;
    font-size: 13px;
    margin-top: 16px;
  }
  .bottom_btn {
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
</style>
