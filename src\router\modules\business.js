import Layout from '@/layout'

const businessRouter = {
    path: '/businessCenter',
    name: 'businessCenter',
    redirect: 'businessArchives/list',
    meta: {
      title: '商家中心',
      icon: 'component'
    },
    alwaysShow: true,
    component: Layout, 
    children: [
      {
        path: 'businessArchives/list',
        name: 'businessArchives',
        meta: { title: '商家列表' },
        component: () => import('@/views/businessCenter/businessList/list')
      },
      {
        path: 'businessArchives/list/edit',
        name: 'businessArchivesItem',
        hidden: true ,
        meta: { title: '编辑',activeMenu:'/businessCenter/businessArchives/list' },
        component: () => import('@/views/businessCenter/businessList/editItem')
      },
      {
        path: 'businessArchives/detail',
        name: 'businessArchivesdetail',
        hidden: true ,
        meta: { title: '商家详情',activeMenu:'/businessCenter/businessArchives/list' },
        component: () => import('@/views/businessCenter/businessList/detail')
      }
    ]
}

export default businessRouter