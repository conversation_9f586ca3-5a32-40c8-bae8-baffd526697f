import Vue from 'vue'

import Cookies from 'js-cookie'

import 'normalize.css/normalize.css' // a modern alternative to CSS resets

import Element from 'element-ui'
import './styles/element-variables.scss'



import '@/styles/index.scss' // global css

import App from './App'
import store from './store'
import router from './router'

import './icons' // icon
import './permission' // permission control
import './utils/error-log' // error log

import * as filters from './filters' // global filters


if (process.env.NODE_ENV === 'production') {
  const { mockXHR } = require('../mock')
  mockXHR()
}

Vue.use(Element, {
  size: Cookies.get('size') || 'medium'
})

import iconPicker from 'vue-fontawesome-elementui-icon-picker';
Vue.use(iconPicker);

import ImSearchPad from '@/components/ImSearchPad/index'
Vue.use(ImSearchPad)
import ImSearchPadItem from '@/components/ImSearchPadItem/index'
Vue.use(ImSearchPadItem)
import TablePager from '@/components/TablePager'
Vue.use(TablePager)
import ImDialog from '@/components/ImDialog'
Vue.use(ImDialog)
import ImTablePage from '@/components/ImTablePage/index'
Vue.use(ImTablePage)
import ImPagination from '@/components/ImPagination/index'
Vue.use(ImPagination)
import TabsLayout from '@/components/TabsLayout/install'
Vue.use(TabsLayout)
import PageTitle from '@/components/PageTitle/install'
Vue.use(PageTitle)
import PageModuleTitle from '@/components/PageModuleTitle/install'
Vue.use(PageModuleTitle)
import PageModuleCard from '@/components/PageModuleCard/install'
Vue.use(PageModuleCard)

import VueDND from 'awe-dnd'
Vue.use(VueDND)

// 自定义指令
import throttle from "@/directive/throttle"
Vue.use(throttle)


// register global utility filters
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
})

Vue.config.productionTip = false

Vue.prototype.$uploadUrl = process.env.VUE_APP_UPLOAD_API
Vue.prototype.$authorization = process.env.VUE_APP_AUTHORIZATION_CODE
Vue.prototype.$excelImportUrl = process.env.VUE_APP_EXCELIMPORT_API
Vue.prototype.$filePathHost = process.env.VUE_APP_FILE_PATH_HOST

Vue.filter('getDecimals',function (value) {
  var value = Math.round(parseFloat(value) * 100) / 100;
  var xsd = value.toString().split(".");
  if (xsd.length == 1) {
    value = value.toString() + ".00";
    return value;
  }
  if (xsd.length > 1) {
    if (xsd[1].length < 2) {
      value = value.toString() + "0";
    }
    return value;
  }
},)
new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})
