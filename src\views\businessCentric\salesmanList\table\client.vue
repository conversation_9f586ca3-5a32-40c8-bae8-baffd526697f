<template>
  <div class="goods">
    <div>
      <el-tabs
        v-model="listQuery.model.approvalStatus.code"
        class="typeTabs"
        @tab-click="chageTabsFun"
      >
        <el-tab-pane label="已绑定客户（32）" name="ACCEPTED"></el-tab-pane>
        <el-tab-pane label="未绑定客户(400)" name="PENDING"></el-tab-pane>
      </el-tabs>
    </div>
    <div>
      <el-form>
        <el-form-item>
          <el-input
            placeholder="请输入商品名称/通用名/商品编码"
            v-model="listQuery.model.text"
            style="width: 250px; padding-right: 10px"
          ></el-input>
          <el-button type="primary">搜索</el-button>
          <el-button>重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table
      v-loading="isLoading"
      @selection-change="selectTableItemFun"
      border
      fit
      :data="list"
      style="width: 100%"
      max-height="450px"
    >
      <el-table-column
        type="selection"
        width="55"
        fixed="left"
        align="center"
      ></el-table-column>
      <el-table-column
        v-for="(item, index) in tableTitle"
        :key="index"
        :fixed="item.name == 'code' || item.name == 'name' ? 'left' : false"
        :min-width="item.width ? item.width : '350px'"
        :label="item.label"
        show-overflow-tooltip
        align="left"
      >
        <template slot-scope="{ row }">
          <span>{{ row[item.name] }}</span>
        </template>
      </el-table-column>
      <el-table-column
        type="selection"
        fixed="right"
        width="80"
        label="操作"
        align="center"
        class="table_leftborder"
      >
        <el-button type="text" >绑定</el-button>
      </el-table-column>
    </el-table>
    <pagination
      v-if="total > 0"
      :pageSizes="[2, 10, 20, 50]"
      :total="total"
      :page.sync="listQuery.current"
      :limit.sync="listQuery.size"
      @pagination="getlist"
    />
    <div class="dialog-footer">
      <el-button @click="cancelFun">取 消</el-button>
      <el-button type="primary" @click="confirmFun">确 定</el-button>
    </div>
  </div>
</template>

<script>
const tableInfo = [
  {
    name: "code",
    label: "客户编码",
    width: "170px",
  },
  {
    name: "name",
    label: "客户名称",
    width: "140px",
  },
  {
    name: "socialCode",
    label: "社会统一信用代码",
    width: "220px",
  },
  {
    name: "merchantType",
    label: "企业类型",
    width: "170px",
  },
  {
    name: "ceoName",
    label: "联系人",
    width: "120px",
  },
  {
    name: "ceoMobile",
    label: "联系电话",
    width: "170px",
  },
  {
    name: "region",
    label: "所在地区",
    width: "170px",
  },
  {
    name: "cateGory",
    label: "经营范围",
    width: "240px",
  },
];
import Pagination from "@/components/Pagination";
import checkPermission from "@/utils/permission";
export default {
  data() {
    return {
      isLoading: false,
      tableTitle: tableInfo,
      total: 11,
      list: [1, 2, 3, 4, 5],
      listQuery: {
        current: 1,
        size: 10,
        model: {
          approvalStatus: {
            code: "PENDING",
          },
        },
      },
      tableSelectTitle: [0, 1, 2, 3, 4],
      showSelectTitle: false,
    };
  },
  methods: {
    chageTabsFun() {},
    selectTableItemFun() {},
    getlist() {
      this.isLoading = true;
      setTimeout(() => {
        this.isLoading = false;
      }, 1000);
    },
    confirmFun() {},
    cancelFun() {
      this.$emit("update:visible", false);
    },
    renderHeader(h, { column }) {
      return (
        <div style="position:relative">
          <i class="el-icon-menu" />
        </div>
      );
    },
    checkPermission,
  },
  created() {
    if (this.$props.row) {
      this.getlist();
    } else this.getAll();
  },
  components: {
    Pagination,
  },
  props: {
    row: {
      type: Object,
    },
    visible: {
      type: Boolean,
      default: false,
      required: true,
    },
  },
};
</script>

<style lang="less" scoped>
.goods {
  margin: -30px -20px;
  border-top: 1px solid #ebecee;
  padding: 30px 20px;
  .table_leftborder {
    border-left: 1px solid #dfe6ec;
  }
  .dialog-footer {
    border-top: 1px solid #efefef;
    margin: -30px -20px;
    margin-top: 30px;
    padding: 20px;
    padding-top: 10px;
    text-align: right;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
  }
}
</style>
