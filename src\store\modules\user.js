import { login, logout, getInfo, getUserResourceVisible, reloadToken } from '@/api/user'
import { getToken, setToken, removeToken, getUser, setUser, removeUser, setRefreshToken, getRefreshToken, removeRefreshToken, getExpire, setExpire, removeExpire } from '@/utils/auth'
import router, { resetRouter } from '@/router'

const state = {
  token: getToken(), // token
  userId: getUser("userId"),
  name: getUser("name"),
  avatar: getUser("avatar"),
  expiration: getUser("expiration"),
  introduction: '',
  roles: [],
  refreshToken: getRefreshToken(), // 刷新token
  expire: getExpire(), // 刷新token过期时间
}

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_TOKENTIMEOUT: (state, expiration) => {
    state.expiration = expiration
  },
  SET_USERID: (state, userId) => {
    state.userId = userId
  },
  SET_INTRODUCTION: (state, introduction) => {
    state.introduction = introduction
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles
  },
  SET_REFRESH_TOKEN: (state, refreshToken) => {
    state.refreshToken = refreshToken
  },
  SET_EXPIRE: (state, expire) => {
    state.expire = expire
  }
}

const actions = {
  login({ commit }, userInfo) {
    return new Promise((resolve, reject) => {
      login(userInfo).then(response => {
        const { data } = response
        const { token, userId, name, avatar, refreshToken, expire } = data
        console.info(data)
        commit('SET_TOKEN', "Bearer " + token)
        setToken("Bearer " + token)
        commit('SET_REFRESH_TOKEN', "Bearer " + refreshToken)
        setRefreshToken("Bearer " + refreshToken)
        commit('SET_USERID', userId)
        commit('SET_NAME', name)
        commit('SET_AVATAR', avatar)
        commit('SET_EXPIRE', expire)
        setExpire(expire)
        setUser(data)
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },
  oauthLogin({ commit }, token) {
    return new Promise((resolve, reject) => {
      commit('SET_TOKEN', "Bearer " + token)
      setToken("Bearer " + token)
      // commit('SET_REFRESH_TOKEN', "Bearer " + refreshToken)
      // setRefreshToken("Bearer " + refreshToken)
      resolve()
    })
  },
  // get user info
  getInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      getInfo(state.token).then(response => {
        const { data } = response

        if (!data) {
          reject('Verification failed, please Login again.')
        }
        const { roles, name, avatar, introduction } = data
        // roles must be a non-empty array
        if (!roles || roles.length <= 0) {
          reject('getInfo: roles must be a non-null array!')
        }
        commit('SET_ROLES', roles)
        commit('SET_NAME', name)
        commit('SET_AVATAR', avatar)
        commit('SET_INTRODUCTION', introduction)
        resolve(data)
      }).catch(error => {
        reject(error)
      })
    })
  },
  getRoleS({ commit, state }) {
    return new Promise((resolve, reject) => {
      getUserResourceVisible(state.userId).then(response => {
        const { data } = response
        commit('SET_ROLES', data)
        resolve(data)
      }).catch(error => {
        reject(error)
      })
    })
  },
  // user logout
  logout({ commit, state, dispatch }) {
    return new Promise((resolve, reject) => {
      // logout(state.token).then(() => {
      //   commit('SET_TOKEN', '')
      //   commit('SET_ROLES', [])
      //   removeToken()
      //   removeUser()
      //   resetRouter()
      //   dispatch('tagsView/delAllViews', null, { root: true })

      //   resolve()
      // }).catch(error => {
      //   reject(error)
      // })
      commit('SET_TOKEN', '')
      commit('SET_ROLES', [])
      commit('SET_REFRESH_TOKEN', '')
      commit('SET_EXPIRE', '')
      removeToken()
      removeRefreshToken()
      removeUser()
      removeExpire()
      resetRouter()
      dispatch('tagsView/delAllViews', null, { root: true })
      dispatch('permission/resetRoutes', [], { root: true })
      resolve()
    })
  },
  reloadToken({ commit, state }) {
    return new Promise((resolve, reject) => {
      login({
        grantType: "refresh_token",
        refreshToken: state.token,
        // refreshToken: state.token.replace("Bearer ",""),
      }).then(response => {
        if (response.code == 0) {
          const { data } = response
          const { token, userId, name, avatar, refreshToken } = data
          commit('SET_TOKEN', "Bearer " + token)
          setToken("Bearer " + token)
          commit('SET_REFRESH_TOKEN', "Bearer " + refreshToken)
          setRefreshToken("Bearer " + refreshToken)
          commit('SET_USERID', userId)
          commit('SET_NAME', name)
          commit('SET_AVATAR', avatar)
          setUser(data)
        }
        resolve()

      }).catch(error => {
        reject(error)
      })
    })
  },
  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      commit('SET_TOKEN', '')
      commit('SET_REFRESH_TOKEN', '')
      commit('SET_ROLES', [])
      removeToken()
      removeRefreshToken()
      removeUser()
      resolve()
    })
  },

  // dynamically modify permissions
  async changeRoles({ commit, dispatch }, role) {
    const token = role + '-token'

    commit('SET_TOKEN', token)
    setToken(token)

    const { roles } = await dispatch('getInfo')

    resetRouter()

    // generate accessible routes map based on roles
    const accessRoutes = await dispatch('permission/generateRoutes', roles, { root: true })
    // dynamically add accessible routes
    router.addRoutes(accessRoutes)

    // reset visited views and cached views
    dispatch('tagsView/delAllViews', null, { root: true })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
