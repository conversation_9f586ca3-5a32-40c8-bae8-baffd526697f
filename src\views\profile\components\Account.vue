<template>
  <el-form :model="user" class="form" label-position="right" label-width="80px" ref="form">
    <el-form-item label="名称" prop="name">
      <el-input v-model="user.name" />
    </el-form-item>
    <el-form-item label="联系电话" prop="mobile">
      <el-input v-model="user.mobile" />
    </el-form-item>
    <el-form-item label="性别" prop="sex">
      
    </el-form-item>
    <el-form-item label="工作描述" prop="workDescribe">
      <el-input rows="3" type="textarea" v-model="user.workDescribe" />
    </el-form-item>
    <el-form-item>
      <el-button  plain type="primary">提交</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { validMobile } from '@/utils/my-validate'

export default {
  components: {},
  props: {
    user: {
      type: Object,
      default: () => {
        return {
          name: '',
          email: '',
          sex: {
            code: 'M'
          }
        }
      }
    }
  },
  data () {
    return {
    }
  },
  computed: {
  },
  mounted () {
  },
  methods: {
    

  }
}
</script>
<style lang="scss" scoped>
.form {
  padding: 10px 0 0 0;
}
</style>
