import requestAxios from '@/utils/requestAxios'
// import requestAxios from "@/views/businessCentric/request"
// 获取详情
export function getList(data) {
  return requestAxios({
    url: '/api/agent/agentProduct/salesmanProduct/queryRepeals-platform',
    method: 'post',
    data
  })
}

// 获取同意
export function agree(data) {
  return requestAxios({
    url: '/api/agent/agentProduct/salesmanProduct/consent/' + data,
    method: 'put',
  })
}

// 获取驳回
export function reject(data) {
  return requestAxios({
    url: '/api/agent/agentProduct/salesmanProduct/reject/' + data.id,
    method: 'put',
    data: data.reject
  })
}