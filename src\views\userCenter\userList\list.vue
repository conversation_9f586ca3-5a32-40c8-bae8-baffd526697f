<template>
  <div class="archivesPageContent">
    <im-search-pad
      :is-expand.sync="isExpand"
      :model="listQuery"
      @reset="resetForm"
      @search="onSearchSubmitFun"
    >
      <im-search-pad-item prop="name">
        <el-input v-model="listQuery.model.name" placeholder="请输入姓名" />
      </im-search-pad-item>
      <im-search-pad-item prop="account">
        <el-input v-model="listQuery.model.account" placeholder="请输入登录账号" />
      </im-search-pad-item>
      <im-search-pad-item prop="mobile">
        <el-input v-model="listQuery.model.mobile" placeholder="请输入手机号" />
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="status">
        <el-select v-model="listQuery.model.status" placeholder="请选择账号状态">
          <el-option label="已启用" value="true"></el-option>
          <el-option label="已冻结" value="false"></el-option>
        </el-select>
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        :tabs="tabList"
        @change="changeTab"
      >
        <template slot="button">
          <el-button v-if="checkPermission(['admin', 'admin-user:release'])" :disabled="selectArr.length==0" @click="batchEnable">批量启用</el-button>
          <el-button v-if="checkPermission(['admin', 'admin-user:freeze'])" :disabled="selectArr.length==0" @click="batchFreeze">批量冻结</el-button>
          <!-- <el-button @click="outExcel" :disabled="selectArr.length==0">导出档案</el-button> -->
          <el-button @click="getlist">刷新</el-button>
          <!-- <el-button @click="addUserFlag = true" type="primary">+新增用户</el-button> -->
        </template>
      </tabs-layout>
      <div class="table">
        <el-table v-if="list" @selection-change="selectTableItemFun" v-loading="listLoading" :data="list" row-key="id" border fit highlight-current-row style="width: 100%">
          <el-table-column align="center" width="80" :render-header="renderHeader">
            <template slot-scope="scope">
              <span>{{ scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column type="selection" width="55"> </el-table-column>
          <el-table-column v-for="(item, index) in tableTitle" :key="index" :min-width="(item.width?item.width:'350px')" :label="item.label" show-overflow-tooltip align="left">
            <template slot-scope="{row}">
            <span v-if="item.name=='status'" :style="!row[item.name]?'color:#ff0066':''">
              {{row[item.name] ? '已启用' : '已冻结'}}
            </span>
              <span v-else>{{ row[item.name] }}</span>
            </template>
          </el-table-column>

          <el-table-column fixed="right" align="center" label="操作" width="180" class="itemAction">
            <template slot-scope="scope">
              <el-row class="table-edit-row">
                <span class="table-edit-row-item" v-if="checkPermission(['admin', 'admin-user:edit'])">
                  <el-button @click="editFun(scope.row)" type="text" >编辑</el-button>
                </span>
                <span v-if="checkPermission(['admin', 'admin-user:password'])" class="table-edit-row-item">
              <el-button @click="resetPwd(scope.row)" type="text" >重置密码</el-button>
                </span>
                <span v-if="scope.row.status&&checkPermission(['admin', 'admin-user:singleFreeze'])" class="table-edit-row-item">
              <el-button @click="freeze(scope.row)" type="text" >冻结</el-button>
                </span>
                <span v-else-if="!scope.row.status && checkPermission(['admin', 'admin-user:singleRelease'])" class="table-edit-row-item">
              <el-button @click="enable(scope.row)" type="text" >启用</el-button>
                </span>
              </el-row>
            </template>
          </el-table-column>
        </el-table>
        <pagenation v-show="total > 0" :total="total" :page.sync="listQuery.current" :limit.sync="listQuery.size" @pagination="getlist" />
      </div>
    </div>

    <el-dialog v-if="showPwdFlag" title="重置密码" :visible.sync="showPwdFlag" width="500px" @close='closePwd'>
      <el-form ref="pwdForms" :model="pwd" label-width="110px" :rules="rule">

        <el-form-item label="请输入密码:" prop="password">
          <el-input v-model="pwd.password" style="width: 300px" placeholder="请输入密码:" show-password clearable></el-input>
        </el-form-item>

        <el-form-item label="确认密码:" prop="confirmPassword">
          <el-input v-model="pwd.confirmPassword" style="width: 300px" placeholder="请确认密码" show-password clearable></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closePwd">取 消</el-button>
        <el-button type="primary" @click="resetPwdConfirm">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog v-if="addUserFlag" :title="'新增用户'" :visible.sync="addUserFlag" width="500px" @close='closeAddUser'>
      <el-form ref="addUserForm" :model="addUserForm" label-width="110px" :rules="rule">

        <el-form-item label="登录账号:" prop="account">
          <el-input v-model="addUserForm.account" style="width: 300px" placeholder="请输入登录账号:"  clearable></el-input>
        </el-form-item>
        <el-form-item label="请输入密码:" prop="password" v-if="!row.id">
          <el-input v-model="addUserForm.password" style="width: 300px" placeholder="请输入密码:" show-password clearable></el-input>
        </el-form-item>
        <el-form-item label="用户姓名:" prop="name">
          <el-input v-model="addUserForm.name" style="width: 300px" placeholder="请输入用户姓名:"  clearable></el-input>
        </el-form-item>
        <el-form-item label="注册手机:" prop="mobile">
          <el-input v-model="addUserForm.mobile" style="width: 300px" placeholder="请输入注册手机:" clearable></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeAddUser">取 消</el-button>
        <el-button type="primary" @click="addUser">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import checkPermission from '@/utils/permission'
import rule from "@/utils/rules";
import {
  list,
  resetPwd,
  enable,
  freeze,
  batchFreeze,
  batchEnable,
  addUser,
  editUser,
  getUserType
} from "@/api/userCenter/userList";
import pagenation from "@/components/Pagination";
import { downloadFile } from "@/utils/commons";
import TabsLayout from '@/components/TabsLayout'

export default {
  data() {
    var validatePass2 = (rule, value, callback) => {
      if (value !== this.pwd.password) {
        callback(new Error("两次输入密码不一致!"));
      }
      callback();
    };
    return {
      isExpand: false,
      tabList: [],
      tabPermission: {
        SALE: 'admin-user:saleMerchantView',
        PUR_MERCHANT: 'admin-user:purMerchantView',
        SALES_MAN: 'admin-user:salesmanView',
        STAFF: 'admin-user:employeeView'
      },
      rule: {
        password: [
          { required: true, message: "密码不能为空", trigger: "blur" },
          {
            required: true,
            min: 6,
            max: 20,
            message: "长度在 6 到 20 个字符",
            trigger: "blur",
          },
        ],
        confirmPassword: [
          { required: true, message: "确认密码不能为空", trigger: "blur" },
          {
            required: true,
            min: 6,
            max: 20,
            message: "长度在 6 到 20 个字符",
            trigger: "blur",
          },
          { validator: validatePass2, trigger: "blur" },
        ],
        account: [
           { required: true, message: "登录账号不能为空", trigger: "blur" },
        ],
        mobile: rule.phone,
        name: [
           { required: true, message: "用户姓名不能为空", trigger: "blur" },
        ]
      },
      list: [],
      listLoading: false,
      listQuery: {
        current: 1,
        size: 10,
        model: {},
      },
      total: 0,
      tableTitle: [],
      tableInfo: [
        { key: 0, label: "账号状态", name: "status", width: "120px", disabled: true },
        { key: 1, label: "用户ID", name: "id", width: "240px", disabled: true },
        { key: 2, label: "登录账户", name: "account", width: "240px", disabled: true },
        { key: 3, label: "姓名", name: "name", width: "240px" },
        { key: 4, label: "注册手机", name: "mobile", width: "240px" },
        { key: 5, label: "注册时间", name: "createTime", width: "240px" },
      ],
      tableSelectTitle: [0, 1, 2, 3],
      showSelectTitle: false,
      showPwdFlag: false,
      pwd: {},
      row: {},
      selectArr: [],
      tabs: 'list',
      addUserFlag: false,
      addUserForm: {}
    };
  },
  methods: {
    checkPermission,
    changeTab(e) {
      this.listQuery.model.userParamType = e.value
      this.getlist()
    },
    editFun(row) {
      this.row = row
      this.addUserFlag = true
      this.$set(this.addUserForm, 'account', this.row.account)
      this.$set(this.addUserForm, 'name', this.row.name)
      this.$set(this.addUserForm, 'mobile', this.row.mobile)
      this.addUserForm = this.addUserForm
    },
    addUser() {
      this.$refs.addUserForm.validate( async (valid) => {
        if(valid) {
          if(this.row.id) {
            let obj = JSON.parse(JSON.stringify(this.addUserForm))
            obj.id = this.row.id
            let res = await editUser(obj)
            if(res.code === 0) {
              this.addUserFlag = false
              this.row = {}
              this.$message.success('修改用户成功')
              this.getlist()
            }
          } else {
            let data = await addUser(this.addUserForm)
            if(data.code == 0) {
              this.$message.success('添加用户成功')
              this.closeAddUser()
              this.getlist()
            }
          }
        } else {
          this.$message.error('请填写用户信息')
        }
      })
    },
    // 重制密码弹窗
    resetPwd(row) {
      this.showPwdFlag = true;
      this.row = row;
      this.pwd.id = row.id;
    },
    // 重制密码
    resetPwdConfirm() {
      this.$refs.pwdForms.validate(async (valid) => {
        if (valid) {
          let data = await resetPwd(this.pwd);
          this.$message.success("重置密码成功");
          this.closePwd();
        }
      });
    },
    async enable(row) {
      let { data } = await enable({ userId: row.id });
      if (data) {
        row.status = true;
        this.$message.success("已启用  " + row.account);
      }
    },
    async freeze(row) {
      let { data } = await freeze({ userId: row.id });
      if (data) {
        row.status = false;
        this.$message.success("已冻结  " + row.account);
      }
    },
    async batchFreeze() {
      let { data } = await batchFreeze({ userIds: this.selectArr });
      if (data) {
        this.$message.success("批量冻结成功");
        this.getlist();
      }
    },
    async batchEnable() {
      let { data } = await batchEnable({ userIds: this.selectArr });
      if (data) {
        this.$message.success("批量启用成功");
        this.getlist();
      }
    },
    // 获取列表
    async getlist() {
      this.listLoading = true;
      this.list = [];
      if (!this.listQuery.model.userParamType) {
        this.listQuery.model.userParamType = this.tabList[0].value
      }
      let { data } = await list(this.listQuery);
      this.total = data.total;
      this.list = data.records;
      this.listLoading = false;
    },
    onSearchSubmitFun() {
      this.getlist();
    },
    resetForm() {
      const userParamType = this.listQuery.model.userParamType
      this.listQuery = {
        current: 1,
        size: 10,
        model: {
          userParamType
        },
      };
      this.initTbaleTitle();
      this.getlist();
    },
    selectTableItemFun(e) {
      this.multipleSelection = e;
      let arr = [];
      e.forEach((element) => {
        arr.push(element.id);
      });
      this.selectArr = arr;
    },
    closeAddUser() {
      this.addUserFlag = false
      this.addUserForm = {}
      this.row={}
    },
    closePwd() {
      this.pwd = {};
      this.showPwdFlag = false;
    },
    initTbaleTitle() {
      this.tableTitle = this.tableInfo;
      this.tableSelectTitle = []
    },
    renderHeader(h, { column }) {
      var titles = this.tableInfo;
      var titlesName = ["显示字段项", "隐藏字段项"];
      return (
        <div style="position:relative">
          <div onClick={this.showHeaer}>
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="640px"
            center
            append-to-body={true}
          >
            <el-transfer
              vModel={this.tableSelectTitle}
              data={titles}
              titles={titlesName}
              onChange={this.setleftTitleFun}
            ></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button onClick={this.closeHeaer}>取消</el-button>
              <el-button type="primary" onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      );
    },
    setleftTitleFun(val) {
      this.tableSelectTitle = val;
    },
    showHeaer: function () {
      this.showSelectTitle = true;
    },
    closeHeaer: function () {
      this.showSelectTitle = false;
      this.tableSelectTitle = [];
    },
    setHeaer: function () {
      var titles = this.tableInfo
      var listinfo = titles.filter((element, index, self) => {
        return !this.tableSelectTitle.includes(element.key);
      });
      this.tableTitle = listinfo;
      this.showSelectTitle = !this.showSelectTitle;
    },
    async outExcel() {
      if (this.multipleSelection.length > 0) {
        const tHeader = ["id"];
        const filterVal = ["id"];
        this.tableTitle.forEach(function (item) {
          tHeader.push(item.label);
          filterVal.push(item.name);
        });
        let exportData = this.formatJson(this.multipleSelection, filterVal);
        downloadFile({
          tHeader: tHeader,
          fileName: "用户列表",
          exportData: exportData,
        });
      } else {
        this.$message.error("请在用户列表中勾选需要导出的用户");
      }
    },
    formatJson(dataList, filterVal) {
      return dataList.map((v) =>
        filterVal.map((j) => {
          if (j === "status") {
            return v[j] ? '已启用' : '已冻结';
          } else {
            return v[j];
          }
        })
      );
    },
  },
  created() {
    getUserType().then(res => {
      let tabList = []
      res.forEach(item => {
        tabList.push({
          name: item.desc,
          value: item.code,
          hide: !checkPermission(['admin', this.tabPermission[item.code]])
        })
      })
      this.tabList = tabList
      this.getlist();
    })
  },
  mounted() {
    this.initTbaleTitle();
  },
  components: {
    pagenation,
    TabsLayout
  },
};
</script>

<style lang="less" scoped>
.archivesPageContent {
  padding: 0;
  .temp_searchBox {
    height: 64px;
    overflow: hidden;
    margin-bottom: 0;
  }
  .form-inline {
    height: 60px;
    overflow: hidden;
  }
  .title {
    border-bottom: 2px solid #ebecee;
    margin-bottom: 16px;
    span {
      margin-bottom: -2px;
      padding: 0 15px;
      height: 40px;
      line-height: 30px;
      display: block;
      background: rgba(255, 255, 255, 0);
      border-bottom: 2px solid rgb(64, 158, 255);
      font-size: 16px;
      font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
        "PingFang SC"-400;
      font-weight: 400;
      color: rgb(64, 158, 255);
    }
  }
  .formItem {
    width: 586px;
  }
  .line {
    color: #dfe6ec;
    margin: 0 6px;
  }
  .typeTabs {
    height: 40px;
    margin-bottom: -2px;
    margin-left: 30px;
  }
}
</style>
