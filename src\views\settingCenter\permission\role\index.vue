<template>
  <div class="rolePageContent">
    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        :tabs="[ { name: '角色管理' } ]"
      >
        <template slot="button">
          <el-button @click="getList()" size="medium">刷新</el-button>
          <el-button size="medium" type="primary" v-if="checkPermission(['admin','admin-setting-permission-role:add'])"  @click="editClickFun(null)">+新增角色</el-button>
        </template>
      </tabs-layout>
      <div class="table">
        <el-table
          v-loading="listLoading"
          :data="list"
          row-key="id"
          border
          fit
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column
            align="center"
            width="65"
            show-overflow-tooltip
            :render-header="renderHeader"
            fixed
          >
            <template slot-scope="scope">
              <span>{{ scope.$index+1 }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-for="(item, index) in tableTitle"
            :key="index"
            :width="item.width"
            :min-width="(item.width?item.width:'350px')"
            :label="item.label"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              <span v-if="item.name=='status'">{{ row[item.name]?'启用':'禁用' }}</span>
              <span v-else>{{ row[item.name] }}</span>
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            align="center"
            label="操作"
            width="150"
            class="itemAction"
          >
            <template slot-scope="{row}">
              <el-row class="table-edit-row">
                <span v-if="checkPermission(['admin','admin-setting-permission-role:edit'])" class="table-edit-row-item">
                  <el-button @click="editClickFun(row)" type="text" >编辑</el-button>
                </span>
                <span v-if="checkPermission(['admin','admin-setting-permission-role:del'])" class="table-edit-row-item">
                  <el-button type="text"  @click="deleteFun(row)">删除</el-button>
                </span>
              </el-row>
            </template>
          </el-table-column>
        </el-table>
        <pagination  v-if="total>0" :pageSizes="[ 10, 20, 50]" :total="total" :page.sync="listQuery.current" :limit.sync="listQuery.size" @pagination="getList" />
      </div>
    </div>
    <!-- 设置 编辑 -->
    <el-dialog :close-on-click-modal="false" v-if="showEdit" :title="(row.id>0?'编辑':'新增')+'角色'" :show-close="false" :visible.sync="showEdit" :width="'1000px'" >
        <edit :visible.sync="showEdit" :isReload.sync="submitReload" :row.sync="row"></edit>
    </el-dialog>
    <!-- 设置 编辑 -->
  </div>
</template>
<script>
import {setContextData,getContextData} from '@/utils/auth'
import checkPermission from '@/utils/permission'
import { list,deleteApi } from '@/api/setting/permission/userRole'
import edit from '@/views/settingCenter/permission/role/edit'
import Pagination from '@/components/Pagination'
import TabsLayout from '@/components/TabsLayout'

export default {
  data() {
    return {
      showEdit:false,
      row:{
          id:0,
          type:"",
          name:"",
      },

      tableTitle:[
        {
          label:'角色名称	',
          name: "name",
          width:'150px'
        },
        {
          label:'角色状态	',
          name: "status",
          width:'150px'
        },
        {
          label:'描述',
          name: "describe",
        }
      ],
      submitReload:false,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        model:{},
        current: 1,
        size: 10
      },
    };
  },
  watch:  {
    submitReload:function(newVal,oldVal){
      if(newVal){
        this.submitReload=false;
        this.getList()
      }
    }
  },
  components: {
    Pagination,
    edit,
    TabsLayout
  },
  filters:{},
  beforeRouteEnter (to, from, next) {
    next(vm => {
      if(from.path=="/settingCenter/permission/role/edit"){
        vm.listQuery=getContextData("permission_role_list")
      }
      vm.getList()
    })
  },
  methods: {
    checkPermission,
    editClickFun(row) {
      setContextData("permission_role_list",this.listQuery)
      this.$router.push({path:"/settingCenter/permission/role/edit",
        query: {
          id: row==null?undefined:row.id
        }
      })
    },
    deleteFun(row){
      var _this=this;
      this.$confirm('此操作将永久删除该信息, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        _this.actionDeleteFun(row)
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    async actionDeleteFun(row) {
      var data= await deleteApi(row.id)
      if(data.code==0){
          this.listQuery.current=1;
          this.getList()
      }
    },
    renderHeader (h,{column}) {
      return (
        <div style="position:relative">
          <div>
            <i class="el-icon-menu" />
          </div>
        </div>
      )
    },
    setHeaer:function(){
      this.showSelectTitle=!this.showSelectTitle
    },
    async getList() {
      this.listLoading = true
      const { data } = await list(this.listQuery)
      this.list = data.records
      this.total = data.total
      this.listLoading = false
    }
  },
  mounted() {
      this.getList()
  },
  beforeDestroy() {}
};
</script>
<style lang="scss" scoped>
@import "@/styles/element-variables.scss";

.rolePageContent {
  padding: 0;
  .title{
      border-bottom:2px solid #EBECEE;
    margin-bottom: 16px;
      span{
        margin-bottom: -2px;
        padding:0 15px;
        height: 40px;
        line-height: 30px;
        display:block;
        background: rgba(255,255,255,0);
        border-bottom:2px solid rgb(64, 158, 255);
        font-size: 16px;
        font-family: 'PingFangSC-Regular', 'PingFang SC', 'PingFangSC-Regular', 'PingFang SC'-400;
        font-weight: 400;
        color:rgb(64, 158, 255);
      }
  }
  .formItem{width:586px;}
  .line{color:#dfe6ec; margin:0 6px;}
}
</style>
