<template>
  <div class="temp_RejectButton">
    <el-button     type="text" @click="showExamineDialog = true" >审核</el-button>
    <el-dialog
      :close-on-click-modal="false"
      append-to-body
      title="审核产品"
      :visible.sync="showExamineDialog"
      :before-close="closeDialogFun"
      width="400px"
    >
    <span>该产品是否确认通过？</span>
    <span slot="footer" class="dialog-footer">
      <el-button @click="showExamineDialog = false">取 消</el-button>
      <RejectButton :buttonType="''" :rejectValue="rejectValue" @submitCallbackFun="submitRejectFun"  @clearCallbackFun="clearRejectFun"></RejectButton>
      <el-button type="primary" @click="submitToStore()">确 定</el-button>
    </span>
    </el-dialog>
  </div>
</template>
<script>
import RejectButton from '@/components/eyaolink/Product/RejectButton'
import { list,pageCount,rejectProductPlatformApi,acceptProductPlatformApi } from '@/api/products/archives'
export default {
  data() {
    return {
        showExamineDialog: false,
        rejectValue:""
    };
  },
  components:{
    RejectButton
  },
  props: {
    row: {
      type: Object
    },
    isReload: {
      type: Boolean,
      default: false,
      required: true
    }
  },
  methods: {
    closeDialogFun(){
      this.showExamineDialog=false;
    },
    submitToStore: async function() { //审核通过提交
      var data= await acceptProductPlatformApi(
        {
          "list": [
            {
              id:this.row.id,
              approvalStatus:"ACCEPTED",
              rejectReason: "通过"
            }
          ]
        })
      if(data.code==0){
        this.$message({
          message: '提交审核完成！',
          type: 'success',
          duration: 1500
        });
        this.showExamineDialog= false,
        this.$emit("update:isReload", true);
      }
    },
     submitRejectFun: async function(obj){
      var data= await rejectProductPlatformApi(
        {
        "list": [
            {
              id:this.row.id,
              approvalStatus:"REJECTED",
              rejectReason: obj.rejectValueInfo
            }
          ]
        })
      if(data.code==0){
        this.$message({
          message: '提交驳回完成！',
          type: 'success',
          duration: 1500
        });
        this.showExamineDialog= false,
        this.$emit("update:isReload", true);
      }

    },
    clearRejectFun:function(){
        console.info("clearRejectFun")
    }
  },
  mounted() {},
  beforeDestroy() {}
};
</script>
<style lang="less" scoped>
.temp_RejectButton {
  display: inline;
  margin: 0 8px;

}

</style>
