<template>
  <div class="archivesPageContent">
    <im-search-pad
      :has-expand="false"
      :model="listQuery"
      @reset="resetForm"
      @search="onSearchSubmitFun"
    >
      <im-search-pad-item prop="title">
        <el-input v-model="listQuery.model.title" placeholder="请输入资讯标题" />
      </im-search-pad-item>
      <im-search-pad-item prop="timePicker">
        <el-date-picker v-model="timePicker" type="daterange" align="right" unlink-panels range-separator="至" start-placeholder="查询起始日期" end-placeholder="查询结束日期" :picker-options="pickerOptions"
                        value-format="yyyy-MM-dd" @change="selectTime">
        </el-date-picker>
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        :tabs="[ { name: '公告通知' } ]"
        @change="handleChangeTab"
      >
        <template slot="button">
          <el-button v-if="checkPermission(['admin', 'admin-content-notic:add'])" type="primary" @click="newFun">+新增公告</el-button>
        </template>
      </tabs-layout>
      <div class="table">
        <el-table v-if="list" v-loading="listLoading" :data="list" row-key="id" border fit highlight-current-row style="width: 100%">
          <el-table-column align="center" width="65" :render-header="renderHeader">
            <template slot-scope="scope">
              <span>{{ scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column v-for="(item, index) in tableTitle" :key="index" :min-width="(item.width?item.width:'350px')" :label="item.label" show-overflow-tooltip align="left">
            <template slot-scope="{row}">
            <span v-if="item.name == 'publishStatus'">
              <el-switch v-if="checkPermission(['admin', 'announcement:release'])" v-model="row[item.name].type" @change="swchange(row)"> </el-switch>
              <span v-else :style="row[item.name].code == 'Y' ? '' : 'color:#ff0036'">{{ row[item.name].code == 'Y' ? '已发布' : '未发布' }}</span>
            </span>
              <span v-else>{{ row[item.name] }}</span>
            </template>
          </el-table-column>

          <el-table-column fixed="right" align="center" label="操作" width="160" class="itemAction">
            <template slot-scope="scope">
              <el-row class="table-edit-row">
                <span v-if="checkPermission(['admin', 'admin-content-notic:edit'])" class="table-edit-row-item">
                  <el-button @click="edit(scope.row)" type="text" >编辑</el-button>
                </span>
                <span v-if="checkPermission(['admin', 'admin-content-notic:del'])" class="table-edit-row-item">
                  <el-button @click="del(scope.row)" type="text" >删除</el-button>
                </span>
              </el-row>
              <!-- <el-button @click="detailFun(scope.row)" type="text" >预览</el-button> -->
            </template>
          </el-table-column>
        </el-table>
        <pagenation v-if="total > 0" :pageSizes="[2, 4, 20, 50]" :total="total" :page.sync="listQuery.current" :limit.sync="listQuery.size" @pagination="getlist" />
      </div>
    </div>
    <!-- 设置 编辑 -->
    <!-- <el-dialog v-if="showEditPage" :title="(artItem.id?'编辑':'新增')+'公告'" :visible.sync="showEditPage" width="80%"  :show-close="true">
      <edit :visible.sync="showEditPage" :isReload.sync="submitReload" :artshowFlag.sync="artflag" :row.sync="artItem"></edit>
    </el-dialog> -->
    <!-- 设置 编辑 -->
    <!-- 预览 -->
    <!-- <el-dialog v-if="artflag" :title="artItem.title" :visible.sync="artflag" width="70%" :show-close="true"  center :before-close="artclose">
      <div v-loading="artLoading">
        <div class="artTime" v-if="artItem.createTime">创建时间： {{artItem.createTime}}</div>
        <div v-html="artItem.content"></div>
      </div>
    </el-dialog> -->
    <!-- 预览 -->
  </div>
</template>

<script>
import checkPermission from "@/utils/permission";
import {
  list,
  getitem,
  del,
  start,
  disable,
} from "@/api/contentCenter/announcement";
import { setContextData, getContextData } from "@/utils/auth";
import pagenation from "@/components/Pagination";
// import edit from "@/views/contentCenter/announcement/edit";
import { enable } from "@/api/businessCenter/businessList";
import TabsLayout from '@/components/TabsLayout'

export default {
  data() {
    return {
      list: [],
      tabs: "list",
      showEditPage: false,
      tableTitle: [
        {
          label: "公告标题",
          name: "title",
          width: "300px",
        },
        {
          label: "发布状态",
          name: "publishStatus",
          width: "100px",
        },
        // {
        //   label: "浏览数",
        //   name: "sfsf",
        //   width: "240px",
        // },
        {
          label: "创建人",
          name: "createUser",
          width: "220px",
        },
        {
          label: "创建时间",
          name: "createTime",
          width: "270px",
        },
      ],
      listQuery: {
        current: 1,
        size: 10,
        model: {},
        map: {},
      },
      pickerOptions: {
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      timePicker: [],
      total: 0,
      listLoading: false,
      submitReload: "",
      artflag: false,
      artItem: {
        title: "",
      },
      artLoading: false,
    };
  },
  methods: {
    checkPermission,
    // 获取列表
    async getlist() {
      this.listLoading = true;
      let { data } = await list(this.listQuery);
      this.total = data.total;
      data.records.forEach((item) => {
        if (item.publishStatus.code == "Y") {
          item.publishStatus.type = true;
        } else {
          item.publishStatus.type = false;
        }
      });
      this.list = data.records;
      console.log(this.list);
      this.listLoading = false;
    },
    edit(row) {
      // this.artItem = row;
      // this.showEditPage = true;
      setContextData("announcementEdit", this.listQuery);
      this.$router.push({
        path: "/contentCenter/announcementEdit",
        query: {
          id: row.id,
        },
      });
    },
    // 删除公告
    del(row) {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          let { data } = await del({ ids: [row.id] });
          // console.log(data)
          if (data) {
            this.list.forEach((item, index) => {
              if (item.id == row.id) {
                this.list.splice(index, 1);
                this.$message.success('已删除 "' + row.title + '"');
              }
            });
          }
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    async swchange(row) {
      if (row.publishStatus.type) {
        let { data } = await start(row.id);
        this.$message.success(`已将 ${row.title} 的发布状态修改为发布`);
        if (!data) {
          row.publishStatus.type = !row.publishStatus.type;
        }
      } else {
        let { data } = await disable(row.id);
        this.$message.success(`已将 ${row.title} 的发布状态修改为未发布`);
      }
    },
    // async detailFun(row) {
    //   this.artflag = true;
    //   this.artLoading = true;
    //   let { data } = await getitem(row.id);
    //   this.artItem = data;
    //   this.artLoading = false;
    // },
    artclose() {
      this.artItem = {};
      this.artflag = false;
    },
    selectTime(e) {
      this.listQuery.map.createTime_st = e[0];
      this.listQuery.map.createTime_ed = e[1];
    },
    onSearchSubmitFun() {
      this.getlist();
    },
    resetForm() {
      this.listQuery = {
        current: 1,
        size: 10,
        model: {},
        map: {},
      };
      this.timePicker = [];
      this.getlist();
    },
    newFun() {
      // this.showEditPage = true;
      // this.artItem = {};
      setContextData("announcementEdit", this.listQuery);
      this.$router.push({
        path: "/contentCenter/announcementEdit",
      });
    },
    renderHeader(h, { column }) {
      return (
        <div style="position:relative">
          <i class="el-icon-menu" />
        </div>
      );
    },
  },
  created() {},
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (from.path == "/contentCenter/announcementEdit") {
        if (getContextData("announcementEdit") != "") {
          vm.listQuery = getContextData("announcementEdit");
        }
      }
      // vm.initTbaleTitle();
      vm.getlist();
    });
  },
  components: {
    pagenation,
    TabsLayout
    // edit,
  },
};
</script>

<style lang="less" scoped>
.archivesPageContent {
  padding: 0;
  .temp_searchBox {
    height: 64px;
    overflow: hidden;
    margin-bottom: 0;
  }
  .form-inline {
    height: 60px;
    overflow: hidden;
  }
  .title {
    border-bottom: 2px solid #ebecee;
    margin-bottom: 16px;
    span {
      margin-bottom: -2px;
      padding: 0 15px;
      height: 40px;
      line-height: 30px;
      display: block;
      background: rgba(255, 255, 255, 0);
      border-bottom: 2px solid rgb(64, 158, 255);
      font-size: 16px;
      font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
        "PingFang SC"-400;
      font-weight: 400;
      color: rgb(64, 158, 255);
    }
  }
  .artTime {
    text-align: center;
    color: #969696;
  }
  .formItem {
    width: 586px;
  }
  .line {
    color: #dfe6ec;
    margin: 0 6px;
  }
  .typeTabs {
    height: 40px;
    margin-bottom: -2px;
    margin-left: 30px;
  }
}
</style>
