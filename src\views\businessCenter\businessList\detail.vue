<template>
  <div class="archivesEditContent">
    <el-form :inline="true" label-width="140px" :model="query" ref="editForm" :rules="rules">
      <page-title title="商家档案">
        <template>
          <template>
            <template v-if="$route.query.tabType == 'PENDING'">
              <el-popover v-model="rejectFlag" placement="bottom-end" title="驳回理由" width="300" trigger="click">
                <el-button slot="reference" v-if="checkPermission(['admin', 'admin-merchant:reject'])">驳回</el-button>
                <el-form ref="rejectform" :model="rejectText">
                  <el-form-item prop="text"
                                :rules="[{required: true, message: '请填写驳回理由',trigger: 'blur'},{required: true,min:5, message: '请至少填写5个字！',trigger: 'blur'}]">
                    <el-input type="textarea" :rows="3" placeholder="请输入驳回理由" v-model="rejectText.text">
                    </el-input>
                  </el-form-item>
                </el-form>
                <div style="text-align: right; margin: 0;padding-top:14px">
                  <el-button size="mini" @click="rejectFlag = false">取消</el-button>
                  <el-button type="primary" size="mini" @click="rejected">确定</el-button>
                </div>
              </el-popover>
              <el-button type="primary" v-if="checkPermission(['admin', 'admin-merchant:accept'])" @click="accepted">通 过</el-button>
            </template>
            <template v-else-if="$route.query.tabType == 'REJECTED'">
              <el-popover v-model="rejectFlag" placement="bottom-end" title="驳回理由" width="300" trigger="click">
                <el-button slot="reference">驳回理由</el-button>
                {{query.rejectReason}}
                <div style="text-align: right; margin: 0;padding-top:14px">
                  <el-button type="primary" size="mini" @click="rejectFlag = false">知道了</el-button>
                </div>
              </el-popover>
            </template>
            <template v-else-if="$route.query.tabType == 'ACCEPTED'&&query.publishStatus">
              <el-button v-if="checkPermission(['admin', 'admin-merchant:freeze'])" v-show="query.publishStatus.code == 'Y'"
                         @click="frozen">冻结
              </el-button>
              <el-button v-if="checkPermission(['admin', 'admin-merchant:release'])" @click="enable"
                         v-show="query.publishStatus.code == 'N'">启用
              </el-button>
            </template>
          </template>
          <el-button v-if="checkPermission(['admin', 'admin-merchant:edit'])" @click="editItem">编辑</el-button>
        </template>
      </page-title>
      <div class="item">
        <page-module-title title="基础信息"/>
        <el-row>
          <el-col :span="6">
            <el-form-item class="formItem" label="商家类型:">
              {{merchantType}}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" label="商家编码:">
              {{query.code}}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" prop="productNumber" label="商家名称:"
                          :rules="[{ required: true, message: '请填写产品名称',trigger: 'blur' }]">
              {{query.name}}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" prop="productNumber" label="商家识别码:"
                          :rules="[{ required: true, message: '请填写助记码',trigger: 'blur' }]">
              {{query.identifyCode}}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item class="formItem" prop="productNumber" label="社会统一信用代码:"
                          :rules="[{ required: true, message: '请选择经营类目',trigger: 'blur' }]">
              {{query.socialCreditCode}}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" prop="productNumber" label="法定代表人:">
              {{query.legalPerson}}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" prop="productNumber" label="负责人:"
                          :rules="[{ required: true, message: '请填写产品分类',trigger: 'blur' }]">
              {{query.ceoName}}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" prop="productNumber" label="负责人手机:">
              {{query.ceoMobile}}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item class="formItem" prop="productNumber" label="所在区域:"
                          :rules="[{ required: true, message: '请填写产品品牌',trigger: 'blur' }]">
              {{query.region}}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" prop="productNumber" label="注册资金:">
              {{query.registerCapital}} (万)
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" prop="productNumber" label="店铺名称:"
                          :rules="[{ required: true, message: '请填写生产厂家',trigger: 'blur' }]">
              {{query.shopName}}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" prop="productNumber" label="经营开始时间:"
                          :rules="[{ required: true, message: '请填写产品计量单位',trigger: 'blur' }]">
              {{query.managementStartDate}}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item class="formItem" prop="productNumber" label="经营结束时间:"
                          :rules="[{ required: true, message: '请填写产地',trigger: 'blur' }]">
              {{query.managementEndDate}}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" prop="productNumber" label="注册地址:"
                      :rules="[{ required: true, message: '请填写产品剂型',trigger: 'blur' }]">
          {{query.registerAddress}}
        </el-form-item>
          </el-col>
        </el-row>
      </div>

      <div class="item">
        <page-module-title title="账户信息"/>
        <el-row>
          <el-col :span="6">
            <el-form-item class="formItem" prop="productNumber" label="登录账号:"
                          :rules="[{ required: true, message: '请填写产地',trigger: 'blur' }]">
              {{query.loginAccount}}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" prop="productNumber" label="手机号码:"
                          :rules="[{ required: true, message: '请填写产地',trigger: 'blur' }]">
              {{query.userMobile}}
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <div class="item">
        <page-module-title title="结款信息"/>
        <el-row>
          <el-col :span="6">
            <el-form-item class="formItem" prop="bankAccount" label="银行账户:"
                          :rules="[{ required: true, message: '请填写产地',trigger: 'blur' }]">
              {{query.bankAccount}}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" prop="bankNumber" label="银行账号:"
                          :rules="[{ required: true, message: '请填写产地',trigger: 'blur' }]">
              {{query.bankNumber}}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" prop="bankName" label="开户银行:"
                          :rules="[{ required: true, message: '请填写产地',trigger: 'blur' }]">
              {{query.bankName}}
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <div class="item">
        <page-module-title title="经营类目"/>
        <div style="padding: 10px 30px 30px 20px" class="cateGory">
          <el-row :gutter="20" v-if="cateGory" style="width:100%">
            <el-col v-for="(ids, keys) in cateGory" :key="keys" :span="8"
                    :style="'width:' + getFlexNum()+'%;flex:1;display:flex;flex-wrap:wrap;'">
              <span>{{keys + ':'}}</span> <span style="white-space: nowrap;color:#aaaaaa" v-for="ite in ids"
                                                :key="ite.id">{{ite.label + '、'}}</span>
            </el-col>
          </el-row>
          <span style="padding-left:30px;color:#a9a9ac" v-else>无</span>
        </div>
      </div>

      <div class="item">
        <page-module-title title="结算规则"/>
        <div>
          <el-form-item label-width="150px" label="佣金计算规则: " :rules="[{message: '请选择佣金计算规则'}]">
            <el-radio-group v-model="query.statementRule" :disabled="true">
              <el-radio v-if="query.statementRule == 'N'" label="N">不收取佣金</el-radio>
              <el-radio v-if="query.statementRule == 'Y'" label="Y">按订单金额比例</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div>
          <el-form-item v-if="query.statementRule=='Y'" label-width="150px" class="formItem" prop="orderAmountRate"
                        label="金额比例:">
            <el-input style="width: 200px" disabled v-model="query.orderAmountRate" placeholder="请填写金额比例">
              <i slot="suffix">%</i>
            </el-input>
          </el-form-item>
        </div>
      </div>
      <div class="item">
        <page-module-title title="商家资质"/>
        <lisence-table v-if="lisenceTableDate.length!=0" :lisenceTableDate.sync="lisenceTableDate"
                       :edit="['admin','business-qualification:edit']"
                       :confirm="['admin','business-qualification:submit']"/>
      </div>

      <div class="item">
        <page-module-title title="采购资质"/>
        <procurement-table v-if="newLisenceTableDate.length!=0" :newLisenceTableDate.sync="newLisenceTableDate"
                           :edit="['admin','business-qualification:edit']"
                           :confirm="['admin','business-qualification:submit']"/>
      </div>


      <div class="item">
        <page-module-title title="发票信息"/>
        <template>
          <div>
            <el-form-item class="formItem" prop="fpinvoiceType" label="发票类型:"
                          :rules="[{ required: true, message: '请选择发票类型', trigger: 'blur' },]">
              <el-radio disabled v-model="isInvoiceInfo" label="VATINVOICE">普通发票</el-radio>
              <el-radio disabled v-model="isInvoiceInfo" label="SPECIALINVOICE">增值税专用发票</el-radio>
            </el-form-item>
          </div>
          <div>
            <el-form-item class="formItem" prop="name" label="发票抬头:"
                          :rules="[{ required: true, message: '请填写发票抬头', trigger: 'blur' },]">
              <span class="graytext">{{query.invoiceInfo=='null'?'':query.invoiceInfo.name}}</span>
            </el-form-item>
          </div>
          <div>
            <el-form-item class="formItem" prop="taxNumber" label="税号:"
                          :rules="[{ required: true, message: '请填写税号', trigger: 'blur' },]">
              <span class="graytext">{{query.invoiceInfo.taxNumber}}</span>
            </el-form-item>
          </div>

          <div v-if="isInvoiceInfo== 'SPECIALINVOICE'">
            <el-form-item class="formItem" prop="registerAddress" label="注册地址:"
                          :rules="[{ required: true, message: '请填写注册地址', trigger: 'blur' },]">
              <span class="graytext">{{query.invoiceInfo.registerAddress}}</span>
            </el-form-item>
          </div>

          <div v-if="isInvoiceInfo== 'SPECIALINVOICE'">
            <el-form-item class="formItem" prop="registerMobile" label="注册电话:"
                          :rules="[{ required: true, message: '请填写注册电话', trigger: 'blur' },]">
              <span class="graytext">{{query.invoiceInfo.registerMobile}}</span>
            </el-form-item>
          </div>

          <div v-if="isInvoiceInfo== 'SPECIALINVOICE'">
            <el-form-item class="formItem" prop="bankNumber" label="银行账号:"
                          :rules="[{ required: true, message: '请填写银行账号', trigger: 'blur' },]">
              <span class="graytext">{{query.invoiceInfo.bankNumber}}</span>
            </el-form-item>
          </div>

          <div v-if="isInvoiceInfo=='SPECIALINVOICE'">
            <el-form-item class="formItem" prop="depositBank" label="开户银行:"
                          :rules="[{ required: true, message: '请填写开户银行', trigger: 'blur' },]">
              <span class="graytext">{{query.invoiceInfo.depositBank}}</span>
            </el-form-item>
          </div>
        </template>
      </div>


      <div class="item">
        <page-module-title title="发货地址"/>
        <template>
          <addrTable :addrtableDate.sync="addrtableDate" :edit="['admin','business-address:edit']"
                     :delete="['admin','business-address:delete']" :add="['admin','business-address:add']"
                     :id="$route.query.id"/>
        </template>
      </div>

    </el-form>
<!--    <es-table-pager></es-table-pager>-->
  </div>
</template>
<script>
  import checkPermission from "@/utils/permission";
  import {setContextData, getContextData} from "@/utils/auth";
  import {getToken} from "@/utils/auth";
  import {checkNumPot2} from "@/utils/rules";
  import rule from "@/utils/rules";
  import addrTable from "./addrTable";
  import {
    getitem,
    enable,
    frozen,
    rejected,
    pdateSaleMerchantAcceptedById,
    listByLicenseBaseType,
    areas, listByLicenseMerChan,
  } from "@/api/businessCenter/businessList";
  import LisenceTable from "./lisenceTable.vue";
  import ProcurementTable from "./procurementTable.vue";

  export default {
    data() {
      var checkNumPot3 = (rule, value, callback) => {
        const reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
        if (!value) {
          return callback(new Error("请填写数字"));
        } else if (!reg.test(value)) {
          return callback(new Error("请填写数字,最多2位小数"));
        } else if (value <= 0 || value > 100) {
          return callback(new Error("请填写0-100以内的数"));
        } else {
          callback();
        }
      };
      var validatePass2 = (rule, value, callback) => {
        if (value !== this.query.password) {
          callback(new Error("两次输入密码不一致!"));
        }
        callback();
      };
      return {
        arealist: [],
        isInvoiceInfo: "",
        merchantType: '',
        props: {
          lazy: true,
          async lazyLoad(node, resolve) {
            const {level} = node;
            let id = node.data ? node.data.id : "";
            let res = await areas({parentId: id});
            let list = res.data;
            list.forEach((item) => {
              item.value = item.id;
              item.leaf = level >= 2;
            });
            resolve(list);
          },
        },
        rules: {
          password: [
            {required: true, message: "确认密码不能为空", trigger: "blur"},
            {
              required: true,
              min: 6,
              max: 20,
              message: "长度在 6 到 20 个字符",
              trigger: "blur",
            },
          ],
          confirmPassword: [
            {required: true, message: "确认密码不能为空", trigger: "blur"},
            {
              required: true,
              min: 6,
              max: 20,
              message: "长度在 6 到 20 个字符",
              trigger: "blur",
            },
            {validator: validatePass2, trigger: "blur"},
          ],
          registerCapital: [
            {validator: checkNumPot2, trigger: "blur", required: true},
          ],
          ceoMobile: rule.phone,
          orderAmountRate: [
            {validator: checkNumPot3, trigger: "blur", required: true},
          ],
        },
        addrRules: {
          mobilPhone: rule.phone,
        },
        rejectFlag: false,
        listLoading: false,
        checkList: [],
        businessScope: {},
        editBusiness: false,
        tableDate: [],
        businessTableLabel: [
          {
            name: "label",
            label: "证件类型",
            width: "320px",
          },
          {
            name: "licenseNumber",
            label: "证件号",
            width: "300px",
          },
          {
            name: "licenseEndTime",
            label: "过期时间",
            width: "300px",
          },
          {
            name: "filePath",
            label: "附件",
            width: "340px",
          },
        ],
        editCertificates: false,
        dialogImageUrl: "",
        query: {
          invoiceType:"VATINVOICE",
          invoiceInfo:{}
        },
        statementRule: "N",
        baseType: [],
        // 预览Dialog
        imgDialogFlag: false,
        //  商家资质编辑Dialog
        editFunItem: {},
        rejectText: {},
        // 地址
        adAddr: {},
        addrtableDate: [],
        addrSelectText: "",
        editLisenceItem: {},
        lisenceTableDate: [],
        newLisenceTableDate: [],
        insertProgram: {
          folderId: 0,
        },
        headersProgram: {
          token: getToken(),
          Authorization: "Basic YWRtaW5fdWk6YWRtaW5fdWlfc2VjcmV0",
        },
        lisenceClickItem: {},
        oldAddrtableDate: [],
        areasTree: [],
        cateGory: false,
      };
    },
    methods: {
      checkPermission,
      getFlexNum() {
        let num = 0
        this.cateGory
        for (const key in this.cateGory) {
          num++
        }
        if (num == 1) {
          return 100
        } else if (num == 2) {
          return 50
        } else {
          return 33
        }
      },
      back() {
        this.$router.go(-1);
        this.$store.dispatch("tagsView/delView", this.$route);
      },
      editItem() {
        this.$router.push({
          path: "/businessCenter/businessArchives/list/edit",
          query: {
            tabType: this.$route.query.tabType,
            id: this.$route.query.id,
          },
        });
      },
      async getareas() {
        let {data} = await areas();
        this.areasTree = data;
      },
      getsrc(str) {
        if (!str) {
          return [];
        } else {
          let arr = str.split(",");
          let list = [];
          arr.forEach((item) => {
            let obj = {
              response: {
                data: {
                  url: "",
                },
              },
            };
            // 
            console.log('99999999',item);
            // obj.response.data.url = item?(item.indexOf('http')>1?item:`${this.$filePathHost}${item}`):"";
            // obj.url = item?(item.indexOf('http')>1?item:`${this.$filePathHost}${item}`):"";
            obj.response.data.url = item;
            obj.url = item;
            list.push(obj);
          });
          return list;
        }
      },
      resetSucss(type, msg, tabType) {
        if (type) {
          this.$message.success(msg);
          let listQuery = {
            current: 1,
            size: 10,
            model: {
              approvalStatus: {code: tabType},
            },
          };
          this.$store.dispatch("tagsView/delView", this.$route);
          setContextData("businessArchives_list", listQuery);
          this.$router.go(-1);
        }
      },
      // 启用
      async enable() {
        let {data} = await enable(this.query.id);
        let listQuery = getContextData("businessArchives_list");
        setContextData("businessArchives_list", listQuery);
        this.$router.go(-1);
      },
      // 冻结
      async frozen() {
        let {data} = await frozen(this.query.id);
        let listQuery = getContextData("businessArchives_list");
        setContextData("businessArchives_list", listQuery);
        this.$router.go(-1);
      },
      // 通过审核
      async accepted() {
        let {data} = await pdateSaleMerchantAcceptedById(this.query.id);
        this.resetSucss(data, "已通过该商家信息！", "ACCEPTED");
      },
      // 驳回
      rejected() {
        this.$refs.rejectform.validate(async (valid) => {
          if (valid) {
            let {data} = await rejected({
              id: this.query.id,
              rejectReason: this.rejectText.text,
            });
            this.resetSucss(data, "已驳回商家信息！", "REJECTED");
          }
        });
      },
      checkListFun(e) {
        this.query.businessCategoryId = e;
      },

      async getitem() {
        this.listLoading = true;
        if (!this.$route.query.id) {
          let tableDate = (await listByLicenseBaseType()).data;
          console.log(tableDate, "tableDate===");
          let newTableDate = (await listByLicenseMerChan()).data;
          console.log(newTableDate, "newTableDate====");
          tableDate.forEach((item) => {
            let obj = {
              licenseBaseId: item.id,
              licenseEndTime: "",
              filePath: "",
              licenseNumber: "",
              label: item.name,
              isEdit: false,
              filePathList: this.getsrc(item.filePath),
              limit: item.multiple.code == "Y" ? 5 : 1,
            };
            this.lisenceTableDate.push(obj);
          });
          newTableDate.forEach((item) => {
            let obj = {
              licenseBaseId: item.id,
              licenseEndTime: "",
              filePath: "",
              licenseNumber: "",
              label: item.name,
              isEdit: false,
              filePathList: this.getsrc(item.filePath),
              limit: item.multiple.code == "Y" ? 5 : 1,
            };
            this.newLisenceTableDate.push(obj);
          });
          this.listLoading = false;
          this.query.deliveryAddressSaveDTOList = [];
          return;
        }
        let { data } = await getitem(this.$route.query.id);
        switch (data.commerceModel.code) {
          case 'SAAS_PLATFORM':
            this.merchantType = '平台商家'
            break
          case 'SAAS':
            this.merchantType = 'SAAS商家'
            break
          default:
            this.merchantType = '推广商家'
        }
        let list = (await listByLicenseBaseType()).data;
        let newList = (await listByLicenseMerChan()).data;
        list.forEach((item) => {
          let obj = {
            licenseBaseId: item.id,
            licenseEndTime: "",
            filePath: "",
            isForever: "",
            licenseNumber: "",
            label: item.name,
            isEdit: false,
            id: "",
            filePathList: [],
            limit: item.multiple.code == "Y" ? 5 : 1,
          };
          data.merchantLicenses.find((ids) => {
            if (item.id == ids.licenseBaseId) {
              obj.licenseEndTime = ids.isForever.code === 'Y' ? '' : ids.licenseEndTime;
              // obj.filePath = ids.filePath?(ids.filePath.indexOf('http')>1?ids.filePath:`${this.$filePathHost}${ids.filePath}`):"";
              obj.filePath = ids.filePath;
              obj.filePathList = this.getsrc(ids.filePath);
              obj.licenseNumber = ids.licenseNumber;
              obj.label = item.name;
              obj.merchantId = ids.merchantId;
              obj.id = ids.id;
              obj.isForever = ids.isForever.code === 'Y'
            }
          });
          this.lisenceTableDate.push(obj);
        });

        newList.forEach((item) => {
          let obj = {
            licenseBaseId: item.id,
            licenseEndTime: "",
            filePath: "",
            isForever: "",
            licenseNumber: "",
            label: item.name,
            isEdit: false,
            id: "",
            filePathList: [],
            limit: item.multiple.code == "Y" ? 5 : 1,
          };
          data.merchantLicenses.find((ids) => {
            if (item.id == ids.licenseBaseId) {
              obj.licenseEndTime = ids.isForever.code === 'Y' ? '' : ids.licenseEndTime;
              // obj.filePath = ids.filePath?(ids.filePath.indexOf('http')>1?ids.filePath:`${this.$filePathHost}${ids.filePath}`):"";
              obj.filePath = ids.filePath;
              obj.filePathList = this.getsrc(ids.filePath);
              obj.licenseNumber = ids.licenseNumber;
              obj.label = item.name;
              obj.merchantId = ids.merchantId;
              obj.id = ids.id;
              obj.isForever = ids.isForever.code === 'Y'
            }
          });
          this.newLisenceTableDate.push(obj);
          console.log("newLisenceTableDate===", this.newLisenceTableDate);
        });

        this.query = data;
        this.query.statementRule = data.statementRule.code;
        try {
          this.query.invoiceInfo = data.invoiceInfo==null? {} :data.invoiceInfo;
          this.isInvoiceInfo = data.invoiceInfo.name==undefined?'VATINVOICE':data.invoiceInfo.invoiceType.code;
        } catch (error) {
          console.log('error',error);
        }
        this.query.regionId = [data.provinceId, data.cityId, data.county_id];
        this.query.deliveryAddressSaveDTOList = [];
        this.tableDate = list;
        this.listLoading = false;
        let obj = {};
        if (!data.businessCategoryDetailList) {
          this.cateGory = false;
        } else {
          data.businessCategoryDetailList.forEach((item) => {
            if (!obj[item.parentName]) {
              obj[item.parentName] = [];
              obj[item.parentName].push(item);
            } else {
              obj[item.parentName].push(item);
            }
          });
          this.cateGory = obj;
        }
      },
    },
    created() {
      this.getareas();
      this.getitem();
    },
    components: {
      LisenceTable,
      addrTable,
      ProcurementTable
    },
    mounted() {
    },
    beforeDestroy() {
    },
  };
</script>
<style lang="less" scoped>
  .archivesEditContent {
    // margin: -30px -20px;
    background-color: #fff;
    border-top: 1px solid #ebecee;
    padding: 0px 20px;

    .item {
      width: 100%;
      margin-bottom: 30px;
      border-bottom: 1px solid #eeeeee;

      .cateGory {
        display: flex;

        .el-col-8 {
          word-break: break-all;
          font-size: 14px;
          line-height: 20px;
          padding-bottom: 10px;
          text-overflow: wrap;
        }
      }

      .title {
        padding: 0 0 15px;

        span {
          font-size: 16px;
          padding-left: 10px;
          border-left: 4px solid rgba(64, 158, 255, 1);
        }
      }

      /deep/ .el-form-item--medium .el-form-item__content {
        text-align: left;
        color: #cdced3;
      }

      /deep/ .el-col {
        line-height: unset;
      }
    }

    .top_title {
      height: 56px;
      line-height: 56px;
      font-family: "PingFangSC-Regular", "PingFang SC", sans-serif;
      font-size: 18px;
      text-align: left;
      border-bottom: 1px solid #eeeeee;
      margin-bottom: 20px;

      .el-button {
        margin-left: 10px;
      }
    }

    .uploadPic {
      padding-bottom: 100%;
      margin-bottom: -100%;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-wrap: wrap;

      > div {
        min-width: 100%;
        height: 25px;
      }
    }

    .productPicContent .text p {
      font-family: "PingFangSC-Regular", "PingFang SC", sans-serif,
      "PingFangSC-Regular", "PingFang SC", sans-serif-400;
      font-weight: 400;
      color: #aaaaaa;
      line-height: 20px;
      font-size: 13px;
      margin: 0;
    }

    .detailMsg {
      font-family: "PingFangSC-Regular", "PingFang SC", sans-serif,
      "PingFangSC-Regular", "PingFang SC", sans-serif-400;
      font-weight: 400;
      color: #aaaaaa;
      line-height: 20px;
      padding-bottom: 20px;
      font-size: 13px;
    }

    .formItem {
      display: flex;
    }

    // /deep/ .el-input.is-disabled .el-input__inner {
    //   color: #04060c;
    //   background-color: #eaf7e7;
    // }
    // /deep/ .is-checked .is-disabled .el-checkbox__inner {
    //   color: #2fa338;
    //   background-color: #1b9e38;
    // }
    // /deep/ .is-checked .el-checkbox__label {
    //   color: #04060c;
    // }
    // /deep/ .is-disabled .is-checked .el-radio__inner {
    //   background-color: #1b9e38;
    // }
    // /deep/ .is-disabled.is-checked.el-radio > .el-radio__label {
    //   color: #04060c;
    // }
    /deep/ .el-col {
      line-height: 40px;
    }

    /deep/ .el-upload {
      width: 40px;
      height: 40px;
      position: relative;
    }

    /deep/ .el-upload > i {
      font-size: 16px;
      position: absolute;
      left: 50%;
      top: 50%;
      -webkit-transform: translateX(-50%) translateY(-50%);
      transform: translateX(-50%) translateY(-50%);
    }

    /deep/ .el-upload-list .el-upload-list__item {
      width: 40px;
      height: 40px;
    }

    /deep/ .hide .el-upload--picture-card {
      display: none;
    }
  }
</style>
