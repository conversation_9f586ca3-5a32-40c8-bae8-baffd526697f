<template>
  <el-dialog title="设置服务机构数" :visible.sync="visible" :close-on-click-modal="false" width="550px">
    <el-form ref="form" :model="form" label-width="100px">
      <div class="sku_setting">
        <el-form-item label="经销商名称:">
          <div style="color:#505465">{{ agencyName }}</div>
        </el-form-item>
        <el-form-item label="服务机构数:" prop="total" :rules="[{ required: true, message: '请输入服务机构数：', trigger: 'blur' }]">
          <el-input v-model="form.total" oninput="value=value.replace(/[^0-9.]/g,'')"/>
        </el-form-item>
      </div>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="hide">取消</el-button>
      <el-button :loading="loading" type="primary" @click="confirm">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { modifyAgencyOrgNo } from '@/api/dealerManagement';

export default {
  name: 'setServiceOrgNum',
  data() { 
    return {
      visible: false,
      loading: false,
      agencyName: '',
      form: {
        total: '',
        id: '',
      }
    }
  },
  methods: {
    show(row) {
      if (this.$refs.form) this.$refs.form.resetFields()
      if (row) {
        this.agencyName = row.agencyName
        Object.keys(this.form).forEach(key => {
          this.$set(this.form, key, row[key])
        })
      }
      this.visible = true
    },
    hide() {
      this.visible = false
    },
    confirm() {
      this.$refs.form.validate((valid) => {
        if (!valid) return
        this.loading = true
        modifyAgencyOrgNo(this.form).then(res => {
          if (res.code !== 0) return
          this.$message.success('修改成功')
          this.hide()
          this.$emit('setNumSuccess')
        }).finally(() => {
          this.loading = false
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.sku_setting {
  display: flex;
  flex-direction: column;
}
</style>
