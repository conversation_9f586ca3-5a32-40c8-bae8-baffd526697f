<template>
  <div >
    <el-table border :data="listData">
      <template v-for="(item,index) in columnData" v-key="index">
          <el-table-column v-if="item.prop =='updateTime'"  :prop="item.prop" :label="item.label" :width="item.width">
            <template slot-scope="{row}">
                <div v-if="item.prop =='updateTime'">{{row[item.prop]}}</div>
            </template>
          </el-table-column>
          <el-table-column v-if="item.prop !='updateTime'"  :prop="item.prop" :label="item.label"  :min-width="item.width">
            <template slot-scope="{row}">
              <div v-if="item.prop =='updateTime'">{{row[item.prop]}}</div>
              <div v-else class="table_column">
                <div>
                  <div>会员推广费：<span
                      v-if="row[item.prop].memberRatio!=null && row[item.prop].memberRatio != ''">{{row[item.prop].memberRatio}}%</span>
                  </div>
                  <div style="margin-top:5px">非会员推广费：<span
                      v-if="row[item.prop].noMemberRatio!=null && row[item.prop].noMemberRatio != ''">{{row[item.prop].noMemberRatio}}%</span>
                  </div>
                </div>
                <div v-if="item.prop != 'sumData'">
                  <i class="el-icon-edit" style="color: blue;cursor:pointer;" @click="openEdit(item,row)"></i>
                </div>
              </div>
            </template>
          </el-table-column>


      </template>
    </el-table>
    <dialogSetting ref="dialogSetting" @submitSetting="submitSetting" :currentRow="currentRow"></dialogSetting>
  </div>
</template>


<script>
  import {
    mapGetters
  } from 'vuex'
  import {
    orgRoleList,
    updateRoleRatio
  } from '@/api/organization/index';
  import dialogSetting from '@/views/team/promote/components/dialogSetting'
  export default {
    //import引入的组件
    components: {
        dialogSetting
    },

    data() {
      return {
        columnData: [], // 表头
        listData: [], // 数据
        otherColumn: [{
            label: '总推广费',
            prop: 'sumData',
            width: 190
          },
          {
            label: '操作时间',
            prop: 'updateTime',
             width: 160
          }
        ],
        currentRow:null
      }
    },
    //生命周期 - 挂载完成（可以访问DOM元素）
    mounted() {},

    computed: {
      ...mapGetters([
        'organizationInfo'
      ]),
    },

    created() {
      this.initData()
    },

    filters: {},

    //方法集合
    methods: {
      async initData() {
          this.columnData = [];
          this.listData = [];
        let params = {
          organizationId: this.organizationInfo.id
        }
        let result = await orgRoleList(params);
        if (result.code != 0 && result.msg != 'ok') {
          return
        };
        let sumMemberRatio = 0; // 总会员推广费
        let sumNoMemberRatio = 0; // 总非会员推广费
        if (result.data && result.data.length > 0) {
          let listObj = {};
          result.data.forEach(element => {
            //   表头
            this.columnData.push(
              Object.assign({}, {
                label: element.name,
                prop: element.id,
                width: 190
              })
            );
            if (element.memberRatio != null && element.noMemberRatio != '') {
              // 计算总会员推广费
              sumMemberRatio += Number(element.memberRatio);
            }
            if (element.noMemberRatio != null && element.noMemberRatio != '') {
              // 总非会员推广费
              sumNoMemberRatio += Number(element.noMemberRatio);
            }
            listObj[element.id] = {
                memberRatio: element.memberRatio,
                noMemberRatio: element.noMemberRatio,
                id: element.id
              },
              listObj['sumData'] = {
                memberRatio: sumMemberRatio,
                noMemberRatio: sumNoMemberRatio
              }
            listObj['item'] = element
          });
          listObj['updateTime'] = result.data[0].updateTime;
          this.columnData = [...this.columnData, ...this.otherColumn];
          this.listData.push(listObj);
        }
      },
      //   开启编辑
      openEdit(item,row) {
        this.currentRow = {
            ...item,
            rowData: row[item.prop]
        };
        this.$refs.dialogSetting.openDia(this.currentRow);
      },
      submitSetting(form){
        if(this.currentRow == null) {
            return
        }
        let params = {
            id: this.currentRow.prop,
            ...form
        }
        updateRoleRatio(params).then(res=>{
            if(res.code == 0 && res.msg == 'ok') {
                this.$message.success('设置推广费成功');
                this.initData();
                this.currentRow = null;
            }
        })
      },
    },

  }

</script>


<style lang='scss' scoped>
  .table_column {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

</style>
