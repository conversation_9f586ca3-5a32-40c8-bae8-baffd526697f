import requestAxios from '@/utils/requestAxios'
import requestExport from '@/utils/requestExport'
import { outlist } from '../businessCenter/businessList'

// 机构服务-分页列表
export function getInstitutionsList(data) {
  return requestAxios({
    url: '/api/org/admin/agencyOrgNo/page',
    method: 'post',
    data
  })
}

// 机构服务 - 修改
export function modifyAgencyOrgNo(data) {
  return requestAxios({
    url: '/api/org/admin/agencyOrgNo',
    method: 'put',
    data
  })
}

// 机构服务 - 详情 - 已服务的机构
export function getInstitutionsDetailList(data) {
  return requestAxios({
    url: '/api/org/admin/agencyOrgInfo/page',
    method: 'post',
    data
  })
}

// 机构服务 - 详情 - 已服务的机构
export function getOrgList(data) {
  return requestAxios({
    url: '/api/org/admin/agencyOrgInfo/page',
    method: 'post',
    data
  })
}

// 机构服务 - 详情 - 新增/编辑服务机构
export function modifyAgencyOrgInfo(data) {
  const method = data.id ? 'put' : 'post'
  return requestAxios({
    url: '/api/org/admin/agencyOrgInfo',
    method,
    data
  })
}

// 机构服务 - 详情 - 新增/编辑机构列表
export function getOrgInfo(agencyId) {
  return requestAxios({
    url: '/api/org/admin/agencyOrgInfo/getOrgInfo/'+agencyId,
    method: 'get',
  })
}

// 机构服务 - 详情 - 关联账号 - 账号列表
export function getAccountList(orgId) {
  return requestAxios({
    url: '/api/org/admin/agencyOrgAccount/getAccountList/'+orgId,
    method: 'get',
  })
}

// 机构服务 - 详情 - 关联账号 - 确定
export function saveAccountList(data) {
  return requestAxios({
    url: '/api/org/admin/agencyOrgAccount',
    method: 'post',
    data
  })
}

// 经销商管理列表
export function getResellerList(data) {
  if (data && data.model) data.model.commerceModel = "DEALER"
  return outlist(data)
}

// 经销商管理 -> 批量启用 / 停用 => 使用原本接口

// 经销商管理 -> 新增 / 编辑
export function editBusinessReport(data) {
  const method = data.id ? 'put' : 'post'
  return requestAxios({
    url: '/api/merchant/admin/saleMerchant',
    method,
    data
  })
}
  
// 业务报表列表
export function getBusinessReportList(data) {
  return requestAxios({
    url: '/api/org/admin/physical/page',
    method: 'post',
    data
  })
}

// 业务报表 -> 汇总
export function getBusinessReportTotal(data) {
  return requestAxios({
    url: '/api/org/admin/physical/total',
    method: 'post',
    data
  })
}

// 业务报表 -> 导出
export function businessReportExport(data) {
  return requestAxios({
    url: '/api/org/admin/physical/export',
    method: 'post',
    data,
  })
}