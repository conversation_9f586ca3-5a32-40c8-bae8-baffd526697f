<template>
  <div class="paySettingTableDialog">
    <!-- <div class="searchBox">
      <el-form
        ref="searchForm"
        :inline="true"
        :model="listQuery"
        class="form-inline"
      >
        <el-form-item prop="model.productName" class="item" style="width:230px">
          <el-input style="width:230px" v-model="listQuery.model.productName" placeholder="输入商品名称"></el-input>
        </el-form-item>
        <el-form-item prop="model.productCode" class="item" style="width:230px">
          <el-input style="width:230px" v-model="listQuery.model.productCode" placeholder="输入商品编码"></el-input>
        </el-form-item>
        <el-form-item class="item">
          <el-button type="primary" @click="submitJoinListSearchFun">搜索</el-button>
          <el-button type="text"  @click="resetForm('searchForm')" class="info">重置</el-button>
        </el-form-item>
      </el-form>
    </div> -->
    <div class="searchBox">
      <im-search-pad
        :has-expand="false"
        :model="listQuery"
        @reset="resetForm"
        @search="submitJoinListSearchFun"
      >
      <im-search-pad-item prop="model.productName">
        <el-input v-model="listQuery.model.productName" placeholder="输入商品名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="model.productCode">
        <el-input v-model="listQuery.model.productCode" placeholder="输入商品编码" />
      </im-search-pad-item>
    </im-search-pad>
    </div>
    <div class="table">
      <el-table ref="pager-table" v-loading="listLoading"  :data="list" row-key="id" border fit height="450" highlight-current-row style="width: 100%">
        <el-table-column
          align="center"
          width="100"
          show-overflow-tooltip
          :render-header="renderHeader"
        >
          <template slot-scope="scope">
            <span>{{scope.$index+1 }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-for="(item, index) in tableTitle"
          :key="index"
          :min-width="(item.width?item.width:'180px')"
          :width="item.name=='pictIdS'?item.width:''"
          :label="item.label"
          show-overflow-tooltip
          :align="item.name=='pictIdS'?'center':'left'"
        >
          <template slot-scope="{row}">
              <el-popover  v-if="item.name=='pictIdS'" placement="right" trigger="hover" >
                  <el-image
                style="width: 200px; height: 200px"
                fit="contain"
                 :src="row[item.name]|imgFilter"
                ></el-image>
                <el-image
                slot="reference"
                style="width: 30px; height: 30px"
                fit="cover"
                :src="row[item.name]|imgFilter"
                ></el-image>
              </el-popover>
              <span  v-else-if="item.name=='approvalStatus'"  >{{row[item.name].desc}}</span>
              <span  v-else-if="item.name=='publishStatus'&&row[item.name].code=='PUT_ON_SALE'"  >{{row[item.name].desc}}</span>
              <el-button  v-else-if="item.name=='publishStatus'&&row[item.name].code=='PULL_OFF_SHELVES'"  type="text"  >{{row[item.name].desc}}</el-button>

              <span v-else-if="item.name=='stockQuantityStatus'"  >{{row[item.name].desc}}</span>
              <el-button  v-else-if="item.name=='salePrice'"  type="text" style="color:#FF6E1B" >{{row[item.name]}}</el-button>
              <el-button  v-else-if="item.name=='costPrice'"  type="text" style="color:#2DAC0C" >{{row[item.name]}}</el-button>
              <el-button  v-else-if="item.name=='grossProfit'"  type="text" style="color:#2DAC0C" >{{row[item.name]}}</el-button>
              <span  v-else>{{ row[item.name] }}</span>
          </template>
        </el-table-column>

        <!-- <el-table-column fixed="right" align="center"  label="操作" width="120" class="itemAction">
            <template slot-scope="{ row }">
                <el-button @click="deleteFun(row)" type="text" >删除</el-button>
            </template>
        </el-table-column> -->
      </el-table>
       <pagination v-show="total>0" :total="total" :page.sync="listQuery.current" :limit.sync="listQuery.size" @pagination="getList" />
    </div>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import { bannerProductList } from '@/api/products/product'
export default {
  data() {
    return {
      tableTitle: [

        {
            key: 1,
            label: '库存状态',
            name: "stockQuantityStatus",
            width: '110px',
            disabled: true
        },
        {
            key: 2,
            label: '商品主图',
            name: "pictIdS",
            width: '110px',
            disabled: true
        },
        {
            key: 3,
            label: '商品编码',
            name: "productCode",
            width: '150px',
            disabled: true
        },
        {
            key: 4,
            label: '商品名称',
            name: "productName",
            width: '200px'
        },
        {
            key: 16,
            label: '销售商名称',
            name: "saleMerchantName",
            width:'200px'
        },
        {
            key: 5,
            label: '规格',
            name: "spec",
            width:'150px'
        },
        {
            key: 6,
            label: '批准文号',
            name: "approvalNumber",
            width:'150px'
        },
        {
            key: 7,
            label: '销售价',
            name: "salePrice",
            width:'150px'
        },
        {
            key: 8,
            label: '成本价',
            name: "costPrice",
            width:'150px'
        },
        {
            key: 9,
            label: '毛利率',
            name: "grossProfitRate",
            width:'150px'
        },
        {
            key: 10,
            label: '毛利',
            name: "grossProfit",
            width:'150px'
        },
        {
            key: 11,
            label: '医保价',
            name: "medicarePrice",
            width:'150px'
        },
        {
            key: 12,
            label: '零售价',
            name: "retailPrice",
            width:'150px'
        },
        {
            key: 13,
            label: '剂型',
            name: "agentiaType",
            width:'120px'
        },
        {
            key: 14,
            label: '生产厂家',
            name: "manufacturer",
        },
        {
            key: 15,
            label: '商品条形码',
            name: "barCode",
            width: '180px'
        },
         
      ],
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        model:{
          productName:"",
          productCode:"",
          brandId:"",
        },
        current: 1,
        size: 10
      },
    };
  },
  components: {Pagination  },
  props: {
   row: {
      type: Object
    },
    visible: {
      type: Boolean,
      default: false,
      required: true
    }
  },
  filters:{
    imgFilter:function(value){
      if(value!=""){
        return value.split(",")[0]
      }
    }
  },
  methods: {
    resetForm(formName) {
      // this.$refs[formName].resetFields();
      // this.$refs['pager-table'].doRefresh()
      this.listQuery.model.productName = "";
      this.listQuery.model.productCode = "";
      this.listQuery.current = 1;
      this.getList();
    },
    submitJoinListSearchFun() {
      this.listQuery.current=1;
      this.getList();
    },
    renderHeader(h, { column }) {
      return (
        <div style="position:relative">
          <i class="el-icon-menu" />
        </div>
      );
    },
    async getList() {
      this.listLoading = true;
      const { data } = await bannerProductList(this.listQuery);

      this.list = data.records;
      this.total = data.total;
      this.listLoading = false;
    },
    onSubmit: function() {},
    deleteFun(row){
       var item = this.list.filter((item) => ( item.id != row.id));
       this.list=item
       this.row.joinProductCount=this.list.length
    }
  },
  created() {
    },
  mounted() {
    this.listQuery.model.brandId=this.row.id
    this.getList();
  },
  beforeDestroy() {}
};
</script>
<style lang="scss" scoped>
@import "@/styles/element-variables.scss";
.paySettingTableDialog {
  margin: -30px -20px;
  border-top: 1px solid #ebecee;
  padding: 30px 20px;
  .form-inline .info {
    color: $--color-info;
  }
  .form-inline .item {
    margin-bottom: 0;
  }
  .flex_start_center p {
    margin-right: 20px;
  }
  .flex_start_center span {
    color: $--color-danger;
  }
  .table{margin-top: 16px;}
}
</style>
