{{#if template}}
<template>
  <div class="{{name}}-detail">
    <page-title title="页面标题">
      <template>
        <!--此处放置操作按钮，若需要分组则以template划分-->
      </template>
    </page-title>
    <div class="item">
      <page-module-title title="模块标题一" />
      <!--以下为当前模块内容-->
    </div>
    <div class="item">
      <page-module-title title="模块标题二" />
      <!--以下为当前模块内容-->
    </div>
  </div>
</template>
{{/if}}

{{#if script}}
<script>
import { api } from '@/api/xxx' // TODO 替换成对应用的列表api
export default {
  name: '{{ properCase name }}Detail',
  components: {
  },
  props: {
  },
  data() {
    return {
    }
  },
  computed: {
  },
  created() {
  },
  mounted() {
  },
  methods: {
  }
}
</script>
{{/if}}

{{#if style}}
<style lang="scss" scoped>
  .{{name}}-detail {
    padding: 0 20px;
    background: #fff;
    .item {
      width: 100%;
      margin-bottom: 30px;
      border-bottom: 1px solid #eeeeee;
    }
  }
</style>
{{/if}}
