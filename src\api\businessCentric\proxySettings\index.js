import requestAxios from '@/utils/requestAxios'
// import requestAxios from "@/views/businessCentric/request"
// 获取详情
export function getItem(data) {
  return requestAxios({
    url: '/api/agent/agency/proxyConfig/query',
    method: 'get',
  })
}

// 通过el-tree 的参数获取地址
export function checkAddr(data) {
  return requestAxios({
    url: '/api/authority/area/anno/analysisByCheck',
    method: 'post',
    data
  })
}

// 提交
export function submit(data) {
  return requestAxios({
    url: '/api/agent/agency/proxyConfig/insert',
    method: 'post',
    data
  })
}

// 提交
export function ReSubmit(data) {
  return requestAxios({
    url: '/api/agent/agency/proxyConfig/save',
    method: 'post',
    data
  })
}

// 新增地区
export function addAddr(data) {
  return requestAxios({
    url: '/api/agent/agency/proxyConfig/insert-area',
    method: 'post',
    data
  })
}

// 删除地区
export function delAddr(data) {
  return requestAxios({
    url: '/api/agent/agency/proxyConfig/delete/'+ data,
    method: 'delete'
  })
}


// 编辑地区
export function putAddr(data) {
  return requestAxios({
    url: '/api/agent/agency/proxyConfig/update',
    method: 'put',
    data
  })
}
