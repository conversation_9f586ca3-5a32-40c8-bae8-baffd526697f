import requestAxios from '@/utils/requestAxios'

// 获取列表数据
export function list(data) {
  return requestAxios({
    url: "/api/finance/admin/financeCollect/page",
    method: 'post',
    data
  })
}

// 获取单一收款单
export function detail(data) {
  return requestAxios({
    url: "/api/finance/admin/financeCollect/" + data,
    method: 'get',
  })
}

// 确认收款
export function confirmReceipt(data) {
  return requestAxios({
    url: "/api/finance/admin/financeCollect/confirmCollect?id=" + data.id+'&remarks=' + data.remarks,
    method: 'post'
  })
}

//修改状态
export function updateStatus(data) {
  return requestAxios({
    url: "/api/finance/admin/financeCollect/updateCollectStatus",
    method: 'post',
    params: data
  })
}