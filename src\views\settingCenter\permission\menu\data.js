export default {
    "code": 0,
    "data": [
        {
            "id": "643464272629728001",
            "createTime": "2019-11-11 14:57:18",
            "createUser": "3",
            "updateTime": "2019-11-11 15:01:31",
            "updateUser": "3",
            "label": "务必详看",
            "parentId": "0",
            "sortValue": 0,
            "children": [
                {
                    "id": "643464953478514081",
                    "createTime": "2019-11-11 15:00:00",
                    "createUser": "3",
                    "updateTime": "2019-11-11 15:01:36",
                    "updateUser": "3",
                    "label": "在线文档",
                    "parentId": "643464272629728001",
                    "sortValue": 0,
                    "children": null,
                    "describe": "",
                    "isPublic": true,
                    "path": "https://www.kancloud.cn/zuihou/zuihou-admin-cloud",
                    "component": "Layout",
                    "isEnable": true,
                    "icon": "",
                    "group": ""
                },
                {
                    "id": "643464392888812545",
                    "createTime": "2019-11-11 14:57:46",
                    "createUser": "3",
                    "updateTime": "2019-11-11 15:00:05",
                    "updateUser": "3",
                    "label": "后端代码",
                    "parentId": "643464272629728001",
                    "sortValue": 1,
                    "children": null,
                    "describe": "",
                    "isPublic": true,
                    "path": "https://github.com/zuihou/zuihou-admin-cloud",
                    "component": "Layout",
                    "isEnable": true,
                    "icon": "",
                    "group": ""
                },
                {
                    "id": "643464517879071841",
                    "createTime": "2019-11-11 14:58:16",
                    "createUser": "3",
                    "updateTime": "2019-11-11 15:00:09",
                    "updateUser": "3",
                    "label": "租户平台-前端代码",
                    "parentId": "643464272629728001",
                    "sortValue": 2,
                    "children": null,
                    "describe": "",
                    "isPublic": true,
                    "path": "https://github.com/zuihou/zuihou-ui",
                    "component": "Layout",
                    "isEnable": true,
                    "icon": "",
                    "group": ""
                },
                {
                    "id": "643464706228487361",
                    "createTime": "2019-11-11 14:59:01",
                    "createUser": "3",
                    "updateTime": "2019-11-11 15:00:11",
                    "updateUser": "3",
                    "label": "运营平台-前端代码",
                    "parentId": "643464272629728001",
                    "sortValue": 3,
                    "children": null,
                    "describe": "",
                    "isPublic": true,
                    "path": "https://github.com/zuihou/zuihou-admin-ui",
                    "component": "Layout",
                    "isEnable": true,
                    "icon": "",
                    "group": ""
                },
                {
                    "id": "643874916004790785",
                    "createTime": "2019-11-12 18:09:03",
                    "createUser": "3",
                    "updateTime": "2019-12-04 16:20:13",
                    "updateUser": "641577229343523041",
                    "label": "运营平台演示地址",
                    "parentId": "643464272629728001",
                    "sortValue": 4,
                    "children": null,
                    "describe": null,
                    "isPublic": true,
                    "path": "http://127.0.0.1:8081/zuihou-admin-ui",
                    "component": "Layout",
                    "isEnable": true,
                    "icon": null,
                    "group": null
                }
            ],
            "describe": "",
            "isPublic": true,
            "path": "/doc",
            "component": "zuihou/doc/Index",
            "isEnable": true,
            "icon": "el-icon-notebook-1",
            "group": ""
        },
        {
            "id": "101",
            "createTime": "2019-07-25 15:35:12",
            "createUser": "1",
            "updateTime": "2019-11-11 14:32:02",
            "updateUser": "3",
            "label": "用户中心",
            "parentId": "0",
            "sortValue": 1,
            "children": [
                {
                    "id": "603982542332235201",
                    "createTime": "2019-07-25 16:11:00",
                    "createUser": "1",
                    "updateTime": "2019-11-11 14:28:40",
                    "updateUser": "3",
                    "label": "组织管理",
                    "parentId": "101",
                    "sortValue": 0,
                    "children": null,
                    "describe": "",
                    "isPublic": false,
                    "path": "/user/org",
                    "component": "zuihou/user/org/Index",
                    "isEnable": true,
                    "icon": "",
                    "group": ""
                },
                {
                    "id": "603982713849908801",
                    "createTime": "2019-07-25 16:11:41",
                    "createUser": "1",
                    "updateTime": "2019-11-11 14:28:43",
                    "updateUser": "3",
                    "label": "岗位管理",
                    "parentId": "101",
                    "sortValue": 1,
                    "children": null,
                    "describe": "",
                    "isPublic": false,
                    "path": "/user/station",
                    "component": "zuihou/user/station/Index",
                    "isEnable": true,
                    "icon": "",
                    "group": ""
                },
                {
                    "id": "603983082961243905",
                    "createTime": "2019-07-25 16:13:09",
                    "createUser": "1",
                    "updateTime": "2019-11-11 14:28:49",
                    "updateUser": "3",
                    "label": "用户管理",
                    "parentId": "101",
                    "sortValue": 2,
                    "children": null,
                    "describe": "",
                    "isPublic": false,
                    "path": "/user/user",
                    "component": "zuihou/user/user/Index",
                    "isEnable": true,
                    "icon": "",
                    "group": ""
                }
            ],
            "describe": "用户组织机构",
            "isPublic": false,
            "path": "/user",
            "component": "Layout",
            "isEnable": true,
            "icon": "el-icon-user-solid",
            "group": ""
        },
        {
            "id": "102",
            "createTime": "2019-07-27 11:48:49",
            "createUser": "1",
            "updateTime": "2019-11-11 14:35:39",
            "updateUser": "3",
            "label": "权限管理",
            "parentId": "0",
            "sortValue": 2,
            "children": [
                {
                    "id": "603976297063910529",
                    "createTime": "2019-07-25 15:46:11",
                    "createUser": "1",
                    "updateTime": "2019-11-11 14:31:52",
                    "updateUser": "3",
                    "label": "菜单配置",
                    "parentId": "102",
                    "sortValue": 0,
                    "children": null,
                    "describe": "",
                    "isPublic": false,
                    "path": "/auth/menu",
                    "component": "zuihou/auth/menu/Index",
                    "isEnable": true,
                    "icon": "",
                    "group": ""
                },
                {
                    "id": "603981723864141121",
                    "createTime": "2019-07-25 16:07:45",
                    "createUser": "1",
                    "updateTime": "2019-11-11 14:31:57",
                    "updateUser": "3",
                    "label": "角色管理",
                    "parentId": "102",
                    "sortValue": 1,
                    "children": null,
                    "describe": "",
                    "isPublic": false,
                    "path": "/auth/role",
                    "component": "zuihou/auth/role/Index",
                    "isEnable": true,
                    "icon": "",
                    "group": ""
                }
            ],
            "describe": "管理权限相关",
            "isPublic": false,
            "path": "/auth",
            "component": "Layout",
            "isEnable": true,
            "icon": "el-icon-lock",
            "group": ""
        },
        {
            "id": "103",
            "createTime": "2019-11-11 14:38:29",
            "createUser": "1",
            "updateTime": "2019-11-11 14:35:42",
            "updateUser": "3",
            "label": "基础配置",
            "parentId": "0",
            "sortValue": 3,
            "children": [
                {
                    "id": "605078371293987105",
                    "createTime": "2019-07-28 16:45:26",
                    "createUser": "1",
                    "updateTime": "2019-11-11 14:34:23",
                    "updateUser": "3",
                    "label": "数据字典维护",
                    "parentId": "103",
                    "sortValue": 0,
                    "children": null,
                    "describe": "",
                    "isPublic": false,
                    "path": "/base/dict",
                    "component": "zuihou/base/dict/Index",
                    "isEnable": true,
                    "icon": "",
                    "group": ""
                },
                {
                    "id": "605078463069552993",
                    "createTime": "2019-07-28 16:45:48",
                    "createUser": "1",
                    "updateTime": "2019-11-11 14:34:26",
                    "updateUser": "3",
                    "label": "地区信息维护",
                    "parentId": "103",
                    "sortValue": 1,
                    "children": null,
                    "describe": "",
                    "isPublic": false,
                    "path": "/base/area",
                    "component": "zuihou/base/area/Index",
                    "isEnable": true,
                    "icon": "",
                    "group": ""
                },
                {
                    "id": "1225042542827929600",
                    "createTime": "2020-02-05 21:04:37",
                    "createUser": "3",
                    "updateTime": "2020-02-05 21:04:37",
                    "updateUser": "3",
                    "label": "参数配置",
                    "parentId": "103",
                    "sortValue": 3,
                    "children": null,
                    "describe": "",
                    "isPublic": false,
                    "path": "/base/parameter",
                    "component": "zuihou/base/parameter/Index",
                    "isEnable": true,
                    "icon": "",
                    "group": ""
                }
            ],
            "describe": "基础的配置",
            "isPublic": false,
            "path": "/base",
            "component": "Layout",
            "isEnable": true,
            "icon": "el-icon-set-up",
            "group": ""
        },
        {
            "id": "104",
            "createTime": "2019-11-11 14:38:34",
            "createUser": "1",
            "updateTime": "2019-11-11 14:35:44",
            "updateUser": "3",
            "label": "开发者管理",
            "parentId": "0",
            "sortValue": 4,
            "children": [
                {
                    "id": "605078538881597857",
                    "createTime": "2019-07-28 16:46:06",
                    "createUser": "1",
                    "updateTime": "2019-12-25 16:19:43",
                    "updateUser": "3",
                    "label": "应用管理",
                    "parentId": "104",
                    "sortValue": 0,
                    "children": null,
                    "describe": "",
                    "isPublic": false,
                    "path": "/developer/application",
                    "component": "zuihou/developer/application/Index",
                    "isEnable": true,
                    "icon": "",
                    "group": ""
                },
                {
                    "id": "605078979149300257",
                    "createTime": "2019-07-28 16:47:51",
                    "createUser": "1",
                    "updateTime": "2019-11-16 16:35:50",
                    "updateUser": "3",
                    "label": "数据库监控",
                    "parentId": "104",
                    "sortValue": 2,
                    "children": null,
                    "describe": "",
                    "isPublic": false,
                    "path": "/developer/db",
                    "component": "zuihou/developer/db/Index",
                    "isEnable": true,
                    "icon": "",
                    "group": ""
                },
                {
                    "id": "605078672772170209",
                    "createTime": "2019-07-28 16:46:38",
                    "createUser": "1",
                    "updateTime": "2019-11-11 14:35:14",
                    "updateUser": "3",
                    "label": "操作日志",
                    "parentId": "104",
                    "sortValue": 3,
                    "children": null,
                    "describe": "",
                    "isPublic": false,
                    "path": "/developer/optLog",
                    "component": "zuihou/developer/optLog/Index",
                    "isEnable": true,
                    "icon": "",
                    "group": ""
                },
                {
                    "id": "645215230518909025",
                    "createTime": "2019-11-16 10:54:59",
                    "createUser": "3",
                    "updateTime": "2019-11-16 10:54:59",
                    "updateUser": "3",
                    "label": "登录日志",
                    "parentId": "104",
                    "sortValue": 4,
                    "children": null,
                    "describe": "",
                    "isPublic": false,
                    "path": "/developer/loginLog",
                    "component": "zuihou/developer/loginLog/Index",
                    "isEnable": true,
                    "icon": "",
                    "group": ""
                },
                {
                    "id": "605079239015793249",
                    "createTime": "2019-07-28 16:48:53",
                    "createUser": "1",
                    "updateTime": "2019-11-16 10:55:03",
                    "updateUser": "3",
                    "label": "接口文档",
                    "parentId": "104",
                    "sortValue": 5,
                    "children": null,
                    "describe": "",
                    "isPublic": false,
                    "path": "http://127.0.0.1:8760/api/gate/doc.html",
                    "component": "Layout",
                    "isEnable": true,
                    "icon": "",
                    "group": ""
                },
                {
                    "id": "605079411338773153",
                    "createTime": "2019-07-28 16:49:34",
                    "createUser": "1",
                    "updateTime": "2019-11-16 10:55:06",
                    "updateUser": "3",
                    "label": "注册&配置中心",
                    "parentId": "104",
                    "sortValue": 6,
                    "children": null,
                    "describe": "",
                    "isPublic": false,
                    "path": "http://127.0.0.1:8848/nacos",
                    "component": "Layout",
                    "isEnable": true,
                    "icon": "",
                    "group": ""
                },
                {
                    "id": "605079545585861345",
                    "createTime": "2019-07-28 16:50:06",
                    "createUser": "1",
                    "updateTime": "2019-11-16 10:55:08",
                    "updateUser": "3",
                    "label": "缓存监控",
                    "parentId": "104",
                    "sortValue": 7,
                    "children": null,
                    "describe": "",
                    "isPublic": false,
                    "path": "http://www.baidu.com",
                    "component": "Layout",
                    "isEnable": true,
                    "icon": "",
                    "group": ""
                },
                {
                    "id": "605079658416833313",
                    "createTime": "2019-07-28 16:50:33",
                    "createUser": "1",
                    "updateTime": "2019-11-16 10:55:15",
                    "updateUser": "3",
                    "label": "服务器监控",
                    "parentId": "104",
                    "sortValue": 8,
                    "children": null,
                    "describe": "",
                    "isPublic": false,
                    "path": "http://127.0.0.1:8762/zuihou-monitor",
                    "component": "Layout",
                    "isEnable": true,
                    "icon": "",
                    "group": ""
                },
                {
                    "id": "605080816296396097",
                    "createTime": "2019-07-28 16:55:09",
                    "createUser": "1",
                    "updateTime": "2019-11-16 10:55:18",
                    "updateUser": "3",
                    "label": "定时调度中心",
                    "parentId": "104",
                    "sortValue": 9,
                    "children": null,
                    "describe": "",
                    "isPublic": false,
                    "path": "http://127.0.0.1:8767/zuihou-jobs-server",
                    "component": "Layout",
                    "isEnable": true,
                    "icon": "",
                    "group": ""
                },
                {
                    "id": "644111530555611361",
                    "createTime": "2019-11-13 09:49:16",
                    "createUser": "3",
                    "updateTime": "2019-11-13 09:56:51",
                    "updateUser": "3",
                    "label": "链路调用监控",
                    "parentId": "104",
                    "sortValue": 10,
                    "children": null,
                    "describe": "",
                    "isPublic": false,
                    "path": "http://127.0.0.1:8772/zipkin",
                    "component": "Layout",
                    "isEnable": true,
                    "icon": "",
                    "group": ""
                },
                {
                    "id": "644111530555611362",
                    "createTime": "2019-11-13 09:49:16",
                    "createUser": "3",
                    "updateTime": "2019-11-13 09:56:51",
                    "updateUser": "3",
                    "label": "SkyWalking链路调用",
                    "parentId": "104",
                    "sortValue": 12,
                    "children": null,
                    "describe": null,
                    "isPublic": false,
                    "path": "http://***********:12080",
                    "component": "Layout",
                    "isEnable": true,
                    "icon": null,
                    "group": null
                }
            ],
            "describe": "开发者",
            "isPublic": false,
            "path": "/developer",
            "component": "Layout",
            "isEnable": true,
            "icon": "el-icon-user-solid",
            "group": ""
        },
        {
            "id": "105",
            "createTime": "2019-11-11 14:38:32",
            "createUser": "1",
            "updateTime": "2019-11-11 14:35:47",
            "updateUser": "3",
            "label": "消息中心",
            "parentId": "0",
            "sortValue": 5,
            "children": [
                {
                    "id": "605079751035454305",
                    "createTime": "2019-07-28 16:50:55",
                    "createUser": "1",
                    "updateTime": "2019-11-11 14:28:30",
                    "updateUser": "3",
                    "label": "消息推送",
                    "parentId": "105",
                    "sortValue": 0,
                    "children": null,
                    "describe": "",
                    "isPublic": false,
                    "path": "/msgs/sendMsgs",
                    "component": "zuihou/msgs/sendMsgs/Index",
                    "isEnable": true,
                    "icon": "",
                    "group": ""
                },
                {
                    "id": "605080023753294753",
                    "createTime": "2019-07-28 16:52:00",
                    "createUser": "1",
                    "updateTime": "2019-11-11 14:28:27",
                    "updateUser": "3",
                    "label": "我的消息",
                    "parentId": "105",
                    "sortValue": 1,
                    "children": null,
                    "describe": "",
                    "isPublic": false,
                    "path": "/msgs/myMsgs",
                    "component": "zuihou/msgs/myMsgs/Index",
                    "isEnable": true,
                    "icon": "",
                    "group": ""
                }
            ],
            "describe": "站内信",
            "isPublic": false,
            "path": "/msgs",
            "component": "Layout",
            "isEnable": true,
            "icon": "el-icon-chat-line-square",
            "group": ""
        },
        {
            "id": "106",
            "createTime": "2019-11-11 14:38:36",
            "createUser": "1",
            "updateTime": "2019-11-11 14:35:49",
            "updateUser": "3",
            "label": "短信中心",
            "parentId": "0",
            "sortValue": 6,
            "children": [
                {
                    "id": "605080359394083937",
                    "createTime": "2019-07-28 16:53:20",
                    "createUser": "1",
                    "updateTime": "2019-11-21 19:53:09",
                    "updateUser": "3",
                    "label": "短信管理",
                    "parentId": "106",
                    "sortValue": 0,
                    "children": null,
                    "describe": "",
                    "isPublic": false,
                    "path": "/sms/manage",
                    "component": "zuihou/sms/manage/Index",
                    "isEnable": true,
                    "icon": "",
                    "group": ""
                },
                {
                    "id": "605080107379327969",
                    "createTime": "2019-07-28 16:52:20",
                    "createUser": "1",
                    "updateTime": "2019-11-21 19:53:17",
                    "updateUser": "3",
                    "label": "账号配置",
                    "parentId": "106",
                    "sortValue": 1,
                    "children": null,
                    "describe": "",
                    "isPublic": false,
                    "path": "/sms/template",
                    "component": "zuihou/sms/template/Index",
                    "isEnable": true,
                    "icon": "",
                    "group": ""
                }
            ],
            "describe": "短信接口",
            "isPublic": false,
            "path": "/sms",
            "component": "Layout",
            "isEnable": true,
            "icon": "el-icon-chat-line-round",
            "group": ""
        },
        {
            "id": "107",
            "createTime": "2019-11-11 14:38:38",
            "createUser": "1",
            "updateTime": "2019-11-11 14:35:51",
            "updateUser": "3",
            "label": "文件中心",
            "parentId": "0",
            "sortValue": 7,
            "children": [
                {
                    "id": "605080648767505601",
                    "createTime": "2019-07-28 16:54:29",
                    "createUser": "1",
                    "updateTime": "2019-11-11 14:28:07",
                    "updateUser": "3",
                    "label": "附件列表",
                    "parentId": "107",
                    "sortValue": 0,
                    "children": null,
                    "describe": "",
                    "isPublic": false,
                    "path": "/file/attachment",
                    "component": "zuihou/file/attachment/Index",
                    "isEnable": true,
                    "icon": "",
                    "group": ""
                }
            ],
            "describe": "附件接口",
            "isPublic": false,
            "path": "/file",
            "component": "Layout",
            "isEnable": true,
            "icon": "el-icon-folder-add",
            "group": ""
        },
        {
            "id": "1291625710699413504",
            "createTime": "2020-08-07 14:42:21",
            "createUser": "3",
            "updateTime": "2020-08-07 14:42:21",
            "updateUser": "3",
            "label": "网关管理",
            "parentId": "0",
            "sortValue": 10,
            "children": [
                {
                    "id": "1291625997229096960",
                    "createTime": "2020-08-07 14:43:30",
                    "createUser": "3",
                    "updateTime": "2020-08-07 14:57:56",
                    "updateUser": "3",
                    "label": "限流规则",
                    "parentId": "1291625710699413504",
                    "sortValue": 1,
                    "children": null,
                    "describe": "",
                    "isPublic": false,
                    "path": "/gateway/ratelimiter",
                    "component": "zuihou/gateway/ratelimiter/Index",
                    "isEnable": true,
                    "icon": "",
                    "group": ""
                },
                {
                    "id": "1291626135842455552",
                    "createTime": "2020-08-07 14:44:03",
                    "createUser": "3",
                    "updateTime": "2020-08-07 14:44:03",
                    "updateUser": "3",
                    "label": "阻止访问",
                    "parentId": "1291625710699413504",
                    "sortValue": 2,
                    "children": null,
                    "describe": "",
                    "isPublic": false,
                    "path": "/gateway/blocklist",
                    "component": "zuihou/gateway/blocklist/Index",
                    "isEnable": true,
                    "icon": "",
                    "group": ""
                }
            ],
            "describe": "",
            "isPublic": false,
            "path": "/gateway",
            "component": "zuihou/gateway/Index",
            "isEnable": true,
            "icon": "el-icon-odometer",
            "group": ""
        },
        {
            "id": "1303978131115212800",
            "createTime": "2020-09-10 16:46:28",
            "createUser": "3",
            "updateTime": "2020-09-10 16:52:17",
            "updateUser": "3",
            "label": "流程管理",
            "parentId": "0",
            "sortValue": 11,
            "children": [
                {
                    "id": "1303980213637480448",
                    "createTime": "2020-09-10 16:54:44",
                    "createUser": "3",
                    "updateTime": "2020-10-20 23:20:03",
                    "updateUser": "3",
                    "label": "流程部署",
                    "parentId": "1303978131115212800",
                    "sortValue": 1,
                    "children": null,
                    "describe": "",
                    "isPublic": false,
                    "path": "/activiti/deploymentManager",
                    "component": "zuihou/activiti/deploymentManager/Index",
                    "isEnable": true,
                    "icon": "el-icon-place",
                    "group": ""
                },
                {
                    "id": "1303979491734847488",
                    "createTime": "2020-09-10 16:51:52",
                    "createUser": "3",
                    "updateTime": "2020-09-10 16:51:52",
                    "updateUser": "3",
                    "label": "模型管理",
                    "parentId": "1303978131115212800",
                    "sortValue": 2,
                    "children": null,
                    "describe": "",
                    "isPublic": false,
                    "path": "/activiti/modelManager",
                    "component": "zuihou/activiti/modelManager/Index",
                    "isEnable": true,
                    "icon": "el-icon-ship",
                    "group": ""
                },
                {
                    "id": "1303980518802456576",
                    "createTime": "2020-09-10 16:55:57",
                    "createUser": "3",
                    "updateTime": "2020-09-10 17:09:38",
                    "updateUser": "3",
                    "label": "请假流程",
                    "parentId": "1303978131115212800",
                    "sortValue": 3,
                    "children": [
                        {
                            "id": "1303981611234099200",
                            "createTime": "2020-09-10 17:00:18",
                            "createUser": "3",
                            "updateTime": "2020-09-10 17:00:18",
                            "updateUser": "3",
                            "label": "请假管理",
                            "parentId": "1303980518802456576",
                            "sortValue": 1,
                            "children": null,
                            "describe": "",
                            "isPublic": false,
                            "path": "/activiti/leave/instantManager",
                            "component": "zuihou/activiti/leave/instantManager/Index",
                            "isEnable": true,
                            "icon": "el-icon-edit",
                            "group": ""
                        },
                        {
                            "id": "1303981798207782912",
                            "createTime": "2020-09-10 17:01:02",
                            "createUser": "3",
                            "updateTime": "2020-09-26 23:18:14",
                            "updateUser": "3",
                            "label": "请假任务",
                            "parentId": "1303980518802456576",
                            "sortValue": 2,
                            "children": null,
                            "describe": "",
                            "isPublic": false,
                            "path": "/activiti/leave/ruTask",
                            "component": "zuihou/activiti/leave/ruTask/Index",
                            "isEnable": true,
                            "icon": "skill",
                            "group": ""
                        }
                    ],
                    "describe": "",
                    "isPublic": false,
                    "path": "/activiti/leave",
                    "component": "zuihou/activiti/leave/Index",
                    "isEnable": true,
                    "icon": "el-icon-date",
                    "group": ""
                },
                {
                    "id": "1303980730522533888",
                    "createTime": "2020-09-10 16:56:48",
                    "createUser": "3",
                    "updateTime": "2020-09-10 17:10:03",
                    "updateUser": "3",
                    "label": "报销流程",
                    "parentId": "1303978131115212800",
                    "sortValue": 4,
                    "children": [
                        {
                            "id": "1303982140681093120",
                            "createTime": "2020-09-10 17:02:24",
                            "createUser": "3",
                            "updateTime": "2020-09-10 17:02:24",
                            "updateUser": "3",
                            "label": "报销管理",
                            "parentId": "1303980730522533888",
                            "sortValue": 0,
                            "children": null,
                            "describe": "",
                            "isPublic": false,
                            "path": "/activiti/reimbursement/instantManager",
                            "component": "zuihou/activiti/reimbursement/instantManager/Index",
                            "isEnable": true,
                            "icon": "el-icon-edit",
                            "group": ""
                        },
                        {
                            "id": "1303982265327419392",
                            "createTime": "2020-09-10 17:02:54",
                            "createUser": "3",
                            "updateTime": "2020-09-10 17:02:54",
                            "updateUser": "3",
                            "label": "报销任务",
                            "parentId": "1303980730522533888",
                            "sortValue": 2,
                            "children": null,
                            "describe": "",
                            "isPublic": false,
                            "path": "/activiti/reimbursement/ruTask",
                            "component": "zuihou/activiti/reimbursement/ruTask/Index",
                            "isEnable": true,
                            "icon": "el-icon-tickets",
                            "group": ""
                        }
                    ],
                    "describe": "",
                    "isPublic": false,
                    "path": "/activiti/reimbursement",
                    "component": "zuihou/activiti/reimbursement/Index",
                    "isEnable": true,
                    "icon": "el-icon-coin",
                    "group": ""
                }
            ],
            "describe": "",
            "isPublic": false,
            "path": "/activiti",
            "component": "zuihou/activiti/Index",
            "isEnable": true,
            "icon": "el-icon-set-up",
            "group": ""
        },
        {
            "id": "4769114505659394",
            "createTime": "2020-09-27 20:30:26",
            "createUser": "3",
            "updateTime": "2020-10-15 20:36:21",
            "updateUser": "3",
            "label": "多级菜单",
            "parentId": "0",
            "sortValue": 13,
            "children": [
                {
                    "id": "4769114505659399",
                    "createTime": "2020-09-27 20:31:17",
                    "createUser": "3",
                    "updateTime": "2020-10-15 20:19:49",
                    "updateUser": "3",
                    "label": "菜单1",
                    "parentId": "4769114505659394",
                    "sortValue": 1,
                    "children": [
                        {
                            "id": "4769114505659482",
                            "createTime": "2020-09-27 20:41:04",
                            "createUser": "3",
                            "updateTime": "2020-09-27 20:41:04",
                            "updateUser": "3",
                            "label": "菜单1-1",
                            "parentId": "4769114505659399",
                            "sortValue": 1,
                            "children": null,
                            "describe": "",
                            "isPublic": true,
                            "path": "/nested/menu1/menu1-1",
                            "component": "zuihou/nested/menu1/menu1-1/index",
                            "isEnable": true,
                            "icon": "",
                            "group": ""
                        },
                        {
                            "id": "4769114505659489",
                            "createTime": "2020-09-27 20:41:39",
                            "createUser": "3",
                            "updateTime": "2020-09-27 20:42:04",
                            "updateUser": "3",
                            "label": "菜单1-2",
                            "parentId": "4769114505659399",
                            "sortValue": 2,
                            "children": [
                                {
                                    "id": "4769114505659504",
                                    "createTime": "2020-09-27 20:43:11",
                                    "createUser": "3",
                                    "updateTime": "2020-09-27 20:43:11",
                                    "updateUser": "3",
                                    "label": "菜单1-2-1",
                                    "parentId": "4769114505659489",
                                    "sortValue": 1,
                                    "children": null,
                                    "describe": "",
                                    "isPublic": true,
                                    "path": "/nested/menu1/menu1-2/menu1-2-1",
                                    "component": "zuihou/nested/menu1/menu1-2/menu1-2-1/index",
                                    "isEnable": true,
                                    "icon": "",
                                    "group": ""
                                },
                                {
                                    "id": "4769114505659507",
                                    "createTime": "2020-09-27 20:43:34",
                                    "createUser": "3",
                                    "updateTime": "2020-09-27 20:43:34",
                                    "updateUser": "3",
                                    "label": "菜单1-2-2",
                                    "parentId": "4769114505659489",
                                    "sortValue": 2,
                                    "children": null,
                                    "describe": "",
                                    "isPublic": true,
                                    "path": "/nested/menu1/menu1-2/menu1-2-2",
                                    "component": "zuihou/nested/menu1/menu1-2/menu1-2-2/index",
                                    "isEnable": true,
                                    "icon": "",
                                    "group": ""
                                }
                            ],
                            "describe": "",
                            "isPublic": true,
                            "path": "/nested/menu1/menu1-2",
                            "component": "Layout",
                            "isEnable": true,
                            "icon": "",
                            "group": ""
                        },
                        {
                            "id": "4769114505659500",
                            "createTime": "2020-09-27 20:42:33",
                            "createUser": "3",
                            "updateTime": "2020-09-27 20:42:33",
                            "updateUser": "3",
                            "label": "菜单1-3",
                            "parentId": "4769114505659399",
                            "sortValue": 3,
                            "children": null,
                            "describe": "",
                            "isPublic": true,
                            "path": "/nested/menu1/menu1-3",
                            "component": "zuihou/nested/menu1/menu1-3/index",
                            "isEnable": true,
                            "icon": "",
                            "group": ""
                        }
                    ],
                    "describe": "",
                    "isPublic": true,
                    "path": "/nested/menu1",
                    "component": "Layout",
                    "isEnable": true,
                    "icon": "fa fa-bug",
                    "group": ""
                },
                {
                    "id": "4769114505659475",
                    "createTime": "2020-09-27 20:39:57",
                    "createUser": "3",
                    "updateTime": "2020-09-27 20:41:13",
                    "updateUser": "3",
                    "label": "菜单2",
                    "parentId": "4769114505659394",
                    "sortValue": 2,
                    "children": null,
                    "describe": "",
                    "isPublic": true,
                    "path": "/nested/menu2",
                    "component": "zuihou/nested/menu2/index",
                    "isEnable": true,
                    "icon": "el-icon-eleme",
                    "group": ""
                }
            ],
            "describe": "演示专用",
            "isPublic": true,
            "path": "/nested",
            "component": "Layout",
            "isEnable": true,
            "icon": "fa fa-align-left",
            "group": ""
        }
    ],
    "msg": "ok",
    "path": null,
    "extra": null,
    "timestamp": 1605428468298,
    "isError": false,
    "isSuccess": true
}