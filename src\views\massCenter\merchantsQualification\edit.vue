<template>
  <div class="archivesEditContent" v-loading="listLoading">
    <div class="item">
      <div class="title"><span>基础信息</span></div>
      <el-row :gutter="20">
        <el-col :span="6">
          <div><span style="color:#232333">客户编码:</span> {{query.code || '无'}}</div>
        </el-col>
        <el-col :span="6">
          <div><span style="color:#232333">客户名称:</span> {{query.name || '无'}}</div>
        </el-col>
        <el-col :span="6">
          <div><span style="color:#232333">统一社会信用代码:</span> {{query.socialCreditCode || '无'}}</div>
        </el-col>
        <el-col :span="6">
          <div><span style="color:#232333">法人代表:</span> {{query.legalPerson || '无'}}</div>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="6">
          <div><span style="color:#232333">企业类型:</span> {{query.merchantType || '无'}}</div>
        </el-col>
        <el-col :span="6">
          <div><span style="color:#232333">联系人:</span> {{query.ceoName || '无'}}</div>
        </el-col>
        <el-col :span="6">
          <div><span style="color:#232333">联系电话:</span> {{query.ceoMobile || '无'}}</div>
        </el-col>
        <el-col :span="6">
          <div><span style="color:#232333">所在区域:</span> {{query.region || '无'}}</div>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="6">
          <div><span style="color:#232333">注册地址:</span> {{query.registerAddress || '无'}}</div>
        </el-col>
        <el-col :span="6">
          <div><span style="color:#232333">创建时间:</span> {{query.createTime || '无'}}</div>
        </el-col>
      </el-row>
    </div>
    <div class="item">
      <div class="title"><span>经营范围</span></div>
      <el-row :gutter="20" v-if="cateGory">
        <el-col v-for="(ids, keys) in cateGory" :key="keys" :span="8">
          <el-form :inline="false" label-width="100px">
            <el-form-item :label="keys + ':'">
              <span style="white-space: nowrap" v-for="ite in ids" :key="ite.id">{{ite.label + '、'}}</span>
            </el-form-item>
          </el-form>
          <!-- {{keys}}: <span v-for="ite in ids" :key="ite.id">{{ite.label + '发的发射点傻大个撒、'}}</span> -->
        </el-col>
      </el-row>
      <span style="padding-left:30px;color:#a9a9ac" v-else>无</span>
    </div>
    <div class="item">
      <div class="title"><span>客商资质</span></div>
      <div class="content">
        <div style="padding:0 0 15px 10px">企业类型： {{query.merchantType}}</div>
        <div class="table">
          <!-- <el-table v-if="list" :data="lisenceTableDate" row-key="id" border fit highlight-current-row style="width: 100%">
            <el-table-column v-for="(item, index) in tableTitle" :key="index" :width="item.width" :min-width="(item.width?item.width:'300px')" :label="item.label"
              :show-overflow-tooltip="item.name!='qualityReportRelVoList'" align="center">
              <template slot-scope="{row}">
                <el-popover v-if="item.name=='fileIds'" placement="right" trigger="hover">
                  <img :src="row[item.name]" alt="">
                  <img slot="reference" :src="row[item.name]" alt="" width="40" height="40">
                </el-popover>
                <span v-else-if="item.name == 'merchantLicenses'">
                  {{row[item.name].desc }}
                </span>
                <span v-else>{{row[item.name] }}</span>
              </template>
            </el-table-column>

            <el-table-column fixed="right" align="center" label="操作" width="150" class="itemAction">
              <template slot-scope="scope">
                <el-button @click="edit(scope.row.id)" type="text" >编辑</el-button>
                <el-button @click="detailFun(scope.row.id)" type="text" >预览</el-button>
                <el-button @click="detailFun(scope.row.id)" type="text" >下载</el-button>
              </template>
            </el-table-column>
          </el-table> -->

          <el-table :data="lisenceTableDate" style="width: 100%" border>
            <el-table-column prop="label" label="证件类型"></el-table-column>
            <el-table-column prop="licenseNumber" label="证件号">
              <template slot-scope="{ row }">
                <el-input v-if="row.isEdit" placeholder="请输入证件号" v-model="row.licenseNumber"></el-input>
                <span v-else>{{ row.licenseNumber }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="licenseEndTime" label="过期时间">
              <template slot-scope="{ row }">
                <el-date-picker v-if="row.isEdit" v-model="row.licenseEndTime" type="datetime" style="width: 240px" placeholder="选择日期" value-format="yyyy-MM-dd HH:mm:ss"></el-date-picker>
                <span v-else>{{ row.licenseEndTime }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="filePath" label="附件">
              <template slot-scope="{ row }">
                <el-button v-if="!row.isEdit&&!row.filePath" type="text" >查看示例图片</el-button>
                <el-upload v-if="row.isEdit" :class="{hide: !row.isEdit}" ref="uploadlisence" :limit="3" :file-list="row.filePathList" :action="$uploadUrl" :data="insertProgram"
                  :headers="headersProgram" list-type="picture-card" :on-remove="handleRemove" :on-success="uploadSuccess" :before-upload="beforeUpload" accept=".jpg,.png,.bmp,.jpeg">
                  <i class="el-icon-plus"></i>
                </el-upload>

                <span v-else>
                  <img v-for="file in row.filePathList" :key="file.url" class="el-upload-list__item-thumbnail" :src="file.url" alt="" style="contain:cover;width:40px;height:40px;margin-right:5px">
                </span>
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template slot-scope="{ row }">
                <el-button type="text"  v-show="!row.isEdit" @click="editLisenceFun(row)"> 编 辑 </el-button>
                <el-button type="text"  v-show="row.isEdit" @click="cancelLisenceEdit(row)" style="color: rgb(127, 127, 127);"> 取 消 </el-button>
                <el-button type="text"  v-show="row.isEdit" @click="confirmLisenceEdit(row)"> 确 定 </el-button>
                <el-button type="text"  v-show="!row.isEdit">预览</el-button>
                <el-button type="text"  v-show="!row.isEdit">下载</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getitem } from "@/api/massCenter/merchantsQualification/index";
import { listByLicenseBaseType } from "@/api/purchasingAgent/archivesList";
export default {
  data() {
    return {
      listLoading: false,
      query: {},
      item: {},
      list: [],
      cateGory: {},
      lisenceTableDate: [],
      tableTitle: [
        {
          label: "证件类型",
          name: "label",
        },
        {
          label: "证件号",
          name: "licenseNumber",
        },
        {
          label: "过期时间",
          name: "licenseEndTime",
        },
        {
          label: "附件",
          name: "fileIds",
        },
      ],
    };
  },
  methods: {
    editLisenceFun(row) {
      row.isEdit = true;
      this.editLisenceItem = JSON.parse(JSON.stringify(row));
    },
    cancelLisenceEdit(row) {
      row.isEdit = false;
      this.lisenceTableDate = this.lisenceEdit;
    },
    async confirmLisenceEdit(row) {
      row.isEdit = false;
      row.filePathList.forEach((item) => {
        item.url = item.response.data.url;
      });
    },
    getsrc(str) {
      if (!str) {
        return [];
      } else {
        let arr = str.split(",");
        let list = [];
        arr.forEach((item) => {
          let obj = {
            response: {
              data: {
                url: "",
              },
            },
          };
          obj.response.data.url =
            "http://eyaolink-dev-bucket.oss-cn-shenzhen.aliyuncs.com/" + item;
          obj.url =
            "http://eyaolink-dev-bucket.oss-cn-shenzhen.aliyuncs.com/" + item;
          list.push(obj);
        });
        return list;
      }
    },
    uploadSuccess(res, file, fileList) {
      this.lisenceTableDate.forEach((item, index) => {
        if (item.licenseBaseId == this.editLisenceItem.licenseBaseId) {
          item.filePath = this.getFilePath(fileList);
          item.filePathList = fileList;
        }
      });
    },
    handleRemove(file, fileList) {
      this.lisenceTableDate.forEach((item, index) => {
        if (item.licenseBaseId == this.editLisenceItem.licenseBaseId) {
          item.filepath = this.getFilePath(fileList);
          item.filePathList = fileList;
        }
      });
    },
    getFilePath(fileList) {
      let str = "";
      fileList.forEach((item) => {
        let url = item.response.data.url;
        str += item.response.data.url + ",";
      });
      return str.substr(0, str.length - 1);
    },
    beforeUpload(file) {
      let fileTypeList = ["image/png", "image/pjpeg", "image/jpeg", "image/bmp", "image/jpg"];
      const isValidType = fileTypeList.indexOf(file.type) > -1;
      const isLt2M = file.size / 1024 / 1024 < 5;

      if (!isValidType) {
        this.$message.error("上传图片格式错误! 仅支持 JPG、PNG、BMP、JPEG 格式");
      }
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 5MB!");
      }
      return isValidType && isLt2M;
    },
    async getitem() {
      this.listLoading = true;
      let { data } = await getitem(this.row.id);
      this.item = data;
      let obj = {};

      if (!data.businessCategoryDetailList) {
        this.cateGory = false;
      } else {
        data.businessCategoryDetailList.forEach((item) => {
          if (!obj[item.parentName]) {
            obj[item.parentName] = [];
            obj[item.parentName].push(item);
          } else {
            obj[item.parentName].push(item);
          }
        });
        this.cateGory = obj;
      }

      let lisenceData = (await listByLicenseBaseType()).data;
      this.listLoading = false;
      lisenceData.forEach((item) => {
        let obj = {
          licenseBaseId: item.id,
          licenseEndTime: "",
          filePath: "",
          isForever: "",
          licenseNumber: "",
          label: item.name,
          isEdit: false,
        };
        console.log(item);
        data.merchantLicenses.find((ids) => {
          if (item.id == ids.licenseBaseId) {
            obj.licenseEndTime = ids.licenseEndTime;
            obj.filePath = ids.filePath;
            obj.licenseNumber = ids.licenseNumber;
            obj.filePathList = this.getsrc(ids.filePath);
            obj.label = item.name;
            obj.merchantId = ids.merchantId;
          }
        });
        console.log(obj);
        this.lisenceTableDate.push(obj);
      });
    },
    editLisenceFun(row) {
      row.isEdit = true;
      this.editLisenceItem = JSON.parse(JSON.stringify(row));
    },
    getsrc(str) {
      console.log(str);
      if (!str) {
        return [];
      } else {
        let arr = str.split(",");
        let list = [];
        arr.forEach((item) => {
          let obj = {
            response: {
              data: {
                url: "",
              },
            },
          };
          obj.response.data.url = item;
          obj.url = item;
          list.push(obj);
        });
        return list;
      }
    },
  },
  created() {
    this.getitem();
  },
  mounted() {
    this.query = this.row;
  },
  props: {
    row: {
      type: Object,
    },
  },
};
</script>

<style lang="less" scoped>
.archivesEditContent {
  margin: -30px -20px;
  border-top: 1px solid #ebecee;
  padding: 30px 40px;
  .item {
    width: 100%;
    margin-bottom: 30px;
    padding-bottom: 30px;
    border-bottom: 1px solid #eeeeee;
    .title {
      padding: 0 0 30px;
      span {
        font-size: 16px;
        padding-left: 10px;
        border-left: 4px solid rgba(64, 158, 255, 1);
      }
    }
    .formItem {
      width: 300px;
    }
  }
  .el-row {
    margin-bottom: 30px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .el-col {
    border-radius: 4px;
    color: #a9a9ac;
    span {
      color: #333 !important;
      padding-right: 10px !important;
    }
  }
  .grid-content {
    border-radius: 4px;
    min-height: 36px;
  }
  .row-bg {
    padding: 10px 0;
    background-color: #f9fafc;
  }
  .productPicContent .text p {
    font-family: "PingFangSC-Regular", "PingFang SC", sans-serif,
      "PingFangSC-Regular", "PingFang SC", sans-serif-400;
    font-weight: 400;
    color: #aaaaaa;
    line-height: 20px;
    font-size: 13px;
    margin: 0;
  }
  .content {
    // padding: 20px;
    background-color: #fff;
    .title {
      padding-bottom: 20px;
      span {
        margin-bottom: -2px;
        padding: 0 15px;
        height: 40px;
        line-height: 30px;
        display: block;
        background: rgba(255, 255, 255, 0);
        border-bottom: 2px solid rgb(64, 158, 255);
        font-size: 16px;
        font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
          "PingFang SC"-400;
        font-weight: 400;
        color: rgb(64, 158, 255);
      }
    }
  }
  .detailMsg {
    font-family: "PingFangSC-Regular", "PingFang SC", sans-serif,
      "PingFangSC-Regular", "PingFang SC", sans-serif-400;
    font-weight: 400;
    color: #aaaaaa;
    line-height: 20px;
    padding-bottom: 20px;
    font-size: 13px;
  }
  /deep/ .el-input.is-disabled .el-input__inner {
    color: #04060c;
    background-color: #eaf7e7;
  }
  /deep/ .is-checked .is-disabled .el-checkbox__inner {
    color: #2fa338;
    background-color: #1b9e38;
  }
  /deep/ .is-checked .el-checkbox__label {
    color: #04060c;
  }
  /deep/ .is-disabled .is-checked .el-radio__inner {
    background-color: #1b9e38;
  }
  /deep/ .is-disabled.is-checked.el-radio > .el-radio__label {
    color: #04060c;
  }
  /deep/ .el-col .el-col-6 span {
    padding-left: 100px;
  }
  /deep/ .el-upload {
    width: 40px;
    height: 40px;
    position: relative;
  }
  /deep/ .el-upload > i {
    font-size: 16px;
    position: absolute;
    left: 50%;
    top: 50%;
    -webkit-transform: translateX(-50%) translateY(-50%);
    transform: translateX(-50%) translateY(-50%);
  }
  /deep/ .el-upload-list .el-upload-list__item {
    width: 40px;
    height: 40px;
  }
  /deep/ .hide .el-upload--picture-card {
    display: none;
  }
}
</style>
