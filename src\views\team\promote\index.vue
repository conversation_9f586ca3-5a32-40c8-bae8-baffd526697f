<template>
   <div>
      <div class="header_box">
         <page-title title="推广费设置"></page-title>
      </div>
      <div class="content">
         <div class="item_box">
            <module-title title="基础信息" />
            <basicsSetting></basicsSetting>
         </div>
         <div class="item_box">
            <module-title title="按商品分组" />
            <productGroup></productGroup>
         </div>
         <div class="item_box">
            <module-title title="按单品">
               <template>
                  <div>
                     <el-button @click="handleRemove">批量移除</el-button>
                     <el-button type="primary" @click="addProduct">+ 新增商品</el-button>
                  </div>
               </template>
            </module-title>
            <single ref="singleRef"></single>
         </div>
      </div>
      <dialogProduct ref="dialogProduct" @updataChange="updataChange"></dialogProduct>
   </div>
</template>


<script>
import ModuleTitle from '@/components/PageModuleTitle'
import basicsSetting from '@/views/team/promote/components/basicsSetting'
import productGroup from '@/views/team/promote/components/productGroup'
import single from '@/views/team/promote/components/single'
import dialogProduct from '@/views/team/promote/components/dialogProduct'
export default {
   //import引入的组件
   components: {
      ModuleTitle,
      basicsSetting,
      productGroup,
      single,
      dialogProduct
   },

   data() {
      return {}
   },
   //生命周期 - 挂载完成（可以访问DOM元素）
   mounted() {},

   computed: {},

   created() {},

   filters: {},

   //方法集合
   methods: {
      addProduct(){
         this.$refs.dialogProduct.openDia();
      },
      // 批量移除
      handleRemove(){
         this.$refs.singleRef.remove();
      },
      updataChange(){
         this.$refs.singleRef.initData();
      },
   },

}
</script>


<style lang='scss' scoped>
.header_box {
   background-color: #fff;
   padding: 0 20px;
   overflow: hidden;
}
.item_box {
    width: 100%;
    padding-bottom: 20px;
   //  border-bottom: 1px solid #eeeeee;
    .cateGory{
      display: flex;
      .el-col-8{
        word-break:break-all;
        font-size: 14px;
        line-height: 20px;
        padding-bottom: 10px;
        text-overflow: wrap;
      }
    }
  }
  .content {
     background-color: #fff;
     padding: 0 20px;
  }
</style>