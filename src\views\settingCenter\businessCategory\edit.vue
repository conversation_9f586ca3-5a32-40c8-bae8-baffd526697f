<template>
   <el-dialog :close-on-click-modal="false" v-if="visible"  :title="(row.id>0?'编辑':'新增')+'经营类目'" :visible.sync="visible" :before-close="clearFun" width="450px" >
      <div class="businessScopeEditContent">
        <el-form class="form" :model="query" ref="ruleForm" label-width="120px">
          <el-form-item   class="formItem" prop="categoryCode" label="经营类目编码:" :rules="[{ required: true, message: '请填写经营类目编码',trigger: 'blur' }]">
            <el-input :disabled="query.id!=0" clearable style="width:250px"  v-model="query.categoryCode" placeholder="请填写经营类目编码"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="name" label="名称:" :rules="[{ required: true, message: '请填写经营类目名称',trigger: 'blur' }]">
            <el-input    clearable style="width:250px"  v-model="query.name" placeholder="请填写经营类目名称"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
          <el-button @click="clearFun()">取 消</el-button>
            <el-button type="primary" @click="submitFun('ruleForm')"
              >确 定</el-button
            >
      </span>
   </el-dialog>
</template>
<script>
import {editApi,getApi } from '@/api/setting/businessCategory'
export default {
  data() {
    return {
      query: {}
    };
  },
  props: {
    row: {
      type: Object
    },
    visible: {
      type: Boolean,
      default: false,
      required: true
    },
    isReload: {
      type: Boolean,
      default: false,
      required: true
    }
  },
  methods: {
    clearFun: function() {
      this.$emit("update:visible", false);
      this.$emit("update:row", {});
    },
    submitFun: function(businessScopeForm) {
      let _this=this;
      _this.$refs[businessScopeForm].validate( async valid => {
        if (valid) {
          await editApi({
            id:_this.query.id,
            whetherShowFrontend:"Y",
            label:_this.query.name
          })
          _this.$emit("update:visible", false);
          _this.$emit("update:isReload", true);
        } else {
          return false;
        }
      });
    }
  },
  async mounted() {
    this.query =JSON.parse(JSON.stringify(this.row));
    this.query.whetherShowFrontend="Y"
    // if(this.query.id==undefined || this.query.id==0){
    //   this.query.whetherShowFrontend="Y"
    //   this.query.sortValue=0
    // }
    // else{
    //   if(this.query.whetherShowFrontend!=null){
    //     this.query.whetherShowFrontend=this.query.whetherShowFrontend.code
    //   }
    // }
  },
  beforeDestroy() {}
};
</script>
<style lang="less" scoped>
.businessScopeEditContent {
  margin: -30px -20px;
  border-top: 1px solid #ebecee;
  padding: 30px 20px;
}
</style>
