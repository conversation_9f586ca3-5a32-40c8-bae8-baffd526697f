<template>
  <div class="dia_content">
    <el-dialog
      title="提示"
      :visible.sync="visible"
      width="70%"
      :before-close="closeBox"
      :show-close="false"
    >
      <div slot="title" class="title_box">
        <!-- v-model="model.recommendState"  -->
        <tabs-layout ref="tabs-layout" :tabs="tabList" @change="chageTabsFun">
          <template slot="button">
            <el-button
              size="medium"
              style="font-size: 20px"
              type="text"
              icon="el-icon-close"
              @click="closeBox"
            >
            </el-button>
          </template>
        </tabs-layout>
      </div>
      <im-search-pad
        class="im-search-pad"
        :has-expand="false"
        :model="model"
        @reset="reload"
        @search="onSubmit"
      >
        <im-search-pad-item prop="erpCode">
          <el-input v-model="model.erpCode" placeholder="请输入商家名称" />
        </im-search-pad-item>
        <im-search-pad-item prop="productName">
          <el-input v-model="model.productName" placeholder="请输入商品名称" />
        </im-search-pad-item>
        <im-search-pad-item prop="erpCode">
          <el-input v-model="model.erpCode" placeholder="请输入ERP编码" />
        </im-search-pad-item>
        <im-search-pad-item prop="manufacturer">
          <el-input v-model="model.manufacturer" placeholder="请输入生产厂家" />
        </im-search-pad-item>
      </im-search-pad>
      <table-pager
        ref="productTable"
        :options="tableTitle"
        :reserveSelection="reserveSelection"
        :remote-method="productList"
        :data.sync="tableData"
        :selection="true"
        :rowKey="rowKey"
        :minHeight="450"
        :height="500"
        @selection-change="onSelect"
        @selection-all="onAllSelect"
        :isNeedButton="false"
      >
        <template slot="salePrice">
          <el-table-column label="销售价">
            <slot slot-scope="{ row }">
              <span class="redColor">{{ row.salePrice || "" }}</span>
            </slot>
          </el-table-column>
        </template>
        <template slot="memberPrice">
          <el-table-column label="会员价">
            <slot slot-scope="{ row }">
              <span class="redColor">{{ row.memberPrice || "" }}</span>
            </slot>
          </el-table-column>
        </template>
        <template slot="costPrice">
          <el-table-column label="成本价">
            <slot slot-scope="{ row }">
              <span class="costPriceColor">{{ row.costPrice || "" }}</span>
            </slot>
          </el-table-column>
        </template>
      </table-pager>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeBox" size="small">取消</el-button>
        <el-button @click="submitSelectFun" size="small" type="primary">
          {{ model.recommendState == "ACCEPTED" ? "移除" : "添加"
          }}{{
            multipleSelection.length > 0 ? `(${multipleSelection.length})` : ""
          }}
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>


<script>
import { mapState, mapGetters, mapActions } from "vuex";
import {
  getProductCount,
  getProductsDepartment,
  editSellProductByDepartmentId,
} from "@/api/organization";
import { merchantsStatistics, list, audit } from "@/api/finance/deposit/index";
import checkPermission from "@/utils/permission";

const TableColumns = [
  {
    label: "商家名称",
    name: "erpCode",
    width: "110",
    prop: "erpCode",
  },
  {
    label: "商品名称",
    name: "productName",
    width: "150",
    prop: "productName",
  },
  {
    label: "ERP商品编码",
    name: "erpCode",
    width: "110",
    prop: "erpCode",
  },
  {
    label: "规格",
    name: "spec",
    prop: "spec",
  },
  {
    label: "单位",
    name: "unit",
    prop: "unit",
  },
  {
    label: "生产厂家",
    name: "manufacturer",
    width: "150",
    prop: "manufacturer",
  },
  {
    label: "销售价",
    name: "salePrice",
    prop: "salePrice",
    slot: true,
  },
  {
    label: "会员价",
    name: "memberPrice",
    prop: "memberPrice",
    slot: true,
  },
];

const TableColumnList = [];

for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({
    key: i,
    ...TableColumns[i],
  });
}
export { TableColumnList };
export default {
  //import引入的组件
  components: {},
  props: {
    /**
     * @description  显示状态
     * @param {String} visible
     */
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      rowKey: "id",
      multipleSelection: [],
      tabs: [
        {
          name: "已关联商品",
          value: "ACCEPTED",
          count: 0,
          // permission: 'groupOrder-all:view',
          countName: "recommended",
        },
        {
          name: "未关联商品",
          value: "PENDING",
          count: 0,
          // permission: 'groupOrder-waitpay:view',
          countName: "notRecommended",
        },
      ],
      delText: "您确定移除选中商品？",
      reserveSelection: true,
      tableTitle: TableColumnList,
      tableVal: [],
      tableData: [],
      model: {
        approvalStatus: {
          code: "PENDING",
        },
        name: "",
        ceoMobile: "",
        merchantTypeId: "",
        publishStatus: "",
      },
    };
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getCount();
  },
  watch: {
    visible: function (newVal, oldVal) {
      if (newVal == true) {
        this.getCount();
        this.reload();
      }
    },
  },
  computed: {
    _visible: {
      get() {
        this.visible;
      },
      set(val) {
        this.tableData = [];
        this.multipleSelection = [];
        this.$refs.productTable.clearSelection();
        this.$emit("update:visible", val);
      },
    },
    ...mapGetters([
      "organizationInfo",
      "paremDepartmentInfo",
      "departmentInfo",
    ]),
    tabList() {
      console.info(this.tabs);
      return [
        { name: `已关联商品(${this.tabs[0].count || 0})`, value: "ACCEPTED" },
        { name: `未关联商品(${this.tabs[1].count || 0})`, value: "PENDING" },
      ];
    },
  },

  created() {},

  filters: {},

  //方法集合
  methods: {
    checkPermission,
    async getCount() {
      const query = {
        ceoMobile: "",
        merchantTypeId: "",
        name: "",
        publishStatus: "",
      };
      const { data } = await getProductCount(query);
      this.tabs.forEach((item) => {
        console.info(data[item.value.toLowerCase() + "Count"]);
        item.count = data[item.value.toLowerCase() + "Count"];
      });
    },
    async productList(params) {
      const listQuery = {
        model: {
          ...this.model,
        },
      };
      Object.assign(listQuery, params);
      listQuery.model.departmentId = this.departmentId;
      return await getProductsDepartment(listQuery);
    },
    onAllSelect(selection) {
      this.onSelect(selection);
    },
    onSelect(val) {
      this.multipleSelection = val;
    },
    handleClose() {
      this.visible = false;
    },
    chageTabsFun(tab) {
      this.model.approvalStatus.code = tab.value;
      this.model.recommendState = tab.value;
      this.getCount();
      this.tableData = [];
      this.multipleSelection = [];
      this.$refs.productTable.clearSelection();
      this.$refs.productTable.doRefresh({
        page: 1,
        pageSize: 10,
      });
    },
    onSubmit() {
      this.$refs.productTable.doRefresh({
        page: 1,
        pageSize: 10,
      });
    },
    reload() {
      this.model = {
        ...this.model,
        ...{
          erpCode: "",
          productName: "",
          manufacturer: "",
          recommendState: this.model.recommendState,
        },
      };
      this.$refs.productTable.doRefresh();
    },
    submitSelectFun() {
      console.log("submitSelectFun", this.multipleSelection);
      let list = [];
      if (this.multipleSelection.length > 0) {
        this.multipleSelection.forEach((element) => {
          list.push(element.id);
        });
      } else {
        this.$message.warning("请选择商品");
        return;
      }

      if (this.model.recommendState == "ACCEPTED") {
        // 移除
        console.info("移除", {
          departmentId: this.departmentId,
          list,
          ids: list.toString(),
        });
        // editSellProductByDepartmentId(list.toString()).then(res => {
        //   if (res.code == 0 && res.msg == 'ok') {
        //     this.$message.success('移除成功');
        //   }
        // })
      } else {
        // 添加
        console.info("添加", {
          departmentId: this.departmentId,
          list,
          ids: list.toString(),
        });
        // editSellProductByDepartmentId(queryList).then(res => {
        //   if (res.code == 0 && res.msg == 'ok') {
        //     this.$message.success('添加成功');
        //   }
        // })
      }
      // this.reload();
      // this.$refs.productTable.clearSelection();
      // this.multipleSelection = [];
    },
    closeBox: function () {
      console.log("closeBox");
      this._visible = false;
      // this.$emit('update:visible',)
    },
  },
};
</script>


<style lang="less" scoped>
/deep/ .el-dialog__header {
  border: none;
  padding: 0;
}

::v-deep .el-dialog__body {
  padding-top: 0;
}
.im-search-pad {
  padding-left: 0;
  border: none;
}
.title_box {
  padding-top: 10px;

  ::v-deep .varietiesBan-list-tabs {
    padding-left: 20px;
  }
  ::v-deep .el-icon-close {
  }
  ::v-deep .operations {
    padding-right: 20px;
  }
}

.title_box
  .varietiesBan-list-container
  .varietiesBan-list-tabs-wrapper
  .operations {
  bottom: 0px;
}
</style>
