
import requestAxios from '@/utils/requestAxios'


// 商家列表数据
export function list(data) {
  return requestAxios({
    url: "/api/finance/admin/financeMerchantsEarnestMoney/page",
    method: 'post',
    data
  })
}
//取消
export function cancel(data) {
  return requestAxios({
    url: "/api/finance/admin/financeMerchantsEarnestMoney/cancel",
    method: 'post',
    params:data
  })
}
//退还审核,状态 4 -> 通过 5 -> 驳回
export function audit(data) {
  return requestAxios({
    url: "/api/finance/admin/financeMerchantsEarnestMoney/audit",
    method: 'post',
    params: data
  })
}
//商家状态统计
export function merchantsStatistics(data) {
  return requestAxios({
    url: '/api/finance/admin/financeMerchantsEarnestMoney/countStatus',
    method: 'post',
    data
  })
}



//品种缴纳金列表
export function breedList(data) {
  return requestAxios({
    url: "/api/finance/admin/financeBreedEarnestMoney/page",
    method: 'post',
    data
  })
}
//品种状态统计
export function statistics(data) {
  return requestAxios({
    url: '/api/finance/admin/financeBreedEarnestMoney/countStatus',
    method: 'post',
    data
  })
}
export function updateCashRemarks(data) {
  return requestAxios({
    url: "/api/finance/admin/financeBreedEarnestMoney/audit",
    method: 'post',
    params: data
  })
}
//取消
export function cancelPay(data) {
  return requestAxios({
    url: "/api/finance/admin/financeBreedEarnestMoney/cancel",
    method: 'post',
    params: data
  })
}



