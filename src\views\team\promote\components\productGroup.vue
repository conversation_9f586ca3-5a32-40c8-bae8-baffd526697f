<template>
  <div>
    <el-table border :data="listData">
       <template v-for="(item,index) in columnData" v-key="index">
          <el-table-column v-if="item.prop =='updateTime' || item.prop =='productGroupName'"  :prop="item.prop" :label="item.label" :width="item.width">
            <template slot-scope="{row}">
                <div >{{row[item.prop]}}</div>
            </template>
          </el-table-column>
          <el-table-column v-else :prop="item.prop" :label="item.label" :min-width="item.width" >
            <template slot-scope="{row}">
              <div >{{row[item.prop]}}</div>
              <div v-if="item.prop == 'sumData'">
                <div>会员推广费：<span v-if="row.numMemberRatio!=null && row.numMemberRatio != ''">{{row.numMemberRatio}}%</span>
                </div>
                <div style="margin-top:5px">非会员推广费：<span
                    v-if="row.numNoMemberRatio!=null && row.numNoMemberRatio != ''">{{row.numNoMemberRatio}}%</span>
                </div>
              </div>
              <div v-else class="table_column" v-for="(its,index) in row.orgRolePopularizeDTOS" :key="index">
                <div v-if="item.prop == its.roleId">
                  <div>会员推广费：<span v-if="its.memberRatio!=null && its.memberRatio != ''">{{its.memberRatio}}%</span>
                  </div>
                  <div style="margin-top:5px">非会员推广费：<span
                      v-if="its.noMemberRatio!=null && its.noMemberRatio != ''">{{its.noMemberRatio}}%</span>
                  </div>
                </div>
                <div v-if="item.prop == its.roleId">
                  <i class="el-icon-edit" style="color: blue;cursor:pointer;" @click="openEdit(item,row)"></i>
                </div>
              </div>
            </template>
          </el-table-column>
      </template>
    </el-table>
    <dialogSetting ref="dialogSetting" @submitSetting="submitSetting" :currentRow="currentRow"></dialogSetting>
  </div>
</template>


<script>
  import {
    mapGetters
  } from 'vuex'
  import {
    getOrganizationIdPopularize,
    updateProductGroupRatio
  } from '@/api/organization/index';
  import dialogSetting from '@/views/team/promote/components/dialogSetting'
  export default {
    //import引入的组件
    components: {
      dialogSetting
    },

    data() {
      return {
        columnData: [], // 表头
        listData: [], // 数据
        otherColumn: [{
            label: '总推广费',
            prop: 'sumData',
            width: 190
          },
          {
            label: '操作时间',
            prop: 'updateTime',
             width: 160
          }
        ],
        currentRow: null
      }
    },
    //生命周期 - 挂载完成（可以访问DOM元素）
    mounted() {},

    computed: {
      ...mapGetters([
        'organizationInfo'
      ]),
    },

    created() {
      this.initData()
    },

    filters: {},

    //方法集合
    methods: {
      async initData() {
        let result = await getOrganizationIdPopularize(this.organizationInfo.id);
        if (result.code != 0 && result.msg != 'ok') {
          return
        };
        // console.log('result',result);
        this.listData = result.data;
        let headerList = [{
          label: '商品分组',
          prop: 'productGroupName',
          width: 100
        }];
        if (result.data && result.data.length > 0) {
          result.data[0].orgRolePopularizeDTOS.forEach(element => {
            headerList.push(
              Object.assign({}, {
                label: element.roleName,
                prop: element.roleId,
                width: 190
              })
            )
          });
          this.columnData = [...headerList, ...this.otherColumn];
        }
      },
      //   开启编辑
      openEdit(item, row) {
          console.log('item,row',item, row);
          let rowData = {};
          row.orgRolePopularizeDTOS.forEach(element=>{
              if(element.roleId == item.prop) {
                  rowData = element;
              }
          })
        this.currentRow = {
          ...item,
          rowData
        };
        this.$refs.dialogSetting.openDia(this.currentRow);
      },
      submitSetting(form) {
        if (this.currentRow == null) {
          return
        }
        console.log('form---->',form);
        let params = {
            organizationId: this.currentRow.rowData.organizationId,
            productGroupId: this.currentRow.rowData.productGroupId,
            roleId: this.currentRow.rowData.roleId,
          ...form
        }
        if(this.currentRow.rowData.id != null && this.currentRow.rowData.id != '') {
            // 修改
            params.id = this.currentRow.rowData.id;
        }
        // return
        updateProductGroupRatio(params).then(res => {
          if (res.code == 0 && res.msg == 'ok') {
            this.$message.success('设置推广费成功');
            this.initData();
            this.currentRow = null;
          }
        })
      },
    },

  }

</script>


<style lang='scss' scoped>
.table_column {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
</style>
