<template>
  <div class="items" @click="checkPermission(['admin','fitment-wechat:edit'])&&showDrawerFun()">
    <div class="capsuleBanner">
      <el-image class="bannerImg" :src="queryCapsuleBanner.image" :fit="'cover'" />
    </div>
    <el-drawer
      :destroy-on-close="true"
      :size="'450px'"
      append-to-body
      :wrapper-closable="false"
      :visible.sync="drawer"
      :with-header="false"
    >
      <div class="flex_between_center top">
        <div>胶囊图片</div>
        <div>
          <el-button @click="drawer=false">取 消</el-button>
          <el-button type="primary" @click="submitFun()">提交</el-button>
        </div>
      </div>
      <div class="tipBox">
        <p class="title">添加胶囊图片</p>
      </div>
      <el-form label-width="75px" class="form">
        <el-form-item class="setItemBox" :label-width="'0'" label=" ">
          <i class="el-icon-error closeI" @click="removeItem()" />
          <div class="flex_between_start">
            <div class="uploadImgBox">
              <el-upload
                class="avatar-uploader"
                :action="$uploadUrl"
                :data="insertProgram"
                :headers="headersProgram"
                :on-success="uploadSuccess"
                :before-upload="beforeUpload"
                :show-file-list="false"
                multiple
              >
                <el-image v-if="capsuleBanner.image!=''" style="width: 80px; height: 80px" :src="capsuleBanner.image" :fit="'cover'" />
                <i v-else class="el-icon-plus avatar-uploader-icon" />
              </el-upload>
            </div>
            <div class="inputBox">
              <!-- <el-form-item label="链接类型" class="inputItem">
                                    <el-select v-model="capsuleBanner.linkUrl" placeholder="请选择链接类型">
                                    <el-option value="">请选择</el-option>
                                    <el-option
                                        v-for="itemInfo in selectList"
                                        :key="itemInfo.describe"
                                        :label="itemInfo.name"
                                        :value="itemInfo.describe">
                                    </el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="链接" class="inputItem">{{capsuleBanner.linkUrl}}</el-form-item>
                                <el-form-item label="链接值"  class="inputItem"><el-input placeholder="录入JSON格式值" v-model="capsuleBanner.linkParam"></el-input></el-form-item>
                                <el-form-item label="是否显示"  class="inputItem">
                                    <el-radio-group v-model="capsuleBanner.showStatus">
                                        <el-radio :label="'Y'">是</el-radio>
                                        <el-radio :label="'N'">否</el-radio>
                                    </el-radio-group>
                                </el-form-item> -->

              <el-form-item label="链接类型" class="inputItem">
                <el-select v-model="capsuleBanner.linkUrl" placeholder="请选择链接类型" @change="changeLinkFun($event,capsuleBanner)">
                  <el-option value="">请选择</el-option>
                  <el-option
                    v-for="itemInfo in selectList"
                    :key="itemInfo.describe"
                    :label="itemInfo.name"
                    :value="itemInfo.describe"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="链接" class="inputItem">{{ capsuleBanner.linkUrl }}</el-form-item>
              <el-form-item v-for="(paraItem,i) in capsuleBanner.linkParam" class="inputItem" :label="'链接值'+paraItem.key">
                <el-input v-model="capsuleBanner.linkParam[i].val" placeholder="录入值" @change="change($event)">
                  <!-- 商品详情 PRODUCT_DETAIL-->
                  <ProductItemTable v-if="capsuleBanner.searchType=='PRODUCT_DETAIL'" slot="append" :width="'100px'" :select-items.sync="capsuleBanner.linkParam[i].val" />
                  <!-- 商品详情 end-->
                  <!-- 商家首页 STORE_INDEX-->
                  <StoreListTable v-if="capsuleBanner.searchType=='STORE_INDEX'" slot="append" :width="'100px'" :select-items.sync="capsuleBanner.linkParam[i].val" />
                  <!-- 商家首页 end-->
                  <!-- 分类列表页面  CATEGORYTYPE_LIST-->
                  <ProductTypeItemTable v-if="capsuleBanner.searchType=='CATEGORYTYPE_LIST'" slot="append" :width="'100px'" :select-items.sync="capsuleBanner.linkParam[i].val" />
                  <!-- 分类列表页面 end-->
                  <!-- 专题页 -->
                  <SpecialTable v-if="capsuleBanner.searchType=='SPECIAL_PAGE'" slot="append" :width="'100px'" :select-items.sync="capsuleBanner.linkParam[i].val" />
                  <!-- 专题页 end-->
                </el-input>
              </el-form-item>
              <el-form-item label="是否显示" class="inputItem">
                <el-radio-group v-model="capsuleBanner.showStatus">
                  <el-radio :label="'Y'">是</el-radio>
                  <el-radio :label="'N'">否</el-radio>
                </el-radio-group>
              </el-form-item>

            </div>
          </div>
        </el-form-item>
      </el-form>
    </el-drawer>
  </div>
</template>
<script>
import checkPermission from '@/utils/permission'
import ProductItemTable from '@/components/eyaolink/Product/productItemCodeTable'
import ProductTypeItemTable from '@/components/eyaolink/Product/ProductTypeItemTable'
import StoreListTable from '@/components/eyaolink/Store/listTable'
import { param2Obj } from '@/utils/index'
import SpecialTable from '@/components/eyaolink/Product/SpecialTable'
import {
  pageComponentList,
  deleteByPageComponentId,
  pageADList,
  pageADAdd,
  pageADEdit
} from '@/api/fitment'
import { query } from '@/api/setting/data/dictionaryItem'
import { uploadFile } from '@/api/file'
import { getToken } from '@/utils/auth'
export default {
  components: {
    ProductItemTable,
    ProductTypeItemTable,
    StoreListTable,
    SpecialTable
  },
  props: {
    pagePlateId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      drawer: false,
      capsuleBanner: {},
      queryCapsuleBanner: {
        id: '',
        showStatus: 'Y',
        pagePlateId: this.pagePlateId,
        pageComponentId: '',
        dataType: 'CUSTOM',
        image: '',
        linkUrl: ''
      },
      headersProgram: {
        token: getToken(),
        Authorization: 'Basic YWRtaW5fdWk6YWRtaW5fdWlfc2VjcmV0'
      },
      insertProgram: {
        folderId: 0
      },
      selectList: [],
      pageComponentId: '',
      uploadIndex: 0
    }
  },
  mounted() {
    this.initFun()
  },
  beforeDestroy() {},
  methods: {
    checkPermission,
    showDrawerFun() {
      this.drawer = true
      this.getSelectList()
    },
    async submitFun() {
      await deleteByPageComponentId(this.pageComponentId)
      this.queryCapsuleBanner = { ...{}, ...this.capsuleBanner }
      var itemValJson = {}
      if (this.queryCapsuleBanner.linkParam instanceof Array) {
        this.queryCapsuleBanner.linkParam.forEach(keyVal => {
          itemValJson = { ...itemValJson, ...{ [keyVal.key]: keyVal.val }}
        })
        this.queryCapsuleBanner.linkParam = JSON.stringify(itemValJson)
      }
      var data = await pageADAdd(this.queryCapsuleBanner)
      if (data.code == 0) {
        this.drawer = false
        this.initFun()
      } else {
        this.$message.error('提交胶囊图片失败！')
      }
    },
    change(e) {
      this.$forceUpdate()
    },
    changeLinkFun(url, item) {
      var selectItem = this.selectList.filter(itemInfo => {
        return url == itemInfo.describe
      })

      if (selectItem != null && selectItem.length > 0) {
        item['searchType'] = selectItem[0].code
      } else {
        item['searchType'] = ''
      }
      var propList = []
      var linkPareObject = param2Obj(url)
      console.info(item)
      Object.keys(linkPareObject).map(key => {
        var val = linkPareObject[key]
        propList.push({ key, val: '' })
      })

      if (propList.length > 0) {
        item.linkParam = propList
      } else {
        item.linkParam = null
      }
    },
    removeItem() {
      this.capsuleBanner = {
        id: '',
        pagePlateId: this.pagePlateId,
        showStatus: 'Y',
        pageComponentId: '',
        image: '',
        linkUrl: '',
        searchType: '',
        linkParam: [{ key: '', val: '' }],
        sortValue: this.capsuleBanner.length
      }
    },
    async initFun() {
      var { data } = await pageComponentList({
        current: 1,
        map: {},
        model: {
          componentCode: 'banner'
        },
        order: 'descending',
        size: 10,
        sort: 'id'
      })
      this.pageComponentId = data.records[0].id
      const adList = await pageADList({
        current: 1,
        map: {},
        model: {
          pageComponentId: this.pageComponentId
        },
        order: 'descending',
        size: 10,
        sort: 'id'
      })
      console.info('capsulebanner', adList)
      if (adList.data.records != null && adList.data.records.length > 0) {
        this.queryCapsuleBanner = adList.data.records[0]
        this.queryCapsuleBanner.showStatus = adList.data.records[0].showStatus.code

        var propList = []
        var linkPareObject = param2Obj(this.queryCapsuleBanner.linkUrl)
        Object.keys(linkPareObject).map(key => {
          var val = linkPareObject[key]
          propList.push({ key, val })
        })
        if (this.queryCapsuleBanner.linkParam != undefined && this.queryCapsuleBanner.linkParam != '' && this.queryCapsuleBanner.linkParam != null) {
          propList = []
          var linkParamObj = JSON.parse(this.queryCapsuleBanner.linkParam)
          Object.keys(linkParamObj).map(key => {
            var val = linkParamObj[key]
            propList.push({ key, val })
          })
        } else {
          propList = [{ key: '', val: '' }]
        }
        this.queryCapsuleBanner.linkParam = propList
      } else {
        this.queryCapsuleBanner.linkParam = [{ key: '', val: '' }],
        this.queryCapsuleBanner.pageComponentId = this.pageComponentId
      }
    },
    //  上传功能
    async getSelectList() {
      this.list = []
      this.isLoading = true
      const { data } = await query(
        {
          dictionaryId: '40083396470731372'
          // code:"WXCHARTLOCATION"
        }
      )
      this.selectList = data
      var selectItem = this.selectList.filter(itemInfo => {
        return this.queryCapsuleBanner.linkUrl == itemInfo.describe
      })
      if (selectItem != null && selectItem.length > 0) {
        this.queryCapsuleBanner.searchType = selectItem[0].code
      } else {
        this.queryCapsuleBanner.searchType = ''
      }

      this.capsuleBanner = Object.assign({}, this.queryCapsuleBanner)
    },

    uploadIndexItem: function(index) {
      this.uploadIndex = index
    },
    beforeUpload(file) {
      const fileTypeList = ['image/png', 'image/pjpeg', 'image/jpeg', 'image/bmp']
      const isJPG = fileTypeList.indexOf(file.type) > -1
      const isLt2M = file.size / 1024 / 1024 < 5

      if (!isJPG) {
        this.$message.error('上传图片格式错误!')
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
      }
      return isJPG && isLt2M
    },
    uploadSuccess(res, file) {
      this.capsuleBanner.image = res.data.url
    }

  }
}
</script>
<style lang="less" scoped>
.items{
    border:1px  dashed  red;
    margin-bottom: 12px;
    cursor: pointer;
}
.capsuleBanner{ margin: 0 auto;width: 345px; height: 80px;background: rgba(238,238,238,0.97);border-radius: 173px; overflow: hidden;}
.capsuleBanner .bannerImg{width: 345px; height: 80px;}

.top{
    border-bottom: 1px solid #efefef;
    height:60px;
    padding: 0 15px;
}
.tipBox{
    margin-top: 16px;
    padding: 0 15px;
    width: 100%;
    p.title{
        font-size: 14px;
        height: 19px;
        font-size: 14px;
        font-weight: 400;
        margin:0;
    }
    p.tip{
        font-size: 14px;
        margin:0;
        height: 40px;
        font-family:  -400;
        font-weight: 400;
        color: #aaaaaa;
        line-height: 20px;

    }
}
.form{
     margin:0 auto;
     width:100%;
     padding:10px;
     position: relative;
    .formItem{margin-bottom: 5px;}
    .closeI{
        opacity: 0;
        position: absolute;
        right: -18px;
        top: -25px;
        font-size: 20px;
        cursor: pointer;
    }
    .fromBox{padding-top:10px; max-height: calc(100vh - 320px); overflow-y: auto;}
    .setItemBox {border: 1px solid #efefef; padding:18px 10px; width:420px;}
    .setItemBox:hover .closeI{
    opacity: 1;
}
}

.addbtn{
    cursor: pointer;
    margin:0 auto;
    text-align: center;
    margin-top: 16px;
    width:325px;
    height: 40px;
    line-height: 40px;
    background: #ffffff;
    border: 1px solid #409eff;
    font-size: 14px;
    font-weight: 400;
    color: #409eff;
}

.avatar-uploader{height:80px; width:80px;border: 1px solid #efefef; }
.avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 80px;
    height: 80px;
    line-height: 80px;
    text-align: center;
  }
  .avatar {
    width: 80px;
    height: 80px;
    display: block;
  }
  .inputItem{margin-bottom:12px;}
</style>
