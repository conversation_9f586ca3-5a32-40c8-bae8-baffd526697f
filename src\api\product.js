import request from '@/utils/request'

export function fetchList(query) {
  return request({
    url: '/product/admin/productDescriptionRel/page',
    method: 'get',
    params: query
  })
}
//渠道控销列表
export function productControlSaleList(query) {
  return request({
    url: '/product/admin/productControlSale/page',
    method: 'post',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//删除
export function deleteProductControlSale(ids) {
  return request({
    url: `/product/admin/productControlSale`,
    method: 'delete',
    params: {
      ids: ids
    }
  })
}
//保存控销
export function productControlSaleAdd(query) {
  return request({
    url: '/product/admin/productControlSale',
    method: 'post',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//控销详情
export function productControlSaleById(id) {
  return request({
    url: `/product/admin/productControlSale/${id}`,
    method: 'get'
  })
}
//修改控销
export function productControlSaleEdit(query) {
  return request({
    url:'/product/admin/productControlSale',
    method: 'PUT',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//删除控销里的某种客户规则数据
export function deleteControlItems(id,selectCustomer) {
  return request({
    url: '/product/admin/productControlSale/deleteControlItem',
    method: 'post',
    params: {
      id: id,
      selectCustomer:selectCustomer
    }

  })
}
//分页查询工具
export function productList(query) {
  return request({
    url: '/product/admin/product/anno/page',
    method: 'post',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
