<template>
  <div>
    <im-search-pad
      :has-expand="false"
      :model="model"
      @reset="reload"
      @search="searchLoad"
    >
      <im-search-pad-item prop="businessNo">
        <el-input v-model="model.businessNo" placeholder="请输入业务单号" />
      </im-search-pad-item>
      <im-search-pad-item prop="applicantUserName">
        <el-input v-model="model.applicantUserName" placeholder="请输入申请人" />
      </im-search-pad-item>
      <im-search-pad-item prop="during">
        <el-date-picker
          v-model="during"
          type="datetimerange"
          align="right"
          unlink-panels
          range-separator="至"
          start-placeholder="起始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd hh:mm:ss"
        >
        </el-date-picker>
      </im-search-pad-item>
    </im-search-pad>

    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        :tabs="tabList"
        @change="handleChangeTab"
      >
        <template slot="button">
          <!-- <el-button  v-if="checkPermission(['admin','givePack:export'])">导出</el-button> -->
          <el-button  @click="reload">刷新</el-button>
        </template>
      </tabs-layout>
      <table-pager ref="bussinessTabel" :options="tableTitle" :remote-method="load" :data.sync="tableData" :pageSize="pageSize" :isNeedButton="isWait"  @selection-change="handleSelectionChange">
        <template slot="amount">
          <el-table-column label="金额（元）" width="100">
            <slot slot-scope="{row}">
              {{row.amount|getDecimals}}
            </slot>
          </el-table-column>
        </template>
        <template slot="reviewUser" v-if="model.businessStatus!=='WAIT'">
          <el-table-column label="审核人" width="160" prop="reviewUser" />
        </template>
        <template slot="reviewTime" v-if="model.businessStatus!=='WAIT'">
          <el-table-column label="审核时间" width="160" prop="reviewTime" />
        </template>
        <template slot="remarks" v-if="model.businessStatus!=='WAIT'">
          <el-table-column label="审核备注" width="320" prop="remarks" />
        </template>
        <template slot="payCollectId" v-if="model.businessStatus==='REFUND'||model.businessStatus==='COMPLETED'">
          <el-table-column label="付款单号" width="160">
            <slot slot-scope="{row}">
              <span class="text-primary">{{row.payCollectId}}</span>
            </slot>
          </el-table-column>
        </template>
        <div slot-scope="props">
          <el-row class="table-edit-row">
            <span v-if="checkPermission(['admin','giveBack:verify'])" class="table-edit-row-item">
              <el-button type="text" @click="handleVerify(props.row.id)">审核</el-button>
            </span>
          </el-row>
        </div>
      </table-pager>
    </div>
    <el-dialog title="商家保证金退还审核" :visible.sync="verifyVisible" width="500px" @close="handleClose">
      <el-form :model="vForm" ref="vForm">
        <el-form-item prop="remarks" :rules="[{required: true,message: '请输入备注',trigger: 'blur'}]">
          <el-input placeholder="请输入备注信息" v-model="vForm.remarks" type="textarea" rows="4"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button v-if="checkPermission(['admin', 'admin-finance-merchantMargin:reject'])" @click="submit(5)">驳 回</el-button>
        <el-button v-if="checkPermission(['admin', 'admin-finance-merchantMargin:accept'])" type="primary" @click="submit(4)">审核通过</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  const TableColumns = [
    { label: "业务单号", name: "id",prop: "id",width: "170"},
    { label: "业务类型", name: "type.desc", prop:"type.desc",width: "170" },
    { label: "业务单状态", name: "businessStatus.desc", prop:"businessStatus.desc",width: "98" },
    { label: "申请人", name: "applicantUserName", prop:"applicantUserName",width: '220' },
    { label: "金额（元）", name: "amount", prop:"amount",slot: true  },
    { label: "制单人", name: "createUserName",prop:'createUserName',width: "100" },
    { label: "制单时间", name: "createTime",prop:'createTime',width: "170" },
    { label: "取消人", name: "reviewUserName", prop:"reviewUserName",slot: true  },
    { label: "取消时间", name: "reviewTime", prop:"reviewTime",slot: true  },
    { label: "取消原因", name: "remarks", prop:"remarks",slot: true  },
    { label: "收款单号", name: "payCollectId", prop:"payCollectId",slot: true }
  ];
  const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({ key: i, ...TableColumns[i] });
  }
  import checkPermission from "../../../utils/permission";
  import { merchantsStatistics,list,audit } from '@/api/finance/deposit/index.js'
  import TabsLayout from '@/components/TabsLayout'
  export default {
    components: {
      TabsLayout
    },
    data () {
      return {
        loading: '',
        currentTab: 0,
        tabs: [
          { name: '待审核', value: 'WAIT',count: 0,permission: 'giveBack-wait:view' },
          { name: '退还中', value: 'REFUND',count: 0,permission: 'giveBack-refund:view'  },
          { name: '已驳回', value: 'REJECT',count: 0,permission: 'giveBack-reject:view'  },
          { name: '已完成', value: 'COMPLETED',count: 0,permission: 'giveBack-completed:view'  }
        ],
        tableData: [],
        page: 1,
        pageSize: 10,
        totalPage: 0,
        total: 0,
        tableTitle: TableColumnList,
        ids: [],
        during: '',
        model: {
          businessStatus: 'WAIT',
          businessNo: '',
          applicantUserName: '',
          type: 'REFUND'
        },
        verifyVisible: false,
        isWait: true,//操作列
        vForm: {
          remarks: '',
          'ids[]': ''
        }
      }
    },
    computed: {
      tabList() {
        return [
          { name: `待审核（${this.tabs[0].count}）`, value: 'WAIT', hide: !checkPermission(['admin', 'admin-finance-merchantMargin:handingView']) },
          { name: `退还中（${this.tabs[1].count}）`, value: 'REFUND', hide: !checkPermission(['admin', 'admin-finance-merchantMargin:backView']) },
          { name: `已驳回（${this.tabs[2].count}）`, value: 'REJECT', hide: !checkPermission(['admin', 'admin-finance-merchantMargin:rejectedView']) },
          { name: `已完成（${this.tabs[3].count}）`, value: 'COMPLETED', hide: !checkPermission(['admin', 'admin-finance-merchantMargin:finishedView']) }
        ]
      }
    },
    mounted() {
      this.getCount()
    },
    methods: {
      checkPermission,
      async getCount() {
        const query = {
          type: 'REFUND'
        }
        const {data} = await merchantsStatistics(query)
        this.tabs.forEach(item=>{
          item.count = data[item.value.toLowerCase()]
        })
      },
      handleSelectionChange(val) {
      },
      handleVerify(id) {
        this.vForm['ids[]'] = id
        this.verifyVisible=true
      },
      handleClose() {
        this.verifyVisible = false
        this.$refs['vForm'].resetFields();
      },
      submit(status) {
        const param = {
          ...this.vForm,
          status: status //1,//状态 1 -> 通过 3 -> 驳回
        }
        this.$refs['vForm'].validate(async valid => {
          if (valid) {
            audit(param).then(res => {
              if (res.code === 0) {
                this.$message.success('审核操作完成！')
                this.getCount()
                this.handleRefresh({
                  page: 1,
                  pageSize: 10
                })
              }
              this.verifyVisible = false
            })
          } else {
            return false
          }
        })
      },
      async load(params) {
        const listQuery = {
          model: {
            ...this.model,
            startTime: this.during[0],
            endTime: this.during[1]
          }
        }
        Object.assign(listQuery, params)
        this.loading = true
        const {data} = await list(listQuery)
        this.todoName = data.records[0].merchantName
        this.loading = false
        return { data }
      },
      searchLoad() {
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      reload() {
        this.model={
          ...this.model,
          ...{ 
            businessNo: '',
            applicantUserName: ''
          }
        }
        this.during = ''
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      handleChangeTab(tab) {
        this.model.businessStatus = tab.value
        if(tab.value !== 'WAIT'){
          this.isWait = false
        } else {
          this.isWait = true
        }
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      handleRefresh(pageParams) {
        this.$refs.bussinessTabel.doRefresh(pageParams)
      }
    }
  }
</script>
