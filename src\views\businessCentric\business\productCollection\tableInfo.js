export default {
  RECORD: [
    {
      label: "商家名称",
      name: "saleMerchantName",
      prop: "saleMerchantName",
      width: "150"
    },
    {
      label: "采集人编码",
      name: "saleManCode",
      prop: "saleManCode",
      width: "120"
    },
    {
      label: "采集人姓名",
      name: "saleManName",
      prop: "saleManName",
      width: "120"
    },
    {
      label: "采集时间",
      name: "createTime",
      prop: "createTime",
      width: "150"
    },
    {
      label: "客户名称",
      name: "purMerchantName",
      prop: "purMerchantName"
    },
    {
      label: '采集商品数',
      name: "itemCount",
      prop: "itemCount",
      width: '120',
      slot: true
    }
  ],
  COLLECTDATA:[
    {
      label: "商家名称",
      name: "saleMerchantName",
      prop: "saleMerchantName",
      width: "150"
    },
    {
        label: "客户名称",
        name: "purMerchantName",
        prop: "purMerchantName",
        width: "150"
      },
      {
        label: "商品编码",
        name: "productCode",
        prop: "productCode",
        width: "160"
      },
      {
        label: "商品名称",
        name: "productName",
        prop: "productName",
        width: "150"
      },
      {
        label: "规格",
        name: "spec",
        prop: "spec",
        width: "120"
      },
      {
        label: "生产厂家",
        name: "manufacturer",
        prop: "manufacturer",
        width: "150"
      },
      {
        label: "最新销售价",
        name: "price",
        prop: "price",
        width: "100"
      },
      {
        label: "最新库存",
        name: "stockQuantity",
        prop: "stockQuantity",
        width: "100"
      },
      {
        label: "最新动销量",
        name: "changeAmount",
        prop: "changeAmount",
        width: "100"
      },
      {
        label: "最新动销周期",
        name: "changePeriod",
        prop: "changePeriod",
        width: "150"
      },
      {
        label: "最新采集时间",
        name: "createTime",
        prop: "createTime",
        width: "150"
      },
      {
        label: "最新采集人",
        name: "saleManName",
        prop: "saleManName",
        width: "120"
      },
  ]
}
