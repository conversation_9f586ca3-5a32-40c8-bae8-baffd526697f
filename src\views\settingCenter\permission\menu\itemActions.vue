<template>
  <div class="itemActions">
    <el-table v-loading="isLoading" border fit :data="list" style="width: 100%" max-height="500px">
      <el-table-column prop="code" label="编码"></el-table-column>
      <el-table-column prop="name" label="名称"></el-table-column>
      <el-table-column v-if="checkPermission(['admin','permission-menu-function:edit','permission-menu-function:delete'])" fixed="right" align="center" label="操作"  width="150" >
        <template slot-scope="scope">
          <el-button v-if="checkPermission(['admin','permission-menu-function:edit'])" @click="editActionFun(scope.row)" type="text" >编辑</el-button>
          <el-button v-if="checkPermission(['admin','permission-menu-function:delete'])" @click="deleteActionFun(scope.row)" type="text" >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="tableCenter">
      <el-button v-if="checkPermission(['admin','permission-menu-function:add'])" type="text" icon="el-icon-plus" @click="editActionFun({id:0,menuId:row.id})">新增功能</el-button>
    </div>
    <el-dialog
      :append-to-body="true"
      :show-close="true"
      :title="(query.id==0?'编辑':'新增')+'功能'"
      :visible.sync="showEditAction"
      width="500px"
      :close-on-click-modal="false"
      :before-close="editActionClose"
    >
      <div class="editContent">
        <el-form class="form" :model="query" ref="ruleForm" label-width="60px">
          <el-form-item label="编码" prop="code"  :rules="[{ required: true, message: '请填写编码',trigger: 'blur' }]" >
            <el-input
              :disabled="query.id !== 0"
              @keyup.enter.native="submitFun('ruleForm')"
              v-model="query.code"
            />
            <p class="note" style=" font-size: 12px;margin: 0;padding: 0;line-height: 20px;">
              建议使用:作为分隔符，并以view、add、edit、detail、delete、export、import、download、upload等关键词结尾
            </p>
            <p class="note" style=" font-size: 12px;margin: 0;padding: 0;    line-height: 26px;">如：menu:add、 resource:view、 file:upload、 product-rejected:view 、product-accepted:view</p>
          </el-form-item>
          <el-form-item
            label="名称"
            @keyup.enter.native="submitFun('ruleForm')"
            prop="name"
            :rules="[{ required: true, message: '请填写名称',trigger: 'blur' }]"
          >
            <el-input v-model="query.name" />
          </el-form-item>
          <el-form-item
            label="描述"
            prop="describe"
            @keyup.enter.native="submitFun('ruleForm')"
          >
            <el-input v-model="query.describe" />
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
          <el-button @click="showEditAction = false">取 消</el-button>
          <el-button v-if="checkPermission(['admin','permission-menu-function:add','permission-menu-function:edit'])"   type="primary" @click="submitFun('ruleForm')">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import checkPermission from '@/utils/permission'
import { list, editApi, deleteApi } from "@/api/setting/permission/resource";
export default {
  data() {
    return {
      isLoading: false,
      showEditAction: false,
      list: [],
      query: {},
      row: {},
      listQuery: {
        size: 999,
        current: 1,
        sort: "id",
        order: "descending",
        map: {},
        model: {
          menuId: 0
        }
      }
    };
  },
  methods: {
    checkPermission,
    editActionFun(row){
      this.query = Object.assign({},row) ;
      this.showEditAction=true
    },
    deleteActionFun(row) {
      var _this=this;
      this.$confirm('此操作将永久删除该信息, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        var data = await deleteApi(row.id);
        if(data.code==0){
          _this.$message.success('删除成功！')
          _this.list=data.records
          _this.getList()
        }else{
            this.$message({
              showClose: true,
              message: data.msg,
              type: 'error'
            });
        }
      }).catch(() => {});
    },
    async submitFun(ruleForm) {
      var _this = this;
      _this.$refs[ruleForm].validate(async valid => {
        if (valid) {
          var data = await editApi(this.query);
          if(data.code==0){
            _this.$message.success('操作成功！')
            _this.getList()
            _this.showEditAction = false;
          }else{
            this.$message({
              showClose: true,
              message: data.msg,
              type: 'error'
            });
          }
        } else {
          return false;
        }
      });
    },
    editActionClose() {
      this.showEditAction = false;
    },
    async getList(){
      this.list=[]
      try {
        this.isLoading=true;
        let { data } = await list(this.listQuery);
        this.list=data.records
      } finally {
        this.isLoading=false
      }
    },
    getAuthList(row) {
      this.row = row;
      this.listQuery.model.menuId = row.id;
      this.getList();
    },
    resetAuthList() {
      this.list=[]
    }
  },
  beforeDestroy() {}
};
</script>
<style lang="less" scoped>
.itemActions {
  .tableCenter{
    border: 1px solid #efefef;
    text-align: center;
  }
  p.note {
    font-size: 12px;
    margin: 0;
    padding: 0;
  }

}
.editContent {
  margin: -30px -20px;
  border-top: 1px solid #ebecee;
  padding: 30px 20px;
}
</style>
