<template>
  <div class="app-container">
    <div v-if="user">
      <el-row :gutter="20">
        <el-col :span="8" :xs="24">
          <user-card :user="user"/>
        </el-col>
        <el-col :span="16" :xs="24">
          <el-card v-if="user">
           <el-tabs v-model="prTab" >
              <el-tab-pane label="个人信息" name="account" >
                <account :user="user"/>
              </el-tab-pane>
              <el-tab-pane label="密码修改" name="password">
                <!-- <password :user="user"/> -->
              </el-tab-pane>
            </el-tabs> 
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
  import Cookies from 'js-cookie'
  import UserCard from './components/UserCard'
  // import Password from './components/Password'
  // import Timeline from './components/Timeline'
  import Account from './components/Account'
  import { getUser } from '@/utils/auth'
  export default {
    name: 'Profile',
    components: {
      UserCard, Account
      // , Password, Timeline
    },
    data() {
      return {
        prTab:'account',
        user:null
      }
    },
    created() {
    },
    mounted() {
       this.user =JSON.parse(Cookies.get("loginUser"))
    }
  }
</script>
<style lang="scss" scoped>
  .el-card.is-always-shadow {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }

  .el-card {
    border: none;
    border-radius: 0;
  }
</style>
