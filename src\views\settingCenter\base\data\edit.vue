<template>
    <el-dialog
        :close-on-click-modal="false"
        v-if="visible"
        :before-close="clearFun"    :show-close="true" :title="(row.id>0?'编辑':'新增')+'字典'"
        :visible.sync="visible"
        width="450px"
      >
      <div class="editContent">
        <el-form :model="query"  label-position="right" label-width="80px" ref="ruleForm">
          <el-form-item label="类型" prop="type" :rules="[{ required: true, message: '请填写类型',trigger: 'blur' }]">
            <el-input :disabled="query.id>0" v-model="query.type" />
          </el-form-item>
          <el-form-item  label="名称" prop="name" :rules="[{ required: true, message: '请填写名称',trigger: 'blur' }]">
            <el-input v-model="query.name" />
          </el-form-item>
          <el-form-item label="状态" prop="status" :rules="[{ required: true, message: '请填写状态',trigger: 'blur' }]">
            <el-radio-group v-model="query.status">
              <el-radio :label="true">启用</el-radio>
              <el-radio :label="false">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="描述" prop="describe">
            <el-input v-model="query.describe" />
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
          <el-button @click="clearFun()">取 消</el-button>
          <el-button type="primary" @click="submitFun('ruleForm')">确 定</el-button>
      </span>
    </el-dialog>
  
</template>
<script>
 import { editApi } from '@/api/setting/data'
export default {
  data() {
    return {
      query: {}
    };
  },
  props: {
    row: {
      type: Object
    },
    visible: {
      type: Boolean,
      default: false,
      required: true
    },
    isReload: {
      type: Boolean,
      default: false,
      required: true
    }
  },
  methods: {
    clearFun: function() {
      this.$emit("update:visible", false);
      this.$emit("update:row", {});
    },
    async submitFun(ruleForm) {
      var _this=this;
      _this.$refs[ruleForm].validate(async (valid) => {
        if (valid) {
          var data= await editApi(this.query);
          if(data.code==0){
            _this.$emit("update:visible", false);
            _this.$emit("update:isReload", true);
          }
        } else {
          return false;
        }
      });
    }
  },
  mounted() {
    this.query =JSON.parse( JSON.stringify(this.row)) ;
    if(this.row.id==undefined){
      this.query.id=0;
      this.query.status=true;
    }
  },
  beforeDestroy() {}
};
</script>
<style lang="less" scoped>
.editContent {
  margin: -30px -20px;
  border-top: 1px solid #ebecee;
  padding: 30px 20px 20px;
.avatar-uploader {
    width: 120px;
    height: 120px;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 120px;
    height: 120px;
    line-height: 120px;
    text-align: center;
  }
  .avatar {
    width:120px;
    height: 120px;
    display: block;
  }
}
</style>
