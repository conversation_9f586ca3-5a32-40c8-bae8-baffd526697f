<template>
  <div class="archivesPageContent">
    <im-search-pad
      :is-expand.sync="isExpand"
      :model="listQuery"
      @reset="resetForm"
      @search="onSearchSubmitFun"
    >
      <im-search-pad-item prop="name">
        <el-input v-model="listQuery.model.name" placeholder="请输入客户名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="ceoMobile">
        <el-input v-model="listQuery.model.ceoMobile" placeholder="请输入负责人手机" />
      </im-search-pad-item>
      <im-search-pad-item prop="merchantTypeId">
        <el-select v-model="listQuery.model.merchantTypeId" placeholder="请选择企业类型">
          <el-option v-for="item in listmerchantType" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="cityValue">
        <el-cascader placeholder="请选择所在区域" v-model="cityValue" :props="props" @change="cityChange" clearable />
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="publishStatus">
        <el-select v-model="listQuery.model.publishStatus" placeholder="请选择客户状态">
          <el-option label="已启用" value="Y"></el-option>
          <el-option label="已冻结" value="N"></el-option>
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="listTime">
        <el-date-picker @change="timeSelect" v-model="listTime" type="daterange" align="right" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" style="width:240px"
                        value-format="yyyy-MM-dd" :picker-options="pickerOptions">
        </el-date-picker>
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout
        v-model="listQuery.model.approvalStatus.code"
        :tabs="approvalList"
        @change="chageTabsFun"
      >
        <template slot="button">
          <div>
            <el-button v-if="listQuery.model.approvalStatus.code== 'ACCEPTED' && checkPermission(['admin', 'purchasingAgentlist:batchOn'])" :disabled="multipleSelectionIds.length== 0" @click="updatePurPublishStatus('Y')">批量启用</el-button>
            <el-button v-if="listQuery.model.approvalStatus.code== 'ACCEPTED' && checkPermission(['admin', 'purchasingAgentlist:batchOff'])" :disabled="multipleSelectionIds.length== 0" @click="updatePurPublishStatus('N')">批量冻结</el-button>
            <el-button v-if="listQuery.model.approvalStatus.code== 'PENDING' && checkPermission(['admin', 'purchasingAgentlist:batchRejection'])" :disabled="multipleSelectionIds.length== 0" @click="updatePurMerchantRejected">批量驳回</el-button>
            <el-button v-if="listQuery.model.approvalStatus.code== 'PENDING' && checkPermission(['admin', 'purchasingAgentlist:batchAudit'])" :disabled="multipleSelectionIds.length== 0" @click="updatePurMerchantAcceptedByIds">批量通过</el-button>
            <el-button v-if="listQuery.model.approvalStatus.code== 'REJECTED' && checkPermission(['admin', 'purchasingAgentlist:batchPending']) " :disabled="multipleSelectionIds.length== 0" @click="updatePurMerchantPendingByIds">批量待审</el-button>
            <el-button :disabled="multipleSelectionIds.length== 0" @click="outExcel">导出档案</el-button>
            <el-button @click="refresh">刷新</el-button>
            <el-button v-if="checkPermission(['admin', 'purchasingAgentlist:add'])" type="primary" @click="newFun">+新增客户</el-button>
          </div>
        </template>
      </tabs-layout>
      <div class="table">
        <el-table v-if="list" @selection-change="selectTableItemFun" v-loading="listLoading" :data="list" row-key="id" border fit highlight-current-row style="width: 100%">
          <el-table-column align="center" width="65" fixed :render-header="renderHeader">
            <template slot-scope="scope">
              <span>{{ scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column type="selection" width="55" align="center" fixed> </el-table-column>
          <el-table-column v-for="(item, index) in tableTitle" :key="index" :min-width="(item.width?item.width:'350px')" :label="item.label" show-overflow-tooltip align="left">
            <template slot-scope="{row}">
            <span v-if="item.name=='publishStatus'" :style="row[item.name].code=='N'?'color:#ff0066':''">
              {{row[item.name].code=='Y'? '已启用' : '已冻结'}}
            </span>
              <span v-else>{{ row[item.name] }}</span>
            </template>
          </el-table-column>

          <el-table-column fixed="right" align="center" label="操作" width="150" class="itemAction">
            <template slot-scope="scope">
              <el-button v-if="checkPermission(['admin', 'purchasingAgentlist:detail'])" @click="detailFun(scope.row)" type="text">查看详情</el-button>
              <el-button v-if="listQuery.status == 'reviewed' && checkPermission(['admin', 'purchasingAgentlist:audit'])" @click="reviewedFun(scope.row)" type="text">审核</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-if="total>0" :pageSizes="[2, 10, 20, 50]" :total="total" :page.sync="listQuery.current" :limit.sync="listQuery.size" @pagination="getlist" />
      </div>
    </div>
    <!-- 设置 编辑 -->
    <el-dialog v-if="showEditPage" :title="(row.id>0?'编辑':'新增')+'客户档案'" :visible.sync="showEditPage" width="80%" :show-close="false">
      <edit :visible.sync="showEditPage" :isReload.sync="submitReload" :listmerchantType="listmerchantType" :tabType.sync="listQuery.model.approvalStatus.code" :row.sync="row"></edit>
    </el-dialog>
    <!-- 设置 编辑 -->
  </div>
</template>

<script>
import checkPermission from '@/utils/permission'
import Pagination from "@/components/Pagination";
import { downloadFile } from "@/utils/commons";
import {
  list,
  updatePurPublishStatus,
  updatePurMerchantRejected,
  updatePurMerchantAcceptedByIds,
  updatePurMerchantPendingByIds,
  getCount,
  merchantType,
} from "@/api/purchasingAgent/archivesList";
import { setContextData, getContextData } from "@/utils/auth";
import tableInfo from "@/views/purchasingAgent/archivesList/tableInfo";
import edit from "@/views/purchasingAgent/archivesList/edit";
import { areas } from "@/api/enterprise";
import TabsLayout from '@/components/TabsLayout'

export default {
  components: {
    Pagination,
    edit,
    TabsLayout
  },
  data() {
    return {
      isExpand: false,
      list: [],
      total: 0,
      multipleSelectionIds: [],
      multipleSelection: [],
      listLoading: true,
      listmerchantType: [],
      tableTitle: [],
      pageCount: {
        pendingCount: 0,
        acceptedCount: 0,
        rejectedCount: 0,
        expireCount: 0,
      },
      listQuery: {
        page: 1,
        size: 10,
        model: {
          approvalStatus: { code: "PENDING" },
        },
      },
      showEditPage: false,
      row: {},
      submitReload: false,
      tabType: "",
      // 地区选择
      props: {
        lazy: true,
        checkStrictly: true,
        async lazyLoad(node, resolve) {
          const { level } = node;
          let id = node.data ? node.data.id : "";
          let res = await areas({ parentId: id });
          let list = res.data;
          list.forEach((item) => {
            item.value = item.id;
            item.leaf = level >= 2;
          });
          resolve(list);
        },
      },
      cityValue: [],
      tableSelectTitle: [0, 1, 2, 3],
      showSelectTitle: false,
      listTime: [],
      pickerOptions: {
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
    };
  },
  computed: {
    approvalList() {
      return [
        {
          name: '待审核('+this.pageCount.pendingCount+')',
          value: 'PENDING',
          hide: !checkPermission(['admin', 'purchasingAgentlist-pending:view'])
        },
        {
          name: '已通过('+this.pageCount.acceptedCount+ ')',
          value: 'ACCEPTED',
          hide: !checkPermission(['admin', 'purchasingAgentlist-accepted:view'])
        },
        {
          name: '已驳回('+this.pageCount.rejectedCount+')',
          value: 'REJECTED',
          hide: !checkPermission(['admin', 'purchasingAgentlist-rejected:view'])
        }
      ]
    }
  },
  methods: {
    timeSelect(e) {
      this.listQuery.model.createTimeStart = e[0];
      this.listQuery.model.createTimeEnd = e[1];
    },
    checkPermission,
    refresh() {
      this.listQuery = {
        ...this.listQuery,
        current: 1,
        size: 10
      }
      this.initTbaleTitle()
      this.cityValue = []
      this.getlist()
    },
    cityChange(e) {
      this.listQuery.model.provinceId = e[0];
      this.listQuery.model.cityId = e[1];
      this.listQuery.model.countyId = e[2];
    },
    selectTableItemFun: function (val) {
      this.multipleSelection = val;
      let arr = [];
      val.forEach((item) => {
        arr.push(item.id);
      });
      this.multipleSelectionIds = arr;
    },
    setSucss(type, msg) {
      // this.listQuery.model.approvalStatus.code = type;
      this.$message.success(msg);
      this.getlist();
    },
    // 批量审核
    async updatePurMerchantAcceptedByIds() {
      this.$confirm("此操作将审核通过采购商，是否继续？","提示",{
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: 'warning',
      }).then(async ()=>{
        if (this.checkSelect()) return 0;
        let { data } = await updatePurMerchantAcceptedByIds({
          ids: this.multipleSelectionIds,
        });
        this.setSucss("ACCEPTED", "批量审核成功！");
      })
    },
    // 批量待审
    async updatePurMerchantPendingByIds() {
      this.$confirm("此操作将批量再审采购商，是否继续？","提示",{
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: 'warning',
      }).then(async ()=>{
        if (this.checkSelect()) return 0;
        let { data } = await updatePurMerchantPendingByIds({
          ids: this.multipleSelectionIds,
        });
        this.setSucss("PENDING", "批量待审成功！");
      })
    },
    //  批量驳回采购商
    async updatePurMerchantRejected() {
      this.$confirm("此操作将审核驳回采购商，是否继续？","提示",{
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: 'warning',
      }).then(async ()=>{
        if (this.checkSelect()) return 0;
        let { data } = await updatePurMerchantRejected({
          ids: this.multipleSelectionIds,
        });
        this.setSucss("REJECTED", "批量驳回成功！");
      });
    },
    // 批量启用 / 冻结采购商
    async updatePurPublishStatus(type) {
      let strText = type === "Y" ? "此操作将批量启用采购商,是否继续" : "此操作将批量冻结采购商,是否继续？";
      this.$confirm(strText,"提示",{
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: 'warning',
      }).then(async ()=>{
        if (this.checkSelect()) return 0;
        let { data } = await updatePurPublishStatus({
          ids: this.multipleSelectionIds,
          publishStatus: type,
        });
        if (data) {
          this.$message.success(
            type === "Y" ? "批量启用成功！" : "批量冻结成功！"
          );
          this.getlist();
        }
      })
    },
    checkSelect() {
      if (this.multipleSelectionIds.length < 1) {
        this.$alert("请至少选择一个商家");
        return true;
      }
    },
    async getCount() {
      let params = {
        ...this.listQuery.model
      };
      delete params.approvalStatus;
      let { data } = await getCount(params);
      this.pageCount = data;
    },
    // 获取商家类型
    async getmerchantType() {
      let { data } = await merchantType();
      this.listmerchantType = data;
    },
    async getlist() {
      this.getCount();
      this.listLoading = true;
      const { data } = await list(this.listQuery);
      this.list = data.records;
      this.total = data.total;
      this.listLoading = false;
    },
    newFun: function () {
      setContextData("purchasingAgentlist_detail", this.listQuery);
      this.$router.push({
        path: "/purchasingAgent/purchasingAgentlist/editItem",
      });
    },
    chageTabsFun: function () {
      this.list = [];
      this.listQuery.current = 1
      this.getlist();
      this.initTbaleTitle();
    },
    onSearchSubmitFun() {
      this.getlist();
    },
    resetForm() {
      this.listQuery = {
        current: 1,
        size: 10,
        model: {
          approvalStatus: { code: this.listQuery.model.approvalStatus.code },
        },
      };
      this.listTime = []
      this.cityValue = [];
      this.getlist();
    },
    renderHeader(h, { column }) {
      return (
        <div style="position:relative">
          <i class="el-icon-menu" />
        </div>
      );
    },
    async detailFun(item) {
      setContextData("purchasingAgentlist_detail", this.listQuery);
      this.$router.push({
        path: "/purchasingAgent/purchasingAgentlist/detail",
        query: {
          tabType: this.listQuery.model.approvalStatus.code,
          id: item.id,
        },
      });

    },

    //  导出档案
    async outExcel() {
      if (this.multipleSelection.length > 0) {
        const tHeader = ["id"];
        const filterVal = ["id"];
        this.tableTitle.forEach(function (item) {
          tHeader.push(item.label);
          filterVal.push(item.name);
        });
        let exportData = this.formatJson(this.multipleSelection, filterVal);
        downloadFile({
          tHeader: tHeader,
          fileName: "客户档案列表",
          exportData: exportData,
        });
      } else {
        this.$message.error("请在客户档案列表中勾选客户档案");
      }
    },
    // 格式化导出档案
    formatJson(dataList, filterVal) {
      return dataList.map((v) =>
        filterVal.map((j) => {
          if (j === "publishStatus") {
            return v[j].code =="Y" ? '已启用' : '已冻结';
          } else {
            return v[j];
          }
        })
      );
    },
    renderHeader(h, { column }) {
      var titles = tableInfo[this.listQuery.model.approvalStatus.code];
      var titlesName = ["显示字段项", "隐藏字段项"];
      return (
        <div style="position:relative">
          <div onClick={this.showHeaer}>
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="640px"
            center
            append-to-body={true}
          >
            <el-transfer
              vModel={this.tableSelectTitle}
              data={titles}
              onChange={this.setleftTitleFun}
              titles={titlesName}
            ></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button onClick={this.closeHeaer}>取消</el-button>
              <el-button type="primary" onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      );
    },
    setleftTitleFun(val) {
      this.tableSelectTitle = val;
    },
    showHeaer: function () {
      this.showSelectTitle = true;
    },
    closeHeaer: function () {
      this.showSelectTitle = false;
      this.tableSelectTitle = [];
    },
    setHeaer: function () {
      var titles = tableInfo[this.listQuery.model.approvalStatus.code];
      var listinfo = titles.filter((element, index, self) => {
        return !this.tableSelectTitle.includes(element.key);
      });
      this.tableTitle = listinfo;
      this.showSelectTitle = !this.showSelectTitle;
    },
    initTbaleTitle() {
      this.tableSelectTitle = [];
      this.tableTitle = tableInfo[this.listQuery.model.approvalStatus.code];
    },
  },
  created() {
    this.getmerchantType();
  },
  mounted() {},
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (from.path == "/purchasingAgent/purchasingAgentlist/detail" || from.path == "/purchasingAgent/purchasingAgentlist/editItem") {
        if (getContextData("purchasingAgentlist_detail") != "") {
          vm.listQuery = getContextData("purchasingAgentlist_detail");
        }
      }
      vm.listQuery = {
        page: 1,
        size: 10,
        model: {
          approvalStatus: { 
            code: "PENDING",
          },
          name: '',
          ceoMobile: '',
          merchantTypeId: '',
          publishStatus: '',
        }
      },
      vm.initTbaleTitle();
      vm.getlist();
    });
  },
  watch: {
    submitReload: function (newVal, oldVal) {
      if (newVal) {
        this.submitReload = false;
        this.listQuery.model.approvalStatus.code = this.listQuery.model.approvalStatus.code;
        this.getlist();
      }
    },
  }
};
</script>

<style lang="scss" scoped>
.archivesPageContent {
  padding: 0;
  .temp_searchBox {
    height: 64px;
    overflow: hidden;
    margin-bottom: 0;
  }
  .form-inline {
    height: 60px;
    overflow: hidden;
  }
  .title {
    border-bottom: 2px solid #ebecee;
    margin-bottom: 16px;
    span {
      margin-bottom: -2px;
      padding: 0 15px;
      height: 40px;
      line-height: 30px;
      display: block;
      background: rgba(255, 255, 255, 0);
      border-bottom: 2px solid rgb(64, 158, 255);
      font-size: 16px;
      font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
        "PingFang SC"-400;
      font-weight: 400;
      color: rgb(64, 158, 255);
    }
  }
  .formItem {
    width: 586px;
  }
  .line {
    color: #dfe6ec;
    margin: 0 6px;
  }
  .typeTabs {
    height: 40px;
    margin-bottom: -2px;
    margin-left: 30px;
  }
}
</style>
