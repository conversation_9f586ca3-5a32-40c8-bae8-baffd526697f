import requestAxios from '@/utils/requestAxios'
// import requestAxios from "@/views/businessCentric/request"
// 获取详情
export function getitem(data) {
  return requestAxios({
    url: '/api/agent/platform/salesman/query/' + data,
    method: 'get'
  })
}

// 获取列表
export function getList(data) {
  return requestAxios({
    url: '/api/agent/platform/salesman/page',
    method: 'post',
    data
  })
}


// 获取列表状态数量
export function getListTabNum(data) {
  return requestAxios({
    url: '/api/agent/platform/salesman/listShow',
    method: 'get',
  })
}



// 批量冻结
export function freeze(data) {
  return requestAxios({
    url: '/api/agent/platform/salesman/locked/' + data,
    method: 'put',
  })
}

// 批量启用
export function enable(data) {
  return requestAxios({
    url: '/api/agent/platform/salesman/enabled/' + data,
    method: 'put',
  })
}

// 批量通过
export function accepted(data) {
  return requestAxios({
    url: '/api/agent/platform/salesman/pass/' + data,
    method: 'put',
  })
}

// 批量驳回
export function rejected(data) {
  return requestAxios({
    url: '/api/agent/platform/salesman/reject/' + data,
    method: 'put',
  })
}

// 新增业务员
export function addSalesMan(data) {
  return requestAxios({
    url: '/api/agent/platform/salesman/insert',
    method: 'post',
    data
  })
}

// 编辑业务员
export function editSalesMan(data) {
  return requestAxios({
    url: '/api/agent/platform/salesman/update',
    method: 'post',
    data
  })
}

// 根据业务员获取商品
export function salesManGoods(data) {
  return requestAxios({
    url: '/api/agent/platform/salesman/queryProduct/' + data.id,
    method: 'post',
    data: data.data
  })
}

// 根据业务员获取 客户
export function salesManMerchant(data) {
  return requestAxios({
    url: '/api/agent/platform/salesman/bindCustomerList/' + data.id,
    method: 'post',
    data: data.data
  })
}

// 获取所有客户
export function allMerchant(data) {
  return requestAxios({
    url: '/api/agent/platform/salesman/customerList/' + data.id,
    method: 'post',
    data: data.data
  })
}


// 根据业务员获取 代理区域
export function salesManAreaList(data) {
  return requestAxios({
    url: '/api/agent/platform/salesman/queryArea/' +data.id,
    method: 'post',
    data
  })
}

// 业务员绑定客户
export function bindMerchant(data) {
  return requestAxios({
    url: '/api/agent/platform/salesman/bindCustomer/' +data.id,
    method: 'post',
    data: data.data
  })
}

// 业务员解除绑定客户
export function unboundMerchant(data) {
  return requestAxios({
    url: '/api/agent/platform/salesman/relieveBind/' +data.id,
    method: 'post',
    data:data.data
  })
}

// 识别身份证信息
export function upLoadIdCard(data) {
  return requestAxios({
    url: '/api/agent/platform/salesman/id-recognition',
    method: 'post',
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    },
    data
  })
}
