import axios from 'axios'
import { Message } from 'element-ui'
import store from '@/store'
import { getToken } from '@/utils/auth'
import {
  handleTokenExpired,
  checkTokenInRequest
} from '@/utils/tokenRefresh'

// create an axios instance
const service = axios.create({
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  },
  responseType: 'blob',
  baseURL: process.env.VUE_APP_ADMIN_API_URL,
  timeout: 15000
})

service.interceptors.request.use(
  async config => {
    if (config.headers['Content-Type'] == undefined) {
      config.headers['Content-Type'] = 'application/json;charset=UTF-8'
    }
    config.headers['Authorization'] = process.env.VUE_APP_AUTHORIZATION_CODE

    // 检查token过期并自动刷新
    await checkTokenInRequest()

    if (store.getters.token) {
      config.headers['token'] = getToken()
    }
    return config
  },
  error => {
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

service.interceptors.response.use(
  response => {
    // 对于blob类型的响应，直接返回
    return response
  },
  error => {
    // 处理token过期错误
    const tokenExpiredPromise = handleTokenExpired(error, service)
    if (tokenExpiredPromise !== Promise.reject(error)) {
      return tokenExpiredPromise
    }

    Message({
      message: error.message,
      type: 'error',
      duration: 5 * 1000
    })
    return Promise.reject(error)
  }
)

export default service
