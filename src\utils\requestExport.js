import axios from 'axios'
import { MessageBox, Message } from 'element-ui'
import store from '@/store'
import { getToken } from '@/utils/auth'

// create an axios instance
const service = axios.create({
  headers: {
    'Content-Type': 'application/json;charse=UTF-8'
  },
  responseType: 'blob',
  baseURL: process.env.VUE_APP_ADMIN_API_URL,
  timeout: 15000 
})

service.interceptors.request.use(
  config => {
    if (config.headers['Content-Type']==undefined) {
      config.headers['Content-Type'] = 'application/json;charse=UTF-8'
    }
    config.headers['Authorization'] = process.env.VUE_APP_AUTHORIZATION_CODE
    if (store.getters.token) {
      config.headers['token'] = getToken()
    }
    return config
  },
  error => {
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

service.interceptors.response.use(
  response => {
    const res = response
    return res
  },
  error => {
    Message({
      message: error.message,
      type: 'error',
      duration: 5 * 1000
    })
    return Promise.reject(error)
  }
)

export default service
