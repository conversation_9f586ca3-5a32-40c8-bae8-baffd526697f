<template>
  <div ref="tablePager" class="es-table-pager border-container">
    <el-table ref="table" v-loading="loading" :data="tableData" :tree-props="treeProps" :row-key="rowKey"
      :span-method="spanMethod" style="width: 100%;" stripe border :max-height="tableHeight" :show-summary="showSummary" :summary-method="summaryMethod"
      @select-all="handleSelectAll" @select="handleSelectionChange" @selection-change="selectionChange" :lazy="lazy"
      :load="load">
      <el-table-column type="" align="center" width="65" :render-header="renderHeader" v-if="showHide" fixed>
        <template slot-scope="scope">
          <span>{{ scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="selection" type="selection" :selectable="selectable" :reserve-selection="reserveSelection"
        :row-key="getRowKeys" fixed align="center" />
      <template v-for="(item, index) in showOptions">
        <slot v-if="item.slot" :name="item.name"></slot>
        <el-table-column v-if="!item.slot && !item.columnSlot" :fixed="item.fixed" :key="item.key" :prop="item.prop"
          :label="item.label" :min-width="item.width" :align="item.align" show-overflow-tooltip>
        </el-table-column>
        <el-table-column v-if="item.columnSlot" :fixed="item.fixed" :key="item.key" :prop="item.prop"
          :label="item.label" :min-width="item.width" :align="item.align" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <slot v-if="item.columnSlot" :name="item.name" :row="row"></slot>
          </template>
        </el-table-column>
      </template>
      <el-table-column label="操作" :width="operationWidth" v-if="isNeedButton" fixed="right" align="center">
        <template slot-scope="{ row }">
          <div class="btn-group">
            <slot :row="row"></slot>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div v-if="paging" class="page-row">
      <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size.sync="limit"
        layout="total, sizes, prev, pager, next, jumper" :total="totalCount">
      </el-pagination>
    </div>
  </div>
</template>
<script>
import settings from '@/settings';
import Pagination from '../Pagination/index.vue'
export default {
  name: 'EsTablePager',
  components: {
    Pagination
  },
  props: {
    showHide: {
      type: Boolean,
      default: true
    },
    selection: {
      type: Boolean,
      default: false,
    },
    options: {
      type: Array,
      default: function () {
        return [];
      }
    },
    operationWidth: {
      type: Number,
      default: 100
    },
    // 表格数据
    data: {
      type: Array,
      default: () => []
    },
    // 渲染嵌套数据的配置选项
    treeProps: {
      type: Object,
      default: () => {
        return { hasChildren: 'hasChildren', children: 'children' }
      }
    },
    rowKey: {
      type: String
    },
    load: {
      type: Function
    },
    lazy: {
      type: Boolean,
      default: false
    },
    // 合并行或列的计算方法
    spanMethod: {
      type: Function,
      default: null
    },
    // 是否分页
    paging: {
      type: Boolean,
      default: true
    },
    // 每页条数
    pageSize: {
      type: Number,
      default: settings.pageSize
    },
    // 当前页码
    currentPage: {
      type: Number,
      default: 1
    },
    // 记录总数
    total: {
      type: Number,
      default: 0
    },
    // 通过css控制高度
    heightByCss: {
      type: Boolean,
      default: false
    },
    // 通过数字指定高度
    height: {
      type: Number,
      default: 0
    },
    // 请求方法
    remoteMethod: {
      type: Function,
      default: null
    },
    // created时候自动请求数据
    loadOnCreated: {
      type: Boolean,
      default: true
    },
    transferProps: {
      type: Object,
      default: function () {
        return {
          key: 'name'
        };
      }
    },
    // 操作列
    isNeedButton: {
      type: Boolean,
      default: true
    },
    reserveSelection: {
      type: Boolean,
      default: false
    },
    // 合计行
    showSummary: {
      type: Boolean,
      default: false
    },
    // 自定义合计方法
    summaryMethod: {
      type: Function,
      default: null,
    }
  },
  data() {
    return {
      showOptions: [],
      showOptionsOrigin: [],
      transferSelectOptions: [],
      transferData: [],
      totalCount: this.total,
      tableHeight: 1000,
      page: this.currentPage,
      limit: this.pageSize,
      loading: false,
      showSelectTitle: false,
      titles: ['隐藏字段项', '显示字段项'],
      sizeChange: false
    }
  },
  computed: {
    tableData: {
      get() {
        return this.data
      },
      set(val) {
        this.$emit('update:data', val)
      },
    },
    pageLeftText() {
      let totalPage = 0;
      if (this.totalCount) {
        totalPage = Math.ceil(this.totalCount / this.limit);
      }
      return `共 ${this.totalCount} 条记录 第 ${this.currentPage} / ${totalPage} 页`;
    }
  },
  watch: {
    options: {
      handler() {
        this.transferSelectOptions = []
        this.showOptions = this.options.map(item => {
          item.prop = item.prop || item.name
          return item
        })
        this.showOptionsOrigin = JSON.parse(JSON.stringify(this.showOptions))
        this.transferData = JSON.parse(JSON.stringify(this.showOptions))
      },
      deep: true,
      immediate: true
    },
    total(newV, oldV) {
      this.totalCount = newV
    },
    currentPage(newV, oldV) {
      this.page = newV
    },
    pageSize(newV, oldV) {
      this.limit = newV
    }
  },
  mounted() {
    if (this.heightByCss) {
      this.tableHeight = this.paging ? this.$refs.tablePager.clientHeight - 35 : this.$refs.tablePager.clientHeight - 10
    }
    if (this.height > 0) {
      this.tableHeight = this.paging ? this.height - 10 : this.height - 35
    }
    this.$refs.table.doLayout()
  },
  created() {
    if (this.loadOnCreated && this.remoteMethod) {
      this.loadData(1)
    }
  },
  methods: {
    setHeader() {
      var listinfo = this.showOptionsOrigin.filter((element, index, self) => {
        return !this.transferSelectOptions.includes(element.key)
      })
      this.showOptions = listinfo;
      this.showSelectTitle = !this.showSelectTitle
    },
    onCancel() {
      this.showSelectTitle = false
    },
    renderHeader(h, { column }) {
      var titlesName = ['显示字段项', '隐藏字段项'];
      return (
        <div style="position:relative">
          <div onClick={this.showHeader} >
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            append-to-body={true}
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="640px"
            center>
            <el-transfer vModel={this.transferSelectOptions} onChange={this.setLeftTitleFun} props={this.transferProps} titles={titlesName} data={this.transferData}></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button onClick={this.closeHeader}>取消</el-button>
              <el-button type="primary" onClick={this.setHeader}>确认</el-button>
            </div>
          </el-dialog>
        </div>
      )
    },
    setLeftTitleFun(val) {
      this.transferSelectOptions = val
    },
    showHeader() {
      this.showSelectTitle = true
    },
    closeHeader() {
      this.showSelectTitle = false
    },
    selectable(row, index) {
      if (row.selectable === undefined || row.selectable === false) {
        return true
      } else {
        return false
      }
    },
    getRowKeys(row) {
      if (this.reserveSelection) {
        return row.id
      }
    },
    async handleChangePage(pager) {
      if (this.remoteMethod) {
        await this.loadData(pager.page)
      }
      this.$emit('update:currentPage', pager.page)
      this.$emit('update:pageSize', pager.pageSize)
      this.$emit('change-page', pager)
    },
    doRefresh() {
      this.page = 1
      this.loadData(1)
    },
    loadData(page) {
      this.loading = true
      return new Promise(async (resolve, reject) => {
        if (this.remoteMethod) {
          try {
            const { data } = await this.remoteMethod({ current: page, size: this.limit })
            this.tableData = data.records
            if (this.sizeChange) {
              this.$emit('size-change', this.tableData)
            }
            if (data.total === 0) {
              this.totalCount = 0
            }
            if (this.paging) {
              this.page = data.current || 1
              this.$emit('update:currentPage', data.current)
              this.totalCount = data.total || data.list.length
            }
            resolve(data)
            this.loading = false
            this.sizeChange = false
          } catch (error) {
            this.tableData = []
            this.loading = false
            this.sizeChange = false
          }
        } else {
          reject('没有异步方法')
          this.loading = false
          this.sizeChange = false
        }
      })
    },
    handleSelectionChange(val) {
      this.$emit('selection-change', val)
    },
    selectionChange(val, row) {
      this.$emit('selectionChangeHandle', val, row)
    },
    handleSelectAll(val) {
      this.$emit('selection-all', val)
    },
    resetPageData() {
      this.totalCount = this.total
      this.tableHeight = 1000
      this.page = this.currentPage
      this.limit = this.pageSize
      this.loading = false
    },
    handleSizeChange(v) {
      this.page = v;
      this.sizeChange = true
      this.loadData(1);
    },
    handleCurrentChange(v) {
      this.page = v;
      this.loadData(v);
    },
    getTable() {
      return this.$refs['table']
    },
    clearSelection() {
      this.$refs.table.clearSelection()
    }
  },
}
</script>
<style lang="scss">
.es-table-pager {
  position: relative;
  background: #FFF;

  .el-table {
    &:before {
      background-color: transparent;
    }
  }

  .el-table--mini td,
  .el-table--mini th {
    padding: 3px 0;
  }

  .el-table th .cell {
    border-right: none;
    height: 23px;
    display: flex;
    align-items: center;
  }

  .el-table th.is-center .cell {
    justify-content: center;
  }

  .page-row {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #505465;
    font-size: 13px;
    margin-top: 16px;
  }

  .page-row-right {
    display: flex;
  }

  .btn-group {
    display: flex;
    justify-content: center;
  }
}
</style>

