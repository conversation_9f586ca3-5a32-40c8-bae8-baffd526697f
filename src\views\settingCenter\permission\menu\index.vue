<template>
  <div class="menuPageContent">
    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        v-model="listQuery.params"
        :tabs="tabs"
        @change="chageTabsFun"
      >
        <template slot="button">
          <el-button @click="refresh">刷新</el-button>
        </template>
      </tabs-layout>
      <div class="column_info">
        <!-- 节点树 -->
        <div class="column_tree">
          <div style="margin-bottom: 20px">
            <el-button @click="addMenuItem">新增根节点</el-button>
            <el-button @click="delMenuItem">删除</el-button>
          </div>
          <el-input placeholder="输入关键字进行过滤" clearable v-model="filterText" @input="inputFilterText" style="margin-bottom: 10px"/>
          <el-tree
            v-loading="listLoading"
            highlight-current
            node-key="id"
            ref="tree"
            :data="list"
            :default-expanded-keys="defaultExpandedKeys"
            :filter-node-method="filterNode"
            @node-contextmenu="nodeContextmenu"
            @node-expand="nodeExpand"
            @node-collapse="nodeCollapse"
            @node-click="nodeClick"
            >
            <span class="custom-tree-node" slot-scope="{ node, data }">
              <span>{{ node.label }}</span>
              <span class="right">
                <template v-if="data.id === currentSelectedItemMenu.id">
                  <el-button type="text" size="mini" icon="el-icon-plus" @click.stop="addMenuItem(false)"/>
                  <el-button type="text" size="mini" icon="el-icon-delete" @click="delMenuItem"/>
                </template>
                <span :class="['is_enable', !data.isEnable ? 'disabled' : '']"></span>
              </span>
            </span>
          </el-tree>
        </div>
        <!-- 当前节点 -->
        <div class="edit_column">
          <EditMenuForm ref="editMenuFormRef" :folderTree="folderTree" :clientId="listQueryParams.clientId" :commerceModel="listQueryParams.commerceModel" @saveMenu="saveMenuSuccess"/>
        </div>
        <!-- 权限表格 -->
        <div class="auth_table">
          <itemActions ref="itemActionsRef"/>
        </div>
      </div>
    </div>
    <ul v-show="rightMenuInfo.visible" :style="{left:rightMenuInfo.left+'px',top:rightMenuInfo.top+'px'}" class="contextmenu">
      <li @click="rightMenuInfo.visible=false">编辑</li>
      <li @click="addMenuItem(false)">新增子节点（全新）</li>
      <li @click="addMenuItemCopy">新增根节点（复制当前节点）</li>
      <li @click="addMenuItemCopy(false, true)">新增同级节点（复制当前节点）</li>
      <li @click="addMenuItemCopy(false)">新增子节点（复制当前节点）</li>
      <li @click="delMenuItem">删除节点</li>
    </ul>
  </div>
</template>
<script>
import checkPermission from '@/utils/permission'
import { tree, deleteApi } from '@/api/setting/permission/menu'
import itemActions from '@/views/settingCenter/permission/menu/itemActions'
import TabsLayout from '@/components/TabsLayout'
import EditMenuForm from './editMenuForm.vue'
import { deepClone } from "@/utils";

// 过滤掉菜单的最后一级路由(只过滤出组件为Layout的目录)
const filterRouter = (arr) => {
  let arrClone = deepClone(arr)
  if (Array.isArray(arr) && arr.length > 0) {
    arrClone = arrClone.filter(item => {
      if (Array.isArray(item.children) && item.children.length > 0) {
        item.children = filterRouter(item.children)
      }
      return item.component === 'Layout' || Array.isArray(item.children) && item.children.length > 0
    })
  }
  return arrClone
}

export default {
  name: 'Menu',
  components: {
    itemActions,
    TabsLayout,
    EditMenuForm
  },
  data() {
    return {
      // 节点右键
      rightMenuInfo: {
        visible: false,
        top: 0,
        left: 0
      },
      filterText: '',
      itemMenu: {},
      defaultExpandedKeys: [],
      defaultCheckedKeys: [],
      currentSelectedItemMenu: {}, // 当前选中节点数据
      currentSelectedNode: {}, // 当前选中节点
      showActionsTable: false,
      showEdit: false,
      listLoading: false,
      listQuery: {
        params: JSON.stringify({
          clientId: 'admin_ui',
          commerceModel: 'SAAS_PLATFORM'
        })
      },
      folderTree: [],
      list: [],
      total: 0,
    }
  },
  computed: {
    listQueryParams() {
      return JSON.parse(this.listQuery.params)
    },
    tabs() {
      return [
        {
          name: '平台端栏目配置',
          value: JSON.stringify({
            clientId: 'admin_ui',
            commerceModel: 'SAAS_PLATFORM'
          }),
          hide: !checkPermission(['admin', 'admin-setting-permission-menu:platformView'])
        },
        {
          name: 'saas销售商端栏目配置',
          value: JSON.stringify({
            clientId: 'sale_ui',
            commerceModel: 'SAAS'
          }),
          hide: !checkPermission(['admin', 'admin-setting-permission-menu:saasDistributorView'])
        },
      ]
    },
  },
  watch: {
    'rightMenuInfo.visible': {
      handler(value) {
        if (value) {
          document.body.addEventListener('click', this.closeMenu)
        } else {
          document.body.removeEventListener('click', this.closeMenu)
        }
      }
    }
  },
  created() {
    this.listQuery.params = this.tabs.find(item => !item.hide).value
    this.getList()
  },
  methods: {
    // 刷新
    refresh() {
      this.defaultExpandedKeys = []
      this.currentSelectedItemMenu = {}
      this.filterText = ''
      this.getList()
      this.addMenuItem()
      this.$refs.itemActionsRef.resetAuthList()
    },
    inputFilterText(val) {
      this.$refs.tree.filter(val);
    },
    checkPermission,
    // 新建根节点
    addMenuItem(isRoot = true) {
      let itemMenu = {
        commerceModel: this.listQueryParams.commerceModel,
        parentName: '根节点'
      }
      if (!isRoot) {
        itemMenu.parentName = this.currentSelectedItemMenu.label
        itemMenu.parentId = this.currentSelectedItemMenu.id
      }
      this.$refs.editMenuFormRef.setItemMenu(itemMenu)
    },
    // 新增、子节点（复制当前节点）
    addMenuItemCopy(isRoot=true, sameLevel=false) {
      let itemMenu = {
        commerceModel: this.listQueryParams.commerceModel,
        parentName: '根节点',
        id: '',
        code: ''
      }
      if (!isRoot) {
        itemMenu.parentName = this.currentSelectedItemMenu.label
        itemMenu.parentId = this.currentSelectedItemMenu.id
      }
      if (sameLevel) {
        itemMenu.parentName = this.currentSelectedNode.parent.label
        itemMenu.parentId = this.currentSelectedItemMenu.parentId
      }
      this.$refs.editMenuFormRef.setItemMenu({
        ...this.currentSelectedItemMenu,
        ...itemMenu
      })
    },
    // 搜索节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.toLowerCase().indexOf(value.toLowerCase()) !== -1;
    },
    // 删除节点
    delMenuItem() {
      const id = this.currentSelectedItemMenu.id
      if (!id) {
        this.$message.warning("请先选择一个节点")
        return
      }
      this.$confirm('此操作将永久删除该信息, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteApi(id).then(res => {
          if (res.isSuccess) {
            this.$message.success("删除成功")
            this.getList()
            this.addMenuItem()
          }
        })
      }).catch(() => {})
    },
    // 获取滚动条高度
    getScrollTop() {
      var scroll_top = 0;
      if (document.documentElement && document.documentElement.scrollTop) {
        scroll_top = document.documentElement.scrollTop;
      }
      else if (document.body) {
        scroll_top = document.body.scrollTop;
      }
      return scroll_top;
    },
    // 右键节点
    nodeContextmenu(e, data, node) {
      this.nodeClick(data, node)
      const offsetLeft = this.$el.getBoundingClientRect().left // container margin left
      const scrollTop = this.getScrollTop()
      this.rightMenuInfo.left = e.clientX - offsetLeft + 5
      this.rightMenuInfo.top = e.clientY - 86 - 10 + scrollTop
      this.rightMenuInfo.visible = true
      this.$refs.tree.setCurrentKey(data.id)
    },
    // 关闭右键菜单方法
    closeMenu() {
      this.rightMenuInfo.visible = false
    },
    // 点击节点
    nodeClick(itemMenu, node) {
      this.currentSelectedItemMenu = itemMenu
      this.currentSelectedNode = node
      this.rightMenuInfo.visible = false
      this.$refs.editMenuFormRef.setItemMenu({
        ...itemMenu,
        parentName: node.parent?.label || '根节点'
      })
      this.$refs.itemActionsRef.getAuthList(itemMenu)
    },
    // 展开节点
    nodeExpand(e) {
      this.defaultExpandedKeys.push(e.id)
    },
    // 缩回节点
    nodeCollapse(e) {
      const { id } = e
      const index = this.defaultExpandedKeys.indexOf(id)
      this.defaultExpandedKeys.splice(index, 1)
    },
    // 保存节点成功
    saveMenuSuccess(id) {
      this.getList().then(() => {
        this.$refs.tree.filter(this.filterText)
        if (id) this.$refs.tree?.setCurrentKey(id)
      })
    },
    // 点击tab
    chageTabsFun: function() {
      this.list = []
      this.refresh()
    },
    // 获取权限列表
    async getList() {
      this.listLoading = true
      const { data } = await tree(JSON.parse(this.listQuery.params))
      if (data) {
        this.list = data
        this.folderTree = filterRouter(data)
      }
      this.listLoading = false
    },
  }
}
</script>
<style lang="less" scoped>
.menuPageContent  {
  position: relative;
  .title{
    border-bottom:2px solid #DCDDE0;
    margin-bottom: 16px;
      span{
        margin-bottom: -2px;
        padding:0 15px;
        height: 40px;
        line-height: 30px;
        display:block;
        background: rgba(255,255,255,0);
        border-bottom:2px solid rgb(64, 158, 255);
        font-size: 16px;
        font-family: 'PingFangSC-Regular', 'PingFang SC', 'PingFangSC-Regular', 'PingFang SC'-400;
        font-weight: 400;
        color:rgb(64, 158, 255);
      }
    .typeTabs .el-tabs__header{margin-bottom: 0px;}
  }
  .el-dropdown-link {
    margin-left:12px;
    cursor: pointer;
    font-size: 12px;
    color: #0056E5;
  }
  .el-icon-arrow-down {
    font-size: 12px;
  }
.formItem{width:586px;}
.line{color:#dfe6ec; margin:0 6px;}
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
  .right {
    display: flex;
    align-items: center;
    .is_enable {
      margin-left: 10px;
      display: inline-block;
      width: 5px;
      height: 5px;
      background-color: #2dac0c;
      border-radius: 50%;
      &.disabled {
        background-color: #ff2d47;
      }
    }
  }
}
.column_info {
  display: flex;
  .column_tree {
    min-width: 350px;
    max-width: 350px;
    box-sizing: border-box;
    padding-right: 10px;
  }
  .edit_column {
    flex:1;
    border-left: 1px solid #ddd;
    box-sizing: border-box;
    padding: 0 20px
  }
  .auth_table {
    flex:1;
    border-left: 1px solid #ddd;
    box-sizing: border-box;
    padding: 0 20px
  }
}

.contextmenu {
  margin: 0;
  background: #fff;
  z-index: 3000;
  position: absolute;
  list-style-type: none;
  padding: 5px 0;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 400;
  color: #333;
  box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, .3);
  li {
    margin: 0;
    padding: 7px 16px;
    cursor: pointer;
    &:hover {
      background: #eee;
    }
  }
}
</style>

<style lang="less">
.menuPageContent {
  .title {
    .typeTabs .el-tabs__header {
      margin-bottom: 0;
    }
    .el-tabs__nav-wrap::after{
      height: 0;
    }
  }
}
</style>
