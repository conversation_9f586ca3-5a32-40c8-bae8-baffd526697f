<template>
  <el-dialog :title="type + '经销商'" top="50px" :visible.sync="visible" :destroy-on-close="true" :close-on-click-modal="false" width="550px">
    <el-form ref="form" :model="form" label-width="115px" :rules="rules">
      <div class="sku_setting">
        <el-form-item label="经销商名称：" prop="name">
          <el-input v-model="form.name" placeholder="请输入经销商名称" />
        </el-form-item>
        <el-form-item label="负责人：" prop="ceoName">
          <el-input v-model="form.ceoName" placeholder="请输入负责人名称" />
        </el-form-item>
        <el-form-item label="负责人手机：" prop="ceoMobile">
          <el-input v-model="form.ceoMobile" placeholder="请输入负责人手机" />
        </el-form-item>
        <el-form-item label="所在区域：" prop="provinceId">
          <el-cascader
            v-model="areaArr"
            style="width: 100%"
            placeholder="请选择所在区域"
            :props="{ value: 'id', label: 'label' }"
            @change="cityChange"
            :options="areasTree"
            clearable
          />
        </el-form-item>
        <el-form-item label="注册地址：" prop="registerAddress">
          <el-input :rows="4" type="textarea" v-model="form.registerAddress" placeholder="请输入注册地址" />
        </el-form-item>
        <template v-if="!form.id">
          <el-form-item label="登录账号：" prop="loginAccount">
            <el-input v-model="form.loginAccount" placeholder="请输入登录账号" />
          </el-form-item>
          <el-form-item label="注册手机号：" prop="userMobile">
            <el-input v-model="form.userMobile" placeholder="请输入注册手机号" />
          </el-form-item>
          <el-form-item label="登录密码：" prop="password">
            <el-input v-model="form.password" placeholder="请输入登录密码" />
          </el-form-item>
          <el-form-item label="确认密码：" prop="rePassword">
            <el-input v-model="form.rePassword" placeholder="请输入确认密码" />
          </el-form-item>
        </template>
        <el-form-item label="启用状态：" prop="note">
          <el-radio-group v-model="form.publishStatus.code">
            <el-radio label="Y">启用</el-radio>
            <el-radio label="N">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </div>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="hide">取消</el-button>
      <el-button :loading="loading" type="primary" @click="confirm">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { editBusinessReport } from '@/api/dealerManagement'
const initForm = () => {
  return {
    id: '',
    publishStatus: { code: 'Y' },
    name: '',
    ceoName: '',
    ceoMobile: '',
    provinceId: '',
    cityId: '',
    countyId: '',
    registerAddress: '',
    loginAccount: '',
    userMobile: '',
    password: '',
    rePassword: '',
    commerceModel: "DEALER"
  }
}

export default {
  name: 'editPurchaserDialog',
  data() {
    var validatePass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入密码'))
      } else {
        this.$refs.form.validateField('rePassword')
        callback()
      }
    }
    var validatePass2 = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入确认密码'))
      } else if (value !== this.form.password) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    return {
      areaArr: [],
      areasTree: [],
      visible: false,
      loading: false,
      type: '',
      orgList: [],
      form: initForm(),
      rules: {
        name: [{ required: true, message: '请输入经销商名称', trigger: 'blur' }],
        ceoName: [{ required: true, message: '请输入负责人姓名', trigger: 'blur' }],
        ceoMobile: [{ required: true, message: '请输入负责人手机', trigger: 'blur' }],
        provinceId: [{ required: true, message: '请选择所在区域', trigger: 'change' }],
        registerAddress: [{ required: true, message: '请输入注册地址', trigger: 'blur' }],
        loginAccount: [{ required: true, message: '请输入登录账号', trigger: 'blur' }],
        password: [{ required: true, validator: validatePass, trigger: 'blur' }],
        rePassword: [{ required: true, validator: validatePass2, trigger: 'blur' }]
      }
    }
  },
  methods: {
    cityChange(e) {
      this.form.provinceId = e[0]
      this.form.cityId = e[1]
      this.form.countyId = e[2]
    },
    show(row, areasTree) {
      this.form = initForm()
      this.areaArr = []
      this.areasTree = areasTree
      delete this.form.id
      if (this.$refs.form) {
        this.$refs.form.resetFields()
        this.$refs.form.clearValidate()
      }
      if (row) {
        this.type = row.id ? '编辑' : '新增'
        Object.keys(this.form).forEach((key) => {
          this.$set(this.form, key, row[key])
        })
        this.$set(this.form, 'id', row.id)
        console.log(row.provinceId)
        if (row.provinceId === '0') this.areaArr = ['0']
        else this.areaArr = [row.provinceId, row.cityId, row.countyId]
      }
      this.visible = true
    },
    hide() {
      this.visible = false
    },
    confirm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true
        editBusinessReport(this.form)
          .then((res) => {
            if (res.code !== 0) return
            this.$message.success('修改成功')
            this.hide()
            this.$emit('setSuccess')
          })
          .finally(() => {
            this.loading = false
          })
        }
       
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.sku_setting {
  display: flex;
  flex-direction: column;
}
</style>
