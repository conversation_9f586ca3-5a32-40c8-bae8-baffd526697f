import requestAxios from '@/utils/requestAxios'

// 商品分组分页查询
export function productGroupPage(data) {
    return requestAxios({
      method: 'post',
      url: '/api/product/admin/productGroup/page',
      data
    })
}

// 商品分组删除
export function delProductGroup(id){
  return requestAxios({
    method: 'delete',
    url: `/api/product/admin/productGroup?id=${id}`,
  })
}

// 商品分组详情
export function detailGroup(id){
  return requestAxios({
    method:'get',
    url: `/api/product/admin/productGroup/${id}`
  })
}

// 商品分组修改
export function editProductGroup(data){
  return requestAxios({
    method: 'put',
    url: '/api/product/admin/productGroup',
    data
  })
}

// 新增商品分组
export function addProductGroup(data){
  return requestAxios({
    method: 'post',
    url: '/api/product/admin/productGroup',
    data
  })
}

// 绑定商品与分组
export function bindingProductGroup(data) {
  return requestAxios({
    method: "post",
    url: '/api/product/admin/productGroup/binding',
    data
  })
}

// 解绑商品与分组
export function noBindingProductGroup(data) {
  return requestAxios({
    method: "post",
    url:'/api/product/admin/productGroup/noBinding',
    data
  })
}

// 查看当前商品分组未关联的商品
export function noRelevance(data){
  return requestAxios({
    method: 'post',
    url: '/api/product/admin/productGroup/noRelevance',
    data
  })
}

// 查看当前商品分组已关联的商品
export function relevance(data) {
  return requestAxios({
    method:'post',
    url:'/api/product/admin/productGroup/relevance',
    data
  })
}

// 查询已关联商品和未关联数量
export function productGroupNum(data){
  return requestAxios({
    method:'post',
    url:'/api/product/admin/productGroup/sum',
    data
  })
}