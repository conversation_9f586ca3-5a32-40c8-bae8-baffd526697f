<template>
  <div class="businessTypeEditContent">
    <el-form class="form" :model="query" ref="ruleForm" label-width="146px">
      <el-form-item
        required
        class="formItem"
        prop="name"
        label="企业类型名称:"
        :rules="[{ required: true, message: '请填写企业类型名称' }]"
      >
        <el-input
          clearable
          style="width:250px"
          v-model="query.name"
          placeholder="请填写企业类型名称"
        ></el-input>
      </el-form-item>
      <el-form-item
        required
        class="formItem"
        prop="types"
        label="企业资质文件:"
        :rules="[{ required: true, message: '请选择企业资质文件' }]"
      >
        <el-checkbox-group v-model="query.types">
          <el-checkbox v-for="(item,index) in checkBoxs" :key="index"  :label="item.value"  name="types">{{item.label}}</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item>
        <el-button @click="clearFun()">取 消</el-button>
        <el-button type="primary" @click="submitFun('ruleForm')"
          >确 定</el-button
        >
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
export default {
  data() {
    return {
      checkBoxs: [
        {
          value: 1,
          label: "药品生产许可证"
        },
        {
          value: 2,
          label: "药品GSP证书"
        },
        {
          value: 3,
          label: "采购员身份证复印件"
        },
        {
          value: 4,
          label: "医疗机构执业许可证"
        },
        {
          value: 5,
          label: "采购授权委托书"
        },
        {
          value: 6,
          label: "授权委托书"
        },
        {
          value: 7,
          label: "计划生育服务机构执业许可证"
        }
      ],
      query: {
        name: "",
        types: ""
      }
    };
  },
  props: {
    row: {
      type: Object
    },
    visible: {
      type: Boolean,
      default: false,
      required: true
    },
    isReload: {
      type: Boolean,
      default: false,
      required: true
    }
  },
  methods: {
    clearFun: function() {
      this.$emit("update:visible", false);
      this.$emit("update:row", {});
    },
    submitFun: function(businessScopeForm) {
      this.$refs[businessScopeForm].validate(valid => {
        if (valid) {
          this.$emit("update:visible", false);
          console.info("提交修改");
          this.$emit("update:isReload", true);
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    }
  },
  mounted() {
    this.query = this.row;
  },
  beforeDestroy() {}
};
</script>
<style lang="less" scoped>
.businessTypeEditContent {
  margin: -30px -20px;
  border-top: 1px solid #ebecee;
  padding: 30px 20px;
}
</style>
