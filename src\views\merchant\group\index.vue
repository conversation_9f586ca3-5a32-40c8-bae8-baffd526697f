<template>
  <div>
    <div class="tab_bg">
      <el-tabs v-model="activeName">
        <el-tab-pane label="客户分组" name="first"></el-tab-pane>
      </el-tabs>
    <div class="tab_btns">
      <el-button @click="getList">刷新</el-button>
      <el-button type="primary" @click="addOrEdit(1,'')">+ 新增分组</el-button>
    </div>
    <el-table
      ref="dragTable"
      v-loading="listLoading"
      :data="list"
      row-key="id"
      border
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column align="center" width="65" :render-header="renderHeader" fixed>
        <template scope="scope">
          <span>{{scope.$index +1}}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-for="(item, index) in tableTitle"
        :key="index"
        min-width="100px"
        :label="item.label"
        show-overflow-tooltip
      >
        <template slot-scope="{ row }">
          <span>{{ row[item.name] }}</span>
        </template>
      </el-table-column>
      <el-table-column width="110" label="操作">
        <template slot-scope="{ row }">
          <a v-on:click="addOrEdit(2,row)" style="margin-right: 10px;">编辑</a>
          <el-dropdown trigger="click" @command="handleCommand">
            <span class="el-dropdown-link">
              更多<i class="el-icon-arrow-down el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="handleAdd(row)">添加客户</el-dropdown-item>
              <el-dropdown-item @click.native="handleCheck(row)">查看客户</el-dropdown-item>
              <el-dropdown-item  @click.native="handleDel(row)">删除</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
      <pagination v-bind:total="total" v-bind:page="page" @pagination="pagination" class="fr"></pagination>
    <!--添加客户-->
    <add-user :visible="showAdd" v-bind:row="rowData" v-bind:saleMerchantId="saleMerchantId" @changeShow="changeAddUser"/>
    <!--查看客户-->
    <list-user :visible="showList" @addUser="showAdd=true" v-bind:row="rowData" v-bind:saleMerchantId="saleMerchantId" @changeShow="changeListUser"/>
    <!--新增/编辑客户组-->
    <add-group :groupVisible="groupVisible" v-bind:title="title" v-bind:saleMerchantId="saleMerchantId" v-bind:detail="detail" @load="getList" @changeShow="changeAddGroup"></add-group>
    <del-group :delVisible="delVisible" v-bind:data="detail" @changeShow="changeDelGroup"></del-group>
  </div>
  </div>
</template>

<script>
import { merchantGroupList,findByUserIdSale } from "@/api/group";
import Sortable from "sortablejs";
import AddUser from "./addUser";
import ListUser from './listUser'
import AddGroup from './addGroup'
import DelGroup from './delGroup'
import Pagination from '@/components/Pagination/index.vue'

const TableColumns = [
  { label: "客户分组名称", name: "name" },
  { label: "客户数", name: "customerNumber",width: '120' },
  { label: "操作人", name: "createUser" },
  { label: "操作时间", name: "updateTime" },
];
const TableColumnList = [];
for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({ key: i, ...TableColumns[i] });
}

export default {
  name: "DragTable",
  components: {
    AddUser,
    ListUser,
    AddGroup,
    DelGroup,
    Pagination
  },
  data() {
    return {
      showSelectTitle: false,
      showAdd: false,
      showList: false,
      groupVisible: false,
      delVisible: false,
      title: '',
      tableTitle: TableColumnList,
      tableVal: [],
      list: null,
      total: 0,
      page: 0,
      listLoading: false,
      listQuery: {
        current: 1,
        size: 10,
        map: {},
        model: {
          code: '',
          name: '',
          priceModulus: '',
          saleMerchantId: ''
        },
        order: 'descending',
        sort: 'id'
      },
      sortable: null,
      oldList: [],
      newList: [],
      activeName: 'first',
      saleMerchantId: '',//经销商id
      detail: {},
      name: '',
      rowData:''
    };
  },
  created() {
   /* this.getSaleId()*/
    this.getList()
  },
  methods: {
    changeAddUser(data) {
      if (data === 'false') {
        this.showAdd = false
      } else {
        this.showAdd = true
      }
    },
    changeListUser(data) {
      if (data === 'false') {
        this.showList = false
      } else {
        this.showList = true
      }
    },
    changeDelGroup(data) {
      if (data === 'false') {
        this.delVisible = false
      } else {
        this.delVisible = true
      }
    },
    changeAddGroup(data) {
      if (data === 'false') {
        this.groupVisible = false
      } else {
        this.groupVisible = true
      }
    },
    //获取经销商id
    async getSaleId() {
      let userId = localStorage.getItem('userId')
      const { data } = await findByUserIdSale(userId)
      this.saleMerchantId = data.id
      this.listQuery.model.saleMerchantId=data.id

    },
    async getList() {
      this.listLoading = false;
      const { data } = await merchantGroupList(this.listQuery);
      this.list = data.records;
      this.total = data.total;
      this.page = data.current
      this.listLoading = false;
      this.$nextTick(() => {
        this.setSort();
      });
    },
    pagination(val) {
      this.listQuery.current = val.page
      this.listQuery.size = val.limit
      this.getList()
    },
    handleCommand(type) {
      if (type === "add") {
        this.showAdd = !this.showAdd
        return
      }
      if (type === "list") {
        this.showList = !this.showList
        return
      }
      if (type === "del") {
      }
    },
    handleDel(row) {
      this.delVisible = true
      this.detail = row
    },
    handleCheck(row) {
      this.showList=true
      this.rowData = row
    },
    handleAdd(row) {
      this.showAdd=true
      this.rowData = row
    },
    //新增分组
    addOrEdit(type,row) {
      this.groupVisible = true
      if(type === 1) {
        this.title='新增客户组'
        this.detail = {}
      } else {
        this.title='编辑客户组'
        this.detail = row
      }
    },
    renderHeader(h, { column }) {
      // h即为cerateElement的简写，具体可看vue官方文档
      return (<div style="position:relative">
          <div onClick={this.setHeaer}>
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="640px"
            center
          >
            <el-transfer
              vModel={this.tableVal}
              data={this.tableTitle}
            ></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button type="primary" onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>);
    },

    setHeaer: function () {
      this.showSelectTitle = !this.showSelectTitle;
    },
    setSort() {
      const el = this.$refs.dragTable.$el.querySelectorAll(
        ".el-table__body-wrapper > table > tbody"
      )[0];
      this.sortable = Sortable.create(el, {
        ghostClass: "sortable-ghost", // Class name for the drop placeholder,
        setData: function (dataTransfer) {
          // to avoid Firefox bug
          // Detail see : https://github.com/RubaXa/Sortable/issues/1012
          dataTransfer.setData("Text", "");
        },
        onEnd: (evt) => {
          const targetRow = this.list.splice(evt.oldIndex, 1)[0];
          this.list.splice(evt.newIndex, 0, targetRow);

          // for show the changes, you can delete in you code
          const tempIndex = this.newList.splice(evt.oldIndex, 1)[0];
          this.newList.splice(evt.newIndex, 0, tempIndex);
        },
      });
    },
    getIndex(row) {
      console.log(row)
    }
  },
};
</script>

<style>
.sortable-ghost {
  opacity: 0.8;
  color: #fff !important;
  background: #42b983 !important;
}
</style>

<style scoped>
</style>
