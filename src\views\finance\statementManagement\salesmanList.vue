<template>
  <div>
    <im-search-pad
      :is-expand.sync="isExpand"
      :model="model"
      @reset="reload"
      @search="searchLoad"
    >
      <im-search-pad-item prop="salesmanSn">
        <el-input v-model="model.salesmanSn" placeholder="请输入业务员编码" />
      </im-search-pad-item>
      <im-search-pad-item prop="name">
        <el-input v-model="model.name" placeholder="请输入业务员姓名" />
      </im-search-pad-item>
      <im-search-pad-item prop="contact">
        <el-input v-model="model.contact" placeholder="请输入业务员手机码" />
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="during">
        <el-date-picker
          v-model="model.during"
          type="datetimerange"
          align="right"
          unlink-panels
          range-separator="至"
          start-placeholder="起始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd hh:mm:ss"
        >
        </el-date-picker>
      </im-search-pad-item>
    </im-search-pad>

    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        :tabs="tabsList"
        @change="handleChangeTab"
      >
        <template slot="button">
          <!-- <el-button  v-if="checkPermission(['admin', 'salesmanList:export'])">导出</el-button> -->
          <el-button  @click="reload">刷新</el-button>
        </template>
      </tabs-layout>
      <table-pager ref="todoTable" :options="tableTitle" :remote-method="load" :data.sync="tableData" :pageSize="pageSize" :isNeedButton="false">
        <template slot="amount">
          <el-table-column label="实收金额（元）" width="120">
            <slot slot-scope="{row}">
              {{row.amount|getDecimals}}
            </slot>
          </el-table-column>
        </template>
        <template slot="settlementCount">
          <el-table-column label="业务账单数" width="110">
            <slot slot-scope="{row}">
              <el-button type="text" @click="$router.push({path: '/finance/businessStatement/salesman/detail',query:{salesmanId: row.salesmanId,currentTab: 2}})">{{row.settlementCount}}</el-button>
            </slot>
          </el-table-column>
        </template>
        <template slot="settlementTime" v-if="this.model.merchantsStatus=='FINISH'">
          <el-table-column label="结款时间" width="170">
            <slot slot-scope="{row}">
              <span>{{row.settlementTime}}</span>
            </slot>
          </el-table-column>
        </template>
        <template slot="settlementSerialNumber" v-if="this.model.merchantsStatus=='FINISH'">
          <el-table-column label="结款流水号" width="170">
            <slot slot-scope="{row}">
              <span>{{row.settlementSerialNumber}}</span>
            </slot>
          </el-table-column>
        </template>
      </table-pager>
    </div>
  </div>
</template>

<script>
  const TableColumns = [
    { label: "结算单编号", name: "id",prop: "id",width: "190"},
    { label: "业务员编码", name: "salesmanSn", prop:"salesmanSn",width: "150" },
    { label: "业务员姓名", name: "name",prop: 'name',width: '100' },
    { label: "业务员手机", name: "contact",prop: 'contact',width: '120' },
    { label: "应收金额(元)", name: "amount", prop:'amount',width:'110',slot: true},
    { label: "业务账单数", name: "settlementCount",prop: 'settlementCount',slot: true},
    { label: "结款方式", name: "settlementType.desc",prop: 'settlementType.desc',width:'110'},
    { label: "结款时间", name: "settlementTime",prop: 'settlementTime',slot: true},
    { label: "结款流水号", name: "settlementSerialNumber",prop: 'settlementSerialNumber',slot: true},
    { label: "制单人", name: "createUserName",prop: 'createUserName'},
    { label: "制单时间", name: "createTime",prop: 'createTime',width:'170'},
  ];
  const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({ key: i, ...TableColumns[i] });
  }
  import checkPermission from "../../../utils/permission";
  import {financeSettlementProduct,fspDetailCountStatus } from '@/api/finance'
  import TabsLayout from '@/components/TabsLayout'

  export default {
    components: {
      TabsLayout
    },
    data () {
      return {
        isExpand: false,
        loading: '',
        search: '',
        controlType: '',
        currentTab: 0,
        tabs: [
          { count: 0 },
          { count: 0 }
        ],
        tableData: [],
        page: 1,
        pageSize: 10,
        totalPage: 0,
        total: 0,
        tableTitle: TableColumnList,
        model: {
          contact: '',
          during: '',
          name: '',
          salesmanSn: '',
          merchantsStatus: 'PROCESS'
        },
        products: [],
        ids: []
      }
    },
    computed: {
      tabsList() {
        return [
          { name: `结算中（${this.tabs[0].count}）`, value: 'PROCESS', count: 0, hide: !checkPermission(['admin', 'admin-finance-salesmanSettlement:handlingView']) },
          { name: `已结算（${this.tabs[1].count}）`, value: 'FINISH', count: 0, hide: !checkPermission(['admin', 'admin-finance-salesmanSettlement:handledView']) }
        ]
      }
    },
    mounted() {
      this.getCount()
    },
    methods: {
      checkPermission,
      async getCount() {
        const {data} = await fspDetailCountStatus({})
        this.tabs[0].count = data.process
        this.tabs[1].count = data.finish
      },
      async load(params) {
        const listQuery = {
          model: {
            ...this.model,
            startTime: this.model.during[0],
            endTime: this.model.during[1],
          }
        }
        delete listQuery.model.during
        Object.assign(listQuery, params)
        this.loading = true
        const { data} = await financeSettlementProduct(listQuery)
        this.loading = false
        return {data}
      },
      searchLoad() {
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      reload() {
        this.model = {
          ...this.model,
          ...{
            settlementSerialNumber: '',
            merchantName: '',
            merchantSn: '',
            during: ''
          }
        }
        
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      handleChangeTab (tab) {
        this.model.merchantsStatus = tab.value
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      handleRefresh(pageParams) {
        this.$refs.todoTable.doRefresh(pageParams)
      }
    }
  }
</script>
