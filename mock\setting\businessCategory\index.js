const Mock = require('mockjs')

const List = []
const count = 100

const baseContent = '<p>I am testing data, I am testing data.</p><p><img src="https://wpimg.wallstcn.com/4c69009c-0fd4-4153-b112-6cb53d1cf943"></p>'
const image_uri = 'https://wpimg.wallstcn.com/e4558086-631c-425c-9430-56ffb46e70b3'

for (let i = 0; i < count; i++) {
    List.push(Mock.mock({
        id: '@increment',
        categoryCode: '@string("lower", 5, 10)',
        label: '@ctitle(2, 5)',
        parentCategoryCode: "@name",
        'parentId|1': '@integer(1, 30)',
        sortValue: 0,
        'status|1': ['ENABLED', 'DISABLED', 'LOCKED', 'EXPIRED'],
        createUser: '@name',
        createTime: '@datetime',
        updateUser: '@increment',
        updateTime: +Mock.Random.date('T'),
        'whetherShowFrontend|1': ['N', 'Y']
    }))
}

module.exports = [
    {
        url: '/api/merchant/admin/businessCategory/listParent',
        type: 'post',
        response: config => {
            const { importance, type, title, page = 1, size = 20, sort } = config.query

            let mockList = List.filter(item => {
                if (importance && item.importance !== +importance) return false
                if (type && item.type !== type) return false
                if (title && item.title.indexOf(title) < 0) return false
                return true
            })

            if (sort === '-id') {
                mockList = mockList.reverse()
            }

            return {
                code: 20000,
                data: {
                    total: mockList.length,
                    records: mockList
                }
            }
        }
    },
    {
        url: '/api/merchant/admin/businessCategory/page',
        type: 'post',
        response: config => {
            const { importance, type, title, page = 1, size = 20, sort } = config.query

            let mockList = List.filter(item => {
                if (importance && item.importance !== +importance) return false
                if (type && item.type !== type) return false
                if (title && item.title.indexOf(title) < 0) return false
                return true
            })

            if (sort === '-id') {
                mockList = mockList.reverse()
            }

            const pageList = mockList.filter((item, index) => index < size * page && index >= size * (page - 1))

            return {
                code: 20000,
                data: {
                    total: mockList.length,
                    records: pageList
                }
            }
        }
    },

    {
        url: '/api/merchant/admin/businessCategory',  //createApi
        type: 'all',
        response: config => {
            return {
                code: 20000,
                data: List[0]
            }
        }
    }
]

