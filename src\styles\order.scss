@import './variables.scss';
.text-warning {
  color: $--color-warning !important;
}
.text-primary {
  color: $--color-primary !important;
}
.text-green {
  color: $green !important;
}
.expand-table {
  .scope-span {
    margin-right: 20px;
    font-weight: bold;
  }
  .el-table__expanded-cell[class*=cell] {
    padding: 0;
  }
  .expanded td:first-child {
    border-right: 0;
  }
}
.link-dialog.el-link--default {
  color: #0056E5;
  margin-top: -3px;
  margin-left: 5px;
}
.bill-list li{
  list-style: none;
  margin-bottom: 8px;
}
.order-detail-items {
  .el-timeline li:nth-child(1) {
    .el-timeline-item__node {
      border: 2px solid  #0056E5;
    }
    .active-content {color: #E86E22}

  }
  .el-timeline-item {
    width: 100%;
  }
  .el-timeline-item__node {
    margin-top: 8px;
    // margin-left: 200px;
    background-color: #fff;
    width: 10px;
    height: 10px;
    border: 2px solid  #DCDDE0;
  }
  .el-step__icon.is-text {
    border: 0;
  }
  .el-step__title {
    font-size: 14px;
  }
  .el-step__title.is-process,.el-step__head.is-process {
    color: #0056E5;
  }
  .el-step__head.is-process .el-step__icon.is-text {
    background: #0056E5;
  }
  .el-step__head.is-wait .el-step__icon.is-text {
    background: #CDCED3;

  }
  .el-step.is-center .el-step__line {
    left: 55%;
    right: -45%;
    top: 10px;
  }
  .el-step__head.is-finish {
    .el-step__icon.is-text,.el-step__line {
      background: #0056E5;
    }
  }
  .el-step__icon {
    width: 10px;height: 10px;
  }
  .el-step__description.is-process {
    &:after {
      content: '';
      display: block;
      margin: 10px auto;
      width: 0;
      height: 0;
      border-left: 10px solid transparent;
      border-right: 10px solid transparent;
      border-bottom: 12px solid #f5f5f5;
    }
  }
  .client-info {
    li{
      display: inline-block;
      width: 33.3%;
    }
  }
}
.watch-icon {
  color: #fff;
  display: inline-block;
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 15px solid $--color-warning !important;
  margin-right: 5px;
}
.order-status {
  width: 100%;
  height: 100px;
  background: #f5f5f5;
  margin-top: -10px;
  padding-left: 20px;
  padding-right: 20px;
  font-size: 14px;
  h3 {
    padding-top: 30px;
    margin-bottom: 14px;
    color: #0056E5;
    span {
      color: #000;
    }
  }
  p {
    margin-left: 35px;
    margin-top: 0;
  }
  img {
    width: 25px;margin-bottom: -5px;margin-right: 10px;
  }
}
.orders {
  padding-left: 0;
  li { float: left;list-style: none;margin-right: 10px;margin-bottom: 20px;}
}
.order-table td .el-table__expand-icon{visibility: hidden;}
.salesReturnVoucher {
  li {display: inline-block;margin-right: 20px;width: 100px !important;}
}
.tab-logs {
  margin-top: 20px;
}
.detail-item {
  margin-top: 20px;
}
.productImg {
  width: 50px;
  height: 50px;
}
.table-top {
  background: #f7f7f8;
  border: 1px solid #dfe6ec;
  margin-bottom: 0;
  padding: 10px 10px;
  border-bottom: 0;
  li {
    display: inline-block;
    list-style: none;
    margin-right: 20px;
  }
}
.info-top {
  span {
    display: inline-block;
    margin-right: 20px;
  }
}
.el-table__empty-block {
  border-bottom: 1px solid #dfe6ec;
}

