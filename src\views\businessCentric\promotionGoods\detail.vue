<template>
  <div class="detailMain">
    <div class="head">
      <div class="head_title flex_between_center">
        <div class="top_title">阿莫西林</div>
        <el-button @click="back">返回</el-button>
      </div>
      <div class="flex_between_center">
        <div class="detail">
          <p><b>规格：</b> 120mg*21粒</p>
          <p><b>批准文号：</b> 国药准H2009306</p>
          <p><b>生产厂家：</b> 成都康弘药业集团有限公司</p>
        </div>

        <div class="detail">
          <p><b>推广商家：</b> 天士力医药广州有限公司</p>
          <p><b>销售价：</b> 200.00元</p>
          <p><b>推广费：</b> <span>20.00~30.00 元</span></p>
        </div>

        <div class="detail">
          <p><b>可代理区域：</b> 省份1 市 3 区 4</p>
          <p><b>代理业务员：</b> 5人</p>
          <p><b>累积推广销售额：</b> 4343444 元</p>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="title flex_between_center">
        <div>
          <el-tabs v-model="tabs" class="typeTabs" @tab-click="chageTabsFun">
            <el-tab-pane label="已代理地区及业务员" name="list"></el-tab-pane>
          </el-tabs>
        </div>
      </div>

      <div class="table">
        <el-table ref="table" v-if="list" v-loading="listLoading" :data="list" row-key="id" border fit highlight-current-row style="width: 100%">
          <el-table-column v-for="(item, index) in tableTitle" :key="index" :min-width="(item.width?item.width:'350px')" :label="item.label" show-overflow-tooltip align="left">
            <template slot-scope="{row}">
              <span>{{ row[item.name] }}</span>
            </template>
          </el-table-column>

          <el-table-column fixed="right" align="center" label="操作" width="170" class="itemAction">
            <template slot-scope="row">
              <el-button type="text" size="mini" @click="cancelQualification(row)">撤销该业务员资格</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- <pagination v-if="total>0" :pageSizes="[2, 10, 20, 50]" :total="total" :page.sync="listQuery.current" :limit.sync="listQuery.size" @pagination="getlist" /> -->
      </div>
    </div>
    <el-dialog title="撤销该业务员资格" :visible.sync="dialogVisible" width="25%" :close-on-click-modal="false" :append-to-body="true">
      <el-input type="textarea" placeholder="请输入撤销该业务员资格理由" :autosize="{ minRows: 6, maxRows: 7}"></el-input>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import DowloadButton from "@/components/eyaolink/DowloadButton";
import { getToken } from "@/utils/auth";
export default {
  name: "productQualificationdetail",
  data() {
    return {
      tabs: "list",
      listLoading: false,
      dialogVisible: false,
      tableTitle: [
        {
          label: "代理区域",
          name: "address",
          width: "260px",
        },
        {
          label: "推广费",
          name: "",
          width: "100px",
        },
        {
          label: "业务员姓名",
          name: "",
          width: "100px",
        },
        {
          label: "销售任务",
          name: "",
          width: "170px",
        },
        {
          label: "本月销售业绩",
          name: "",
          width: "170px",
        },
        {
          label: "累计销售业绩",
          name: "",
          width: "200px",
        },
        {
          label: "累积销售数量",
          name: "",
          width: "170px",
        },
        {
          label: "销售任务是否达标",
          name: "",
          width: "170px",
        },
        {
          label: "推广商家申诉",
          name: "",
          width: "200px",
        },
      ],
      item: {},
      tabflag: "productLicenseRelVoList",
      list: [1],
      showlisenceItem: {},
      insertProgram: {
        folderId: 0,
      },
      headersProgram: {
        token: getToken(),
        Authorization: "Basic YWRtaW5fdWk6YWRtaW5fdWlfc2VjcmV0",
      },
      editLisenceItem: {},
    };
  },
  methods: {
    cancelQualification(row) {
      this.dialogVisible = true;
    },
    back() {
      this.$store.dispatch("tagsView/delView", this.$route);
      this.$router.push("/businessCentric/promotionGoods");
    },
    chageTabsFun() {
      let arr = this.item[this.tabflag] || [];
      arr.forEach((item) => {
        item.filePathList = this.getsrc(item.fileIds);
        item.isEdit = false;
        console.log(item);
      });
      this.list = arr;
    },
    renderHeader(h, { column }) {
      return (
        <div style="position:relative">
          <i class="el-icon-menu" />
        </div>
      );
    },
  },
  components: {
    DowloadButton,
  },
  created() {},
};
</script>

<style lang="less" scoped>
.detailMain {
  padding: 0;
  background-color: #f2f3f4;
  .head {
    background-color: #fff;
    padding: 10px 40px 18px 40px;
    .head_title {
      font-size: 22px;
      line-height: 56px;
      border-bottom: 1px solid #eeeeee;
    }
    .detail {
      width: 30%;
      padding: 10px 0;
      color: #505465;
      background-color: #fff;
      font-size: 14px;
      b {
        color: #232333;
      }
    }
  }
  .content {
    margin-top: 20px;
    padding: 20px;
    background-color: #fff;
    .title {
      span {
        margin-bottom: -2px;
        padding: 0 15px;
        height: 40px;
        line-height: 30px;
        display: block;
        background: rgba(255, 255, 255, 0);
        border-bottom: 2px solid rgb(64, 158, 255);
        font-size: 16px;
        font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
          "PingFang SC"-400;
        font-weight: 400;
        color: rgb(64, 158, 255);
      }
    }
  }
  /deep/ .el-upload {
    width: 40px;
    height: 40px;
    position: relative;
  }
  /deep/ .el-upload > i {
    font-size: 16px;
    position: absolute;
    left: 50%;
    top: 50%;
    -webkit-transform: translateX(-50%) translateY(-50%);
    transform: translateX(-50%) translateY(-50%);
  }
  /deep/ .el-upload-list .el-upload-list__item {
    width: 40px;
    height: 40px;
  }
  /deep/ .hide .el-upload--picture-card {
    display: none;
  }
}
</style>