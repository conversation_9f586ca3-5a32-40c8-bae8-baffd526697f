<template>
  <div class="paySettingPageContent">
    <!-- 银联支付 begin -->
    <div class="payitem flex_between_center">
      <div class="leftInfo flex_between_center">
        <div  class="flex_between_center">
          <div class="icon">
            <img src="@/assets/imgs/yinlin.jpg" alt="" />
          </div>
          <div class="itemInfo">
            <p>银联支付</p>
            <p>
              银联支付，用户通过扫描二维码支付模块完成支付。
            </p>
          </div>
        </div>
        <div>
          <el-switch v-model="isUnionPay" active-text="启动" inactive-text="关闭" @change="handleUnionPay">
          </el-switch>
        </div>
      </div>
      <div class="rightInfo">
        <el-button type="text" class="link" @click="handleDeploy('default')">配置</el-button>
        <el-button type="text" class="link" @click="showBill = true">查看账单</el-button>
      </div>
    </div>
    <!-- 银联支付 end -->


    <!-- 微信支付 begin -->
    <div class="payitem flex_between_center">
      <div class="leftInfo flex_between_center">
        <div  class="flex_between_center">
          <div class="icon">
            <img src="@/assets/imgs/weixin.jpg" alt="" />
          </div>
          <div class="itemInfo">
            <p>微信支付</p>
            <p>
              微信支付，用户通过扫描二维码、微信内打开商品页面购买等多种方式调起微信支付模块完成支付。
            </p>
          </div>
        </div>
        <div>
          <el-switch v-model="weChatPay" active-text="启动" inactive-text="关闭" @change="handleWeChatPay">
          </el-switch>
        </div>
      </div>
      <div class="rightInfo">
        <el-link class="link" type="primary">配置</el-link>
        <el-link class="link" type="primary">查看账单</el-link>
      </div>
    </div>
    <!-- 微信支付 end -->

    <!-- 线下汇款begin -->
    <div class="payitem flex_between_center">
      <div class="leftInfo flex_between_center">
        <div  class="flex_between_center">
          <div class="icon">
            <img src="@/assets/imgs/offline.png" alt="" />
          </div>
          <div class="itemInfo">
            <p>线下汇款</p>
            <p>
              线下汇款线下汇款线下汇款线下汇款线下汇款线下汇款线下汇款线下汇款线下汇款
            </p>
          </div>
        </div>
        <div>
          <el-switch v-model="isOffline" active-text="启动" inactive-text="关闭" @change="handleOffline">
          </el-switch>
        </div>
      </div>
      <div class="rightInfo">
        <el-link class="link" type="primary" @click="handleDeploy('offline')">配置</el-link>
        <el-link class="link" type="primary">查看账单</el-link>
      </div>
    </div>
    <!-- 线下汇款 end -->

    <!-- 支付宝支付 begin -->
    <div class="payitem flex_between_center">
      <div class="leftInfo flex_between_center">
        <div  class="flex_between_center">
          <div class="icon">
            <img src="@/assets/imgs/zhifubao.jpg" alt="" />
          </div>
          <div class="itemInfo">
            <p>支付宝支付</p>
            <p>
              支付宝支付，用户通过扫描二维码、支付宝内打开商品页面购买等多种方式调起支付宝支付模块完成支付。
            </p>
          </div>
        </div>
        <div>
          <el-switch v-model="isAlipay" active-text="启动" inactive-text="关闭">
          </el-switch>
        </div>
      </div>
      <div class="rightInfo">
        <el-link class="link" type="primary">配置</el-link>
        <el-link class="link" type="primary">查看账单</el-link>
      </div>
    </div>
    <!-- 支付宝支付 end -->

    <!-- 收钱吧支付 begin -->
    <div class="payitem flex_between_center">
      <div class="leftInfo flex_between_center">
        <div  class="flex_between_center">
          <div class="icon">
            <img src="@/assets/imgs/shouqianba.jpg" alt="" />
          </div>
          <div class="itemInfo">
            <p>收钱吧支付</p>
            <p>
              收钱吧支付，用户通过扫描二维码调起支付模块完成支付。
            </p>
          </div>
        </div>
        <div>
          <el-switch v-model="isCollectPay" active-text="启动" inactive-text="关闭">
          </el-switch>
        </div>
      </div>
      <div class="rightInfo">
        <el-link class="link" type="primary">配置</el-link>
        <el-link class="link" type="primary">查看账单</el-link>
      </div>
    </div>
    <!-- 收钱吧支付 end -->


    <!-- 设置弹出 -->
    <el-dialog :visible.sync="showSetting" width="35%" center>
        <pay-setting-form :cardId="1" :dialogType="dialogType"></pay-setting-form>
    </el-dialog>
    <!-- 设置弹出 -->

    <!-- 账单弹出 -->
    <el-dialog :visible.sync="showBill" width="65%" title="账单" >
        <pay-setting-table :cardId="1"></pay-setting-table>
    </el-dialog>
    <!-- 账单弹出 -->


  </div>
</template>
<script>
// import paySettingForm from './Form/paySetting';
// import paySettingTable from './Table/paySetting';
import paySettingForm from '@/views/settingCenter/adjourned/paySetting/Form/paySetting';
import paySettingTable from '@/views/settingCenter/adjourned/paySetting/Table/paySetting';
export default {
  data() {
    return {
        isSelect:true,
        showBill:false,
        showSetting:false,
        isUnionPay:true,// 银联支付
        weChatPay:true, // 微信支付
        isOffline:true, // 线下汇款
        isAlipay:true,// 支付宝
        isCollectPay:true, // 收钱吧支付
        dialogType:'default',
    };
  },
  components:{
     paySettingForm,
     paySettingTable 
  },
  methods: {
    // 银联支付
    handleUnionPay(val){
      console.log('val--银联支付--->',val);
    },
    // 微信支付
    handleWeChatPay(val){
      console.log('val--微信支付--->',val);
    },
    // 线下汇款
    handleOffline(val){
      console.log('val---线下汇款---->',val);
    },
    // 配置
    handleDeploy(value){
      console.log('配置----->',value);
      this.showSetting = true;
      this.dialogType = value;
      // if(value=='offline'){
      //   this.offlineSetting = true;
      // }
    }
  },
  mounted() {},
  beforeDestroy() {}
};
</script>
<style lang="scss" scoped>
@import "@/styles/element-variables.scss";

.paySettingPageContent {
    padding: 15px;
    .payitem{
        height: 95px;
        background: #ecf0f5;
        border-radius: 4px;
        margin-bottom: 16px;
        .leftInfo{width:80%;}
        .rightInfo{
            width: 15%;
            text-align: center; 
            .link{margin-right: 15px;}  
        }
        .icon{padding: 16px;}
        .icon img{width: 43px;height: 43px;}
        .itemInfo>p:nth-child(1){
            line-height: 22px;
            font-size: 16px;
            font-weight: 400;
        }
        .itemInfo>p:nth-child(2){
            line-height: 20px;
            font-size: 14px;
            font-weight: 400;
            color: #7f7f7f;
        }
    }
}
</style>
