import requestAxios from '@/utils/requestAxios'
import request from '@/utils/request'

//  获取采购商列表
export function list(data) {
  return requestAxios({
    url: "/api/merchant/admin/purMerchant/page",
    method: 'post',
    data
  })
}

// 批量启用/冻结采购商
export function updatePurPublishStatus(data) {
  return requestAxios({
    url: "/api/merchant/admin/purMerchant/updatePublishStatusByIds",
    method: 'post',
    params: data
  })
}
// 单一启用
export function enable(data) {
  return requestAxios({
    url: '/api/merchant/admin/purMerchant/updatePublishStatusByIds/' + data,
    method: 'post'
  })
}

// 单一冻结
export function frozen(data) {
  return requestAxios({
    url: '/api/merchant/admin/purMerchant/updatePublishFrozenById/' + data,
    method: 'post'
  })
}
// 批量驳回采购商
export function updatePurMerchantRejected(data) {
  return requestAxios({
    url: "/api/merchant/admin/purMerchant/updatePurMerchantRejectedByIds",
    method: 'post',
    params: data
  })
}

// 单一驳回
export function rejected(data) {
  return requestAxios({
    url: "/api/merchant/admin/purMerchant/updatePurMerchantRejectedById/" + data.id,
    method: 'post',
    params: data
  })
}

// 批量审核
export function updatePurMerchantAcceptedByIds(data) {
  return requestAxios({
    url: "/api/merchant/admin/purMerchant/updatePurMerchantAcceptedByIds",
    method: 'post',
    params: data
  })
}

// 单一审核
export function accepted(data) {
  return requestAxios({
    url: "/api/merchant/admin/purMerchant/updatePurMerchantAcceptedById/" + data,
    method: 'post'
  })
}
// 批量待审
export function updatePurMerchantPendingByIds(data) {
  return requestAxios({
    url: "/api/merchant/admin/purMerchant/updatePurMerchantPendingByIds",
    method: 'post',
    params: data
  })
}

// 获取状态数量
export function getCount(data) {
  return requestAxios({
    url:'/api/merchant/admin/purMerchant/getPurMerhcantCount',
    method: 'post',
    data
  })
}

// 获取商家类型采购商
export function merchantType() {
  return requestAxios({
    url: "/api/merchant/admin/merchantType",
    method: 'get'
  })
}

// 获取采购商经营范围
export function listByLicenseBaseType() {
  return requestAxios({
    url: '/api/merchant/admin/licenseBase/anno/listByLicenseBaseType',
    method: 'get',
    params: { type: 'BUYER' }
  })
}

//  新增采购商
export function add(data) {
  return requestAxios({
    url: '/api/merchant/admin/purMerchant',
    method: 'post',
    data
  })
}

//  根据采购商id获取详情
export function getitems(id,data) {
  return requestAxios({
    url: '/api/merchant/admin/purMerchant/getPurMerchantLicenseDetail/' + id,
    method: 'get',
    params: data
  })
}

// 修改采购商
export function edititem(data) {
  return requestAxios({
    url: '/api/merchant/admin/purMerchant',
    method: 'put',
    data
  })
}

// 根据id获取商家类型详细信息
export function MerchantTypeDetailsById(data) {
  return requestAxios({
    url: '/api/merchant/admin/merchantType/findMerchantTypeDetailsById/' + data,
    method: 'get',
  })
}

// 识别营业执照
export function readImg(data) {
  return requestAxios({
    url: '/api/merchant/admin/saleMerchant/businessLicenseDiscern?businessLicenseUrl=' + data,
    method: 'get',
  })
}

// 获取账号
export function getAccount(data) {
  return requestAxios({
    url: '/api/authority/user/page',
    method: 'post',
    data
  })
}
