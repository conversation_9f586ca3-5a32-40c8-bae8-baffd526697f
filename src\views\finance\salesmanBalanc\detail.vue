<template>
  <div class="detail">
    <page-title :title="$route.query.name + '余额记录'" />
    <div class="table">
      <el-table
        ref="table"
        v-if="list"
        v-loading="listLoading"
        :data="list"
        row-key="id"
        border
        fit
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column
          align="center"
          width="80"
          :render-header="renderHeader"
        >
          <template slot-scope="scope">
            <span>{{ scope.$index + 1 }} </span>
          </template>
        </el-table-column>
        <el-table-column
          v-for="(item, index) in tableTitle"
          :key="index"
          :min-width="item.width ? item.width : '350px'"
          :label="item.label"
          show-overflow-tooltip
          align="left"
        >
          <template slot-scope="{ row }">
            <span v-if="[item.name] == 'detailType'">{{
              row[item.name].desc
            }}</span>
            <span v-else-if="item.name == 'paymentsType'"> {{row.paymentsType.desc}}</span>
            <span v-else-if="item.name == 'amount'">
              <span v-if="row[item.name] > 0" style="color: #70b603">{{
                row[item.name]|getDecimals
              }}</span>
              <span v-else style="color: #f59a23">{{ row[item.name]|getDecimals }}</span>
            </span>
            <span v-else>{{ row[item.name] }}</span>
          </template>
        </el-table-column>
        <!--
      <el-table-column
        fixed="right"
        align="center"
        label="操作"
        width="150"
        class="itemAction"
      >
        <template slot-scope="{ row }">
          <el-button type="text"  @click="addFun(row)"
            >充值</el-button
          >
          <el-button type="text"  @click="reduceFun(row)"
            >扣减</el-button
          >
          <el-button type="text" >查看记录</el-button>
        </template>
      </el-table-column> -->
      </el-table>
      <pagination
        v-if="total > 0"
        :pageSizes="[10, 20, 50, 100]"
        :total="total"
        :page.sync="listQuery.current"
        :limit.sync="listQuery.size"
        @pagination="getlist"
      />
    </div>
  </div>
</template>

<script>
import Pagination from "@/components/Pagination";
import tableInfo from "@/views/finance/merchantsBalance/tableInfo";
import { detaillist } from "@/api/finance/salesmanBalanc";
export default {
  data() {
    return {
      listLoading: false,
      tabType: "tableTitleInfo",
      list: [],
      total: 0,
      tableTitle: [],
      tableSelectTitle: [],
      showSelectTitle: false,
      listQuery: {
        current: 1,
        size: 10,
        model: {},
      },
    };
  },
  methods: {
    goback() {
      this.$store.dispatch("tagsView/delView", this.$route);
      this.$router.go(-1);
    },
    async getlist() {
      this.listLoading = true;
      let { data } = await detaillist(this.listQuery);
      this.listLoading = false;
      this.total = data.total;
      this.list = data.records;
    },
    initTbaleTitle() {
      this.tableSelectTitle = [];
      this.tableTitle = tableInfo[this.tabType];
    },
    renderHeader(h, { column }) {
      var titles = tableInfo[this.tabType];
      var titlesName = ["显示字段项", "隐藏字段项"];
      return (
        <div style="position:relative">
          <div onClick={this.showHeaer}>
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="640px"
            center
            append-to-body={true}
          >
            <el-transfer
              vModel={this.tableSelectTitle}
              data={titles}
              titles={titlesName}
              onChange={this.setleftTitleFun}
            ></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button onClick={this.closeHeaer}>取消</el-button>
              <el-button type="primary" onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      );
    },
    setleftTitleFun(val) {
      this.tableSelectTitle = val;
    },
    showHeaer: function () {
      this.showSelectTitle = true;
    },
    closeHeaer: function () {
      this.showSelectTitle = false;
      this.tableSelectTitle = [];
    },
    setHeaer: function () {
      var titles = tableInfo[this.tabType];
      var listinfo = titles.filter((element, index, self) => {
        return !this.tableSelectTitle.includes(element.key);
      });
      this.tableTitle = listinfo;
      this.showSelectTitle = !this.showSelectTitle;
    },
  },
  created() {
    this.listQuery.model.salesmanId = this.$route.query.id;
    this.getlist();
    this.initTbaleTitle();
  },
  components: {
    Pagination,
  },
};
</script>

<style lang="less" scoped>
.detail {
  padding: 0 20px;
  background: #fff;
  .title {
    border-bottom: 2px solid #ebecee;
    margin-bottom: 35px;
    padding: 0 12px;
    padding-top: 10px;
    span {
      margin-bottom: -20px;
      padding: 0 15px;
      height: 40px;
      line-height: 30px;
      display: block;
      background: rgba(255, 255, 255, 0);
      border-bottom: 2px solid rgb(64, 158, 255);
      font-size: 16px;
      font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
        "PingFang SC"-400;
      font-weight: 400;
      color: rgb(64, 158, 255);
    }
  }
  .typeTabs {
    height: 40px;
    margin-bottom: -2px;
  }
}
</style>
