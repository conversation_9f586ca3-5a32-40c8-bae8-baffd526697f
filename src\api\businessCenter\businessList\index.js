import requestAxios from '@/utils/requestAxios'
import request from '@/utils/request'

export function outlist(data) {
  let str = JSON.stringify(data)
  let params = JSON.parse(str)
  if (data.model.approvalStatus.code == "stale") {
    params.model.whetherExpired = "Y";
    delete params.model.approvalStatus
  }
  // console.log(params)
  return requestAxios({
    url: "/api/merchant/admin/saleMerchant/page",
    method: 'post',
    data: params
  })
}
// 获取商家详情
export function getitem(id, data) {
  return requestAxios({
    url: "/api/merchant/admin/saleMerchant/getMerchantDetails/" + id,
    method: 'get',
    params: data
  })
}


// 新增
export function add(data) {
  return requestAxios({
    url: '/api/merchant/admin/saleMerchant',
    method: 'post',
    data
  })
}

// 批量启用和冻结
export function updateSalePublishStatus(data) {
  return requestAxios({
    url: '/api/merchant/admin/saleMerchant/updateSalePublishStatus',
    method: 'post',
    params: data
  })
}

// 单一启用
export function enable(data) {
  return requestAxios({
    url: '/api/merchant/admin/saleMerchant/updateEnableSaleMerchant/' + data,
    method: 'post',
  })
}

// 单一冻结
export function frozen(data) {
  return requestAxios({
    url: '/api/merchant/admin/saleMerchant/updateFrozenSaleMerchant/' + data,
    method: 'post'
  })
}

// 批量通过
export function updateSaleMerchantAccepted(data) {
  return requestAxios({
    url: '/api/merchant/admin/saleMerchant/updateSaleMerchantAcceptedByIds',
    method: 'post',
    params: data
  })
}

//  单一通过审核
export function pdateSaleMerchantAcceptedById(data) {
  return requestAxios({
    url: '/api/merchant/admin/saleMerchant/updateSaleMerchantAcceptedById/' + data,
    method: 'post'
  })
}

//批量待审
export function updateSaleMerchantPending(data) {
  return requestAxios({
    url: '/api/merchant/admin/saleMerchant/updateSaleMerchantPendingByIds',
    method: 'post',
    params: data
  })
}

// 批量驳回
export function updateSaleMerchantRejected(data) {
  return requestAxios({
    url: '/api/merchant/admin/saleMerchant/updateSaleMerchantRejectedByIds',
    method: 'post',
    params: data
  })
}

// 单一驳回
export function rejected(data) {
  return requestAxios({
    url: '/api/merchant/admin/saleMerchant/updateSaleMerchantRejectedById/' + data.id,
    method: 'post',
    params: data
  })
}

// 批量续期
export function MerchantRenewal(data) {
  return requestAxios({
    url: '/api/merchant/admin/saleMerchant/MerchantRenewal',
    method: 'post',
    params: data
  })
}

// // 经营范围
// export function findSaleScope(data) {
//   return requestAxios({
//     url: '/api/merchant/admin/businessCategory/listBusinessCategory',
//     method: 'post',
//     data
//   })
// }
// 经营范围
export function findSaleScope() {
  return requestAxios({
    url: '/api/merchant/admin/businessCategory/getTreeByShow',
    method: 'get'
  })
}

// 获取商家经营范围
export function getAllParentCodeOrChildren(data) {
  return requestAxios({
    url: '/api/merchant/admin/businessCategory/getAllParentCodeOrChildren',
    method: 'get',
    params: data
  })
}

// 修改商家信息
export function saleMerchant(data) {
  return requestAxios({
    url: '/api/merchant/admin/saleMerchant',
    method: 'put',
    data: data
  })
}

//  获取商家状态数量
export function getSaleMerhcantCount(data) {
  return requestAxios({
    url: '/api/merchant/admin/saleMerchant/getSaleMerhcantCount',
    method: 'post',
    data
  })
}
// 系统资质
// export function getAll(data) {
//   return requestAxios({
//     url: '/api/merchant/admin/licenseBase/getAll',
//     method: 'get',
//     params: data
//   })
// }
// 系统资质类型获取
export function listByLicenseBaseType() {
  return requestAxios({
    url: '/api/merchant/admin/licenseBase/anno/listByLicenseBaseType',
    method: 'get',
    params: { type: 'MERCHANT' }
  })
}

//获取商家资质
export function listByLicenseMerChan() {
  return requestAxios({
    url: '/api/merchant/admin/licenseBase/anno/listByLicenseBaseType',
    method: 'get',
    params: { type: 'MERCHANT_BUY' }
  })
}
// 添加资质
export function addLicense(data) {
  return requestAxios({
    url: '/api/merchant/admin/merchantLicense',
    method: 'post',
    data
  })
}

// 修改资质
export function editLicense(data) {
  return requestAxios({
    url: '/api/merchant/admin/merchantLicense',
    method: 'put',
    data
  })
}

// 根据商家ID获取地址
export function addrList(data) {
  return requestAxios({
    url: '/api/merchant/admin/deliveryAddress/listDeliveryAddressByMerchantId/' + data,
    method: 'get',
  })
}

// 新增地址
export function adaddr(data) {
  return requestAxios({
    url: '/api/merchant/admin/deliveryAddress',
    method: 'post',
    data
  })
}

// 修改地址
export function editaddr(data) {
  return requestAxios({
    url: '/api/merchant/admin/deliveryAddress',
    method: 'put',
    data
  })
}

// 删除地址
export function deladdr(data) {
  return requestAxios({
    url: '/api/merchant/admin/deliveryAddress',
    method: 'delete',
    params: data
  })
}


export function areas () {
  return requestAxios({
    url: '/api/authority/area/anno/tree',
    method: 'get'
  })
}

// 经营类目
export function listBusinessCategory () {
  return requestAxios({
    url: '/api/merchant/admin/businessCategory/listBusinessCategory',
    method: 'post'
  })
}

export function helpPage (data) {
  return requestAxios({
    url: '/api/general/admin/article/page',
    method: 'POST',
    data
  })
}

// 获取商家支付设置 -> id取值: 0=银联支付(pc),1=微信(小程序),2=微信app,3=微信(pc),4=银行汇款,5=银联支付(小程序),6=银联支付(app),7=货到付款
export function getPaySetting(id, saleMerchantId) {
  return new Promise((resolve, reject) => {
    requestAxios({
      url: `/api/general/admin/paymentSetting/${id}/${saleMerchantId}`,
      method: 'get'
    }).then(res => {
      if (res.isSuccess) {
        resolve(res.data)
      } else {
        reject(res)
      }
    }).catch(err => {
      reject(err)
    })
  })
}

// 保存商家支付设置
export function savePaySetting(data) { 
  return new Promise((resolve, reject) => {
    requestAxios({
      url: `/api/general/admin/paymentSetting`,
      method: 'post',
      data
    }).then(res => {
      if (res.isSuccess) {
        resolve()
      } else {
        reject(res)
      }
    }).catch(err => {
      reject(err)
    })
  })
}


