<template>
  <div>
    <im-search-pad
      :is-expand.sync="isExpand"
      :model="model"
      @reset="reload"
      @search="searchLoad"
    >
      <im-search-pad-item prop="rejectionNo">
        <el-input v-model="model.rejectionNo" placeholder="请输入退货单号" />
      </im-search-pad-item>
      <im-search-pad-item prop="code">
        <el-input v-model="model.code" placeholder="请输入客户编码" />
      </im-search-pad-item>
      <im-search-pad-item prop="purMerchantName">
        <el-input v-model="model.purMerchantName" placeholder="请输入客户名称" />
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="during">
        <el-date-picker
          type="daterange"
          range-separator="至"
          v-model="model.during"
          value-format="yyyy-MM-dd"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        :tabs="tabs"
        @change="handleChangeTab"
      >
        <template slot="button">
          <!-- <el-button  v-if="checkPermission(['admin','return:export'])">导出单据</el-button> -->
          <el-button  @click="reload">刷新</el-button>
        </template>
      </tabs-layout>
      <table-pager ref="todoTable" :options="tableTitle" :remote-method="load" :data.sync="tableData">
        <div slot-scope="props" style="width: 65px;">
          <el-button v-if="checkPermission(['admin','return:detail'])" type="text" @click="$router.push({path: '/tradeCenter/returnGood/detail',query:{id: props.row.id}})">查看详情</el-button>
        </div>

      </table-pager>
    </div>
  </div>
</template>

<script>
  const TableColumns = [
    { label: "退货单号", name: "salesReturnNo",prop: "salesReturnNo",width:'180'},
    { label: "退货时间", name: "salesReturnTime", prop:'salesReturnTime',width: '160'},
    { label: "退货类型", name: "salesReturnType.desc", prop:'salesReturnType.desc',width: '150'  },
    { label: "关联单号", name: "orderNo",prop: 'orderNo',width:'180'},
    { label: "销售商家", name: "saleMerchantName",prop: 'saleMerchantName',width:'180'},
    { label: "客户编码", name: "code",prop: 'code',width: '150' },
    { label: "客户名称", name: "name",prop:'name',width: '130' },
    { label: "申请人", name: "applicant",prop:'applicant',width: '180'  },
    { label: "退货金额", name: "salesReturnMoney",prop:'salesReturnMoney',  },
    { label: "退货状态", name: "rejectionStatusEnum.desc",prop: 'rejectionStatusEnum.desc'},
  ];
  const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({ key: i, ...TableColumns[i] });
  }
  import checkPermission from "@/utils/permission";
  import {salesReturnInfoList,salesReturnStatistics } from '@/api/trade'
  import TabsLayout from '@/components/TabsLayout'
  export default {
    components: {
      TabsLayout
    },
    data () {
      return {
        isExpand: false,
        loading: '',
        search: '',
        controlType: '',
        currentTab: 0,
        tableData: [],
        page: 1,
        pageSize: 10,
        totalPage: 0,
        total: 0,
        tableTitle: TableColumnList,
        model: {
          deliveryStatus: '',
          purMerchantName: '',
          rejectionNo: '',
          code: '',
          during: ''
        },
        products: [],
        ids: []
      }
    },
    computed: {
      tabs() {
        return [
          { name: '全部', value: 'ALL',count: 0,countName: 'all', hide: !checkPermission(['admin', 'return-all: view'])},
          { name: '待审核', value: 'PENDING',count: 0,countName: 'pending', hide: !checkPermission(['admin','return-pending: view']) },
          { name: '待入库', value: 'WAREHOUSING',count: 0,countName: 'stock', hide: !checkPermission(['admin', 'return-stock: view']) },
          { name: '已入库', value: 'REFUND_PROCESS',count: 0,countName: 'stock', hide: !checkPermission(['admin', 'return-process: view']) },
          { name: '已完成', value: 'ACCEPTED',count: 0,countName: 'finish', hide: !checkPermission(['admin', 'return-finish: view']) },
          { name: '已拒绝', value: 'REJECTED',count: 0,countName: 'refuse', hide: !checkPermission(['admin', 'return-refuse: view']) },
          { name: '已关闭', value: 'CLOSE',count: 0,countName: 'cancel', hide: !checkPermission(['admin', 'return-cancel: view']) }
        ]
      }
    },
    mounted() {
      // TODO 暂时无实际接口，予以屏蔽
      // this.getCount()
    },
    methods: {
      checkPermission,
      //tab数量
      async getCount() {
        const {data} = await salesReturnStatistics()
        this.tabs.forEach(item=> {
          item.count = data[item.countName]
        })
      },
      async load(params) {
        let listQuery = {
          model: {
            purMerchantName: this.model.purMerchantName,
            rejectionNo: this.model.rejectionNo,
            code: this.model.code,
            rejectionStatusEnum: this.model.deliveryStatus,
            startTime: this.model.during[0],
            endTime: this.model.during[1],
          }
        }
        Object.assign(listQuery, params)
        this.loading = true
        return await salesReturnInfoList(listQuery)
      },
      searchLoad() {
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      handleChangeTab (tab) {
        if(tab.value === 'ALL') {
          this.model.deliveryStatus = ''
        } else {
          this.model.deliveryStatus = tab.value
        }
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      reload() {
        this.currentTab = 0
        this.model = {
          ...this.model,
          ...{
            purMerchantName: '',
            rejectionNo: '',
            code: '',
            during: ''
          }
        }
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      handleRefresh(pageParams) {
        this.$refs.todoTable.doRefresh(pageParams)
      },
      splitString (val) {
        return val.split(',')
      },
      handleSelectionChange(val) {
        this.ids = val.map(function(item,index) {
          return item.id;
        })

      }
    }
  }
</script>

<style lang="scss" scoped>
</style>
