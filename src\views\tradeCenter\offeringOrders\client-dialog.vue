<template>
  <div class="app-container">
    <el-dialog
      title="选择客户"
      :visible.sync="addUservisible"
      width="1000px"
      @close="handleClose"
    >
      <search-pad @search="search" @reset="reset" style="margin-bottom: 20px;">
        <el-form-item>
          <el-input placeholder="请输入名称" v-model="listQuery.model.purMerchantName" class="input-with-select" />
        </el-form-item>
      </search-pad>
      <table-pager class="todoTable" ref="todoTable" :options="tableTitle" :data.sync="tableData" :remote-method="load" @radio-change="change" :isRadio="true" :isNeedButton="false">
      </table-pager>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addUservisible = false">取 消</el-button>
        <el-button type="primary" @click="addComfirm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { merchantPurSaleRelList } from "@/api/group";

  const TableColumns = [
    { label: "客户名称", prop: "name" },
    { label: "客户类型", prop: "merchantType" },
    { label: "联系人", prop: "ceoName",width: "80" },
    { label: "联系电话", prop: "ceoMobile" },
    { label: "所在区域", prop: "region" },
    { label: "创建时间", prop: "createTime" },
  ];
  const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({ key: i, ...TableColumns[i] });
  }
  export default {
    name: "DragTable",
    props: ["visible",'saleMerchantId','row'],
    data() {
      return {
        tableTitle: TableColumnList,
        list: null,
        total: null,
        listLoading: false,
        sortable: null,
        tableData: [],
        ids: [],
        listQuery: {
          model:{
            "purMerchantName": '',
          }
        },
        addUservisible: false,
        merchantGroupId: '',
        radio: 0
      };
    },
    created() {
      //this.getList();
    },
    methods: {
      handleClose() {
        this.$emit('changeShow','false')
        this.addUservisible = false
      },
      search() {
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
        this.load()
      },
      reset() {
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
        this.listQuery.model.purMerchantCode = ''
        this.listQuery.model.purMerchantName = ''
        this.load()
      },
      handleRefresh(pageParams) {
        this.$refs.todoTable.doRefresh(pageParams)
      },
      async load(params) {
        this.listLoading = true;
        Object.assign(this.listQuery, params)
        return await merchantPurSaleRelList(this.listQuery);
      },
      change(val) {
        this.radio = val
      },
      addComfirm() {
        this.$emit('getClient',this.tableData[this.radio])
        this.handleClose()
      }
    },
    watch: {
      visible() {
        this.addUservisible = this.visible
      }
    }
  };
</script>
<style lang="scss" scoped>
  .todoTable{
  ::v-deep{
  .el-radio{
  .el-radio__label{
  display: none;
  }
  }
  }
  }
  </style>
