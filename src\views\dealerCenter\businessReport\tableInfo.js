import { deepClone } from '@/utils'

const initTableInfo = [
  {
    key: 0,
    label: '经销商名称',
    name: 'agencyName',
  },
  {
    key: 13,
    label: '机构名称',
    name: 'orgName',
  },
  {
    key: 4,
    label: '服务类型',
    name: 'type',
  },
  {
    key: 5,
    label: '服务日期',
    name: 'physicalDate',
  },
  {
    key: 14,
    label: '学校名称',
    name: 'schoolName',
  },
  {
    key: 9,
    label: '班级名称',
    name: 'className',
  },
  {
    key: 15,
    label: '体检单码',
    name: 'barCode',
  },
]

export default {
  ALL: deepClone(initTableInfo)
}
