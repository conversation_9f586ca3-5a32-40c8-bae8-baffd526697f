import requestAxios from '@/utils/requestAxios'


export function query() {
    return requestAxios({
        url: '/api/merchant/admin/businessCategory/listBusinessCategory',
        method: 'post'
    })
}
export function list(data) {
    return requestAxios({
        url: '/api/merchant/admin/businessCategory/page',
        method: 'post',
        data
    })
}

export function getAllParentCodeOrChildren(id) {
    return requestAxios({
        url: '/api/merchant/admin/businessCategory/getAllParentCodeOrChildren?parentId='+id,
        method: 'get',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
        }
    })
}






export function editApi(data) {
    return requestAxios({
        url: '/api/merchant/admin/businessCategory',
        method: data.id == 0 ? 'post' : 'put',
        data
    })
}

export function getApi(id) {
    return requestAxios({
        url: '/api/merchant/admin/businessCategory/'+id,
        method: 'get'
    })
}

export function deleteApi(id) {
    return requestAxios({
        url: '/api/merchant/admin/businessCategory?ids[]=' + id,
        method: 'delete'
    })
}