<template>
    <div class="container" >
      <template v-if="node !=null"> 
          <OrganizationDepartment :node="node" :organizationFormDate="thisOrganizationInfo"  @onUpdateData="getDepartmentInfo"></OrganizationDepartment>
          <OrganizationSubDepartment   :subDepartmentList='subDepartmentList||[]'  @onUpdateData="getDepartmentInfo"></OrganizationSubDepartment>
          <OrganizationMember ref="organizationMember" :departmentId="departmentId"  @onUpdateData="getDepartmentInfo"></OrganizationMember> 
      </template>
    </div>
</template>
<script>

import { mapState,mapGetters,mapActions} from 'vuex'
// import { organization-department } from '@/views/';
import {getDepartmentDetailById,getTreeNew} from "@/api/organization/index";
import OrganizationDepartment from '@/views/team/index/temp/organization/organization-department';
import OrganizationSubDepartment from '@/views/team/index/temp/organization/organization-subDepartment';
import OrganizationMember from '@/views/team/index/temp/organization/organization-member';
export default {
    data() {
      return {
        departmentId:"",
        thisOrganizationInfo:null,
        subDepartmentList:null
      }
    },
    props: {
    /**
     * @param {string} node  选中节点
     */
			node: {
					type: Array,
					default:function(){
						return []
					},
					required: true,
			},
    },
    components: {
        // 组件
        OrganizationDepartment,
        OrganizationSubDepartment,
        OrganizationMember
    },
    computed: {
        // 计算属性computed : 
        // 1. 支持缓存，只有依赖数据发生改变，才会重新进行计算
        // 2. 不支持异步，当computed内有异步操作时无效，无法监听数据的变化
        // 3. computed 属性值会默认走缓存，计算属性是基于它们的响应式依赖进行缓存的，也就是基于data中声明过或者父组件传递的props中的数据通过计算得到的值
        // 4. 如果一个属性是由其他属性计算而来的，这个属性依赖其他属性，是一个多对一或者一对一，一般用computed
        // 5.如果computed属性属性值是函数，那么默认会走get方法；函数的返回值就是属性的属性值；在computed中的，属性都有一个get和一个set方法，当数据变化时，调用set方法。
      ...mapGetters([
            'organizationNavNode',
            'organizationInfo',
            "paremDepartmentInfo",
            "departmentInfo",
        ]), 
    },
    watch:  {
        // 监听属性watch：
        // 1. 不支持缓存，数据变，直接会触发相应的操作；
        // 2. watch支持异步；
        // 3. 监听的函数接收两个参数，第一个参数是最新的值；第二个参数是输入之前的值；
        // 4. 当一个属性发生变化时，需要执行对应的操作；一对多；
        // 5. 监听数据必须是data中声明过或者父组件传递过来的props中的数据，当数据变化时，触发其他操作，函数有两个参数，
        // 　　immediate：组件加载立即触发回调函数执行，
        // 　　deep: 深度监听，为了发现对象内部值的变化，复杂类型的数据时使用，例如数组中的对象内容的改变，注意监听数组的变动不需要这么做。注意：deep无法监听到数组的变动和对象的新增，参考vue数组变异,只有以响应式的方式触发才会被监听到。
        // 　　deepdemo：
        //      obj:{
        //          handler(){
        //              console.log('obj 变了')
        //          },
        //          deep:true
        //      }
        organizationNavNode: {
            deep: true,
            handler(val) {
              this.node= val;
              this.getDepartmentDetailByIdFun()
            },
        },
    },
    methods: {
        //方法集合
        //  1：获取当前组织 及下级部门
        getDepartmentDetailByIdFun(){
            const loading = this.$loading({
                lock: true,
                text: 'Loading',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.2)'
                });
          if(this.organizationNavNode!=null && this.organizationNavNode.length>0){
              this.departmentId=this.organizationNavNode[this.organizationNavNode.length-1].id
              getDepartmentDetailById(this.organizationNavNode[this.organizationNavNode.length-1].id).then(res=>{
                  let {code,data,msg} = res;
                  if(code==0){
                      this.thisOrganizationInfo = {...data.orgaDepVo, deputyLeaderList: data.deputyLeaderList};
                       //  3 拆分出下级部门信息
                      this.subDepartmentList = data.orgaDepVoList;
                  }else{
                      this.thisOrganizationInfo= {}
                      this.subDepartmentList = []
                  }
                  loading.close();
              })
          }else{
              loading.close();
          }
        },
        getDepartmentInfo(data){
          // 获取部门信息
          this.getDepartmentDetailByIdFun()
          // 更部门成员列表信息
          this.$refs.organizationMember.peopleListFun()
        }
    },
    mounted() {
      // 方法调用
      this.getDepartmentDetailByIdFun();
    },
    beforeDestroy() {}
}
</script>

<style lang="less" scoped>

.container>*{
    margin-bottom: 16px;
}
.container>*:last-child{
    margin-bottom:0
}
</style>
