<template>
  <div class="WeChatPageContent">
    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        :tabs="[ { name: '短信模板' } ]"
      >
        <template slot="button">
          <el-button v-if="checkPermission(['admin','admin-setting-sms:add'])"  type="primary" @click="newFun">+新增短信模板</el-button>
          <el-button  @click="reloadPageFun()">刷新</el-button>
        </template>
      </tabs-layout>
      <div class="table">
        <el-table
          v-loading="listLoading"
          :data="list"
          row-key="id"
          border
          fit
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column label="" :render-header="renderSpecNameHeader" width="160px" min-width="160px" fixed>
            <template slot-scope="{row}">
              {{ row.providerType.desc }}
            </template>
          </el-table-column>

          <el-table-column
            v-for="(item, index) in tableTitle"
            :key="index"
            :width="item.width"
            :min-width="(item.width?item.width:'350px')"
            :label="item.label"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              <span>{{ row[item.name] }}</span>
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            align="center"
            label="操作"
            width="150"
            class="itemAction"
          >
            <template slot-scope="scope">
              <el-row class="table-edit-row">
                <span v-if="checkPermission(['admin','admin-setting-sms:edit'])" class="table-edit-row-item">
                  <el-button type="text"  @click="editFun(scope.row.id)">编辑</el-button>
                </span>
                <span v-if="checkPermission(['admin','admin-setting-sms:del'])" class="table-edit-row-item">
                  <el-button type="text"  @click="deleteFun(scope.row)">删除</el-button>
                </span>
              </el-row>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-if="total>0" :page-sizes="[10, 20, 50]" :total="total" :page.sync="listQuery.current" :limit.sync="listQuery.size" @pagination="getList" />
      </div>
    </div>
  </div>
</template>
<script>
import { setContextData, getContextData } from '@/utils/auth'
import checkPermission from '@/utils/permission'
import tableSelectHeader from '@/views/settingCenter/sms/smsTemplate/tableSelectHeader'
import { tempList, deleteApi } from '@/api/setting/sms/smsTemplate'
import Pagination from '@/components/Pagination'
import TabsLayout from '@/components/TabsLayout'

export default {
  components: {
    Pagination,
    tableSelectHeader,
    TabsLayout
  },
  data() {
    return {
      showEdit: false,
      row: {},
      providerTypeId: '',
      providerTypeOptions: [
        {
          value: '',
          label: '全部服务商'
        },
        {
          value: 'ALI',
          label: '阿里云短信'
        },
        {
          value: 'TENCENT',
          label: '腾讯云短信'
        },
        {
          value: 'BAIDU',
          label: '百度云短信'
        }
      ],
      tableTitle: [
        {
          label: '应用ID',
          name: 'appId',
          width: '180px'
        },
        {
          label: '应用密码',
          name: 'appSecret',
          width: '150px'
        },
        {
          label: '模板名称',
          name: 'name',
          width: '150px'
        },
        {
          label: '模板编码',
          name: 'customCode',
          width: '150px'
        },
        {
          label: '模板CODE',
          name: 'templateCode',
          width: '150px'
        },
        {
          label: '模板签名名称',
          name: 'signName',
          width: '150px'
        },
        {
          label: '模板描述',
          name: 'content'
        },
        {
          label: '创建时间',
          name: 'createTime',
          width: '220px'
        }
      ],
      submitReload: false,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        map: {},
        model: {},
        current: 1,
        size: 10
      }
    }
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      if (from.path == '/settingCenter/sms/smsTemplate/edit') {
        vm.listQuery = getContextData('sms_smsTemplate_list')
      }
      vm.listQuery = {
        map: {},
        model: {},
        current: 1,
        size: 10
      };
      vm.getList()
    })
  },
  mounted() {
    this.getList()
  },
  beforeDestroy() {},
  methods: {
    // 表头 选中
    checkPermission,
    renderSpecNameHeader(createElement, { column, $index }) {
      const self = this
      return createElement(
        'div',
        {
          style: 'display:flex;'
        },
        [
          createElement('div', {
            domProps: {
              innerHTML: column.label
            }
          }),
          createElement(tableSelectHeader, {
            style: 'cursor: pointer;',
            // 组件 prop
            props: {
              type: 'value',
              options: self.providerTypeOptions, // 下拉框选项
              defaultValue: self.providerTypeId, // 默认值
              defaultProps: {
                value: 'value',
                label: 'label'
              }
            },
            // 事件监听器在 `on` 属性内，
            // 但不再支持如 `v-on:keyup.enter` 这样的修饰器。
            // 需要在处理函数中手动检查 keyCode。
            on: {
              selectChange: self.selectChange
              // click: this.clickHandler
            },
            // 仅用于组件，用于监听原生事件，而不是组件内部使用
            // `vm.$emit` 触发的事件。
            nativeOn: {
              // click: this.nativeClickHandler
            }
          })
        ]
      )
    },
    // 选择框回调
    selectChange(val) {
      // 写点啥吧
      this.listQuery = {
        model: {},
        map: {
          providerType: val.value
        },
        current: 1,
        size: 10
      }
      this.getList()
    },
    // 表头 选中
    newFun() {
      setContextData('sms_smsTemplate_list', this.listQuery)
      this.$router.push({ path: '/settingCenter/sms/smsTemplate/edit' })
    },
    editFun(id) {
      setContextData('sms_smsTemplate_list', this.listQuery)
      this.$router.push({ path: '/settingCenter/sms/smsTemplate/edit',
        query: {
          id: id
        }})
    },

    handleClick(row) {
      this.row = row
      this.showEdit = true
    },
    renderHeader(h, { column }) {
      return (
        <div style='position:relative'>
          <div>
            <i class='el-icon-menu' />
          </div>
        </div>
      )
    },
    async getList() {
      this.listLoading = true
      const { data } = await tempList(this.listQuery)
      this.list = data.records
      this.total = data.total
      this.listLoading = false
    },
    reloadPageFun() {
      this.listQuery = {
        model: {},
        map: {},
        current: 1,
        size: 10
      }
      this.getList()
    },
    deleteFun(row) {
      var _this = this
      this.$confirm('此操作将永久删除该信息, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        _this.actionDeleteFun(row)
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    async actionDeleteFun(row) {
      const data = await deleteApi(row.id)
      if (data.code == 0) {
        this.getList()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
@import "@/styles/element-variables.scss";

.WeChatPageContent {
  padding: 0;
  // padding: 15px;
   .title{
        border-bottom:2px solid #EBECEE;
     margin-bottom: 16px;
      span{
        margin-bottom: -2px;
        padding:0 15px;
        height: 40px;
        line-height: 30px;
        display:block;
        background: rgba(255,255,255,0);
        border-bottom:2px solid rgb(64, 158, 255);
        font-size: 16px;
        font-family: 'PingFangSC-Regular', 'PingFang SC', 'PingFangSC-Regular', 'PingFang SC'-400;
        font-weight: 400;
        color:rgb(64, 158, 255);
      }
  }
  .formItem{width:586px;}
  .line{color:#dfe6ec; margin:0 6px;}
}
</style>
