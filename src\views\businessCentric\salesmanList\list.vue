<template>
  <div class="archivesPageContent">
    <im-search-pad
      :has-expand="false"
      :model="listQuery"
      @reset="resetForm"
      @search="onSearchSubmitFun"
    >
      <im-search-pad-item prop="params">
        <el-input v-model="listQuery.model.params" placeholder="请输入编码/姓名/手机号/登录账号" />
      </im-search-pad-item>
      <im-search-pad-item prop="positionStatus">
        <el-select
          v-model="listQuery.model.positionStatus"
          placeholder="请选择在职状态"
        >
          <el-option label="在职" value="WORKING"></el-option>
          <el-option label="离职" value="QUIT"></el-option>
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item prop="status">
        <el-select
          v-model="listQuery.model.status"
          placeholder="请选择账户状态"
        >
          <el-option label="已启用" value="ENABLED"></el-option>
          <el-option label="已冻结" value="LOCKED"></el-option>
        </el-select>
      </im-search-pad-item>
    </im-search-pad>

    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        v-model="listQuery.model.approvalStatus"
        :tabs="approvalStatusList"
        @change="chageTabsFun"
      >
        <template slot="button">
          <el-button @click="enableFun" :disabled="selecttableItemIds.length == 0"
          >批量启用</el-button>
          <el-button @click="freezeFun" :disabled="selecttableItemIds.length == 0"
          >批量冻结</el-button>
          <el-button
            v-if="listQuery.model.approvalStatus == 'PENDING'"
            @click="rejectedFun"
            :disabled="selecttableItemIds.length == 0"
          >批量驳回</el-button>
          <el-button
            v-if="listQuery.model.approvalStatus == 'PENDING'"
            @click="acceptedFun"
            :disabled="selecttableItemIds.length == 0"
          >批量通过</el-button>
          <!-- <el-button>导出档案</el-button> -->
          <el-button icon="el-icon-refresh" @click="resetForm">刷新</el-button>
          <el-button type="primary" @click="newFun">+新增业务员</el-button>
        </template>
      </tabs-layout>
      <div class="table">
        <el-table
          ref="table"
          v-if="list"
          @selection-change="selectTableItemFun"
          v-loading="listLoading"
          :data="list"
          row-key="id"
          border
          fit
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column
            align="center"
            width="65"
            :render-header="renderHeader"
            fixed
          >
            <template slot-scope="scope"> {{ scope.$index + 1 }}</template>
          </el-table-column>
          <el-table-column align="center" type="selection" width="55" fixed>
          </el-table-column>
          <el-table-column
            v-for="(item, index) in tableTitle"
            :key="index"
            :min-width="item.width ? item.width : '350px'"
            :label="item.label"
            show-overflow-tooltip
            align="left"
          >
            <template slot-scope="{ row }">
            <span
              v-if="item.name == 'status'"
              :style="row[item.name].code != 'ENABLED' ? 'color:#ff0066' : ''"
            >
              {{ row[item.name].code == "ENABLED" ? "已启用" : "已冻结" }}
            </span>
              <span v-else-if="item.name == 'positionStatus'">
              {{ row[item.name].code == "WORKING" ? "在职" : "离职" }}
            </span>
              <!-- 客户数量 -->
              <span v-else-if="item.name == 'customersNumber'">
                <el-row class="table-edit-row">
                <span class="table-edit-row-item">
                  <el-button @click="merchantFun(row)" type="text" :disabled="row[item.name]==0" >{{
                      row[item.name]
                    }}</el-button>
                </span>
              </el-row>
            </span>
              <!-- 代理商品数 -->
              <span v-else-if="item.name == 'agencyProductNumber'">
                 <el-row class="table-edit-row">
                <span class="table-edit-row-item">
                  <el-row class="table-edit-row">
                    <span class="table-edit-row-item">
                      <el-button
                        :disabled="row[item.name]==0"
                        type="text"
                        @click="showGoodsTable(row)"

                      >{{ row[item.name] }}</el-button>
                    </span>
                  </el-row>
                </span>
              </el-row>
            </span>
              <!-- 代理区域 -->
              <span v-else-if="item.name == 'agencyAreaNumber'">
              <el-button :disabled="row[item.name]==0" @click="showAreaFun(row)" type="text" >{{
                  row[item.name]
                }}</el-button>
            </span>
              <span v-else>{{ row[item.name] }}</span>
            </template>
          </el-table-column>

          <el-table-column
            fixed="right"
            align="center"
            label="操作"
            width="160"
            class="itemAction"
          >
            <template slot-scope="scope">
              <el-row class="table-edit-row">
                <span v-if="listQuery.model.approvalStatus == 'ACCEPTED'" class="table-edit-row-item">
                  <el-button @click="bindMerchantFun(scope.row)" type="text" >绑定客户</el-button>
                </span>
                <span class="table-edit-row-item">
                  <el-button @click="detailFun(scope.row)" type="text" >查看详情</el-button>
                </span>
              </el-row>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="listQuery.current"
          :limit.sync="listQuery.size"
          @pagination="getlist"
        />
      </div>
    </div>

    <el-dialog
      v-if="showGoodsFlag"
      :visible.sync="showGoodsFlag"
      width="80%"
      :close-on-click-modal="false"
      :append-to-body="true"
    >
      <div slot="title">
        <span>代理商品({{ goodsTotal }})</span>
        <el-button
          disabled

          plain
          type="primary"
          style="margin-left: 10px"
          >业务员: {{ this.row.realName }}</el-button
        >
        <el-button
          disabled

          plain
          :type="
            this.row.positionStatus.code == 'WORKING' ? 'success' : 'danger'
          "
          >{{
            this.row.positionStatus.code == "WORKING" ? "在职" : "离职"
          }}</el-button
        >
      </div>
      <goods
        :visible.sync="showGoodsFlag"
        :isReload.sync="submitReload"
        :row.sync="row"
        :goodsNum.sync="goodsTotal"
      ></goods>
    </el-dialog>

    <el-dialog
      v-if="showMerchantFlag"
      :visible.sync="showMerchantFlag"
      width="80%"
      :close-on-click-modal="false"
      :append-to-body="true"
    >
      <div slot="title">
        <span v-if="isAll">绑定客户</span>
        <span v-else>已绑定客户 ({{ merchantNum }})</span>
        <el-button
          disabled

          plain
          type="primary"
          style="margin-left: 10px"
          >业务员: {{ this.row.realName }}</el-button
        >
        <el-button
          disabled

          plain
          :type="
            this.row.positionStatus.code == 'WORKING' ? 'success' : 'danger'
          "
          >{{
            this.row.positionStatus.code == "WORKING" ? "在职" : "离职"
          }}</el-button
        >
      </div>
      <merchant
        :visible.sync="showMerchantFlag"
        :isReload.sync="submitReload"
        :isAll="isAll"
        :row.sync="row"
        :merchantNum.sync="merchantNum"
      ></merchant>
    </el-dialog>

    <el-dialog
      v-if="showAreaFlag"
      :visible.sync="showAreaFlag"
      width="80%"
      :close-on-click-modal="false"
      :append-to-body="true"
    >
      <div slot="title">
        <span>代理区域({{ showareaNum }})</span>
        <span style="display: inline-block; width: 10px"></span>
        <el-button  type="primary" plain disabled
          >业务员: {{ row.realName }}</el-button
        >
        <span style="display: inline-block; width: 10px"></span>
        <el-button
          v-if="row.positionStatus.code == 'WORKING'"

          type="success"
          plain
          disabled
          >在职</el-button
        >

        <el-button
          v-if="row.positionStatus.code == 'QUIT'"

          type="danger"
          plain
          disabled
          >离职</el-button
        >
      </div>
      <areaDv
        :visible.sync="showAreaFlag"
        :isReload.sync="submitReload"
        :showareaNum.sync="showareaNum"
        :row.sync="row"
      ></areaDv>
    </el-dialog>
  </div>
</template>

<script>
import goods from "./table/goods";
import merchant from "./table/merchant";
import client from "./table/client";
import areaDv from "./table/area";
import checkPermission from "@/utils/permission";
import tableInfo from "@/views/businessCentric/salesmanList/tableInfo";
import Pagination from "@/components/Pagination";
import edit from "@/views/businessCentric/salesmanList/edit";
import TabsLayout from '@/components/TabsLayout'
import {
  getList,
  freeze,
  enable,
  accepted,
  rejected,
  getListTabNum,
} from "@/api/businessCentric/salesmanList/index";
import { setContextData, getContextData } from "@/utils/auth";
export default {
  data() {
    return {
      total: 0,
      list: [],
      showEditPage: false,
      tableTitle: [],
      listLoading: false,
      submitReload: false,
      listQuery: {
        current: 1,
        size: 10,
        model: {
          approvalStatus: "PENDING",
        },
      },
      selecttableItemIds: [],
      tableSelectTitle: [],
      showSelectTitle: false,
      row: {},
      isAll: false,
      showGoodsFlag: false,
      goodsTotal: 0,
      showMerchantFlag: false,
      showClientFlag: false,
      merchantNum: 0,
      showAreaFlag: false,
      showareaNum: 0,
      tabNum: {
        accepted: 0,
        pending: 0,
        rejected: 0,
        repeal: 0,
      },
    };
  },
  computed: {
    approvalStatusList() {
      return [
        {
          name: '待审核' + ((+this.tabNum.pending) > 0 ? '(' + this.tabNum.pending + ')' : ''),
          value: 'PENDING',
        },
        {
          name: '已审核' + ((+this.tabNum.accepted) > 0 ? '(' + this.tabNum.accepted + ')' : ''),
          value: 'ACCEPTED',
        },
        {
          name: '已驳回' + ((+this.tabNum.rejected) > 0 ? '(' + this.tabNum.rejected + ')' : ''),
          value: 'REJECTED',
        }
      ]
    }
  },
  methods: {
    checkPermission,
    refreshFun() {
      this.list = [];
      this.initTbaleTitle();
      this.listQuery = {
        current: 1,
        size: 10,
        model: {
          approvalStatus: "PENDING",
        },
      };
      this.getlist();
      this.getListTabNum();
    },
    showGoodsTable(row) {
      this.showGoodsFlag = true;
      this.row = row;
    },
    bindMerchantFun(row) {
      this.row = row;
      this.isAll = true;
      this.showMerchantFlag = true;
    },
    merchantFun(row) {
      if (row.customersNumber == 0) {
        this.$message.error("该业务员目前没有绑定任何客户");
        return false;
      }
      this.showMerchantFlag = true;
      this.row = row;
      this.isAll = false;
    },
    showAreaFun(row) {
      this.row = row;
      this.showAreaFlag = true;
    },
    async acceptedFun() {
      this.$confirm("此操作将批量通过业务员，是否继续？","提示",{
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: 'warning',
      }).then(async ()=>{
        let arr = [];
        this.selecttableItemIds.forEach((item) => {
          arr.push(item.id);
        });
        let { data } = await accepted(arr);
        if (data) {
          this.$message.success("批量通过成功");
          // this.listQuery.model.approvalStatus = "PENDING";
          this.listQuery.current = 1;
          this.getlist();
          this.getListTabNum();
        }
      })
    },
    async rejectedFun() {
      this.$confirm("此操作将审核驳回业务员，是否继续？","提示",{
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: 'warning',
      }).then(async ()=>{
        let arr = [];
        this.selecttableItemIds.forEach((item) => {
          arr.push(item.id);
        });
        let { data } = await rejected(arr);
        if (data) {
          this.$message.success("批量驳回成功");
          // this.listQuery.model.approvalStatus = "REJECTED";
          this.listQuery.current = 1;
          this.getlist();
          this.getListTabNum();
        }
      })
    },
    async enableFun() {
      this.$confirm("此操作将启用业务员，是否继续？","提示",{
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: 'warning',
      }).then(async ()=>{
        let arr = [];
        this.selecttableItemIds.forEach((item) => {
          arr.push(item.id);
        });
        let { data } = await enable(arr);
        if (data) {
          this.$message.success("批量启用成功");
          this.selecttableItemIds.forEach((item) => {
            item.status.code = "ENABLED";
          });
          this.$refs.table.clearSelection();
        }
      })
    },
    async freezeFun() {
      this.$confirm("此操作将冻结业务员，是否继续？","提示",{
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: 'warning',
      }).then(async ()=>{
        let arr = [];
        this.selecttableItemIds.forEach((item) => {
          arr.push(item.id);
        });
        let { data } = await freeze(arr);
        if (data) {
          this.$message.success("批量冻结成功");
          this.selecttableItemIds.forEach((item) => {
            item.status.code = "DISABLED";
            this.$refs.table.clearSelection();
          });
        }
      })
    },
    showMerchantTable(row) {
      this.showMerchantFlag = true;
      this.row = row;
    },
    showClientTable(row) {
      this.showClientFlag = true;
      this.row = row;
    },
    detailFun(row) {
      setContextData("salesmanList_list", this.listQuery);
      this.$router.push({
        path: "/businessCentric/salesmanListdetail",
        query: {
          id: row.id,
        },
      });
    },
    async getListTabNum() {
      let { data } = await getListTabNum();
      this.tabNum = data;
    },
    async getlist() {
      this.listLoading = true;
      let { data } = await getList(this.listQuery);
      this.total = data.total;
      this.list = data.records;

      this.listLoading = false;
    },
    newFun: function () {
      this.row = {};
      this.showEditPage = true;
      this.$router.push({
        path: "/businessCentric/salesmanListeditItem",
      });
    },
    // renderHeader(h, { column }) {
    //   return (
    //     <div style="position:relative">
    //       <i class="el-icon-menu" />
    //     </div>
    //   );
    // },
    selectTableItemFun(row) {
      // this.selecttableItemIds = []
      // row.forEach(item => {
      //   this.selecttableItemIds.push(item.id)
      // })
      this.selecttableItemIds = row;
    },
    onSearchSubmitFun() {
      this.getlist();
      this.getListTabNum();
    },
    resetForm() {
      this.$refs['tabs-layout'].reset();
      this.listQuery = {
        current: 1,
        size: 10,
        model: {
          approvalStatus: 'PENDING'
        },
      };
      this.getlist();
      this.getListTabNum();
    },
    chageTabsFun: function () {
      this.list = [];
      this.listQuery.current = 1;
      this.initTbaleTitle();
      // if (this.listQuery.model.approvalStatus === 'stale') {
      //   this.listQuery.model.publishStatus = 'N'
      // } else {
      //   delete this.listQuery.model.publishStatus
      // }
      this.getlist();
      this.getListTabNum();
    },
    initTbaleTitle() {
      this.tableTitle = tableInfo[this.listQuery.model.approvalStatus];
      this.tableSelectTitle = [];
    },
    renderHeader(h, { column }) {
      var titles = tableInfo[this.listQuery.model.approvalStatus];
      var titlesName = ["显示字段项", "隐藏字段项"];
      return (
        <div style="position:relative">
          <div onClick={this.showHeaer}>
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="640px"
            center
            append-to-body={true}
          >
            <el-transfer
              vModel={this.tableSelectTitle}
              data={titles}
              titles={titlesName}
              onChange={this.setleftTitleFun}
            ></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button onClick={this.closeHeaer}>取消</el-button>
              <el-button type="primary" onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      );
    },
    setleftTitleFun(val) {
      this.tableSelectTitle = val;
    },
    showHeaer: function () {
      this.showSelectTitle = true;
    },
    closeHeaer: function () {
      this.showSelectTitle = false;
      this.tableSelectTitle = [];
    },
    setHeaer: function () {
      var titles = tableInfo[this.listQuery.model.approvalStatus];
      var listinfo = titles.filter((element, index, self) => {
        return !this.tableSelectTitle.includes(element.key);
      });
      this.tableTitle = listinfo;
      this.showSelectTitle = !this.showSelectTitle;
    },
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (
        from.path == "/businessCentric/salesmanListeditItem" ||
        from.path == "/businessCentric/salesmanListdetail"
      ) {
        if (getContextData("salesmanList_list") != "") {
          vm.listQuery = getContextData("salesmanList_list");
          console.log(getContextData("salesmanList_list"));
        }
      }
      vm.listQuery={
        current: 1,
        size: 10,
        model: {
          approvalStatus: "PENDING",
        },
      },
      vm.initTbaleTitle();
      // vm.getSaleMerhcantCount();
      vm.getlist();
      vm.getListTabNum();
    });
  },
  watch: {
    row(newl, old) {
      this.list.find((item) => {
        if (item.id == newl.id) {
          item.customersNumber = newl.customersNumber;
        }
      });
    },
  },
  components: {
    Pagination,
    goods,
    merchant,
    client,
    areaDv,
    TabsLayout
  },
};
</script>

<style lang="less" scoped>
.archivesPageContent {
  padding: 0;
  .temp_searchBox {
    height: 64px;
    overflow: hidden;
    margin-bottom: 0;
  }
  .form-inline {
    height: 60px;
    overflow: hidden;
  }
  .title {
    border-bottom: 2px solid #ebecee;
    margin-bottom: 16px;
    span {
      margin-bottom: -2px;
      padding: 0 15px;
      height: 40px;
      line-height: 30px;
      display: block;
      background: rgba(255, 255, 255, 0);
      border-bottom: 2px solid rgb(64, 158, 255);
      font-size: 16px;
      font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
        "PingFang SC"-400;
      font-weight: 400;
      color: rgb(64, 158, 255);
    }
  }

  .formItem {
    width: 586px;
  }
  .line {
    color: #dfe6ec;
    margin: 0 6px;
  }
  .typeTabs {
    height: 40px;
    margin-bottom: -2px;
  }
}
</style>
