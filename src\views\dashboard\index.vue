<template>
  <div class="home">
    <div class="select_platform" v-if="false">
      <el-radio-group v-model="commerceModelEnum" @change="changeCommerceModelEnum">
        <el-radio-button label="SAAS_PLATFORM">平台经营看板</el-radio-button>
        <el-radio-button label="SAAS">SaaS经营看板</el-radio-button>
      </el-radio-group>
    </div>
    <div class="cardContain">
      <div class="title">代办事项</div>
      <div class="lists">
        <el-row :gutter="20">
          <el-col :span="8"
            ><div class="item" @click="gotoProductList">
              <div class="content">
                <div class="type">待审核商品</div>
                <div class="number">{{ item.pendingProduct }}</div>
                <div class="msg">已上架商品数：{{ item.saleProduct }}</div>
              </div>
              <img class="bgimg" src="@/assets/home/<USER>" alt="" />
              <div
                class="bg_r"
                :style="
                  'background-image: url(' + bgImg + ');background-size:cover'
                "
              >
                <img src="@/assets/home/<USER>" alt="" />
                <!-- <img src="@/assets/home/<USER>" alt=""> -->
              </div>
              <div class="show1"></div>
            </div>
          </el-col>
          <el-col :span="8"
            ><div class="item" @click="gotoArchivesList">
              <div class="content">
                <div class="type">待审核客户</div>
                <div class="number">{{ item.pendingPurMerchant }}</div>
                <div class="msg">累计客户数：{{ item.totalPurMerchant }}</div>
              </div>
              <img class="bgimg" src="@/assets/home/<USER>" alt="" />
              <div
                class="bg_r"
                :style="
                  'background-image: url(' + bgImg + ');background-size:cover'
                "
              >
                <img src="@/assets/home/<USER>" alt="" />
              </div>
              <div class="show2"></div></div
          ></el-col>
          <el-col :span="8"
            ><div class="item" @click="gotoRefundList">
              <div class="content">
                <div class="type">待退款订单</div>
                <div class="number">{{ item.pendingSalesRefundInfo }}</div>
                <div class="msg">待退款金额：￥{{ item.pendingMoney }}</div>
              </div>
              <img class="bgimg" src="@/assets/home/<USER>" alt="" />
              <div
                class="bg_r"
                :style="
                  'background-image: url(' + bgImg + ');background-size:cover'
                "
              >
                <img src="@/assets/home/<USER>" alt="" />
              </div>
              <div class="show3"></div></div
          ></el-col>
        </el-row>
      </div>
    </div>

    <div class="cardContain">
      <div class="title">数据概况</div>
      <profileData :commerceModelEnum="commerceModelEnum" />
    </div>

    <div class="cardContain">
      <div class="title">订单看板</div>
      <orderCard :commerceModelEnum="commerceModelEnum"/>
    </div>

    <div class="cardContain">
      <div class="title">商品看板</div>
      <goodsCard :commerceModelEnum="commerceModelEnum" />
    </div>

    <div class="cardContain">
      <div class="title">客户看板</div>
      <customerCard :commerceModelEnum="commerceModelEnum" />
    </div>
  </div>
</template>

<script>
import profileData from "@/views/dashboard/cards/profileData";
import goodsCard from "@/views/dashboard/cards/goodsCard";
import OrderCard from "@/views/dashboard/cards/orderCard.vue";
import customerCard from "@/views/dashboard/cards/customerCard.vue";
import { getBacklog } from "@/api/dashboard/index";
export default {
  data() {
    return {
      item: {},
      commerceModelEnum: 'SAAS',
      bgImg: require("@/assets/home/<USER>"),
    };
  },
  computed: {
    isPlatform() {
      return this.commerceModelEnum === 'SAAS_PLATFORM' 
    }
  },
  methods: {
    async getBacklog(commerceModelEnum) {
      let { data } = await getBacklog(commerceModelEnum);
      this.item = data;
    },
    changeCommerceModelEnum(commerceModelEnum) {
      this.getBacklog(commerceModelEnum);
    },
    // 商品列表
    gotoProductList() {
      this.$router.push(this.isPlatform ? '/products/product/platformList' : '/products/product/saasList')
    },
    // 采购商档案
    gotoArchivesList() {
      this.$router.push(this.isPlatform ? '/customerCenter/platform/archivesList' : '/customerCenter/saas/archivesList')
    },
    // 退款单管理
    gotoRefundList() {
      this.$router.push(this.isPlatform ? '/tradingCenter/platform/refundSlipList' : '/tradingCenter/saas/refundSlipList')
    }
  },
  created() {
    this.getBacklog(this.commerceModelEnum);
  },
  components: {
    profileData,
    OrderCard,
    goodsCard,
    customerCard,
  },
};
</script>

<style lang="scss" scoped>
.home {
  margin-top: 0;
  background-color: #f2f3f4;
  .select_platform {
    ::v-deep .el-radio-button__inner {
      padding: 14px 20px;
      font-size: 18px;
    }
  }
  .cardContain:nth-of-type(1) {
    margin-top: 0;
  }
  .cardContain {
    margin-top: 18px;
    background-color: #fff;
    padding: 20px 18px;
    border-radius: 10px;
    .title {
      border-left: 4px solid #0056e5;
      font-size: 20px;
      font-family: PingFang SC, PingFang SC-Medium;
      font-weight: 600;
      color: #0f1831;
      padding-left: 10px;
      margin-bottom: 20px;
    }
    .lists {
      margin-bottom: 20px;
      .item {
        cursor: pointer;
        position: relative;
        width: 100%;
        transition: all 0.5s;
        .bgimg {
          width: 100%;
          height: 154px;
          z-index: 5;
          position: relative;
          display: block;
        }
        .bg_r {
          position: absolute;
          top: 0;
          right: 0;
          z-index: 5;
          height: 100%;
          width: 200px;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          padding-right: 30px;
          img {
            width: 50px;
            height: 50px;
          }
        }
        .content {
          top: 38px;
          color: #fff;
          position: absolute;
          z-index: 6;
          left: 30px;
          .type {
            font-size: 16px;
          }
          .msg {
            font-size: 14px;
          }
          .number {
            font-size: 32px;
            padding: 5px 0;
          }
        }
        .show1,
        .show2,
        .show3 {
          width: 80%;
          height: 10px;
          margin: 0 auto;
          position: absolute;
          bottom: 0;
          transform: translateX(10%);
          margin-left: 10px;
          z-index: 0;

          
          transition: all 0.5s;
        }
      }
      .item:hover {
        transform: translateY(-5px);
      }
      .item:hover .show1 {
        box-shadow: 0 20px 30px 0px #2864ff;
      }
      .item:hover .show2 {
        box-shadow: 0 20px 30px 0px #1695bb;
      }
      .item:hover .show3 {
        box-shadow: 0 20px 30px 0px #6d32fa;
      }
    }
  }
}
</style>