<template>
  <div>
    <im-search-pad
      :has-expand="false"
      :model="model"
      @reset="reload"
      @search="searchLoad"
    >
      <im-search-pad-item prop="id">
        <el-input v-model="model.id" placeholder="请输入业务单号" />
      </im-search-pad-item>
      <im-search-pad-item prop="applicantUserName">
        <el-input v-model="model.applicantUserName" placeholder="请输入申请人" />
      </im-search-pad-item>
      <im-search-pad-item prop="during">
        <el-date-picker
          v-model="during"
          type="datetimerange"
          align="right"
          unlink-panels
          range-separator="至"
          start-placeholder="起始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd hh:mm:ss"
        >
        </el-date-picker>
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        :tabs="tabList"
        @change="handleChangeTab"
      >
        <template slot="button">
          <!-- <el-button  v-if="checkPermission(['admin','productPay:export'])">导出</el-button> -->
          <el-button  @click="reload">刷新</el-button>
        </template>
      </tabs-layout>
      <table-pager ref="bussinessTabel" :options="tableTitle" :remote-method="load" :data.sync="tableData" :pageSize="pageSize" :isNeedButton="isWait">
        <template slot="type">
          <el-table-column label="业务类型" width="120">
            <slot slot-scope="{row}">
              品种保证金{{row.type.desc}}
            </slot>

          </el-table-column>
        </template>
        <template slot="amount">
          <el-table-column label="金额（元）" width="100">
            <slot slot-scope="{row}">
              {{row.amount|getDecimals}}
            </slot>
          </el-table-column>
        </template>
        <template slot="payCollectId">
          <el-table-column label="收款单号" width="160">
            <slot slot-scope="{row}">
              <p class="text-primary">{{row.payCollectId}}</p>
            </slot>
          </el-table-column>
        </template>
        <template slot="reviewUser" v-if="model.businessStatus==='CANCEL'">
          <el-table-column label="取消人" width="160" prop="reviewUser" />
        </template>
        <template slot="reviewTime" v-if="model.businessStatus==='CANCEL'">
          <el-table-column label="取消时间" width="160" prop="reviewTime" />
        </template>
        <template slot="remarks" v-if="model.businessStatus==='CANCEL'">
          <el-table-column label="取消备注" width="160" prop="remarks" />
        </template>
        <div slot-scope="props" v-if="model.businessStatus==='PROCESS'">
          <el-row class="table-edit-row">
            <span v-if="checkPermission(['admin','productPay:cancel'])" class="table-edit-row-item">
              <el-button type="text" @click="handleVerify(props.row.id)">取消</el-button>
            </span>
          </el-row>
        </div>
      </table-pager>
    </div>
    <el-dialog title="取消保证金缴纳" :visible.sync="verifyVisible" width="500px" @close="handleClose">
      <el-form :model="vForm" ref="vForm">
        <el-form-item prop="remarks" :rules="[{required: true,message: '请输入备注',trigger: 'blur'}]">
          <el-input placeholder="请输入备注信息" v-model="vForm.remarks" type="textarea" rows="4"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submit(1)">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  const TableColumns = [
    { label: "业务单号", name: "id",prop: "id",width: "160"},
    { label: "业务类型", name: "type", prop:"type",slot:true,width: "170" },
    { label: "业务单状态", name: "businessStatus.desc", prop:"businessStatus.desc",width: "95" },
    { label: "申请人", name: "applicantUserName", prop:"applicantUserName",width: '160' },
    { label: "商品编码", name: "productSn",prop:'productSn',width: "150" },
    { label: "商品名称", name: "productName",prop:'productName',width: "190" },
    { label: "生产厂家", name: "productFacturer",prop:'productFacturer',width: "160" },
    { label: "规格", name: "productSku",prop:'productSku',width: "120" },
    { label: "代理区域", name: "proxyArea",prop:'proxyArea',width: "180" },
    { label: "金额（元）", name: "amount", prop:"amount",slot: true  },
    { label: "制单人", name: "createUserName",prop:'createUserName',width: "100" },
    { label: "制单时间", name: "createTime",prop:'createTime',width: "170" },
    { label: "支付方式", name: "method.desc", prop:"method.desc"  },
    { label: "收款单号", name: "payCollectId", prop:"payCollectId",slot: true },
    { label: "取消人", name: "reviewUserName", prop:"reviewUserName",slot: true  },
    { label: "取消时间", name: "reviewTime", prop:"reviewTime",slot: true  },
    { label: "取消原因", name: "remarks", prop:"remarks",slot: true  },
  ];
  const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({ key: i, ...TableColumns[i] });
  }
  import checkPermission from "../../../utils/permission";
  import { statistics,breedList,cancelPay} from '@/api/finance/deposit/index.js'
  import TabsLayout from '@/components/TabsLayout'

  export default {
    components: {
      TabsLayout
    },
    data () {
      return {
        loading: '',
        currentTab: 0,
        tabs: [
          { name: '缴纳中', value: 'PROCESS',count: 0,permission: 'productPay-process:view' },
          { name: '缴纳成功', value: 'FINISH',count: 0,permission: 'productPay-finish:view'  },
          { name: '缴纳取消', value: 'CANAEL',count: 0,permission: 'productPay-cancel:view'  },
        ],
        tableData: [],
        page: 1,
        pageSize: 10,
        totalPage: 0,
        total: 0,
        tableTitle: TableColumnList,
        ids: [],
        during: '',
        model: {
          businessStatus: 'PROCESS',
          id: '',
          applicantUserName: '',
          type: 'COLLECT'
        },
        verifyVisible: false,
        isWait: true,//操作列
        vForm: {
          remarks: '',
        },
        id: ''
      }
    },
    computed: {
      tabList() {
        return [
          { name: `缴纳中（${this.tabs[0].count}）`, value: 'PROCESS', hide: !checkPermission(['admin', 'productPay-process:view']) },
          { name: `缴纳成功（${this.tabs[1].count}）`, value: 'FINISH', hide: !checkPermission(['admin', 'productPay-finish:view']) },
          { name: `缴纳取消（${this.tabs[2].count}）`, value: 'CANAEL', hide: !checkPermission(['admin', 'productPay-cancel:view'])  }
        ]
      }
    },
    mounted() {
      this.getCount()
    },
    methods: {
      checkPermission,
      async getCount() {
        const query = {
          type: 'COLLECT'
        }
        const {data} = await statistics(query)
        this.tabs.forEach(item=>{
          item.count = data[item.value.toLowerCase()]
        })
      },
      handleClose() {
        this.verifyVisible = false
        this.vForm.remarks = ''
        this.$refs['vForm'].resetFields();
      },
      handleVerify(id) {
        this.id = id
        this.verifyVisible = true
      },
      submit(status) {
        const param = {
          ...this.vForm,
          ids: [this.id],
          status: status //1,//状态 1 -> 通过 3 -> 驳回
        }
        this.$refs['vForm'].validate(async valid => {
          if (valid) {
            const data = await cancelPay(param)
            if (data.code === 0) {
              this.getCount()
              this.$message.success('缴纳取消成功！')
              this.verifyVisible = false
              this.handleRefresh({
                page: 1,
                pageSize: 10
              })
            }
          } else {
            return false
          }
        })
      },
      async load(params) {
        const listQuery = {
          model: {
            ...this.model,
            startTime: this.during[0],
            endTime: this.during[1]
          }
        }
        Object.assign(listQuery, params)
        this.loading = true
        const {data} = await breedList(listQuery)
        this.loading = false
        return { data }
      },
      searchLoad() {
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      reload() {
        this.model={
          ...this.model,
          ...{
            businessNo: '',
            applicantUserName: ''
          }
        }
        this.during = ''
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      handleChangeTab(tab) {
        this.model.businessStatus = tab.value
        if(tab.value !== 'WAIT'){
          this.isWait = false
        } else {
          this.isWait = true
        }
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      handleRefresh(pageParams) {
        this.$refs.bussinessTabel.doRefresh(pageParams)
      }
    }
  }
</script>
