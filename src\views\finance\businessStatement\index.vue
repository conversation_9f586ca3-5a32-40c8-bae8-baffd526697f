<template>
  <div>
    <im-search-pad
      :has-expand="false"
      :model="model"
      @reset="reload"
      @search="searchLoad"
    >
      <im-search-pad-item prop="merchantName">
        <el-input v-model="model.merchantName" placeholder="请输入商家名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="merchantSn">
        <el-input v-model="model.merchantSn" placeholder="请输入商家编码" />
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        :tabs="tabs"
        @change="handleChangeTab"
      >
        <template slot="button">
          <el-button  type="primary" @click="handleBatch" v-if="checkPermission(['admin','admin-finance-merchantBilling:application'])">+批量申请结算</el-button>
          <!-- <el-button  v-if="checkPermission(['admin','bussinessStatement:export'])">导出</el-button> -->
          <el-button  @click="reload">刷新</el-button>
        </template>
      </tabs-layout>
      <table-pager ref="todoTable" :options="tableTitle" :remote-method="load" :operation-width="160" :data.sync="tableData" :pageSize="pageSize" :selection="true"  @selection-change="handleSelectionChange">
        <template slot="realAmount">
          <el-table-column label="实收金额（元）" width="120">
            <slot slot-scope="{row}">
              {{row.realAmount|getDecimals}}
            </slot>
          </el-table-column>
        </template>
        <template slot="settlementAmount">
          <el-table-column label="可结算金额（元）" width="135">
            <slot slot-scope="{row}">
              {{row.settlementAmount|getDecimals}}
            </slot>
          </el-table-column>
        </template>
        <template slot="settlementServiceAmount">
          <el-table-column label="可结算交易服务费（元）" width="180">
            <slot slot-scope="{row}">
              {{row.settlementServiceAmount|getDecimals}}
            </slot>
          </el-table-column>
        </template>
        <template slot="settlementCommissionAmount">
          <el-table-column label="可结算品种推广佣金（元）" width="190">
            <slot slot-scope="{row}">
              {{row.settlementCommissionAmount|getDecimals}}
            </slot>
          </el-table-column>
        </template>
        <template slot="settlementChargedAmount">
          <el-table-column label="可结算交易手续费（元）" width="180">
            <slot slot-scope="{row}">
              {{row.settlementChargedAmount|getDecimals}}
            </slot>
          </el-table-column>
        </template>
        <template slot="beCountSettlement">
          <el-table-column label="可结算的业务账单数" width="150">
            <slot slot-scope="{row}">
              <span class="text-primary">{{row.beCountSettlement}}</span>
            </slot>
          </el-table-column>
        </template>
        <div slot-scope="props">
          <el-row class="table-edit-row">
            <span v-if="checkPermission(['admin','admin-finance-merchantBilling:view'])" class="table-edit-row-item">
              <el-button type="text" @click="$router.push({path: '/finance/businessStatement/bussiness/detail',query:{merchantsId: props.row.merchantsId}})">查看详情</el-button>
            </span>
            <span v-if="props.row.beCountSettlement !==0 && checkPermission(['admin','admin-finance-merchantBilling:clearing'])" class="table-edit-row-item">
              <el-button type="text" @click="handleSettle(props.row)">一键结算</el-button>
            </span>
          </el-row>
        </div>
      </table-pager>
    </div>
  </div>
</template>

<script>
  const TableColumns = [
    { label: "商家编码", name: "merchantSn",prop: "merchantSn",width: "150"},
    { label: "商家名称", name: "merchantName", prop:"merchantName",width: "150" },
    { label: "实收金额（元）", name: "realAmount",prop: 'realAmount',slot:true },
    { label: "可结算金额（元）", name: "settlementAmount", prop:'settlementAmount',slot:true},
    { label: "可结算交易服务费（元）", name: "settlementServiceAmount", prop:'settlementServiceAmount',slot:true},
    { label: "可结算品种推广佣金（元）", name: "settlementCommissionAmount",prop: 'settlementCommissionAmount',slot:true},
    { label: "可结算交易手续费（元）", name: "settlementChargedAmount",prop:'settlementChargedAmount',slot:true },
    { label: "可结算的业务账单数", name: "beCountSettlement",prop:'beCountSettlement',slot:true}
  ];
  const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({ key: i, ...TableColumns[i] });
  }
  import checkPermission from "../../../utils/permission";
  import { financeBillOrder,merchantSettlement,batchSettlement } from '@/api/finance'
  import SearchPad from '@/components/searchPad'
  import TabsLayout from '@/components/TabsLayout'
  export default {
    components: {
      SearchPad,
      TabsLayout
    },
    data () {
      return {
        loading: '',
        currentTab: 0,
        tabs: [
          { name: '商家账单' },
        ],
        tableData: [],
        page: 1,
        pageSize: 10,
        totalPage: 0,
        total: 0,
        tableTitle: TableColumnList,
        merchantsIds: [],
        selectArray: [],
        model: {
          merchantName: '',
          merchantSn:''
        }
      }
    },
    mounted() {
    },
    methods: {
      checkPermission,
      handleSelectionChange(val) {
        this.merchantsIds = val.map(function(item,index) {
          return item.merchantsId;
        })

      },
      handleBatch() {
        if(this.merchantsIds.length === 0) {
          this.$message.warning('请至少选择一项进行操作！')
          return
        }
        this.$confirm('确定结算所选账单？','提示').then(_ => {
          batchSettlement({
            merchantsIds: this.merchantsIds
          }).then(res=>{
            this.$message.success('操作成功！')
            this.merchantsIds =[]
            this.handleRefresh({
              page: 1,
              pageSize: 10
            })
          })

        }).catch(_ => {});
      },
      handleSettle(row) {
        if(row.settlementAmount > 0) {
          this.$confirm('确定结算所选账单？','提示').then(_ => {
            merchantSettlement(row.merchantsId).then(res=>{
              this.$message.success('操作成功！')
              this.handleRefresh({
                page: 1,
                pageSize: 10
              })
            })

          }).catch(_ => {});
        } else {
          this.$confirm('账单结算总金额为负数，不可结算。', '提示',{
            showConfirmButton: false,
            cancelButtonText: '返回'
          })
        }

      },
      async load(params) {
        const listQuery = {
          model: this.model
        }
        Object.assign(listQuery, params)
        this.loading = true
        const {data} = await financeBillOrder(listQuery)
        data.records.forEach((item,index)=>{
          if (item.settlementAmount < 0||item.beCountSettlement===0) {
            item.selectable = true
          } else {
            item.selectable = false
          }
        })
        return { data }
        this.loading = false
      },
      searchLoad() {
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      reload() {
        this.model={}
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      handleChangeTab (tab) {
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      getType(val) {
        this.model.deliveryStatus = val
      },
      handleRefresh(pageParams) {
        this.$refs.todoTable.doRefresh(pageParams)
      }
    }
  }
</script>

