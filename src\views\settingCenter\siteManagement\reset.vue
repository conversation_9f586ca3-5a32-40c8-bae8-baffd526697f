<template>
  <el-dialog
    :close-on-click-modal="false"
    v-if="visible"
    title="重置密码"
    :visible.sync="visible"
    :before-close="clearFun"
    width="450px"
  >
    <div class="businessScopeEditContent">
      <el-form class="form" :model="query" ref="ruleForm" label-width="120px">
        <el-form-item
          class="formItem"
          prop="password"
          label="请输入密码:"
          :rules="[{ required: true, message: '请输入密码', trigger: 'blur' }]"
        >
          <el-input
            clearable
            style="width: 250px"
            v-model="query.password"
            placeholder="请输入密码"
          ></el-input>
        </el-form-item>
        <el-form-item
          class="formItem"
          prop="confirmPassword"
          label="确认密码:"
          :rules="[{ required: true, message: '请确认密码', trigger: 'blur' }]"
        >
          <el-input
            clearable
            style="width: 250px"
            v-model="query.confirmPassword"
            placeholder="请确认密码"
          ></el-input>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="clearFun()">取 消</el-button>
      <el-button type="primary" @click="submitFun('ruleForm')">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { resetApi } from "@/api/setting/siteManagement";
export default {
  data() {
    return {
      query: {},
    };
  },
  props: {
    row: {
      type: Object,
    },
    visible: {
      type: Boolean,
      default: false,
      required: true,
    },
    // isReload: {
    //   type: Boolean,
    //   default: false,
    //   required: true,
    // },
  },
  methods: {
    clearFun: function () {
      this.$emit("update:visible", false);
      this.$emit("update:row", {});
    },
    submitFun: function (businessScopeForm) {
      console.log(this.row);
      let _this = this;
      _this.$refs[businessScopeForm].validate(async (valid) => {
        if (valid) {
          await resetApi({
            id: _this.row.userId,
            // oldPassword:_this.row.password,
            password: _this.query.password,
            confirmPassword: _this.query.confirmPassword,
    
          }).then(res=>{
         if(res.code == 0){
           _this.$message({
              message: '修改成功！',
              type: 'success',
            })
         }
          });
          _this.$emit("update:visible", false);
          // _this.$emit("update:isReload", true);
        } else {
          return false;
        }
      });
    },
  },
  async mounted() {
    this.query = JSON.parse(JSON.stringify(this.row));
    this.query.whetherShowFrontend = "Y";
    // if(this.query.id==undefined || this.query.id==0){
    //   this.query.whetherShowFrontend="Y"
    //   this.query.sortValue=0
    // }
    // else{
    //   if(this.query.whetherShowFrontend!=null){
    //     this.query.whetherShowFrontend=this.query.whetherShowFrontend.code
    //   }
    // }
  },
  beforeDestroy() {},
};
</script>
<style lang="less" scoped>
.businessScopeEditContent {
  margin: -30px -20px;
  border-top: 1px solid #ebecee;
  padding: 30px 20px;
}
</style>
