import Layout from '@/layout'
const enterpriseRouter = {
    path: '/enterprise',
    name: 'enterprise',
    redirect: 'enterprise/enterpriselist',
    meta: {
      roles: ['admin','enterpriselist:view'],
      title: '企业档案',
      icon: 'component'
    },
    alwaysShow: true,
    component: Layout,
    children: [
      {
        path: 'enterpriselist',
        name: 'enterpriseList',
        meta: { title: '企业档案' },
        component: () => import('@/views/enterprise/list/list')
      },
      {
        path: 'detail',
        name: 'detail',
        hidden: true,
        meta: { title: '编辑企业档案' },
        component: () => import('@/views/enterprise/detail/index')
      }
    ]
}

export default enterpriseRouter