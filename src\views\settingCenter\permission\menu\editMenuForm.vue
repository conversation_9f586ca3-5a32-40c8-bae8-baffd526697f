<template>
  <el-form class="form" :model="query" ref="ruleForm"  label-width="120px">
    <div style="margin-bottom: 20px;display: flex;justify-content: space-between;align-items: center;">
      <el-tag :type="isAdd ? 'success' : 'danger'">{{ isAdd ? '新增' : '编辑' }}</el-tag>
      <el-button :loading="saveLoading" type="primary" plain @click="saveMenu('ruleForm')">
        {{ isAdd ? '新增' : '保存' }}
      </el-button>
    </div>
    <el-form-item class="formItem" prop="label" label="上级菜单:">
      <el-cascader
        clearable
        filterable
        style="width: 100%"
        v-model="query.parentId"
        :options="folderTree"
        :props="{ expandTrigger: 'click', checkStrictly: true, emitPath: false, value: 'id', label: 'label' }"
      >
        <template slot-scope="{ node, data }">
          <span>{{ data.label }}</span>
          <span v-if="!node.isLeaf" style="color: #999"> ({{ data.children.length }}) </span>
        </template>
      </el-cascader>
    </el-form-item>
    <el-form-item class="formItem" prop="label" label="栏目名称:" :rules="[{ required: true, message: '请填写栏目名称' }]" >
      <el-input clearable v-model="query.label" placeholder="请填写栏目名称" ></el-input>
    </el-form-item>
    <el-form-item class="formItem"  label="图标:"  >
        <icon-picker v-model="query.icon" ></icon-picker>
    </el-form-item>

    <el-form-item class="formItem number" label="排序:"  >
      <el-input-number v-model="query.sortValue" :min="1" label="请填排序"></el-input-number>
    </el-form-item>
    <el-form-item class="formItem " prop="group" label="分组:">
      <el-input clearable v-model="query.group" placeholder="请填分组" ></el-input>
    </el-form-item>

    <el-form-item class="formItem max" prop="path" label="路由URI:" :rules="[{ required: true, message: '请填写路由URI' }]" >
      <el-input clearable    v-model="query.path" placeholder="请填写路由URI" ></el-input>
    </el-form-item>
    <el-form-item class="formItem" prop="redirect" label="路由重定向URI:"  >
      <el-input clearable   v-model="query.redirect" placeholder="路由重定向URI" ></el-input>
    </el-form-item>
    <el-form-item class="formItem max" prop="component" label="组件:" :rules="[{ required: true, message: '请填写组件' }]" >
      <el-input clearable v-model="query.component" placeholder="请填写组件" ></el-input>
      <div style="font-size: 12px;word-wrap:break-word;">src/views/{{query.component?query.component:'***/***'}}.vue</div>
    </el-form-item>
    <el-form-item class="formItem max" prop="code" label="组件名称:" >
      <el-input clearable v-model="query.code" placeholder="请填写组件名称(只填英文)" ></el-input>
    </el-form-item>
    <el-form-item  class="formItem max" prop="describe" label="描述:" >
      <el-input   clearable v-model="query.describe" placeholder="请填描述" ></el-input>
    </el-form-item>
    <el-form-item class="formItem"  label="导航隐藏:"  >
      <el-radio-group v-model="query.hidden">
        <el-radio :label="false">否</el-radio>
        <el-radio :label="true">是</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item class="formItem"  label="状态:"  >
      <el-radio-group v-model="query.isEnable">
        <el-radio :label="false">禁用</el-radio>
        <el-radio :label="true">启用</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item class="formItem"  label="是否公有:"  >
      <el-radio-group v-model="query.isPublic">
        <el-radio :label="false">否</el-radio>
        <el-radio :label="true">是</el-radio>
      </el-radio-group>
    </el-form-item>
  </el-form>
</template>

<script>
import { editApi } from "@/api/setting/permission/menu";
import { deepClone } from "@/utils";

export default {
  name: 'editMenuForm',
  props: {
    commerceModel: {
      type: String,
      required: true,
      default: ""
    },
    folderTree: {
      type: Array,
      default: () => []
    },
    clientId: {
      type: String,
      required: true,
      default: ""
    }
  },
  data() {
    return {
      saveLoading: false,
      query: {
        parentName: "根节点", // 上级菜单
        parentId: "", // 父id
        commerceModel: "", // SAAS_PLATFORM 或 SAAS
        clientId: "", // 客户端id
        component: "", // 组件
        isPublic: false, // 是否公有
        isEnable: true, // 启用/禁用
        hidden: false, // 导航隐藏
        describe: "", // 描述
        code: "", // 组件名称
        group: "", // 分组
        sortValue: 1, // 排序
        redirect: "", // 路由重定向URI
        path: "", // 路由URI
        icon: "", // 图标
        label: "", // 栏目名称
        id: 0 // 修改的id
      }
    }
  },
  computed: {
    isAdd() {
      return this.query.id === 0
    }
  },
  methods: {
    // 设置菜单
    setItemMenu(itemMenu) {
      Object.keys(this.query).forEach(key => {
        let newVal = itemMenu[key]
        switch (key) {
          case 'parentName':
            if (!newVal) newVal = ''
            break
          case 'isPublic':
          case 'hidden':
            if (typeof newVal === 'undefined' || newVal === '') newVal = false
            break
          case 'isEnable':
            if (typeof newVal === 'undefined' || newVal === '') newVal = true
            break
          case 'sortValue':
            if (!newVal) newVal = 1
            break
          case 'id':
            if (!newVal) newVal = 0
            break
          default:
            newVal = itemMenu[key] || ''
        }
        this.$set(this.query, key, newVal)
      })
      this.$nextTick(() => {
        this.$refs.ruleForm.clearValidate()
      })
    },
    // 保存菜单
    saveMenu(ruleForm) {
      this.$refs[ruleForm].validate((valid) => {
        if (!valid) return
        this.saveLoading = true
        let query = deepClone(this.query)
        if (!query.commerceModel) {
          query.commerceModel = this.commerceModel
        }
        if (!query.clientId) {
          query.clientId = this.clientId
        }
        if (!query.parentId) {
          query.parentId = '0'
        }
        editApi(query).then(res => {
          if (res.isSuccess) {
            let text = this.isAdd ? '新增' : '编辑'
            this.$message.success(text + "成功")
            this.$emit("saveMenu", res?.data?.id)
          }
        }).finally(() => {
          this.saveLoading = false
        })
      });
    }
  }
}
</script>

<style>

</style>
