<template>
  <div class="temp_ProductTableButton">
    <el-button style="width:100px;padding:0 -10px; text-indent: -10px;" @click="showStoreTableDialogFun()">选择店铺</el-button>
    <el-dialog v-if="showStoreTableDialog==true" append-to-body title="店铺列表" :visible.sync="showStoreTableDialog" :before-close="closeDialogFun" :close-on-click-modal="false" width="80%" :show-close="false">
      <div style="position:absolute;top:10px; right:15px; background:#fff; height:38px;">
        <div>
          <el-button @click="clearFun()">取 消</el-button>
          <!-- <el-button type="primary" @click="submitFun()">确 定</el-button> -->
        </div>
      </div>
      <div class="showProductTable">
        <div class="temp_searchBox">
          <el-form ref="searchForm" :inline="true" :model="listQuery" class="form-inline">
            <el-form-item label="">
              <el-input v-model="listQuery.model.shopName" placeholder="请输入店铺名称" style="width:250px" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="getList()">搜索</el-button>
              <el-button @click="resetForm">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="table">
          <el-table
            v-if="list"
            ref="table"
            v-loading="listLoading"
            :data="list"
            row-key="id"
            border
            fit
            highlight-current-row
            style="width: 100%"
          >
            <el-table-column
              align="center"
              width="50"
              label="序号"
              fixed
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                <span>{{ scope.$index+1 }}</span>
              </template>
            </el-table-column>
            <el-table-column v-for="(item, index) in tableTitle" :key="index" :min-width="(item.width?item.width:'350px')" :label="item.label" show-overflow-tooltip align="left">
              <template slot-scope="{row}">
                <span v-if="item.name=='publishStatus'" :style="row[item.name].code=='N'?'color:#ff0066':''">
                  {{ row[item.name].code=='Y'? '已启用' : '已冻结' }}
                </span>
                <!-- <el-button v-if="item.name=='publishStatus'&&row[item.name].code=='Y'" type="text" style="color:#409EFF">已启用</el-button>
                            <el-button v-else-if="item.name=='publishStatus'&&row[item.name].code=='N'" type="text" style="color:#FF3C54">已冻结</el-button> -->
                <span v-else>{{ row[item.name] }}</span>
              </template>
            </el-table-column>

            <el-table-column fixed="right" align="center" label="操作" width="150" class="itemAction">
              <template slot-scope="scope">
                <el-button type="text" @click="selectProductItemFun(scope.row)">选中</el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination v-show="total>0" :total="total" :page.sync="listQuery.current" :limit.sync="listQuery.size" @pagination="getList" />
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import { outlist } from '@/api/businessCenter/businessList'
export default {
  filters: {
    imgFilter: function(value) {
      if (value != '') {
        return value.split(',')[0]
      }
    }
  },
  components: {
    Pagination
  },
  props: {
    selectItems: {
      type: String,
      default: '',
      required: true
    }
  },
  data() {
    return {
      tableTitle: [
        { key: 0,
          label: '商家状态',
          name: 'publishStatus',
          width: '80px',
          disabled: true
        },
        { key: 1,
          label: '商家ID',
          name: 'id',
          width: '170px',
          hidden: true
        },
        { key: 2,
          label: '商家编码',
          name: 'code',
          width: '140px',
          disabled: true
        },
        { key: 3,
          label: '店铺名称',
          name: 'shopName',
          width: '200px',
          disabled: true
        },
        { key: 4,
          label: '商家名称',
          name: 'name',
          width: '200px',
          disabled: true
        },
        { key: 5,
          label: '社会统一信用代码',
          name: 'socialCreditCode',
          width: '200px'
        },
        { key: 6,
          label: '法人代表',
          name: 'legalPerson',
          width: '80px'
        },
        { key: 7,
          label: '负责人',
          name: 'ceoName',
          width: '80px'
        },
        { key: 8,
          label: '负责人手机',
          name: 'ceoMobile',
          width: '112px'
        },
        { key: 9,
          label: '所在地区',
          name: 'region',
          width: '168px'
        },
        { key: 10,
          label: '注册地址',
          name: 'registerAddress',
          width: '320px'
        },
        { key: 11,
          label: '审批人',
          name: 'approvalUserName',
          width: '120px'
        },
        { key: 12,
          label: '审批时间',
          name: 'approvalDate',
          width: '170px'
        }

      ],
      showStoreTableDialog: false,
      // listQuery: {
      //     model:{
      //         shopName:"",
      //         approvalStatus:{
      //             code:"ACCEPTED"
      //         }
      //     },
      //     current: 1,
      //     size: 10
      // },
      listQuery: {
        current: 1,
        size: 10,
        model: {
          shopName: '',
          approvalStatus: { code: 'ACCEPTED' },
        }
      },
      selectRowItems: [],
      list: [],
      total: 0,
      listLoading: true
    }
  },
  computed: {
    selectRowVal: {
      get() {
        this.selectItems
      },
      set(val) {
        this.$emit('update:selectItems', val)
        this.$emit('confirm')
      }
    }
  },
  mounted() {
  },
  beforeDestroy() {},
  methods: {
    clearFun() {
      this.listQuery = {
        current: 1,
        size: 10,
        model: {
          shopName: '',
          approvalStatus: { code: 'ACCEPTED' }
        }
      }
      this.list = []
      this.total = 0
      this.listLoading = true
      this.showStoreTableDialog = false
    },
    showStoreTableDialogFun() {
      this.listQuery = {
        model: {
          shopName: '',
          approvalStatus: 'ACCEPTED'
        },
        current: 1,
        size: 10
      },
      this.list = [],
      this.total = 0,
      this.listLoading = true,
      this.showStoreTableDialog = true
      this.getList()
    },
    resetForm() {
      this.listQuery.model.shopName = ''
      this.getList()
    },
    async getList() {
      this.listLoading = true
      this.listQuery.model = { ...this.listQuery.model, commerceModel: "SAAS_PLATFORM" }
      const { data } = await outlist(this.listQuery)
      if (data != null) {
        this.list = data.records
        this.total = data.total
      }
      this.listLoading = false
      // this.displayTable()
    },
    closeDialogFun() {
      this.showStoreTableDialog = false
    },
    onAllSelect(selection) {
      this.onSelect(selection)
    },
    onSelect: function(val, row) {
      if (val.length < 10) {
        this.selectRowItems = val
      } else {
        this.$refs.table.toggleRowSelection(row, false)
      }
    },
    displayTable() {
      const vm = this
      vm.selectItems.forEach(item => {
        vm.list.forEach(rowItem => {
          if (item.id === rowItem.dataParam) {
            vm.$refs.table.toggleRowSelection(item, true)
          }
        })
      })
    },
    selectProductItemFun(item) {
      this.selectRowVal = item.id
      this.$emit('confirm')
      this.clearFun()
    }
  }
}
</script>
<style lang="less" scoped>
.temp_ProductTableButton{width:100%;

}
.showProductTable{
    margin: -30px -20px;
    border-top: 1px solid #ebecee;
    padding: 10px 20px;
}
.temp_searchBox{height:45px;overflow: hidden; margin-bottom: 0;  padding:0; border-left:none;}

</style>
