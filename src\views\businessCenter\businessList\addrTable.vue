<template>
  <div>
    <el-table :data="tableDate" style="width: 100%" border>
      <el-table-column prop="name" :label="($route.path.indexOf('purchasingAgentlist') != -1 ? '收': '发')+'货人姓名'" min-width="120">
        <template slot-scope="{ row }">
          <el-input v-if="row.isEdit" :placeholder="'请输入'+($route.path.indexOf('purchasingAgentlist') != -1 ? '收': '发')+'货人'" v-model="row.name"></el-input>
          <span v-else>{{row.name}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="mobilPhone" label="联系手机" min-width="170">
        <template slot-scope="{ row }">
          <el-input v-if="row.isEdit" placeholder="请输入联系手机" v-model="row.mobilPhone"></el-input>
          <span v-else>{{row.mobilPhone}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="fixedPhone" label="联系电话" min-width="170">
        <template slot-scope="{ row }">
          <el-input v-if="row.isEdit" placeholder="请输入联系电话" v-model="row.fixedPhone"></el-input>
          <span v-else>{{row.fixedPhone}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="region" :label="($route.path.indexOf('purchasingAgentlist') != -1 ? '收': '发') +'货区域'" min-width="260">
        <template slot-scope="{ row }">
          <el-cascader :disabled="!row.isEdit" ref="addr" style="width:100%" v-model="row.address" placeholder="请选择所在区域" :props="{ value: 'id', label: 'label'}" :options="areasTree" clearable>
          </el-cascader>
        </template>
      </el-table-column>
      <el-table-column prop="detailedAddress" label="详细地址" min-width="220">
        <template slot-scope="{ row }">
          <el-input v-if="row.isEdit" placeholder="请输入详细地址" v-model="row.detailedAddress"></el-input>
          <span v-else>{{row.detailedAddress}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="isDefault" align="center" width="250" label="是否默认">
        <template slot-scope="{row}">
          <span v-if="row.isEdit && tableDate.length > 1">
            <el-radio v-model="row.defaultOrNot"  label="Y" @change="handleSetDefault(row)">默认</el-radio>
          </span>
          <span v-else :style="row.defaultOrNot === 'Y'? '' : 'color:#ff0066'"> {{row.defaultOrNot == 'Y'? '默认' : '非默认'}} </span>
        </template>
      </el-table-column>
      <el-table-column prop="region" align="center" width="250" label="是否启用">
        <template slot-scope="{row}">
          <span v-if="row.isEdit">
            <el-radio v-model="row.isOpen"  label="Y">启用</el-radio>
            <el-radio v-model="row.isOpen"  label="N">禁用</el-radio>
          </span>
          <span v-else :style="row.isOpen == 'Y'? '' : 'color:#ff0066'"> {{row.isOpen == 'Y'? '启用' : '禁用'}} </span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" align="center" label="操作" width="130" class="itemAction">
        <template slot-scope="scope">
          <el-button :disabled="$route.path.indexOf('detail') != -1" v-hasPermission :pm="$props.edit" type="text" @click="editAddrItem(scope.row, scope.$index)" v-show="!scope.row.isEdit" >编辑</el-button>
          <el-button type="text"  v-show="scope.row.isEdit" @click="canceladdrEdit(scope.row, scope.$index)" style="color: rgb(127, 127, 127);"> 取 消 </el-button>
          <el-button type="text" @click="confirmaddrEdit(scope.row, scope.$index)" v-show="scope.row.isEdit">确定</el-button>
          <el-button :disabled="$route.path.indexOf('detail') != -1" v-hasPermission :pm="$props.delete" type="text" v-show="!scope.row.isEdit && scope.row.defaultOrNot == 'N'" @click="delAddr(scope.row, scope.$index)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div v-hasPermission :pm="$props.add" style="text-align: center;line-height:38px; border:1px solid #dfe6ec;border-top:0;">
      <el-button :disabled="$route.path.indexOf('detail') != -1" @click="address" type="text"  slot="reference">+添加{{($route.path.indexOf('purchasingAgentlist') != -1 ? '收': '发')}}货地址</el-button>
    </div>
  </div>
</template>

<script>
import hasPermission from './hasPermission'
import {
  addrList,
  adaddr,
  deladdr,
  editaddr,
  areas,
} from "@/api/businessCenter/businessList";
export default {
  data() {
    return {
      areasTree: [],
      tableDate: [],
      oldaddrItem: {},
      oldAddrtableDate: [],
    };
  },
  methods: {
    address() {
      const isSetDefault = this.tableDate.length === 0 ? 'Y' : 'N'
      let obj = {
        cityId: "",
        countyId: "",
        detailedAddress: "",
        fixedPhone: "",
        isOpen: "Y",
        defaultOrNot: isSetDefault,
        merchantId: this.$route.query.id || "",
        isEdit: true,
        mobilPhone: "",
        name: "",
        provinceId: "",
        address: this.defaultAddr,
      };
      this.oldAddrtableDate = JSON.parse(JSON.stringify(this.tableDate))
      this.tableDate.push(obj);
    },
    // 删除地址
    async delAddr(row, index) {
      if (row.id) {
        let { data } = await deladdr({ ids: [row.id] });
        if (data) {
          this.$message.success("已删除该地址！");
          this.tableDate.forEach((item, index) => {
            if (item.id == row.id) {
              this.tableDate.splice(index, 1);
            }
          });
        }
      } else {
        this.tableDate.splice(index, 1);
      }
      this.oldAddrtableDate = JSON.parse(JSON.stringify(this.tableDate));
      this.$emit("update:addrtableDate", JSON.parse(JSON.stringify(this.tableDate)));
      // 只有一条收货地址的时候，自动设置为默认
      if (this.tableDate.length === 1) {
        this.tableDate[0].defaultOrNot = 'Y'
        this.confirmaddrEdit(this.tableDate[0])
      }
    },
    // 获取地址
    async getaddrList() {
      if (this.$props.id) {
        let { data } = await addrList(this.$props.id);
        data.forEach((item) => {
          item.isOpen = item.isOpen.code;
          item.defaultOrNot = item.defaultOrNot.code;
          item.isEdit = false;
          item.address = [item.provinceId, item.cityId, item.countyId];
        });
        this.tableDate = data;
        this.oldAddrtableDate = JSON.parse(JSON.stringify(data));
      }
    },
    canceladdrEdit(row, index) {
      if (this.oldAddrtableDate.length == 0) {
        this.tableDate = [];
        return;
      }
      this.tableDate = JSON.parse(JSON.stringify(this.oldAddrtableDate));
    },
    async confirmaddrEdit(row, index) {
      if (!row.name) {
        this.$message.error("请填写"+(this.$route.path.indexOf('purchasingAgentlist') != -1 ? '收': '发')+"货人姓名");
        return;
      }
      if (
        !/^1\d{10}$/.test(
          row.mobilPhone
        )
      ) {
        this.$message.error("联系手机有误");
        return;
      }
      if (!row.detailedAddress) {
        this.$message.error("请填写详细地址");
        return;
      }
      if (row.address.length == 0) {
        this.$message.error("请选择所在区域");
        return;
      }
      row.provinceId = row.address[0];
      row.cityId = row.address[1];
      row.countyId = row.address[2];
      row.isEdit = false;
      if (this.$props.id) {
        if (row.id) {
          let { data } = await editaddr(row);
          if (data.id) {
            this.$message.success("修改地址成功");
            data.isOpen = data.isOpen.code;
            row = data;
            this.tableDate = this.tableDate.map(item=>{
              return Object.assign(
                {},
                item,
                {
                  defaultOrNot:(item.defaultOrNot instanceof Object) ? item.defaultOrNot.code : item.defaultOrNot
                }
              )
            })
            this.oldAddrtableDate = JSON.parse(JSON.stringify(this.tableDate));
            this.$emit("update:addrtableDate", JSON.parse(JSON.stringify(this.tableDate)));
          }
        } else {
          let { data } = await adaddr(row);
          if (data.id) {
            this.$message.success("添加地址成功");
            data.address = [data.provinceId, data.cityId, data.countyId];
            data.isOpen = data.isOpen.code;
            this.tableDate = this.tableDate.map(item=>{
              return Object.assign(
                {},
                item,
                {
                  defaultOrNot:(item.defaultOrNot instanceof Object) ? item.defaultOrNot.code : item.defaultOrNot
                }
              )
            })
            this.oldAddrtableDate = JSON.parse(JSON.stringify(this.tableDate));
            this.$emit("update:addrtableDate", JSON.parse(JSON.stringify(this.tableDate)));
          }
        }
      } else {
        this.tableDate = this.tableDate.map(item=>{
          return Object.assign(
            {},
            item,
            {
              defaultOrNot:(item.defaultOrNot instanceof Object) ? item.defaultOrNot.code : item.defaultOrNot
            }
          )
        })
        let obj = JSON.parse(JSON.stringify(this.tableDate))
        this.$emit("update:addrtableDate", obj);
      }
    },
    editAddrItem(row, index) {
      this.oldAddrtableDate = JSON.parse(JSON.stringify(this.tableDate));
      row.isEdit = true;
    },
    async getareas() {
      let { data } = await areas();
      this.areasTree = data;
    },
    handleSetDefault(row) {
      this.tableDate.forEach(item => item.defaultOrNot = 'N')
      row.defaultOrNot = 'Y'
    }
  },
  directives: {
    hasPermission
  },
  props: {
    addrtableDate: {
      type: Array,
      required: true,
    },
    id: {
      type: String,
      required: false,
    },
    edit: {
      type: Array
    },
    delete: {
      type: Array
    },
    add: {
      type: Array
    },
    defaultAddr: {
      type: Array,
      default(){
        return ["110000","110100","110101"]
      }
    }
  },
  created() {
    this.getareas();
    this.getaddrList();
  },
};
</script>

<style>
</style>
