<template>
  <el-dialog
    title="申请退款"
    :visible.sync="deliveryVisible"
    width="90%"
    @close="handleClose">
    <p class="info-top">
      <span>订单编号：{{delivery.orderNo}}</span>
      <span v-if="delivery.purMerchant">客户名称：{{delivery.purMerchant.name}}</span>
      <span>订单金额：<span v-text="formatterMoney(delivery.totalMoney,2)"></span></span>
      <span>运费：<span v-text="formatterMoney(delivery.freight,2)"></span></span>
      <span>优惠：<span v-text="formatterMoney(delivery.discount,2)"></span></span>
    </p>
    <div class="table-top" style="text-align: right;">
        退款小计：<span class="text-warning">￥</span><span v-text="formatterMoney(delivery.totalMoney,2)" class="text-warning"></span> - 运费
        <el-tooltip content="需要客户支付的运费" placement="top">
          <el-button icon="el-icon-info" type="text"></el-button>
        </el-tooltip>
      <el-input style="width: 100px"/> - 折损
      <el-tooltip content="客户支付商品折损金额" placement="top">
        <el-button icon="el-icon-info" type="text"></el-button>
      </el-tooltip>
      <el-input style="width: 100px"/>  = 退款总金额：
      <span></span>
    </div>
    <el-table border :data="tableData" @selection-change="handleSelectionChange" @select='onTableSelect' ref="table">

      <el-table-column type="selection" width="55"/>
      <el-table-column type="index" fixed />
      <el-table-column prop="productImg" label="商品主图" width="80">
        <template slot-scope="scope">
          <img :src="scope.row.productImg | imgFilter" class="productImg">
        </template>
      </el-table-column>
      <el-table-column label="商品编码" prop="productCode" width="148"/>
      <el-table-column label="商品名称" prop="productName" width="170"/>
      <el-table-column label="规格" prop="spec" width="100"/>
      <el-table-column label="生产厂家" prop="saleMerchantName" width="200"/>
      <el-table-column label="购买数量" prop="totalNum" width="80"/>
      <el-table-column label="可退金额" prop="allowRefundMoney" width="80"/>
      <el-table-column label="退款金额" prop="refundMoney" width="80"/>
      <el-table-column label="可退数量" prop="allowRefundNum" width="110" fixed="right">
        <template slot-scope="{row}">
          <el-input-number v-if="row.show===true" controls-position="right" @change="handleChange" :min="0" :max="row.allowRefundNum" v-model="row.allowRefundNum" style="width:110px;"></el-input-number>
          <span v-if="row.show===false">{{row.allowRefundNum}}</span>
        </template>
      </el-table-column>
    </el-table>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="onsubmit"  v-if="checkPermission(['admin','orderDetail:refund'])">申请退款</el-button>
      </span>
  </el-dialog>
</template>

<script>
  import {listRefundWindowVo} from '@/api/trade'
  import checkPermission from "@/utils/permission";
  export default {
    name: "delivery-dialog",
    props: ['dialogData','deliveryVisible'],
    data() {
      return {
        visible: false,
        way: '1',
        delivery: '',
        logisName: '',
        shippingNo: '',
        tableData: []
      }
    },
    mounted() {
      this.getDevelivery()
    },
    methods: {
      checkPermission,
      async getDevelivery() {
        const {data} = await listRefundWindowVo(this.dialogData)
        this.delivery = data
        this.tableData = data.productList
        this.tableData.forEach(item => {
          item.show = false
        })
      },
      onTableSelect(rows, row) {
        let selected = rows.length && rows.indexOf(row) !== -1
        if(selected===false) {
          let t = 0
          t = this.tableData.map(itm=>itm.productId).indexOf(row.productId)
          this.tableData[t].show = false
        }
      },
      handleSelectionChange(rows){
        if (rows.length > 0) {
          if(rows.length === this.tableData.length) {
            this.tableData.forEach(item => {
              item.show = true
            })
          } else {
            let t = 0
            t = this.tableData.map(itm => itm.productId).indexOf(rows[rows.length - 1].productId)
            this.tableData[t].show = true
          }
        } else {
          this.tableData.forEach(item => {
            item.show = false
          })
        }
      },
      handleClose() {
        this.$emit('componentResult','close')
      },
      formatterMoney(s, n) {
        n = n > 0 && n <= 20 ? n : 2;
        s = parseFloat((s + "").replace(/[^\d\.-]/g, "")).toFixed(n) + "";
        let l = s.split(".")[0].split("").reverse(), r = s.split(".")[1];
        let t = "";
        for (let i = 0; i < l.length; i++) {
          t += l[i] + ((i + 1) % 3 == 0 && (i + 1) != l.length ? "," : "");
        }
        return t.split("").reverse().join("") + "." + r;
      },
      onsubmit(){}
    },
    watch: {
    }
  }
</script>

<style lang="scss">

</style>
