const Mock = require('mockjs')

const List = []
const count = 100

const baseContent = '<p>I am testing data, I am testing data.</p><p><img src="https://wpimg.wallstcn.com/4c69009c-0fd4-4153-b112-6cb53d1cf943"></p>'
const image_uri = 'https://wpimg.wallstcn.com/e4558086-631c-425c-9430-56ffb46e70b3'

for (let i = 0; i < count; i++) {
  List.push(Mock.mock({
    id: '@increment',
    pic: image_uri,
    productNumber: '@string("lower", 5, 10)',
    productName:Mock.Random.csentence(5),
    spec: '@integer(12,150)'+'mg*'+'@integer(12, 24)'+'粒',
    documentNumber: '国药准H' + Mock.Random.date('yyyyMMdd'),
    'dosage|1': ['口服片剂', '口服液'],
    manufacturer: Mock.Random.city(true)+Mock.Random.csentence(),
    'businessCategory|1': ['药品/中成药', '药品/西药'],
    'productType|1': ['西药', '男科用药', '泌尿系统'],
    code:Mock.Random.date('yyyyMMddHHmmsssss'),
    'source|1': ['商家', '平台'],
    approved:'@cname',
    crDate: '@datetime',
    id: '@increment',
    pic: image_uri,
    'sellStatus|1': [1, 2, 3],
    productNumber: '@string("lower", 5, 10)',
    productName: Mock.Random.csentence(5),
    spec: '@integer(12,150)' + 'mg*' + '@integer(12, 24)' + '粒',
    documentNumber: '国药准H' + Mock.Random.date('yyyyMMdd'),
    sellingPrice: '@integer(50,100)',
    costPrice: '@integer(10,20)',
    grossMargin: '@integer(10,20)'+"%",
    grossProfit: '@integer(1,20)',
    medicalInsurancePrice: '@integer(30,50)',
    retailPrice: '@integer(30,100)',
    manufacturer: Mock.Random.city(true) + Mock.Random.csentence(),
    'businessCategory|1': ['药品/中成药', '药品/西药'],
    'productType|1': ['西药', '男科用药', '泌尿系统'],
    code: Mock.Random.date('yyyyMMddHHmmsssss'),
    'source|1': ['商家', '平台'],
    approvalUser: '@cname',
    reason: Mock.Random.csentence(5),
    approvalTime: '@datetime',
  }))
}

module.exports = [
  {
    url: '/vue-element-admin/products/archives/list',
    type: 'get',
    response: config => {
      const { importance, type, title, page = 1, limit = 20, sort } = config.query

      let mockList = List.filter(item => {
        if (importance && item.importance !== +importance) return false
        if (type && item.type !== type) return false
        if (title && item.title.indexOf(title) < 0) return false
        return true
      })

      if (sort === '-id') {
        mockList = mockList.reverse()
      }

      const pageList = mockList.filter((item, index) => index < limit * page && index >= limit * (page - 1))

      return {
        code: 20000,
        data: {
          total: mockList.length,
          items: pageList
        }
      }
    }
  },
  {
    url: '/vue-element-admin/products/archives/update',
    type: 'post',
    response: _ => {
      return {
        code: 20000,
        data: 'success'
      }
    }
  }
]

