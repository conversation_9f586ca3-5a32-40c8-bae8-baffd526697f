<template>
  <div>
    <im-search-pad
      :has-expand="false"
      :model="model"
      @reset="reload"
      @search="searchLoad"
    >
      <im-search-pad-item prop="id">
        <el-input v-model="model.id" placeholder="请输入业务单号" />
      </im-search-pad-item>
      <im-search-pad-item prop="applicantUserName">
        <el-input v-model="model.applicantUserName" placeholder="请输入申请人" />
      </im-search-pad-item>
      <im-search-pad-item prop="during">
        <el-date-picker
          type="daterange"
          range-separator="至"
          v-model="during"
          value-format="yyyy-MM-dd"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </im-search-pad-item>
    </im-search-pad>

    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        :tabs="tabList"
        @change="handleChangeTab"
      >
        <template slot="button">
          <!-- <el-button  v-if="checkPermission(['admin','withdraw:export'])">导出</el-button> -->
          <el-button  @click="reload">刷新</el-button>
        </template>
      </tabs-layout>
      <table-pager ref="bussinessTabel" :options="tableTitle" :remote-method="load" :data.sync="tableData" :pageSize="pageSize" :isNeedButton="isWait"  @selection-change="handleSelectionChange">
        <template slot="amount">
          <el-table-column label="金额（元）" width="100">
            <slot slot-scope="{row}">
              {{row.amount|getDecimals}}
            </slot>
          </el-table-column>
        </template>
        <template slot="payCollectId">
          <el-table-column label="收款单号" width="180">
            <slot slot-scope="{row}">
              <span class="text-primary">{{row.payCollectId}}</span>
            </slot>
          </el-table-column>
        </template>
        <template slot="reviewUserName" v-if="model.businessStatus==='CANAEL'">
          <el-table-column label="取消人" width="160" prop="reviewUserName" />
        </template>
        <template slot="reviewTime" v-if="model.businessStatus==='CANAEL'">
          <el-table-column label="取消时间" width="160" prop="reviewTime" />
        </template>
        <template slot="remarks" v-if="model.businessStatus==='CANAEL'">
          <el-table-column label="取消备注" width="160" prop="remarks" />
        </template>
        <div slot-scope="props">
          <el-row class="table-edit-row">
            <span v-if="checkPermission(['admin','admin-finance-platformManagementFee:cancel'])" class="table-edit-row-item">
              <el-button type="text" @click="handleVerify(props.row.id)">取消</el-button>
            </span>
          </el-row>
        </div>
      </table-pager>
    </div>
    <el-dialog title="取消缴纳平台使用费原因" :visible.sync="verifyVisible" width="500px">
      <el-form :model="vForm">
        <el-form-item>
          <el-input placeholder="请输入备注信息" v-model="vForm.remarks" type="textarea" rows="4"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="verifyVisible = false">取 消</el-button>
        <el-button type="primary" @click="submit(5)">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  const TableColumns = [
    { label: "业务单号", name: "id",prop: "id",width: "160"},
    { label: "业务类型", name: "name", prop:"name",width: "120" },
    { label: "业务单状态", name: "businessStatus.desc", prop:"businessStatus.desc",width: "100" },
    { label: "申请人", name: "applicantUserName", prop:"applicantUserName" },
    { label: "金额（元）", name: "amount", prop:"amount",slot: true  },
    { label: "制单人", name: "createUserName",prop:'createUserName',width: "100" },
    { label: "制单时间", name: "createTime",prop:'createTime',width: "170" },
    { label: "支付方式", name: "method.desc", prop:"method.desc"  },
    { label: "收款单号", name: "payCollectId", prop:"payCollectId",slot: true },
    { label: "取消人", name: "reviewUserName", prop:"reviewUserName",slot: true  },
    { label: "取消时间", name: "reviewTime", prop:"reviewTime",slot: true  },
    { label: "取消原因", name: "remarks", prop:"remarks",slot: true  },
  ];
  const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({ key: i, ...TableColumns[i] });
  }
  import checkPermission from "../../../utils/permission";
  import { statistics,list,updateCashRemarks } from '@/api/finance/plaform/index'
  import TabsLayout from '@/components/TabsLayout'
  export default {
    components: {
      TabsLayout
    },
    data () {
      return {
        loading: '',
        currentTab: 0,
        tabs: [
          { name: '缴纳中', value: 'PROCESS',count: 0,permission: 'platform-wait:view' },
          { name: '缴纳成功', value: 'FINISH',count: 0,permission: 'platform-finish:view'  },
          { name: '缴纳取消', value: 'CANAEL',count: 0,permission: 'platform-reject:view'  },
        ],
        tableData: [],
        page: 1,
        pageSize: 10,
        totalPage: 0,
        total: 0,
        tableTitle: TableColumnList,
        ids: [],
        during: '',
        model: {
          id: '',
          businessStatus: 'PROCESS',
          applicantUserName: '',
        },
        verifyVisible: false,
        isWait: true,//操作列
        vForm: {
          remarks: '',
          'ids[]': ''
        }
      }
    },
    computed: {
      tabList() {
        return [
          { name: `缴纳中（${this.tabs[0].count}）`, value: 'PROCESS', hide: !checkPermission(['admin', 'admin-finance-platformManagementFee:handlingView']) },
          { name: `缴纳成功（${this.tabs[1].count}）`, value: 'FINISH', hide: !checkPermission(['admin', 'admin-finance-platformManagementFee:successView']) },
          { name: `缴纳取消（${this.tabs[2].count}）`, value: 'CANAEL', hide: !checkPermission(['admin', 'admin-finance-platformManagementFee:cancelView']) }
        ]
      }
    },
    mounted() {
      this.getCount()
    },
    methods: {
      checkPermission,
      async getCount() {
        const query = {
          merchantsId: this.$route.params.merchantsId
        }
        const {data} = await statistics(query)
        this.tabs.forEach(item=>{
          item.count = data[item.value.toLowerCase()]
        })
      },
      handleSelectionChange(val) {
      },
      handleVerify(id) {
        this.vForm['ids[]'] = id
        this.verifyVisible = true
      },
      async submit(status) {
        const param = {
          ...this.vForm,
        }
        const data = await updateCashRemarks(param)
        if(data.code === 0) {
          this.$message.success('取消成功！')
          this.getCount()
          this.verifyVisible = false
          this.handleRefresh({
            page: 1,
            pageSize: 10
          })
        }
      },
      async load(params) {
        const listQuery = {
          model: {
            ...this.model,
            startTime: this.during[0],
            endTime: this.during[1]
          }
        }
        Object.assign(listQuery, params)
        this.loading = true
        const {data} = await list(listQuery)
        this.todoName = data.records[0].merchantName
        this.loading = false
        return { data }
      },
      searchLoad() {
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      reload() {
        this.currentTab = 0
        this.model = {
          ...this.model,
          ...{
            id: '',
            applicantUserName: ''
          }
        },
        this.during = ''
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      handleChangeTab(tab) {
        this.model.businessStatus = tab.value
        if (tab.value !== 'PROCESS') {
          this.isWait = false
        } else {
          this.isWait = true
        }
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      handleRefresh(pageParams) {
        this.$refs.bussinessTabel.doRefresh(pageParams)
      }
    }
  }
</script>
