import requestAxios from '@/utils/requestAxios'

export function list(data) {
    return requestAxios({
        url: '/api/merchant/admin/merchantType/page',
        method: 'post',
        data
    })
}

export function editApi(data) {
    return requestAxios({
        url: '/api/merchant/admin/merchantType',
        method: data.id == 0 ? 'post' : 'put',
        data
    })
}

export function deleteApi(id) {
    return requestAxios({
        url: '/api/merchant/admin/merchantType?ids[]=' + id,
        method: 'delete'
    })
}

export function getLicenseBaseByType(type) {
    return requestAxios({
        url: '/api/merchant/admin/licenseBase/anno/listByLicenseBaseType?type=' + type,
        method: 'GET'
    })
}



