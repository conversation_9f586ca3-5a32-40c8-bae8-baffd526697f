import Cookies from 'js-cookie'

export function getToken() {
  return Cookies.get('LoginUserToken')
}

export function setToken(token) {
  return Cookies.set('LoginUserToken', token)
}

export function removeToken() {
  return Cookies.remove('LoginUserToken')
}
export function getRefreshToken() {
  return Cookies.get('RefreshToken')
}

export function setRefreshToken(refreshToken) {
  return Cookies.set('RefreshToken', refreshToken)
}

export function removeRefreshToken() {
  return Cookies.remove('RefreshToken')
}
export function getExpire() {
  return Cookies.get('expire')
}

export function setExpire(expire) {
  return Cookies.set('expire', expire)
}

export function removeExpire() {
  return Cookies.remove('expire')
}

export function getUser(key) {
  // console.info(Cookies.get("loginUser"))
  if(Cookies.get("loginUser")!= undefined&&Cookies.get("loginUser")!= null){
    var data=JSON.parse(Cookies.get("loginUser"));

    // console.info('tag', data[key])
    return data[key]
  }else{
    return ""
  }
}

export function setUser(loginUser) {
  return Cookies.set("loginUser", loginUser)
}

export function removeUser() {
  return Cookies.remove("loginUser")
}



//给sessionStorage存值
export function setContextData(key, value) {
  if (typeof value == "string") {
    sessionStorage.setItem(key, value);
  } else {
    sessionStorage.setItem(key, JSON.stringify(value));
  }
}
// 从sessionStorage取值
export function getContextData(key) {
  const str = sessionStorage.getItem(key);
  if (typeof str == "string") {
    try {
      return JSON.parse(str);
    } catch (e) {
      return str;
    }
  }
  return "";
}
