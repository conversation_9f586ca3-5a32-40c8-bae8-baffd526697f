<template>
  <div class="paySettingTableDialog">
    <div class="searchBox">
      <el-form
        :inline="true"
        :model="paySettingTableSearch"
        class="form-inline"
      >
        <el-form-item class="item" style="width:230px">
          <el-input
            style="width:230px"
            v-model="paySettingTableSearch.orderNumber"
            placeholder="输入商户订单号/退款订单号"
          ></el-input>
        </el-form-item>
        <el-form-item class="item" style="width:150px">
          <el-select
            v-model="paySettingTableSearch.status"
            placeholder="请选择交易状态"
          >
            <el-option label="全部" value="all"></el-option>
            <el-option label="收入" value="shanghai"></el-option>
            <el-option label="支出" value="beijing"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="item" style="width:320px">
          <el-date-picker
            style="width:320px"
            v-model="paySettingTableSearch.time"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item class="item">
          <el-button type="primary" @click="onSubmit">搜索</el-button>
          <el-button type="text" native-type="reset" class="info"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
    </div>
    <div class="table">
      <div class="flex_start_center">
        <p>
          总交易单数 <span>{{ total }}</span> 单
        </p>
        <p>
          收入总金额 <span>{{ totalPrice }}</span> 元
        </p>
        <p>
          退款总金额 <span>{{ totalRefundPrice }}</span> 元
        </p>
        <p>
          手续费 <span>{{ serviceCharge }}</span> 元
        </p>
      </div>
      <el-table
        v-loading="listLoading"
        :data="list"
        row-key="id"
        border
        fit
        height="450"
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column
          align="center"
          width="65"
          :render-header="renderHeader"
          fixed
        >
          <template slot-scope="{ row }">
            <span>{{ row.id }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-for="(item, index) in tableTitle"
          :key="index"
          min-width="300px"
          :label="item.label"
          v-if="tableVal.indexOf(index) > -1"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            <span>{{ row[item.name] }}</span>
          </template>
        </el-table-column>
      </el-table>
       <pagination :layout="layout" v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>
  </div>
</template>
<script>
import { billList } from "@/api/setting/card/bill";
import Pagination from '@/components/Pagination'
export default {
  data() {
    return {
      showSelectTitle: false,
      tableTitle: [
        {
          key: 0,
          label: "交易时间",
          name: "crDate",
          disabled: false
        },
        {
          key: 1,
          label: "商户订单号",
          name: "orderNumber",
          disabled: false
        },
        {
          key: 2,
          label: "交易类型",
          name: "type",
          disabled: false
        },
        {
          key: 3,
          label: "交易金额",
          name: "price",
          disabled: false
        },
        {
          key: 4,
          label: "商户退款单号",
          name: "refundNumber",
          disabled: false
        },
        {
          key: 5,
          label: "退款金额",
          name: "refundPrice",
          disabled: false
        },
        {
          key: 6,
          label: "退款状态",
          name: "refundStatus",
          disabled: false
        }
      ],
      tableVal: [0, 1, 2, 3, 4, 5, 6],
      list: [],
      total: 0,
      totalPrice: null,
      totalRefundPrice: null,
      serviceCharge: null,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 10
      },
      layout:'total,slot,sizes, prev, pager, next, jumper',
      paySettingTableSearch: {
        orderNumber: "",
        status: "",
        time: ""
      }
    };
  },
  components: { Pagination },
  props: {
    cardId: {
      required: true,
      type: Number,
      default: 0
    }
  },
  methods: {
    renderHeader(h, { column }) {
      return (
        <div style="position:relative">
          <div onClick={this.setHeaer}>
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            append-to-body
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="630px"
            center
          >
            <el-transfer
              vModel={this.tableVal}
              data={this.tableTitle}
            ></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button type="primary" onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      );
    },
    setHeaer: function() {
      this.showSelectTitle = !this.showSelectTitle;
    },
    async getList() {
      this.listLoading = true;
      const { data } = await billList(this.listQuery);
      this.list = data.items;
      this.total = data.total;
      this.totalPrice = data.totalPrice;
      this.serviceCharge = data.serviceCharge;
      this.totalRefundPrice = data.totalRefundPrice;
      this.listLoading = false;
    },
    onSubmit: function() {}
  },
  created() {
    this.getList();
  },
  mounted() {},
  beforeDestroy() {}
};
</script>
<style lang="scss" scoped>
@import "@/styles/element-variables.scss";
.paySettingTableDialog {
  margin: -30px -20px;
  border-top: 1px solid #ebecee;
  padding: 30px 20px;
  .form-inline .info {
    color: $--color-info;
  }
  .form-inline .item {
    margin-bottom: 0;
  }
  .flex_start_center p {
    margin-right: 20px;
  }
  .flex_start_center span {
    color: $--color-danger;
  }
}
</style>
