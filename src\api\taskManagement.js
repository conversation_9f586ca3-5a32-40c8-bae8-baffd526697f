import request from "@/utils/request";
import qs from 'qs'

// 任务列表
export function salesmanTaskList(data) {
  return request({
    url: "/crm/admin/salesmanTask/list",
    method: "post",
    data,
  });
}

// 新增任务
export function salesmanTaskAdd(data) {
  return request({
    url: "/crm/admin/salesmanTask/add",
    method: "post",
    data,
  });
}

// 删除任务
export function salesmanTaskDelete(id) {
  return request({
    url: `/crm/admin/salesmanTask/delete/${id}`,
    method: "post",
  });
}

// 更新任务指标
export function salesmanTask3Update(id) {
  return request({
    url: `/crm/admin/salesmanTask/updateTargetMoney/${id}`,
    method: "post",
  });
}

// 更新任务
export function salesmanTaskUpdate(data) {
  return request({
    url: "/crm/salesmanTask/update",
    method: "post",
    data,
  });
}

// 查询任务详情
export function salesmanTaskDetail(id) {
  return request({
    url: `/crm/admin/salesmanTask/detail/${id}`,
    method: "post",
  });
}

// 修改任务状态
export function salesmanTaskUpdateStatus(data) {
  return request({
    url: `/crm/admin/salesmanTask/updateStatus/${data.taskId}/${data.status}`,
    method: "post",
  });
}

// 查询企业
export function getMerchantList(data) {
  return request({
    url: `/crm/admin/salesmanTask/merchantList`,
    method: "post",
    data,
  });
}

// 拜访任务达成明细
export function taskReportDetail0(data) {
  return request({
    url: `/crm/taskDetail/visitList`,
    method: "post",
    data,
  });
}

// 拜访任务明细导出
export function taskReportExport0(data) {
  return request({
    url: `/crm/taskDetail/visitListExport`,
    method: "post",
    data,
  });
}

// 协访任务达成明细
export function taskReportDetail1(data) {
  return request({
    url: `/crm/taskDetail/coachedList`,
    method: "post",
    data,
  });
}

// 协访任务明细导出
export function taskReportExport1(data) {
  return request({
    url: `/crm/taskDetail/coachedListExport`,
    method: "post",
    data,
  });
}

// 拓客任务达成明细-人员
export function taskReportDetail21(data) {
  return request({
    url: `/crm/admin/SalesmanMerchantApplyReport/pageList`,
    method: "post",
    data,
  });
}

// 拓客任务达成明细-部门
export function taskReportDetail22(data) {
  return request({
    url: `/crm/admin/SalesmanMerchantApplyReport/pageDepartmentList`,
    method: "post",
    data,
  });
}

// 拓客任务达成明细-明细
export function taskReportDetail23(data) {
  return request({
    url: `/crm/admin/SalesmanMerchantApplyReport/pageSaleManList`,
    method: "post",
    data,
  });
}

// 拓客任务明细导出
export function taskReportExport2(data) {
  return request({
    url: `/crm/admin/SalesmanMerchantApplyReport/export`,
    method: "post",
    data,
  });
}

// 业绩任务明细
export function taskReportDetail3(data) {
  return request({
    url: `/order/admin/reportformProductDay/page`,
    method: "post",
    data,
  });
}

// 业绩任务明细导出
export function taskReportExport3(data) {
  return request({
    url: `/order/admin/reportformProductDay/export`,
    method: "post",
    data,
  });
}

// 客户发展数任务达成明细-人员
export function taskReportDetail41(data) {
  return request({
    url: `/crm/admin/salesmanCustomerDevelopReport/pageList`,
    method: "post",
    data,
  });
}

// 客户发展数任务达成明细-部门
export function taskReportDetail42(data) {
  return request({
    url: `/crm/admin/salesmanCustomerDevelopReport/pageDepartmentList`,
    method: "post",
    data,
  });
}

// 客户发展数任务达成明细-明细
export function taskReportDetail43(data) {
  return request({
    url: `/crm/admin/salesmanCustomerDevelopReport/pageSaleManList`,
    method: "post",
    data,
  });
}

// 客户发展数任务明细导出
export function taskReportExport4(data) {
  return request({
    url: `/crm/admin/salesmanCustomerDevelopReport/export`,
    method: "post",
    data,
  });
}




/**
 * @description 参数配置/系统参数 查询 key 值
 */
export function getParameterByKey(data) {
  return request({
    url: '/authority/parameter/getByKey',
    method: 'POST',
    transformRequest: [function () {
      return qs.stringify(data, { arrayFormat: 'brackets' })
    }],
    data,
    headers: { 'Content-type': 'application/x-www-form-urlencoded ' }
  })

}

// 根据商家id获取组织架构id
export function getDepartmentId(id) {
  return request({
    url: `/authority/merchant/orgaStructure/list4Task/${id}`,
    method: 'get',
  });
}

// 获取组织架构
export function getDepartmentList(id) {
  return request({
    url: `/authority/merchant/orgaStructure/orgaTree/${id}`,
    method: 'post',
  });
}