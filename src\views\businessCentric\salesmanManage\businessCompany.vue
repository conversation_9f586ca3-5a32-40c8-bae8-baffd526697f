<template>
  <div class="archivesEditContent">
    <!-- 顶部按钮部分 -->
    <div class="top_title flex_between_center">
      <span>查看商业公司</span>
      <el-button slot="reference" @click="back">返回</el-button>
    </div>
    <!-- 主要信息-->
    <div class="title"><span>商业公司信息</span></div>
    <div class="top_btn">
      <el-button type="primary" @click="handleMerchant">+ 绑定商业公司</el-button>
      <el-button @click="reload">刷新</el-button>
    </div>
    <!-- 分页table -->
    <table-pager ref="pager-table" :options="tableColumns" :remote-method="load" :data.sync="tableData"
      :selection="false" :pageSize="pageSize" :operation-width="150">
      <el-table-column label="服务客户数" width="140" slot="purCount">
        <slot slot-scope="{row}">
          <el-button type="text" @click="openCustomer(row.id)">{{ row.purCount }}</el-button>
        </slot>
      </el-table-column>

      <!--操作栏-->
      <div slot-scope="{row}">
        <el-row class="table-edit-row">
          <span class="table-edit-row-item">
            <el-button type="text" @click="handleUnbind(row.id)">解绑</el-button>
          </span>
        </el-row>
      </div>
    </table-pager>
    <el-dialog title="选择商业公司" append-to-body top="80px" width="1020px" v-if="dialogStatus" :visible.sync="dialogStatus"
      :before-close="closeDialogFun">
      <merchant-list :salesmanId="$route.query.id" @closeDia="closeDialogFun"></merchant-list>
    </el-dialog>
    <el-dialog title="客户列表" append-to-body top="80px" width="1020px" v-if="customerStatus" :visible.sync="customerStatus"
      :before-close="closeCustomer">
      <customer-list :salesmanId="$route.query.id" :saleMerchantId="currentId" @closeCustomer="closeCustomer">
      </customer-list>
    </el-dialog>
  </div>
</template>


<script>
  const TableColumns = [{
      label: "企业编码",
      name: "code",
      prop: "code",
      width: "150"
    },
    {
      label: "企业名称",
      name: "name",
      prop: "name",
      width: "170"
    },
    {
      label: "负责人",
      name: "ceoName",
      prop: 'ceoName',
      width: "150"
    },
    {
      label: "负责人手机",
      name: "ceoMobile",
      prop: 'ceoMobile',
      width: "100"
    },
    {
      label: "服务客户数",
      name: "purCount",
      prop: 'purCount',
      width: "150",
      slot: true
    }
  ];
  const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({
      key: i,
      ...TableColumns[i]
    });
  }
  import merchantList from '@/views/businessCentric/salesmanManage/components/merchantList'
  import customerList from '@/views/businessCentric/salesmanManage/components/customerList'
  import {
    bondedSaleMerchants,
    unbindSaleMerchant
  } from '@/api/salemanCenter/index'
  export default {
    //import引入的组件
    components: {
      merchantList,
      customerList
    },

    data() {
      return {
        tableData: [],
        pageSize: 10,
        tableColumns: TableColumnList,
        dialogStatus: false,
        customerStatus: false,
        currentId: '',
      }
    },
    //生命周期 - 挂载完成（可以访问DOM元素）
    mounted() {},

    computed: {},

    created() {},

    filters: {},

    //方法集合
    methods: {
      async load(params) {
        let listQuery = {
          model: {
            salesmanId: this.$route.query.id
          }
        };
        Object.assign(listQuery, params);
        return await bondedSaleMerchants(listQuery);
      },
      //   刷新
      reload() {
        this.handleRefresh({
          page: 1,
          pageSize: this.pageSize
        })
      },
      handleRefresh(pageParams) {
        this.$refs['pager-table'].doRefresh(pageParams)
      },
      handleUnbind(id) {
        let param = {
          saleMerchantIds: [id],
          salesmanId: this.$route.query.id
        };
        this.$confirm("您确定解绑这个企业吗?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: 'warning',
        }).then(res => {
          unbindSaleMerchant(param).then(res => {
            if (res.code == 0 && res.msg == 'ok') {
              this.$message.success('解绑企业成功');
              this.reload();
            }
          })
        })
      },
      handleMerchant() {
        this.dialogStatus = true;
      },
      closeDialogFun() {
        this.dialogStatus = false;
        this.reload();
      },
      //   打开客户弹窗
      openCustomer(id) {
        console.log('id----->', id);
        this.currentId = id;
        this.customerStatus = true;
      },
      //   关闭客户弹窗
      closeCustomer() {
        this.customerStatus = false;
        this.reload();
      },
      back() {
        this.$router.go(-1);
      },
    },

  }

</script>


<style lang='scss' scoped>
  .archivesEditContent {
    border-top: 1px solid #ebecee;
    padding: 20px 20px;
    background-color: #fff;

    .title {
      margin-top: 10px;
      padding: 0 0 15px;

      span {
        font-size: 16px;
        padding-left: 10px;
        border-left: 4px solid rgba(64, 158, 255, 1);
      }
    }

    .top_btn {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;
    }
  }

</style>
