<template>
  <div class="list-index">
    <!--搜索Form-->
    <im-search-pad :has-expand="false" :is-expand.sync="isExpand" :model="model" @reset="reload" @search="searchLoad">
      <im-search-pad-item prop="groupName">
        <el-input v-model="model.groupName" @keyup.enter.native="searchLoad" placeholder="请输入分组名称" />
      </im-search-pad-item>
    </im-search-pad>

    <div class="tab_bg">
      <!--Tabs布局-->
      <tabs-layout ref="tabs-layout" :tabs="[{ name: '商品分组' }]">
        <!--tabs右上角相关按钮-->
        <template slot="button">
          <el-button @click="reload">刷新</el-button>
          <el-button v-if="checkPermission(['admin', 'admin-platformProduct-group:add'])" type="primary" @click="showDialogFun">+ 新增商品分组</el-button>
        </template>
      </tabs-layout>

      <!-- table -->
      <table-pager ref="pager-table" :options="tableColumns" :remote-method="load" :data.sync="tableData"
        :pageSize="pageSize" :operation-width="120">

        <template slot="grossMargin">
          <el-table-column label="毛利率区间" width="200">
            <template slot-scope="{row}">
              <span>{{ row.beginGrossProfitMargin }}%&lt;毛利率&le;{{ row.endGrossProfitMargin }}%</span>
            </template>
          </el-table-column>
        </template>

        <template slot="exhibit">
          <el-table-column label="分组展示" width="150">
            <slot slot-scope="{row}">
              <div class="boxStyle disp" :style="{ backgroundColor: row.groupColour }">{{ row.groupName }}</div>
            </slot>
          </el-table-column>
        </template>

        <template slot="showStatus">
          <el-table-column label="前端是否显示" width="200">
            <slot slot-scope="{row}">
              <el-radio-group v-model="row.showStatus" @change="showStatusChange(row)">
                <el-radio :label="1">显示</el-radio>
                <el-radio :label="2">隐藏</el-radio>
              </el-radio-group>
            </slot>
          </el-table-column>
        </template>
        <template slot="bindingNum">
          <el-table-column label="关联商品" width="140">
            <slot slot-scope="{row}">
              <el-button v-if="checkPermission(['admin', 'admin-platformProduct-group:relation'])" type="text" @click="handlEassociation(row.id)">{{ row.bindingNum }}</el-button>
              <span v-else>{{ row.bindingNum }}</span>
            </slot>
          </el-table-column>
        </template>
        <!--操作栏-->
        <div slot-scope="{ row }">
          <el-row class="table-edit-row">
            <span class="table-edit-row-item">
              <el-button v-if="checkPermission(['admin', 'admin-platformProduct-group:edit'])" type="text" @click="handleEdit(row.id)">编辑</el-button>
              <del-el-button v-if="checkPermission(['admin', 'admin-platformProduct-group:del'])" style="margin-left:5px" :targetId="row.id" :text="delText" @handleDel="handleDel">
              </del-el-button>
            </span>
          </el-row>
        </div>
      </table-pager>
    </div>
    <el-dialog :title="diaText" :visible.sync="dialogVisible" width="600px">
      <el-form ref="productFrom" :rules="productFromRules" :model="productFrom" label-width="130px">
        <el-form-item label="分组名称：" prop="groupName">
          <el-input v-model="productFrom.groupName" :maxlength="5" placeholder="请输入分组名称,限五个字">
          </el-input>
        </el-form-item>
        <el-form-item label="毛利率区间：" required>
          <el-col :span="9">
            <el-form-item prop="beginGrossProfitMargin">
              <el-input v-model="productFrom.beginGrossProfitMargin"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5" style="text-align: center;">
            <span>%&lt;毛利率&le;</span>
          </el-col>
          <el-col :span="9">
            <el-form-item prop="endGrossProfitMargin">
              <el-input v-model="productFrom.endGrossProfitMargin"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="1" style="text-align: center;">
            <span>%</span>
          </el-col>

        </el-form-item>
        <el-form-item label="分组颜色：" prop="groupColour">
          <div style="display: flex; align-items: center">
            <el-color-picker v-model="productFrom.groupColour"></el-color-picker>
            <div class="boxStyle" v-if="
              productFrom.groupName.length > 0 &&
              productFrom.groupColour != '#FFFFFF'
            " :style="{ backgroundColor: productFrom.groupColour }">
              {{ productFrom.groupName }}
            </div>
          </div>
        </el-form-item>
        <el-form-item class="formItem" prop="sort" label="分类排序:">
          <el-input-number clearable :min="1" v-model="productFrom.sort" placeholder="请填写分类排序"></el-input-number>
        </el-form-item>
        <el-form-item class="formItem" prop="showStatus" label="是否前端显示:">
          <el-radio v-model="productFrom.showStatus" :label="1">显示</el-radio>
          <el-radio v-model="productFrom.showStatus" :label="2">隐藏</el-radio>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog :title="dialogTitle" append-to-body v-if="productStatus" width="70%" :visible.sync="productStatus"
      :before-close="closeProduct">
      <product-list :title.sync="dialogTitle" :currentId="currentId" @closeProduct="closeProduct">
      </product-list>
    </el-dialog>
  </div>
</template>


<script>
const TableColumns = [{
  label: "分组编码",
  name: "groupCode",
  prop: "groupCode",
  width: "120",
},
{
  label: "分组名称",
  name: "groupName",
  prop: "groupName",
  width: "150",
},
{
  label: "毛利率区间",
  name: "grossMargin",
  prop: "grossMargin",
  width: "150",
  slot: true
},
{
  label: "分组颜色",
  name: "groupColour",
  prop: "groupColour",
  width: "150",
},
{
  label: "分组展示",
  name: "exhibit",
  prop: "exhibit",
  width: "150",
  slot: true
},
{
  label: "排序",
  name: "sort",
  prop: "sort",
  width: "100",
},
{
  label: "前端是否展示",
  name: "showStatus",
  prop: "showStatus",
  width: "200",
  slot: true
},
{
  label: "关联商品",
  name: "bindingNum",
  prop: "bindingNum",
  width: "140",
  slot: true
},
];
const TableColumnList = [];
for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({
    key: i,
    ...TableColumns[i],
  });
}

import {
  productGroupPage,
  delProductGroup,
  detailGroup,
  editProductGroup,
  addProductGroup
} from '@/api/products/productGroup' // TODO 替换成对应用的列表api
import delElButton from "@/components/eyaolink/delElButton";
import productList from "@/views/products/productGroup/components/productList.vue"
import checkPermission from '@/utils/permission';
export default {
  //import引入的组件
  components: {
    delElButton,
    productList
  },

  data() {
    const beginGrossProfitMarginValidator = (rule, value, callback) => {
      if (!/^-?(0|([1-9]\d*))(\.\d{1,2})?$/.test(value)) {
        callback(new Error('不是合法数值'))
      } else if (this.productFrom.endGrossProfitMargin && Number(this.productFrom.endGrossProfitMargin) < Number(value)) {
        callback(new Error('不可大于最大毛利率'))
      } else {
        callback()
      }
    }
    const endGrossProfitMarginValidator = (rule, value, callback) => {
      this.$refs['productFrom'] && this.$refs['productFrom'].validateField('beginGrossProfitMargin')
      if (!/^-?(0|([1-9]\d*))(\.\d{1,2})?$/.test(value)) {
        callback(new Error('不是合法数值'))
      } else {
        callback()
      }
    }
    return {
      dialogTitle: '已关联商品',
      isExpand: false,
      model: {
        groupName: "",
      },
      diaText: '新增商品分组',
      productFromRules: {
        groupName: [
          { required: true, trigger: 'blur', message: '请选择分组名称' },
        ],
        beginGrossProfitMargin: [
          { required: true, validator: beginGrossProfitMarginValidator, trigger: 'blur' }
        ],
        endGrossProfitMargin: [
          { required: true, validator: endGrossProfitMarginValidator, trigger: 'blur' }
        ],
        groupColour: [
          { required: true, trigger: 'change', message: '请选择分组颜色' },
        ],
        showStatus: [
          { required: true, message: '请选择是否前端显示', trigger: 'change' },
        ]
      },
      productFrom: {
        beginGrossProfitMargin: '',
        endGrossProfitMargin: '',
        groupName: "",
        groupColour: "#00CB25",
        sort: 1,
        showStatus: 1,
      },
      currentId: '',
      delText: '您确定删除该商品分组吗？',
      dialogVisible: false,
      productStatus: false,
      tableData: [],
      pageSize: 10,
      tableColumns: TableColumnList,
    };
  },
  watch: {
    dialogVisible(val) {
      if (!val) {
        this.productFrom = {
          groupName: "",
          groupColour: "#00CB25",
          sort: 1,
          showStatus: 1,
        };
      }
    }
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() { },

  computed: {},

  created() { },

  filters: {},

  //方法集合
  methods: {
    checkPermission,
    async load(params) {
      let listQuery = {
        model: this.model,
      };
      Object.assign(listQuery, params);
      return await productGroupPage(listQuery);
    },
    // 表格中前端是否展示的radio的改变事件
    showStatusChange(val) {
      let params = {
        groupName: val.groupName,
        groupColour: val.groupColour,
        beginGrossProfitMargin: val.beginGrossProfitMargin,
        endGrossProfitMargin: val.endGrossProfitMargin,
        sort: val.sort,
        showStatus: val.showStatus,
        id: val.id
      };
      editProductGroup(params).then(res => {
        if (res.code == 0 && res.msg == 'ok') {
          this.$message.success('修改显示状态成功');
          this.reload();
        }
      })
    },
    //    编辑
    handleEdit(id) {
      detailGroup(id).then(res => {
        if (res.code == 0 && res.msg == 'ok') {
          this.dialogVisible = true;
          this.$refs["productFrom"] && this.$refs["productFrom"].resetFields();
          this.diaText = "编辑商品分组";
          this.productFrom = {
            groupName: res.data.groupName,
            id: res.data.id,
            beginGrossProfitMargin: res.data.beginGrossProfitMargin,
            endGrossProfitMargin: res.data.endGrossProfitMargin,
            showStatus: res.data.showStatus,
            groupColour: res.data.groupColour,
            sort: res.data.sort,
          }
        }
      })
    },
    // 删除
    handleDel(id) {
      delProductGroup(id).then(res => {
        if (res.code == 0 && res.msg == "ok") {
          this.$message.success('删除成功');
          this.reload();
        }
      })
    },
    // 新增商品分组
    showDialogFun() {

      this.diaText = "新增商品分组";
      this.dialogVisible = true;
      this.$refs["productFrom"] && this.$refs["productFrom"].resetFields();
      this.productFrom = {
        beginGrossProfitMargin: '',
        endGrossProfitMargin: '',
        groupName: "",
        groupColour: "#00CB25",
        sort: 1,
        showStatus: 1,
      }
    },
    reload() {
      this.$refs['tabs-layout'].reset()
      this.handleRefresh({
        page: 1,
        pageSize: this.pageSize
      })
    },
    searchLoad() {
      this.handleRefresh({
        page: 1,
        pageSize: this.pageSize
      })
    },
    handleRefresh(pageParams) {
      this.$refs['pager-table'].doRefresh(pageParams)
    },
    //   关联商品弹窗
    handlEassociation(id) {
      console.log('id', id);
      this.currentId = id;
      this.productStatus = true;
    },
    closeProduct() {
      this.productStatus = false;
      this.reload();
    },
    submit() {
      this.$refs["productFrom"].validate((valid) => {
        console.log('productFrom', valid)
        if (valid) {
          // 验证成功
          let params = {
            ...this.productFrom
          }
          if (params.id && this.diaText == "编辑商品分组") {
            // 编辑
            editProductGroup(params).then(res => {
              if (res.code == 0 && res.msg == 'ok') {
                this.$message.success('修改商品分组成功');
                this.dialogVisible = false;
                this.reload();
              }
            })
          } else {
            // 新增
            addProductGroup(params).then(res => {
              if (res.code == 0 && res.msg == 'ok') {
                this.$message.success("新增商品分组成功");
                this.dialogVisible = false;
                this.reload();
              }
            })
          }
        } else {
          // 验证失败
        }
      });
    },
  },
};

</script>


<style lang='scss' scoped>
.boxStyle {
  margin-left: 10px;
  padding: 0 10px;
  border-radius: 5px;
  color: #fff;
}

.disp {
  display: inline;
  padding: 5px 10px;
}
</style>
