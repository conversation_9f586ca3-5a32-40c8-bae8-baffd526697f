<template>
<div class="dowloadButtonContent">
    <el-button :type="buttonType" :size="size" @click="showImgsDialogFun">下载</el-button>
    <el-dialog
        append-to-body
        title="请点击需要下载的内容"
        :visible.sync="showImgsDialog"
        :before-close="closeDialogFun"
        width="800px">
        <div class="imgItems flex_start_start">
            <div v-for="(item, index) in imgItems" :key="index"  :class="item.click?'item selected':'item'" >
                <!-- <el-image @click="selectItem(index)"
                    style="width: 100%; height: 100%"
                    :src="item.url"
                    :fit="fit"></el-image> -->
                     <el-image
                    style="width: 100%; height: 80%"
                    :src="item.url"
                    :fit="fit"></el-image>
                <a style="height: 100%;display: block;width: 100%;position: absolute;top: 0;" :href="item.url"></a>
                <!-- <i class="el-icon-check"></i> -->
            </div>
        </div>
        <!-- <div slot="footer">
            <el-button @click="closeDialogFun">取 消</el-button>
            <el-button type="primary" @click="downImg">确 定</el-button>
        </div> -->
    </el-dialog>
</div>
</template>
<script>

import JSZip from 'jszip'
import FileSaver from "file-saver"

export default {
data() {
return {
    fit:"contain",
    imgItems:[],
    showImgsDialog:false,
};
},
props: {
    size: {
        type: String,
        default: 'normal'
    },
    buttonType:{
        default:"primary",
        type:String
    },
    imgList:{
        default:() => {
            return []
        },
        type:Array
    }
},
methods: {
    showImgsDialogFun(){
        this.showImgsDialog=true;
        this.imgItems = []
        this.imgList.forEach(element => {
            this.imgItems.push({
                click:false,
                name:new Date().getTime().toString(),
                url:element.fileIds
            })
        });
    },
    getSelectImgs(){
         var items = this.imgItems.filter((item) => ( item.click ==true));
         return items;
    },
    downImg() {
        var blogTitle = 'dowloadImg';
        var zip = new JSZip();
        var imgs = zip.folder(blogTitle);
        var baseList = [];
        var selectImgs=this.getSelectImgs();
        // var selectImgs = [{url: 'https://ss1.bdstatic.com/70cFuXSh_Q1YnxGkpoWK1HF6hhy/it/u=1089874897,1268118658&fm=26&gp=0.jpg'}]
        // var selectImgs = [{url: 'https://eyaolink-dev-bucket.oss-cn-shenzhen.aliyuncs.com/0/2021/01/7386bcad-e5af-401b-a1ff-821d58377a40.png'}]
        for (var i = 0; i < selectImgs.length; i++) {
            let image = new Image();
            // 解决跨域 Canvas 污染问题
            image.setAttribute('crossOrigin', '*');
            image.src = selectImgs[i].url;
          
            image.onload = function () {
                let canvas = document.createElement('canvas');
                canvas.width = image.width;
                canvas.height = image.height;
                
                let context = canvas.getContext('2d');
                context.drawImage(image, 0, 0, image.width, image.height);
                
                let url = canvas.toDataURL(); // 得到图片的base64编码数据
                canvas.toDataURL('image/png');
                
                baseList.push(url.substring(22));    // 去掉base64编码前的 data:image/png;base64,

                if (baseList.length === selectImgs.length && baseList.length > 0) {
                    for (let k = 0; k < baseList.length; k++) {
                        imgs.file(selectImgs[k].name + '.png',baseList[k], {base64: true})
                        console.info( selectImgs[k])
                    }
                    zip.generateAsync({type: 'blob'}).then(function (content) {
                        FileSaver.saveAs(content, blogTitle + '.zip');
                    });
                }
 
            };
           
        }
    },
    closeDialogFun:function(){
      this.showImgsDialog=false,
       this.imgItems.forEach((element)=>{
           element.click=false
       })
    },
    selectItem:function(index){ 
       this.imgItems[index].click=!this.imgItems[index].click;
    }

},
mounted() {
    
    
},
beforeDestroy() {}
};
</script>
<style lang="less" scoped>
    .dowloadButtonContent{display:inline; margin:0 8px}
    .imgItems{
        margin:-30px 0;
        max-height:350px;
        width:100%;
        overflow:auto;
    }
    .imgItems .item{
        border:1px solid #eeeeee;
        cursor: pointer;
        width:150px; 
        height:150px; 
        margin:10px;
        position: relative;
    }
    .imgItems .item .el-icon-check{display:none; position: absolute; right:0;top:0;}
    .imgItems .item.on .el-icon-check{display:block;}


    
    .selected{
        border-color: #5FB878;
    }
 
    .selected:after {
        content: "";
        position: absolute;
        top: 0;
        right: 0;
        border-top: 40px solid #5FB878;
        border-left: 40px solid transparent;
    }
 
    .selected:before {
        content: '';
        position: absolute;
        width: 14px;
        height: 8px;
        background: transparent;
        top: 5px;
        right: 5px;
        border: 2px solid white;
        border-top: none;
        border-right: none;
        transform: rotate(-45deg);
        z-index: 9;
    }


</style>