<template>
  <div class="salesManList">
    <div>
      <el-form class="form-inline" :inline="true">
        <el-form-item>
          <el-input
            v-model="listQuery.model.salesmanCode"
            placeholder="请输入业务员编码"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-input
            v-model="listQuery.model.realName"
            placeholder="请输入业务员姓名"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-cascader
            placeholder="请选择所在区域"
            v-model="addrArr"
            :options="areaTree"
            :props="{
              checkStrictly: true,
              expandTrigger: 'hover',
              value: 'id',
              label: 'label',
              children: 'children',
            }"
            @change="parentChangeAction"
            clearable
            style="width: 240px"
          ></el-cascader>
        </el-form-item>
        <el-button @click="getlist" type="primary">搜索</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form>
    </div>
    <el-table
      v-loading="isLoading"
      border
      fit
      :data="list"
      style="width: 100%"
      max-height="450px"
      @selection-change="selectTableItemFun"
    >
      <el-table-column
        type="selection"
        width="55"
        align="center"
        fixed="left"
      ></el-table-column>
      <el-table-column
        align="center"
        width="80"
        fixed="left"
        :render-header="renderHeader"
      >
        <template slot-scope="scope">
          <span>{{ scope.$index + 1 }} </span>
        </template>
      </el-table-column>

      <el-table-column
        v-for="(item, index) in tableTitle"
        :key="index"
        :min-width="item.width ? item.width : '350px'"
        :fixed="
          item.name == 'publishStatus' || item.name == 'pictIdS'
            ? 'left'
            : false
        "
        :label="item.label"
        show-overflow-tooltip
        :align="item.name == 'pictIdS' ? 'center' : 'left'"
      >
        <template slot-scope="{ row }">
          <!-- <el-popover v-if="item.name=='pictIdS'" placement="right" trigger="hover">
            <img src="https://ss1.bdstatic.com/70cFuXSh_Q1YnxGkpoWK1HF6hhy/it/u=1089874897,1268118658&fm=26&gp=0.jpg" alt="" width="300" height="300">
            <img slot="reference" src="https://ss1.bdstatic.com/70cFuXSh_Q1YnxGkpoWK1HF6hhy/it/u=1089874897,1268118658&fm=26&gp=0.jpg" alt="" width="40" height="40">
          </el-popover> -->
          <span>{{ row[item.name] }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column
        fixed="right"
        align="center"
        label="操作"
        width="140"
        class="itemAction"
      >
        <template slot-scope="{ row }">
          <el-button @click="backOutFun(row)" type="text"
            >申请撤销代理资格</el-button
          >
        </template>
      </el-table-column> -->
    </el-table>
    <div class="flex_between_center">
      <div style="padding: 32px 16px">
        <!-- 当页已选择
        <span style="display: inline-block; padding: 0 10px; color: #00f">{{
          selectList.length
        }}</span>
        <el-button  @click="backOutListFun">批量申请撤销</el-button> -->
      </div>
      <pagination
        v-if="total > 0"
        :pageSizes="[2, 10, 20, 50]"
        :total="total"
        :page.sync="listQuery.current"
        :limit.sync="listQuery.size"
        @pagination="getlist"
        style="margin-top: 0"
      />
    </div>

    <!-- <div class="dialog-footer">
      <el-button @click="cancelFun">取 消</el-button>
      <el-button type="primary" @click="confirmFun">确 定</el-button>
    </div> -->
    <el-dialog
      title="申请撤销代理资格理由"
      :visible.sync="dialogFlag"
      width="400px"
      :append-to-body="true"
      :close-on-click-modal="false"
    >
      <el-input type="textarea" v-model="applyReason" rows="5"></el-input>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelBackOut">取 消</el-button>
        <el-button type="primary" @click="confirmBackOut">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {areas} from "@/api/businessCenter/businessList";
import requestAxios from '@/utils/request';
import { queryByProductId } from "@/api/businessCentric/promotionGoods";
const tableInfo = [
  {
    key: 0,
    name: "salesmanCode",
    label: "业务员编码",
    width: "170px",
    disabled: true,
  },
  {
    key: 1,
    name: "realName",
    label: "业务员姓名",
    width: "170px",
    disabled: true,
  },
  {
    key: 2,
    name: "contactNumber",
    label: "手机号",
    width: "170px",
  },
  {
    key: 3,
    name: "area",
    label: "所在区域",
    width: "200px",
  },
  {
    key: 4,
    name: "promotionExpenses",
    label: "推广费(元)",
    width: "170px",
  },
    {
    key: 5,
    name: "productArea",
    label: "代理区域",
    width: "170px",
  },
];
import Pagination from "@/components/Pagination";
export default {
  data() {
    return {
      isLoading: false,
      total: 11,
      list: [],
      listQuery: {
        current: 1,
        size: 10,
        model: {},
      },
      rows: {},
      addrArr: [],
      tableSelectTitle: [],
      showSelectTitle: false,
      tableTitle: [],
      cityValue: [],
      selectList: [],
      applyReason: "",
      dialogFlag: false,
    };
  },
  methods: {
    // async getareas() {
    //   let { data } = await areas();
    //   this.areaTree = data;
    // },
    async backOutListFun(obj) {
      if (this.selectList.length == 0) {
        this.$message.error("请至少选择一个需要批量撤销的业务员");
        return;
      }
      let arr = [];
      this.selectList.forEach((item) => {
        arr.push(item.salesmanProductId);
      });
      let { data } = await requestAxios({
        url: "/agent/agentProduct/salesmanProduct/batchRepeal/" + arr,
        method: "put",
      });
      if (data) {
        this.$message.success("申请撤销代理资格成功！");
        this.cancelBackOut();
      }
    },
    async backOutFun(row) {
      this.dialogFlag = true;
      this.rows = row;
      // let { data } = await requestAxios({
      //   url: "/agentProduct/salesmanProduct/repeal/" + row.id,
      //   method: "put",
      // });
      // console.log(data)
    },
    cancelBackOut() {
      this.applyReason = "";
      this.dialogFlag = false;
      this.rows = {};
    },
    async confirmBackOut() {
      if (this.applyReason.length == 0) {
        this.$message.error("请输入申请撤销代理资格的理由");
        return;
      }
      let { data } = await requestAxios({
        url:
          "/agent/agentProduct/salesmanProduct/repeal/" + this.rows.salesmanProductId,
        method: "put",
        data: this.applyReason,
      });
      if (data) {
        this.$message.success("申请撤销代理资格成功！");
        this.cancelBackOut();
      }
    },
    selectTableItemFun(row) {
      this.selectList = row;
    },
    resetForm() {
      this.list = [];
      this.listQuery = {
        current: 1,
        size: 10,
        model: {},
      };
      this.addrArr = []
      this.getlist();
    },
    parentChangeAction(val) {
      this.listQuery.model = {
        provinceId: val[0],
        cityId: val[1],
        countyId: val[2],
      };
    },
    async getlist() {
      this.list = [];
      this.isLoading = true;
      let {data} = await queryByProductId({
        id: this.row.productId,
        data: this.listQuery
      })
      // let { data } = await requestAxios({
      //   url: "/agent/agentProduct/merchant/queryByProductId/" + this.rows.productId,
      //   method: "post",
      //   data: this.listQuery,
      // });
      this.isLoading = false;
      this.list = data.records;
      this.total = data.total;
      this.$emit("update:salesMalNum", data.total);
    },
    confirmFun() {},
    cancelFun() {
      this.$emit("update:visible", false);
    },
    initTbaleTitle() {
      this.tableTitle = tableInfo;
      this.tableSelectTitle = [];
    },
    renderHeader(h, { column }) {
      var titles = tableInfo;
      var titlesName = ["显示字段项", "隐藏字段项"];
      return (
        <div style="position:relative">
          <div onClick={this.showHeaer}>
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="640px"
            center
            append-to-body={true}
          >
            <el-transfer
              vModel={this.tableSelectTitle}
              data={titles}
              titles={titlesName}
              onChange={this.setleftTitleFun}
            ></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button onClick={this.closeHeaer}>取消</el-button>
              <el-button type="primary" onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      );
    },
    setleftTitleFun(val) {
      this.tableSelectTitle = val;
    },
    showHeaer: function () {
      this.showSelectTitle = true;
    },
    closeHeaer: function () {
      this.showSelectTitle = false;
      this.tableSelectTitle = [];
    },
    setHeaer: function () {
      var titles = tableInfo;
      var listinfo = titles.filter((element, index, self) => {
        return !this.tableSelectTitle.includes(element.key);
      });
      this.tableTitle = listinfo;
      this.showSelectTitle = !this.showSelectTitle;
    },
  },
  created() {
    this.initTbaleTitle();
    this.getlist();
    // this.getareas()
  },
  components: {
    Pagination,
  },
  props: {
    row: {
      type: Object,
    },
    visible: {
      type: Boolean,
      default: false,
      required: true,
    },
    salesMalNum: {
      type: Number,
      default: 0,
    },
    areaTree: {
      type: Array,
      required: true,
      default: []
    },
  },
};
</script>

<style lang="less" scoped>
.salesManList {
  margin: -30px -20px;
  border-top: 1px solid #ebecee;
  padding: 30px 20px;
}
/deep/.el-dialog__footer {
  border-top: 1px solid #ccc;
}
</style>
