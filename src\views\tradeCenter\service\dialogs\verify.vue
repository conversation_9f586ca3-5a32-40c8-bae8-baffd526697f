<template>
  <!--审核-->
  <el-dialog
    title="审核订单"
    :visible.sync="visible"
    :close-on-click-modal="false"
    @close="resetForm"
    width="30%"
  >
    <el-dialog
      width="30%"
      title="拒绝理由"
      :visible.sync="innerVisible"
      :close-on-click-modal="false"
      append-to-body>
      <el-input type="textarea" :rows="4" placeholder="请输入拒绝理由" v-model="reason"></el-input>
      <span slot="footer" class="dialog-footer">
        <el-button @click="innerVisible = false">取 消</el-button>
        <el-button type="primary" @click="remarkSubmit">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      width="30%"
      title="同意申请"
      :visible.sync="agreeVisible"
      :close-on-click-modal="false"
      append-to-body>
      <el-form :model="agreeForm" :rules="rules" ref="agreeForm" label-width="100px" class="demo-ruleForm">
        <el-form-item label="审核意见：" prop="agreeReason">
          <el-input type="textarea" :rows="2" placeholder="请输入审核意见" v-model="agreeForm.agreeReason"></el-input>
        </el-form-item>

        <!--<el-form-item label="运费：" prop="freight" v-if="isRefund">
          <el-radio-group v-model="agreeForm.isFreight">
            <el-radio label="收取运费"></el-radio>
            <el-radio label="不收运费"></el-radio>
          </el-radio-group>
          <el-input placeholder="请输入运费金额" v-model="agreeForm.freight" v-if="agreeForm.isFreight==='收取运费'"></el-input>
        </el-form-item>-->
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="agreeVisible = false">取 消</el-button>
        <el-button type="primary" @click="agreeSubmit('agreeForm')">确 定</el-button>
      </span>
    </el-dialog>
    <span>{{title}}</span>
    <span slot="footer" class="dialog-footer">
    <el-button @click="resetForm">取 消</el-button>
    <el-button @click="innerVisible = true">拒 绝</el-button>
    <el-button type="primary" @click="agreeVisible = true">同 意</el-button>
  </span>
  </el-dialog>
</template>

<script>
export default {
  name: "verify",
  props: ['title','verifyVisible','isRefund'],
  data() {
    const freightValid = (rule, value, callback) => {
      if (this.agreeForm.isFreight === '收取运费') {
        if(value === '' || value < 0) {
          return callback(new Error('请输入运费金额'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    return {
      visible: false,
      innerVisible: false,
      agreeVisible: false,
      reason: '',
      rules: {
        agreeReason: [{required: true, message: '请输入审核意见', trigger: 'blur' }],
        freight: [{ validator: freightValid, trigger: 'blur'}]
      },
      agreeForm: {
        agreeReason: '',
        freight: '',
        isFreight: '不收取运费'
      },
    }
  },
  methods: {
    remarkSubmit() {
      this.$emit('verifyRefuse',this.reason)
      this.innerVisible = false
    },
    agreeSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.agreeForm.isFreight !== '收取运费') {
            this.agreeForm.freight = 0
          }
          this.$emit('verifyAgree', this.agreeForm.agreeReason,this.agreeForm.freight)
          this.agreeVisible = false
        } else {
          return false
        }
      })
    },
    resetForm() {
      this.agreeForm = {
        agreeReason: '',
          freight: '',
          isFreight: '收取运费'
      }
      this.reason = ''
      this.visible = false
      this.$emit('changeShow','false')
    },
  },
  watch: {
    verifyVisible() {
      this.visible = this.verifyVisible
    }
  }
}
</script>

<style scoped>

</style>
