<template>
  <div class="detail-wrapper" v-loading="loading">
    <page-title title="订单详情" />
    <div class="order-detail-items">
      <ul class="orders"><li>订单编号：{{detail.orderNo}}</li><li>下单时间：{{detail.orderCreateTime}}</li><li>下单渠道：<span v-text="detail.orderChannel === 'wechat' ? '小程序':detail.orderChannel"></span></li></ul>
      <div class="clearfix"></div>
      <el-steps :active="active" align-center v-if="(detail.orderAuditStatus && detail.orderAuditStatus.code !== 'ACCEPTED'&&detail.orderAuditStatus.code === 'REJECTED')||(detail.orderStatus && detail.orderStatus.code !== 'CANCEL')">
        <el-step title="待审核" :description="detail.pendingTime"></el-step>
        <el-step title="待发货" :description="detail.waitPayTime"></el-step>
        <el-step title="发货中" :description="detail.waitDeliveryTime"></el-step>
        <el-step title="已发货" :description="detail.partDeliveryTime"></el-step>
        <el-step title="已完成" :description="detail.successTime"></el-step>
      </el-steps>
      <div class="order-status" v-if="detail.orderStatus">
        <h3>
          <img src="@/assets/imgs/order_icon.png">
          <span v-if="detail.orderStatus && detail.orderStatus.code !== 'WAIT_PROCESS'" class="text-primary">{{detail.orderStatus.desc}}</span>
          <span v-if="detail.orderAuditStatus && detail.orderAuditStatus.code !== 'PENDING'&& detail.orderStatus && detail.orderStatus.code === 'WAIT_PROCESS'" class="text-primary">{{detail.payStatus.desc}}</span>
          <span v-if="detail.orderStatus && detail.orderStatus.code === 'WAIT_PROCESS'&&detail.orderAuditStatus && detail.orderAuditStatus.code === 'PENDING'" class="text-primary">{{detail.orderAuditStatus.desc}}</span>
          <span v-if="detail.orderStatus && detail.orderStatus.code === 'WAIT_PROCESS'&&detail.orderAuditStatus && detail.orderAuditStatus.code === 'PENDING'">（{{detail.payStatus.desc}}）</span>
        </h3>
        <span>{{illustration}}</span>
        <div class="fr" style="margin-top: -28px;">
        </div>
      </div>
      <page-module-card title="客户信息">
        <ul class="client-info" v-if="detail.purMerchant">
          <li>客户名称：{{detail.purMerchant.name}}
            <span :class="detail.purMerchant.firstCampStatusEnum.code === 'NOT_ESTABLISH' ? 'text-warning' : 'text-green'" style="margin-left: 5px;">{{detail.purMerchant.firstCampStatusEnum.desc}}首营</span>
          </li>
          <li>收货人：{{detail.orderAddressInfo.receiverName}}</li>
          <!-- <li>客户资质：<el-link class="link-dialog" @click="handleSchedule('LicenseDialogSchedule')">查看客户资质</el-link></li> -->
          <li>客户资质：<el-link class="link-dialog" @click="showBigPic">查看客户资质</el-link></li>
          <el-image :ref="`refImage`" style="width:0;height:0" :src="previewImages[0]" :preview-src-list="previewImages"></el-image>
          <li>客户编码：{{detail.purMerchant.code}}</li>
          <li>收货地区：{{detail.orderAddressInfo.fullAddress}}</li>
          <li>发票信息：
            <el-popover
              placement="top"
              width="400"
              title="发票信息 "
              trigger="click">
              <ul class="bill-list" v-if="detail.invoiceInfo">
                <li>发票类型：{{detail.invoiceInfo.invoiceInfoType.desc}}</li>
                <li>发票抬头：{{detail.invoiceInfo.invoiceTitle}}</li>
                <li>税号：{{detail.invoiceInfo.taxNumber}}</li>
                <li>单位地址：{{detail.invoiceInfo.registerAddress}}</li>
                <li>注册电话：{{detail.invoiceInfo.registerMobile}}</li>
                <li>开户银行：{{detail.invoiceInfo.depositBank}}</li>
                <li>银行账号：{{detail.invoiceInfo.bankNumber}}</li>
              </ul>
              <el-link slot="reference" class="link-dialog">查看发票信息</el-link>
            </el-popover>
          </li>
          <li>下单人：{{detail.orderUserName}}</li>
          <li>收货人手机：{{detail.orderAddressInfo.receiverPhone}}</li>
          <li>订单备注：<span class="text-warning" style="display: inline-block;vertical-align: bottom;width: 60%; white-space: nowrap;text-overflow: ellipsis;overflow: hidden;word-break: break-all;">{{detail.remark}}</span>
          </li>
          <li>下单人手机：{{detail.orderUserPhone}}</li>
          <li>详细地址：{{detail.orderAddressInfo.address}}</li>
        </ul>
      </page-module-card>
      <page-module-card title="收款信息">
        <ul class="client-info" v-if="detail.financeCollectVo">
          <li>收款单号：{{ detail.financeCollectVo.financeCollectNo }}</li>
          <li>收款状态：{{ (detail.financeCollectVo.paymentStatus && detail.financeCollectVo.paymentStatus.code)?detail.financeCollectVo.paymentStatus.desc:'' }}</li>
          <li>收款金额：{{ detail.financeCollectVo.paymentAmount }} (元)</li>
          <li>支付方式：{{ (detail.financeCollectVo.method && detail.financeCollectVo.method.code) ? detail.financeCollectVo.method.desc :'' }}</li>
          <li>收款日期：{{ detail.financeCollectVo.paymentTime }}</li>
          <li>汇款凭证：<el-button v-if="detail.financeCollectVo.certificatePath != null && detail.financeCollectVo.certificatePath != 'null'" type="text" @click="handleBigImage">预览图片</el-button></li>
        </ul>
        <el-image :ref="`refSalesDetail`" style="width:0;height:0" :src="previewDetail[0]" :preview-src-list="previewDetail"></el-image>
      </page-module-card>
      <page-module-card title="退款信息" v-if="detail.salesRefundInfoList && detail.salesRefundInfoList.length>0">
        <el-table :data="detail.salesRefundInfoList" style="width: 100%">
          <el-table-column type="index" label="序号" width="50" />
          <el-table-column prop="salesRefundNo" label="退款单号" width="180" />
          <el-table-column prop="createTime" label="申请时间" width="180" />
          <el-table-column prop="date" label="退款类型" width="180">
            <template slot-scope="scope">
              {{ (scope.row.refundType && scope.refundType.code) ? scope.row.refundType.desc : '' }}
            </template>
          </el-table-column>
          <el-table-column prop="date" label="退款状态" width="180">
            <template slot-scope="scope">
              {{ (scope.row.afterRefundStatus && scope.row.afterRefundStatus.code) ? scope.row.afterRefundStatus.desc : '' }}
            </template>
          </el-table-column>
          <el-table-column prop="totalMoney" label="退款金额" width="120" />
        </el-table>
      </page-module-card>
      <detail-item itemName="商品信息" style="border-bottom: 0;">
        <div class="fr" style="margin-bottom: 10px;">
          <span>商品总价：￥{{detail.productTotalMoney}}</span>
          <el-divider direction="vertical"></el-divider>
          <span>运费：￥{{detail.freight}}</span>
          <el-divider direction="vertical"></el-divider>
          <span>
          <el-popover
            placement="top"
            trigger="hover">
          <el-table border :data="detail.promotion">
            <el-table-column  property="promotionType.desc" label="优惠类型"></el-table-column>
            <el-table-column  property="promotionName" label="优惠名称" width="150"></el-table-column>
            <el-table-column  property="promotionDiscountMoney" label="金额"></el-table-column>
          </el-table>
            <i class="el-icon-info" slot="reference"></i>
          </el-popover>
          优惠：{{detail.discount}}</span>
          <el-divider direction="vertical"></el-divider>
          <span>总金额：<span class="text-warning">￥{{detail.totalMoney}}</span></span>
        </div>
        <el-table border :data="productAllList">
          <el-table-column type="index"></el-table-column>
          <el-table-column prop="productImg" width="80" label="商品主图">
            <template slot-scope="scope">
              <img :src="scope.row.productImg | imgFilter" class="productImg">
              <!-- <img :src="productImg" class="productImg" v-if="scope.row.productImg === ''"> -->
            </template>
          </el-table-column>
          <el-table-column prop="productCode" label="商品编码" width="170"></el-table-column>
          <el-table-column prop="productName" label="商品名称"></el-table-column>
          <el-table-column prop="spec" label="规格" width="150" show-overflow-tooltip></el-table-column>
          <el-table-column prop="saleMerchantName" label="生产厂家"></el-table-column>
          <el-table-column prop="realName" label="业务员"></el-table-column>
          <el-table-column prop="originalUnitMoney" label="原单价" width="80"></el-table-column>
          <el-table-column prop="unitMoney" label="成交单价" width="90"></el-table-column>
          <el-table-column prop="totalNum" label="购买数量" width="80"></el-table-column>
          <el-table-column prop="subtotal" label="原价小计" width="100"/>
          <el-table-column prop="dealSubtotal" label="成交小计" width="100"/>
        </el-table>
      </detail-item>
      <detail-item itemName="发货信息" v-if="detail.orderStatus && (detail.orderStatus.code !== 'CANCEL' && detail.payStatus.code !== 'WAIT_PAY' ) && detail.orderAuditStatus && detail.orderAuditStatus.code !=='PENDING'">
        <!--未发货表格-->
        <el-table v-if="detail.orderStatus && (detail.orderStatus.code==='WAIT_DELIVERY'||detail.orderStatus.code==='PART_DELIVERY')" border :data="orderItemList" class="order-table expand-table" :span-method="arraySpanMethod" :default-expand-all="true">
          <el-table-column type="expand" align="left">
            <template scope="{row}">
              <el-table :data="row.orderItemList" :show-header="false">
                <el-table-column type="index"></el-table-column>
                <el-table-column label="商品主图" prop="productImg" width="80">
                  <template slot-scope="scope">
                    <img :src="scope.row.productImg | imgFilter" class="productImg">
                    <!-- <img :src="productImg" class="productImg" v-if="scope.row.productImg === ''"> -->
                  </template>
                </el-table-column>
                <el-table-column label="商品编码" prop="productCode" width="170"></el-table-column>
                <el-table-column prop="productName" label="商品名称"></el-table-column>
                <el-table-column label="规格" prop="spec" width="150" show-overflow-tooltip></el-table-column>
                <el-table-column label="生产厂家" prop="saleMerchantName"></el-table-column>
                <el-table-column label="单价" prop="originalUnitMoney" width="80"></el-table-column>
                <el-table-column prop="unitMoney" label="成交单价" width="90"></el-table-column>
                <el-table-column label="购买数量" prop="totalNum" width="80">
                  <el-table-column prop="totalNum" width="80">
                    <template slot-scope="scope">
                      {{scope.row.totalNum - scope.row.deliveryTotalNum}}
                    </template>
                  </el-table-column>
                </el-table-column>
                <el-table-column label="原价小计" prop="subtotal" width="100" />
                <el-table-column label="成交小计" prop="dealSubtotal" width="100" />
              </el-table>
            </template>
          </el-table-column>
          <el-table-column label="商品主图" width="80">
            <span class="text-warning">未发货</span>
            <!-- <el-button type="primary" class="fr" size="mini" style="margin-right: 20px;"  @click="handleSchedule('DeliveryDialogSchedule')" v-if="checkPermission(['admin','orderDetail:deliver'])">发货</el-button> -->
          </el-table-column>
          <el-table-column label="商品编码" width="170"></el-table-column>
          <el-table-column label="商品名称"></el-table-column>
          <el-table-column label="规格" width="150" show-overflow-tooltip></el-table-column>
          <el-table-column label="生产厂家"></el-table-column>
          <el-table-column label="原单价" width="80"></el-table-column>
          <el-table-column label="成交单价" width="90"></el-table-column>
          <el-table-column label="发货数量" width="80"></el-table-column>
          <el-table-column label="原价小计" width="100"></el-table-column>
          <el-table-column label="成交小计" width="100"></el-table-column>

        </el-table>

        <!--已发货的表格-->
        <el-table v-if="detail.orderStatus && (detail.orderStatus.code==='PART_DELIVERY'||detail.orderStatus.code==='HAD_DELIVERY'||detail.orderStatus.code==='SUCCESS')" border :data="detail.orderDeliveryInfoVoList" :span-method="spanMethod" :show-header="detail.orderStatus.code!=='PART_DELIVERY'" :default-expand-all="true" class="expand-table">
          <el-table-column type="expand" align="left">
            <template slot-scope="scope">
              <el-table :data="scope.row.orderItemVoList" :show-header="false">
                <el-table-column type="index"></el-table-column>
                <el-table-column prop="productImg" width="80">
                  <template slot-scope="scope">
                    <img :src="scope.row.productImg | imgFilter" class="productImg">
                    <!-- <img :src="productImg" class="productImg" v-if="scope.row.productImg === ''"> -->

                  </template>
                </el-table-column>
                <el-table-column prop="productCode" width="170"></el-table-column>
                <el-table-column prop="productName"></el-table-column>
                <el-table-column prop="spec" width="150" show-overflow-tooltip></el-table-column>
                <el-table-column prop="saleMerchantName"></el-table-column>
                <el-table-column prop="originalUnitMoney" width="80"></el-table-column>
                <el-table-column prop="unitMoney" width="90"></el-table-column>
                <el-table-column prop="totalNum" width="80">
                  <template slot-scope="scope">
                    {{scope.row.totalNum - scope.row.deliveryTotalNum}}
                  </template>
                </el-table-column>
                <el-table-column prop="subtotal" width="100"></el-table-column>
                <el-table-column prop="dealSubtotal" width="100"></el-table-column>
              </el-table>
            </template>
          </el-table-column>
          <el-table-column label="商品主图" width="80">
            <template slot-scope="scope">
              <span class="scope-span">[包裹{{scope.$index + 1}}]</span>
              <span class="scope-span">物流公司：{{scope.row.logisName}}</span>
              <span class="scope-span">运单号：{{scope.row.shippingNo}}</span>
              <span class="scope-span">状态：{{scope.row.deliveryStatus.desc}}</span>
              <!-- <el-button class="fr" size="mini" @click="handleSchedule('RejectionDialogSchedule',scope.row)" v-if="scope.row.deliveryStatus.code === 'HAD_DELIVERY'&&checkPermission(['admin','orderDetail:reject'])">申请拒收</el-button> -->

            </template>
          </el-table-column>
          <el-table-column label="商品编码" width="170"></el-table-column>
          <el-table-column label="商品名称"></el-table-column>
          <el-table-column label="规格" width="150"></el-table-column>
          <el-table-column label="生产厂家"></el-table-column>
          <el-table-column label="原单价" width="80"></el-table-column>
          <el-table-column label="成交单价" width="90"></el-table-column>
          <el-table-column label="发货数量" width="80"></el-table-column>
          <el-table-column label="原价小计" width="100"></el-table-column>
          <el-table-column label="成交小计" width="100"></el-table-column>

        </el-table>
      </detail-item>
      <template>
        <tabs-layout
          :tabs="[ { name: '订单日志', value: 'first' }, { name: '物流信息', value: 'second' }, { name: '调价日志', value: 'third' } ]"
          v-model="activeName"
          @change="handleClick"
        />
        <template v-if="activeName === 'first'">
          <el-timeline>
            <el-timeline-item
              hide-timestamp
              v-for="(activity, index) in activities"
              :key="index"
              :icon="activity.icon"
              :type="activity.type"
              :color="activity.color"
              :size="activity.size">
              {{activity.time}}
              <span class="active-content">{{activity.content}}</span>
            </el-timeline-item>
          </el-timeline>
        </template>
        <template v-if="activeName === 'second'">
          <el-collapse @change="handleChange" v-model="activeNames">
            <el-collapse-item name="1">
              <ul>
                <li v-for="(logis,index) in logisNoList" class="logis-item">
                  <h3><span v-if="logisNoList.length > 1">【包裹{{index+1}}】<span style="display:inline-block;margin-right: 20px;"></span></span>物流公司:{{logis.logisName}}<span style="display:inline-block;margin-right: 20px;"></span>运单号：{{logis.logisNo}}</h3>
                  <el-timeline>
                    <el-timeline-item
                      hide-timestamp
                      v-for="(activity, index) in logis.logisData"
                      :key="index"
                      :icon="activity.icon"
                      :type="activity.type"
                      :color="activity.color"
                      :size="activity.size">
                      {{activity.time}}
                      <span class="active-content">{{activity.context}}</span>
                    </el-timeline-item>
                  </el-timeline>
                </li>
              </ul>
              <template slot="title">
                <p class="text-primary">{{title}}<i v-if="title === '收起'" class="header-icon el-icon-arrow-up"></i>
                  <i v-if="title === '打开'" class="header-icon el-icon-arrow-down"></i>
                </p>
              </template>
            </el-collapse-item>
          </el-collapse>
        </template>
        <template v-if="activeName === 'third'">
        </template>
      </template>
      <!--审核-->
      <el-dialog
        title="审核订单"
        :visible.sync="verifyVisible"
        width="30%"
        :close-on-click-modal="false"
      >
        <el-dialog
          width="30%"
          title="拒绝理由"
          :visible.sync="innerVisible"
          append-to-body>
          <el-input type="textarea" :rows="4" placeholder="请输入拒绝理由" v-model="reason" maxlength="50" show-word-limit></el-input>
          <span slot="footer" class="dialog-footer">
        <el-button @click="innerVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleVeriry('REJECTED')">确 定</el-button>
      </span>
        </el-dialog>
        <span>订单总金额：<span class="text-warning">￥{{detail.totalMoney}}</span></span>
        <span slot="footer" class="dialog-footer">
    <el-button @click="verifyVisible = false">取 消</el-button>
    <el-button @click="innerVisible = true">拒 绝</el-button>
    <el-button type="primary" @click="handleVeriry('ACCEPTED')">同 意</el-button>
  </span>
      </el-dialog>
      <!--填写备注-->
      <el-dialog
        title="填写备注"
        :visible.sync="remarkVisible"
        width="30%"
        :close-on-click-modal="false"
      >
        <el-input type="textarea" :rows="4" placeholder="请输入备注内容" v-model="remarks" maxlength="100" show-word-limit></el-input>
        <span slot="footer" class="dialog-footer">
    <el-button @click="remarkVisible = false">取 消</el-button>
    <el-button type="primary" @click="remarkSubmit">确 定</el-button>
  </span>
      </el-dialog>
      <!--取消订单-->
      <el-dialog
        title="取消订单"
        :visible.sync="cancelVisible"
        width="400px"
        :close-on-click-modal="false"
      >
        <el-select placeholder="请选择取消原因" v-model="cancelReason" style="width: 100%">
          <el-option value="不想买了"></el-option>
          <el-option value="信息填写错误，重新下单"></el-option>
          <el-option value="不支持赊销付款"></el-option>
          <el-option value="其他"></el-option>
        </el-select>
        <span slot="footer" class="dialog-footer">
    <el-button @click="cancelVisible = false">取 消</el-button>
    <el-button type="primary" @click="cancelSubmit">确 定</el-button>
  </span>
      </el-dialog>
      <!--商品发货-->
      <component :is="currentComponent" @componentResult="componentResult" :dialogData="dialogData" :deliveryVisible="deliveryVisible"></component>
    </div>
  </div>
</template>

<script>
  import checkPermission from '@/utils/permission';
  import detailItem from '@/views/merchant/list/detail-item'
  import { orderDetail,auditOrderInfo,addOrderCancel,updateRemark,saveApplyReject,getExpressInfo } from "@/api/trade";
  import productImg from "@/assets/product.png";
  export default {
    name: "detail",
    inject: ['reload'],
    components: {
      detailItem,
      DeliveryDialogSchedule(resolve) {
        require(['./dialogs/delivery-dialog'], resolve);
      },
      LicenseDialogSchedule(resolve) {
        require(['./dialogs/license-dialog'], resolve);
      },
      RejectionDialogSchedule(resolve) {
        require(['./dialogs/rejection-dialog'], resolve);
      },
      RefundDialogSchedule(resolve) {
        require(['./dialogs/refund-dialog'], resolve);
      },
      ModifyDialogSchedule(resolve) {
        require(['./dialogs/modify-dialog'], resolve);
      }
    },
    data(){
      return {
        previewImages:[],
        previewDetail:[],
        productImg: productImg,
        loading: false,
        activeName: 'first',
        visible: true,
        verifyVisible: false,//审核弹出框
        remarkVisible: false,
        cancelReason: '不想买了',
        remarks: '',
        innerVisible: false,
        reason: '',
        cancelVisible: false,
        currentComponent: '',
        detail: {},
        dialogData:'',
        tableData: [],
        activities: [],
        illustration: '',
        active: 0,
        gridData: [],
        deliveryVisible: false,
        orderItemList: [],
        productAllList: [],
        unDeliveryList: [],
        logisNoList: [],
        drawer: false,
        title: '收起',
        activeNames: '1'
      }
    },
    mounted() {
      this.getDetail()
    },
    methods: {
      checkPermission,
      arraySpanMethod({ row, column, rowIndex, columnIndex }) {
        if(columnIndex === 1) {
          return [1, 10]
        }
      },
      handleChange(val) {
        if(Object.keys(val).length === 0) {
          this.title='打开'
        } else {
          this.title='收起'
        }
      },
      spanMethod({ row, column, rowIndex, columnIndex }) {
        if(columnIndex === 1) {
          return [1, 10]
        }
      },
      async getDetail() {
        this.orderItemList = []
        const {data} = await orderDetail(this.$route.query.id)
        this.loading = false

        this.productAllList = data.orderItemList
        this.unDeliveryList = data.orderItemList.map(item=>{
          if(item.totalNum!==item.deliveryTotalNum) {
            return item
          }
        })
        this.unDeliveryList.filter((item,index) => {
          if(item === undefined) {
            this.unDeliveryList.splice(index, 1)
          }
        })
        this.detail = data
        this.orderItemList.push({orderItemList: this.unDeliveryList})
        this.activities = data.rejectionJournal
        if(data.payStatus.code === 'WAIT_PAY' && data.orderAuditStatus.code === 'ACCEPTED') {
          this.illustration = '订单提交成功，等待客户进行付款';
        } else if (data.orderAuditStatus.code === 'PENDING') {
          this.illustration = '订单已提交成功，等待管理员进行审核'
          this.active = 0
        } else if (data.orderAuditStatus.code === 'REJECTED') {
          this.illustration = data.auditReason===''?'订单已驳回': '驳回理由：'+data.auditReason
        }
        switch (data.orderStatus.code) {
          case 'WAIT_DELIVERY':
            this.illustration = '订单已通过审核，等待仓库发货';
            this.active = 1
            break
          case 'PART_DELIVERY':
            this.illustration = '订单商品已部分发货';
            this.active = 2
            break
          case 'HAD_DELIVERY':
            this.illustration = '订单商品已全部发货';
            this.active = 3
            break
          case 'SUCCESS':
            this.illustration = '订单已完成';
            this.active = 4
            break
          case 'CANCEL':
            this.illustration = '取消原因：' + data.cancelReason
            break
        }
        if (data.orderDeliveryInfoVoList.length > 0 ) {
          this.logisNoList = data.orderDeliveryInfoVoList.map((item,index) => {
            return {
              logisName: item.logisName,
              logisNo: item.shippingNo
            }
          })
         this.getExpress()
        }
      },
      handleClick() {},
      showBigPic(){
        console.log('----->',this.detail.merchantLicenseList);
        this.detail.merchantLicenseList.map(item=>{
          item.filePath.split(',').forEach(o=>{
            this.previewImages.push(o);
          })
        });
        this.$refs['refImage'].showViewer = true;
      },
      componentResult() {
        this.deliveryVisible = false
        this.reload()
      },
      handleBigImage(){
        if(this.detail.financeCollectVo && this.detail.financeCollectVo.certificatePath) {
          if(this.detail.financeCollectVo.certificatePath != 'null' && this.detail.financeCollectVo.certificatePath != null && this.detail.financeCollectVo.certificatePath != ''){
            this.detail.financeCollectVo.certificatePath.split(',').forEach(item=>{
              this.previewDetail.push(item);
            })
          }
          this.$refs['refSalesDetail'].showViewer = true;
        }
      },
      handleSchedule(name,row) {
        this.currentComponent = name
        this.deliveryVisible = true
        if (name === 'DeliveryDialogSchedule'||name==='RefundDialogSchedule'||name==='ModifyDialogSchedule') {
          this.dialogData = this.$route.query.id
        } else if(name === 'RejectionDialogSchedule') {
          this.dialogData = row.deliveryId
        } else {
          this.dialogData = this.detail.merchantLicenseList

        }
      },
      //审核订单
      async handleVeriry(auditStatus) {
        let param = {
          orderNo: this.detail.orderNo,
          auditStatus: auditStatus,
          auditReason: this.reason
        }
        const {data} = await auditOrderInfo(param)
        if(auditStatus === 'ACCEPTED') {
          this.$message.success('审核通过成功！')
          this.verifyVisible = false
        } else {
          this.$message.success('审核拒绝成功！')
          this.verifyVisible = false
          this.innerVisible = false
        }
        this.reload()
      },
      handleRefuse() {

      },
      openRemark() {
        this.remarkVisible = true
        this.remarks = this.detail.remark
      },
      //备注提交
      async remarkSubmit() {
        const { data} = await updateRemark({
          orderId: this.$route.query.id,
          remark: this.remarks
        })
        this.$message.success('填写备注成功')
        this.remarkVisible = false
        this.getDetail()
      },
      //取消订单
      async cancelSubmit() {
        const data = await addOrderCancel({
          orderId: this.$route.query.id,
          cancelReason: this.cancelReason
        })
        if(data.code === 0) {
          this.$message.success('取消订单成功')
        }
        this.cancelVisible = false
        this.getDetail()
      },
      handleClose() {
      },
      //获取物流
      getExpress() {
        this.logisNoList.forEach(async (item,index)=>{
          const { data } = await getExpressInfo(item.logisNo)
          this.logisNoList[index].logisData = data.length > 0 && data[0].data
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .detail-wrapper {
    padding: 0 20px 30px 20px;
    font-size: 14px;
  }
</style>
