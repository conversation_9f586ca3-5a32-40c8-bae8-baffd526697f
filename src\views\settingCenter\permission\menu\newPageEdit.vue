<template>
  <div class="editContent">
    <div class="temptop_title flex_between_center">
      <div>{{this.$route.query.id ==null?'新增':'编辑'}}栏目</div>
      <div>
        <div>
          <el-button @click="clearFun()">取 消</el-button>
          <el-button  v-if="checkPermission(['admin','permission-menu:add','permission-menu:edit'])" type="primary" @click="submitFun('ruleForm')">确 定</el-button>
        </div>
      </div>
    </div>

    <el-form class="form" :model="query" ref="ruleForm"  label-width="120px">
      <input type="hidden" v-model="query.parentId">
      <input type="hidden" v-model="query.clientId">
      <el-form-item class="formItem" prop="label" label="栏目名称:" :rules="[{ required: true, message: '请填写栏目名称' }]" >
        <el-input clearable   v-model="query.label" placeholder="请填写栏目名称" ></el-input>
      </el-form-item>
      <el-form-item class="formItem"  label="图标:"  >
         <icon-picker v-model="query.icon" ></icon-picker>
      </el-form-item>

       <el-form-item class="formItem number"  label="排序:"  >
        <el-input-number v-model="query.sortValue" :min="1"  label="请填排序"></el-input-number>
      </el-form-item>
      <el-form-item class="formItem " prop="group" label="分组:"  >
        <el-input clearable v-model="query.group" placeholder="请填分组" ></el-input>
      </el-form-item>

      <el-form-item class="formItem max" prop="path" label="路由URI:" :rules="[{ required: true, message: '请填写路由URI' }]" >
        <el-input clearable    v-model="query.path" placeholder="请填写路由URI" ></el-input>
      </el-form-item>
      <el-form-item class="formItem" prop="redirect" label="路由重定向URI:"  >
        <el-input clearable   v-model="query.redirect" placeholder="路由重定向URI" ></el-input>
      </el-form-item>
      <el-form-item class="formItem max" prop="component" label="组件:" :rules="[{ required: true, message: '请填写组件' }]" >
        <el-input clearable v-model="query.component" placeholder="请填写组件" ></el-input>
        <div>组件路径：src/views/{{query.component?query.component:'***/***'}}.vue</div>
      </el-form-item>
      <el-form-item class="formItem max" prop="code" label="组件名称:" >
        <el-input clearable v-model="query.code" placeholder="请填写组件名称(只填英文)" ></el-input>
      </el-form-item>
      <el-form-item  class="formItem max" prop="describe" label="描述:" >
        <el-input   clearable v-model="query.describe" placeholder="请填描述" ></el-input>
      </el-form-item>
      <el-form-item class="formItem"  label="导航隐藏:"  >
        <el-radio-group v-model="query.hidden" @change="$forceUpdate()">
          <el-radio :label="false">否</el-radio>
          <el-radio :label="true">是</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item class="formItem"  label="状态:"  >
        <el-radio-group v-model="query.isEnable" @change="$forceUpdate()" >
          <el-radio :label="false">禁用</el-radio>
          <el-radio :label="true">启用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item class="formItem"  label="是否公有:"  >
        <el-radio-group v-model="query.isPublic" @change="$forceUpdate()">
          <el-radio :label="false">否</el-radio>
          <el-radio :label="true">是</el-radio>
        </el-radio-group>
      </el-form-item>

    </el-form>
  </div>
</template>
<script>
import checkPermission from '@/utils/permission'
import { editApi,getApi } from "@/api/setting/permission/menu";
export default {
  data() {
    return {
      query: {

      }
    };
  },
  methods: {
    checkPermission,
    goBackFun(){
      this.$router.go(-1)
      this.$store.dispatch("tagsView/delView", this.$route);
    },
    clearFun: function() {
      this.goBackFun()
    },
    async submitFun(ruleForm) {
      var _this=this;
      _this.$refs[ruleForm].validate(async (valid) => {
        if (valid) {
          var data= await editApi(this.query);
          if(data.code==0){
           _this.goBackFun()
          }
        } else {
          return false;
        }
      });
    },
    async getFun() {
      const { data } = await getApi(this.$route.query.id);

      if(data!=null){
        this.query =data;
        if(this.query.isPublic==null){
          this.query.isPublic=false;
        }
      }
    }
  },
  mounted() {
    if (this.$route.query.id > 0) {
      this.getFun();
    }else if(this.$route.query.id==undefined){
      this.query.parentId=this.$route.query.parentId==null?"":this.$route.query.parentId;
      this.query.clientId=this.$route.query.clientId;
      this.query.commerceModel=this.$route.query.commerceModel;
      this.query.isEnable=true;
      this.query.isPublic=false;
      this.query.hidden=false;
      this.query.id=0;
    }
  },
  beforeDestroy() {}
};
</script>
<style lang="less" scoped>
.editContent {
  border-top: 1px solid #ebecee;
  padding: 0px 20px 20px;
  background-color: #fff;
    /deep/  .number .el-input-number--medium{width:100%}
    .avatar-uploader {
      width: 120px;
      height: 120px;
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }
    .avatar-uploader .el-upload:hover {
      border-color: #409EFF;
    }
    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 120px;
      height: 120px;
      line-height: 120px;
      text-align: center;
    }
    .avatar {
      width:120px;
      height: 120px;
      display: block;
    }
    .form{width:50%;}
    .formItem{text-align: left;}
  }
</style>
