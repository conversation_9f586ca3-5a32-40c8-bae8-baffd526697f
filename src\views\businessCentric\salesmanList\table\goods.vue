<template>
  <div class="goods">
    <div>
      <el-form :inline="true">
        <el-form-item>
          <el-input
            v-model.trim="listQuery.model.productCode"
            placeholder="请输入商品编码"
            style="width: 200px;"
          ></el-input>
        </el-form-item>

        <el-form-item>
          <el-input
            v-model.trim="listQuery.model.productName"
            placeholder="请输入商品名称"
            style="width: 200px;"
          ></el-input>
        </el-form-item>

        <el-form-item>
          <el-input
            v-model.trim="listQuery.model.merchantId"
            placeholder="请输所属商家"
            style="width: 200px;"
          ></el-input>
        </el-form-item>

        <el-form-item>
          <el-cascader
            placeholder="请选择所在区域"
            v-model="cityValue"
            :props="props"
            @change="cityChange"
            clearable
          >
          </el-cascader>
        </el-form-item>

        <el-button type="primary" @click="searchFun">搜索</el-button>
        <el-button @click="resetFun">重置</el-button>
      </el-form>
    </div>
    <el-table
      v-loading="isLoading"
      border
      fit
      :data="list"
      style="width: 100%"
      max-height="450px"
    >
      <el-table-column
        align="center"
        width="80"
        fixed="left"
        :render-header="renderHeader"
      >
        <template slot-scope="scope">
          <span>{{ scope.$index + 1 }} </span>
        </template>
      </el-table-column>
      <el-table-column
        type="selection"
        width="55"
        align="center"
        fixed="left"
      ></el-table-column>
      <el-table-column
        v-for="(item, index) in tableTitle"
        :key="index"
        :min-width="item.width ? item.width : '350px'"
        :fixed="
          item.name == 'publishStatus' || item.name == 'pictIdS'
            ? 'left'
            : false
        "
        :label="item.label"
        show-overflow-tooltip
        :align="item.name == 'pictIdS' ? 'center' : 'left'"
      >
        <template slot-scope="{ row }">
          <!-- <el-popover v-if="item.name=='pictIdS'" placement="right" trigger="hover">
            <img src="https://ss1.bdstatic.com/70cFuXSh_Q1YnxGkpoWK1HF6hhy/it/u=1089874897,1268118658&fm=26&gp=0.jpg" alt="" width="300" height="300">
            <img slot="reference" src="https://ss1.bdstatic.com/70cFuXSh_Q1YnxGkpoWK1HF6hhy/it/u=1089874897,1268118658&fm=26&gp=0.jpg" alt="" width="40" height="40">
          </el-popover> -->
          <span style="color:#2DAC0C" v-if="item.name == 'promotionExpenses'">{{ row[item.name] }}</span>
          <span style="color:#FF6E1B" v-else-if="item.name == 'salePrice'">{{ row[item.name] }}</span>
          <span v-else>{{ row[item.name] }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-if="totals > 0"
      :pageSizes="[2, 10, 20, 50]"
      :total="totals"
      :page.sync="listQuery.current"
      :limit.sync="listQuery.size"
      @pagination="getlist"
    />
    <!-- <div class="dialog-footer">
      <el-button @click="cancelFun">取 消</el-button>
      <el-button type="primary" @click="confirmFun">确 定</el-button>
    </div> -->
  </div>
</template>

<script>
const tableInfo = [
  {
    key: 0,
    name: "productCode",
    label: "商品编码",
    width: "170px",
    disabled: true,
  },
  {
    key: 1,
    name: "productName",
    label: "商品名称",
    width: "170px",
    disabled: true,
  },
  {
    key: 2,
    name: "spec",
    label: "规格",
    width: "140px",
    disabled: true,
  },
  {
    key: 3,
    name: "manufacturer",
    label: "生产厂家",
    width: "170px",
  },
  {
    key: 4,
    name: "salePrice",
    label: "销售价",
    width: "170px",
  },
  {
    key: 5,
    name: "promotionExpenses",
    label: "推广费用（元）",
    width: "140px",
  },
  {
    key: 6,
    name: "saleMerchantName",
    label: "所属商家",
    width: "140px",
  },
  {
    key: 7,
    name: "areaName",
    label: "区域",
    width: "240px",
  },
];
import { salesManGoods } from "@/api/businessCentric/salesmanList";
import Pagination from "@/components/Pagination";
import checkPermission from "@/utils/permission";
import { areas } from "@/api/enterprise";
export default {
  data() {
    return {
      isLoading: false,
      totals: 0,
      list: [],
      listQuery: {
        current: 1,
        size: 10,
        model: {},
      },
      tableSelectTitle: [0, 1, 2, 3, 4],
      showSelectTitle: false,
      tableTitle: [],
      cityValue: [],
      props: {
        lazy: true,
        checkStrictly: true,
        async lazyLoad(node, resolve) {
          const { level } = node;
          let id = node.data ? node.data.id : "";
          let res = await areas({ parentId: id });
          let list = res.data;
          list.forEach((item) => {
            item.value = item.id;
            item.leaf = level >= 2;
          });
          resolve(list);
        },
      },
    };
  },
  methods: {
    searchFun() {
      this.list = []
      this.getlist()
    },
    resetFun() {
      this.list = []
      this.cityValue = []
      this.listQuery = {
        current: 1,
        size: 10,
        model: {},
      }
      this.getlist()
    },
    cityChange(e) {
      this.listQuery.model.provinceId = e[0];
      this.listQuery.model.cityId = e[1];
      this.listQuery.model.countyId = e[2];
    },
    async getlist() {
      this.isLoading = true;
      let { data } = await salesManGoods({
        id: this.row.id,
        data: this.listQuery,
      });
      this.isLoading = false;
      this.list = data.records;
      this.totals = data.total
      this.$emit("update:goodsNum", data.total);
    },
    initTbaleTitle() {
      this.tableTitle = tableInfo;
      this.tableSelectTitle = [];
    },
    renderHeader(h, { column }) {
      var titles = tableInfo;
      var titlesName = ["显示字段项", "隐藏字段项"];
      return (
        <div style="position:relative">
          <div onClick={this.showHeaer}>
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="640px"
            center
            append-to-body={true}
          >
            <el-transfer
              vModel={this.tableSelectTitle}
              data={titles}
              titles={titlesName}
              onChange={this.setleftTitleFun}
            ></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button onClick={this.closeHeaer}>取消</el-button>
              <el-button type="primary" onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      );
    },
    setleftTitleFun(val) {
      this.tableSelectTitle = val;
    },
    showHeaer: function () {
      this.showSelectTitle = true;
    },
    closeHeaer: function () {
      this.showSelectTitle = false;
      this.tableSelectTitle = [];
    },
    setHeaer: function () {
      var titles = tableInfo;
      var listinfo = titles.filter((element, index, self) => {
        return !this.tableSelectTitle.includes(element.key);
      });
      this.tableTitle = listinfo;
      this.showSelectTitle = !this.showSelectTitle;
    },
    checkPermission,
  },
  created() {
    this.initTbaleTitle();
    this.getlist();
  },
  components: {
    Pagination,
  },
  props: {
    row: {
      type: Object,
    },
    visible: {
      type: Boolean,
      default: false,
      required: true,
    },
    goodsNum: {
      type: Number,
      default: 0,
    },
  },
};
</script>

<style lang="less" scoped>
.goods {
  margin: -30px -20px;
  border-top: 1px solid #ebecee;
  padding: 30px 20px;
  .dialog-footer {
    border-top: 1px solid #efefef;
    margin: -30px -20px;
    margin-top: 30px;
    padding: 20px;
    padding-top: 10px;
    text-align: right;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
  }
}
</style>