import Layout from '@/layout'

const businessCentric = {
  path: '/businessCentric',
  name: 'businessCentric',
  redirect: 'businessCentric/proxySettings',
  meta: {
    title: '业务中心',
    icon: 'component'
  },
  alwaysShow: true,
  component: Layout,
  children: [
    {
      path: 'salesmanList',
      name: 'salesmanListMain',
      meta: { title: '业务员列表' },
      component: () => import('@/views/businessCentric/salesmanList/list')
    },
    {
      path: 'salesmanListDetail',
      name: 'salesmanListDetail',
      hidden: true,
      meta: { title: '查看业务员档案', activeMenu: '/businessCentric/salesmanList' },
      component: () => import('@/views/businessCentric/salesmanList/detail')
    },
    {
      path: 'salesmanListeditItem',
      name: 'salesmanListeditItem',
      hidden: true,
      meta: { title: '编辑业务员档案', activeMenu: '/businessCentric/salesmanList' },
      component: () => import('@/views/businessCentric/salesmanList/editItem')
    },
    {
      path: 'promotionGoods',
      name: 'promotionGoods',
      meta: { title: '推广商品' },
      component: () => import('@/views/businessCentric/promotionGoods/list')
    },
    {
      path: 'promotionGoodsDetail',
      name: 'promotionGoodsDetail',
      hidden:true,
      meta: { title: '推广商品详情',activeMenu: '/businessCentric/promotionGoods' },
      component: () => import('@/views/businessCentric/promotionGoods/detail')
    },
    {
      path: 'applicationForGoods',
      name: 'applicationForGoods',
      meta: { title: '推广商品申请' },
      component: () => import('@/views/businessCentric/applicationForGoods/list')
    },
    {
      path: 'agentWithdrawalAudit',
      name: 'agentWithdrawalAudit',
      meta: { title: '业务员撤销申请' },
      component: () => import('@/views/businessCentric/agentWithdrawalAudit/list')
    },
    {
      path: 'proxySettings',
      name: 'proxySettings',
      meta: { title: '代理设置' },
      component: () => import('@/views/businessCentric/proxySettings/list')
    },
    {
      path:'taskManagement',
      name: 'taskManagement',
      meta: { title: '任务管理' },
      component: () => import('@/views/taskManagement/index'),
  },
  {
      path:'taskAddOrEdit',
      name: 'taskAddOrEdit',
      meta: { title: '新增/编辑任务' },
      component: () => import('@/views/taskManagement/edit'),
  },
  {
      path:'taskDetail',
      name: 'taskDetail',
      meta: { title: '达成明细' },
      component: () => import('@/views/taskManagement/detail'),
  }
  ]
}

export default businessCentric