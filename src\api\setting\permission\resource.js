import requestAxios from '@/utils/requestAxios'

export function list(data) {
    return requestAxios({
        url: '/api/authority/resource/page',
        method: 'post',
        data
    })
}


export function editApi(data) {
    return requestAxios({
        url: '/api/authority/resource',
        method: data.id == 0 ? 'post' : 'put',
        data
    })
}

export function deleteApi(id) {
    return requestAxios({
        url: '/api/authority/resource?ids[]=' + id,
        method: 'delete'
    })
}