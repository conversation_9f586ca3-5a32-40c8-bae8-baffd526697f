import requestAxios from '@/utils/requestAxios'

// 获取列表
export function list(data) {
  return requestAxios({
    url: "/api/merchant/admin/purMerchantApply/pageRegister",
    method: 'post',
    data
  })
}

// 获取状态数量
export function getPurApplyMerhcantCount(data) {
  return requestAxios({
    url: "/api/merchant/admin/purMerchantApply/getPurApplyMerhcantCount",
    method: 'post',
    data
  })
}

export function businessType(data) {
  return requestAxios({
    url: '/api/merchant/admin/merchantType',
    method: 'get',
    params: data
  })
}

// 添加
export function add(data) {
  return requestAxios({
    url: '/api/merchant/admin/purMerchant',
    method: 'post',
    data
  })
}

// 修改
export function edititem(data) {
  return requestAxios({
    url: '/api/merchant/admin/purMerchantApply',
    method: 'put',
    data
  })
}


// 单一获取
export function getitems(data) {
  return requestAxios({
    url: '/api/merchant/admin/purMerchantApply/getPurMerchantApply/' + data,
    method: 'get',
  })
}

// 批量待审
export function updatePurMerchantApplyPending(data) {
  return requestAxios({
    url: '/api/merchant/admin/purMerchantApply/batchUpdatePurMerchantApplyPending',
    method: 'post',
    params: data
  })
}

//批量审核
export function updatePurMerchantApplyAccepted(data) {
  return requestAxios({
    url: '/api/merchant/admin/purMerchantApply/batchUpdatePurMerchantApplyAccepted',
    method: 'post',
    params: data
  })
}

// 批量驳回
export function updatePurMerchantApplyRejected(data) {
  return requestAxios({
    url: '/api/merchant/admin/purMerchantApply/batchUpdatePurMerchantApplyRejected',
    method: 'post',
    params: data
  })
}
// 单一驳回
export function rejected(data) {
  return requestAxios({
    url: '/api/merchant/admin/purMerchantApply/updatePurMerchantApplyRejected/' + data.id,
    method: 'post',
    data
  })
}

// 单一通过
export function accepted(data) {
  return requestAxios({
    url: '/api/merchant/admin/purMerchantApply/updatePurMerchantApplyAccepted/' + data,
    method: 'post'
  })
}

// 获取采购商经营范围
export function listByLicenseBaseType() {
  return requestAxios({
    url: '/api/merchant/admin/licenseBase/anno/listByLicenseBaseType',
    method: 'get',
    params: { type: 'BUYER' }
  })
}

// 单一启用
export function enable(data) {
  return requestAxios({
    url: '/api/merchant/admin/purMerchant/updatePublishStatusByIds/' + data,
    method: 'post'
  })
}

// 单一冻结
export function frozen(data) {
  return requestAxios({
    url: '/api/merchant/admin/purMerchant/updatePublishFrozenById/' + data,
    method: 'post'
  })
}



