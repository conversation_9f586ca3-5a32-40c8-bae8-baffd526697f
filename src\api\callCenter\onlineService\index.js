import requestAxios from '@/utils/requestAxios'

// 获取列表数据
export function list(data) {
  return requestAxios({
    url: "/api/merchant/admin/customerService/page",
    method: 'post',
    data
  })
}

// 删除
export function delFun(data) {
  return requestAxios({
    url: '/api/merchant/admin/customerService',
    method: 'delete',
    params: data
  })
}

// 添加
export function add(data) {
  return requestAxios({
    url: '/api/merchant/admin/customerService',
    method: 'post',
    data
  })
}

// 修改
export function reset(data) {
  return requestAxios({
    url: '/api/merchant/admin/customerService',
    method: 'put',
    data
  })
}



