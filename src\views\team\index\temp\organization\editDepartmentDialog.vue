<template>
    <div class="container" >
    <!-- 编辑部门弹出 -->
      <!-- v-if="showEditSubDepartment" -->
    <el-dialog
      title="编辑部门"
      class="editSubDepartmentDialog"
      :visible.sync="visible"
      width="30%"
      :show-close="true"
      :close-on-click-modal="false"
      :before-close="close"
    >
      <div class="editSubDepartment">
        <el-form

          ref="form"
          :model="editSubDepartmentForm"
          label-width="90px"
        >
          <el-form-item
            label="部门名称:"
            prop="name"
            :rules="[
              {
                required: true,
                message: '请输入部门名称',
                trigger: 'blur',
              },
            ]"
          >
            <el-input
              maxlength="10"
              show-word-limit
              v-model="editSubDepartmentForm.name"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="主管:"
            prop="salesmanId"
            :rules="[
              {
                required: true,
                message: '请选择主管',
                trigger: 'change',
              },
              { validator: checkSalesManByIdFun, trigger: 'change', required: true }
            ]"
          >
            <el-select
              filterable
              v-model="editSubDepartmentForm.salesmanId"
              placeholder="请选择"
            >
              <!-- <el-option
                  key=""
                  label="`请选择`"
                  value="">
                  <span style="float: left">请选择</span>
                </el-option> -->
              <el-option
                v-for="(item,index) in salesmanList"
                :key="index"
                :label="`${item.salesmanName}(${item.mobile})`"
                :value="item.id"
              >
                <span style="float: left">{{ item.salesmanName }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{item.mobile}}</span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="close()">取 消</el-button>
        <el-button type="primary" v-throttle  @click="submitEditFun()">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 编辑部门弹出 end -->
    </div>
</template>
<script>
// import { query } from '@/api/';
import { orgaDepartment,getSalesManList,checkSalesManById } from '@/api/organization/index';
import { mapState,mapGetters,mapActions} from 'vuex'
export default {
    data() {
      return {
        // 新增 下级部门
        showEditSubDepartment: false,
        editSubDepartmentForm:  {
              id:null,
              "name": "",
              "orgaAreaDto": {
                "commonAreaIds": []
              },
              "parentId": 0,
              "salesmanId": ""
          },
          salesmanList: null,
      }
    },
    props:{
      /**
      * @description  创建来源   默认 TreeBtn  树形图新增按钮  contextBtn 内容新增按钮
      * @param {String} crateSource  
      */
      crateSource:{
         type:String,
         default:"TreeBtn"
      },
      /**
      * @description  编辑Form 对象  显示
      * @param {Boolean} visible  
      */
      visible:{
         type:Boolean,
         default:false
      },
        /**
      * @description  编辑Form 对象 
      * @param {String} visible  
      */
      editForm: {
            type:Object,
            default:function(){
                return  {
                  id:null,
                  "name": "",
                  "orgaAreaDto": {
                    "commonAreaIds": []
                  },
                  "parentId": 0,
                  "salesmanId": ""
                }
            }
        }
    },
    components: {
        // 组件
    },
    computed: {
        // 计算属性computed : 
        // 1. 支持缓存，只有依赖数据发生改变，才会重新进行计算
        // 2. 不支持异步，当computed内有异步操作时无效，无法监听数据的变化
        // 3. computed 属性值会默认走缓存，计算属性是基于它们的响应式依赖进行缓存的，也就是基于data中声明过或者父组件传递的props中的数据通过计算得到的值
        // 4. 如果一个属性是由其他属性计算而来的，这个属性依赖其他属性，是一个多对一或者一对一，一般用computed
        // 5.如果computed属性属性值是函数，那么默认会走get方法；函数的返回值就是属性的属性值；在computed中的，属性都有一个get和一个set方法，当数据变化时，调用set方法。
      ...mapGetters([
          'organizationNavNode',
          'organizationInfo',
          "paremDepartmentInfo",
          "departmentInfo",
      ]), 
    
    
    },
    filters: {

    },
    watch:  {
        // 监听属性watch：
        // 1. 不支持缓存，数据变，直接会触发相应的操作；
        // 2. watch支持异步；
        // 3. 监听的函数接收两个参数，第一个参数是最新的值；第二个参数是输入之前的值；
        // 4. 当一个属性发生变化时，需要执行对应的操作；一对多；
        // 5. 监听数据必须是data中声明过或者父组件传递过来的props中的数据，当数据变化时，触发其他操作，函数有两个参数，
        // 　　immediate：组件加载立即触发回调函数执行，
        // 　　deep: 深度监听，为了发现对象内部值的变化，复杂类型的数据时使用，例如数组中的对象内容的改变，注意监听数组的变动不需要这么做。注意：deep无法监听到数组的变动和对象的新增，参考vue数组变异,只有以响应式的方式触发才会被监听到。
        // 　　deepdemo：
        // editForm:{
        //     handler(val){
        //        this.editSubDepartmentForm= Object.assign({}, val) 
        //     },
        //     deep:true
        // }
    
    },
    methods: {
        //方法集合
    checkSalesManByIdFun(rule, value, callback){
      checkSalesManById(value).then(res=>{
        let {code,data,msg} = res;
        if (code == 0 && data != null&& data.organizationId != this.organizationInfo.id&&data.isLeader.code=='Y') {
          this.$message.warning('当前业务员已是其他组织主管')
          // return callback(new Error('当前业务员已是其他组织主管'))
        } else if (code == 0 && data != null&& data.organizationId != this.organizationInfo.id) {
           this.$message.warning('当前业务员是其他组织成员')
          // return callback(new Error('当前业务员是其他组织成员'))
        } else if (code == 0 && data != null&& data.organizationId == this.organizationInfo.id&&data.isLeader.code=='Y') {
          this.$message.warning('当前业务员是其他部门主管')
          // return callback(new Error('当前业务员是其他部门主管'))
        } else if (code == 0 && data != null&& data.organizationId == this.organizationInfo.id) {
           this.$message.warning('当前业务员是其他部门成员')
          // return callback(new Error('当前业务员是其他部门成员'))
        } 
        callback()
      })
    },
        //方法集合
        checkVal(){
              let form =Object.assign({}, JSON.parse(JSON.stringify(this.editSubDepartmentForm)))
              if(this.crateSource=='TreeBtn'&&this.organizationNavNode!=null&&this.organizationNavNode.length>0){
                  // 树形图入口
                form.parentId =this.organizationNavNode[0].id
                this.editSubDepartmentForm = Object.assign({}, form) 
              }else{
                if(form.id!=null){
                    this.editSubDepartmentForm = Object.assign({}, form)  
                }else{
                  // 内页入口
                  if(this.organizationNavNode!=null&&this.organizationNavNode.length>0){
                    form.parentId = this.organizationNavNode[this.organizationNavNode.length-1].id
                    this.editSubDepartmentForm = Object.assign({}, form) 
                  }
                }
            }
        },
        show(){
              this.showEditSubDepartment =true;
              
        },
        close(){
            this.editSubDepartmentForm={
                id:null,
                "name": "",
                "orgaAreaDto": {
                  "commonAreaIds": []
                },
                "parentId": 0,
                "salesmanId": ""
              }
            // this.showEditSubDepartment =false;
            this.$emit("update:visible",false);
        },
        async submitEditFun(){
          this.$refs.form.validate(async (valid) => {
            console.info(valid)
          if (valid) {
            // 提交回调 
            let method= (this.editSubDepartmentForm.id!=undefined&& this.editSubDepartmentForm.id!=null)?'PUT':'POST';
            let {code,data,msg} = await orgaDepartment(this.editSubDepartmentForm,method);
            if(code == 0 ){
              // 提交回调 
              this.$emit("confirm",this.editSubDepartmentForm)
              this.$emit("update:visible",false);
            }else{
              // this.showEditSubDepartment =false
            }
          } else {
        }
      });
            
            
        },
        // 查询业务员列表
        getSalesManListFun(){
          getSalesManList(null).then().then(res => {
            let {code,data,msg} = res;
             if(code == 0 ){
               this.salesmanList=data;
             } 
          })
        },
    },
    mounted() {
        // 方法调用
       
        this.editSubDepartmentForm= Object.assign({}, this.editForm) 
        this.getSalesManListFun()
        this.checkVal()
    },
    beforeDestroy() {
        this.editSubDepartmentForm={
          id:null,
          "name": "",
          "orgaAreaDto": {
            "commonAreaIds": []
          },
          "parentId": 0,
          "salesmanId": ""
        }
    }
   
}
</script>

<style lang="less" scoped>
::v-deep .editSubDepartmentDialog .el-dialog{
  min-width: 400px;
}

::v-deep .editSubDepartmentDialog .el-dialog__body{
padding: 30px 20px !important;

}

.editSubDepartment {
  ::v-deep .el-select {
    width: 100%;
  }
  ::v-deep .sellAreaClass .el-radio {
    padding-top: 10px;
  }
  ::v-deep .sellAreaClass .el-radio {
    padding-top: 10px;
  }
  ::v-deep .sellTypeClass .el-radio {
    padding-top: 10px;
  }
}

</style>
