<template>
  <div class="detail">
    <div class="head">
      <page-title :title="item.productName" />
      <div class="detail">
        <p><b>规格：</b> {{item.spec}}</p>
        <p><b>批准文号：</b> {{item.approvalNumber}}</p>
        <p><b>生产厂家：</b> {{item.manufacturer}}</p>
      </div>
    </div>
    <div class="content">
      <tabs-layout
        v-model="tabflag"
        :tabs="[ { name: '品种资质', value: 'productLicenseRelVoList' }, { name: '质检报告', value: 'qualityReportRelVoList' }]"
        @change="chageTabsFun">
      </tabs-layout>
      <div class="table">
        <!-- <el-table v-if="list" :data="list" row-key="id" border fit highlight-current-row style="width: 100%">
          <el-table-column v-for="(item, index) in tableTitle[tabflag]" :key="index" :min-width="(item.width?item.width:'200px')" :label="item.label"
            :show-overflow-tooltip="item.name!='qualityReportRelVoList'" align="left">
            <template slot-scope="{row}">
              <el-popover v-if="item.name=='fileIds'" placement="right" trigger="hover">
                <img :src="row[item.name]" alt="">
                <img slot="reference" :src="row[item.name]" alt="" width="40" height="40">
              </el-popover>
              <span v-else-if="item.name == 'licenseType'">
                {{row[item.name].desc }}
              </span>
              <span v-else>{{row[item.name] }}</span>
            </template>
          </el-table-column>

          <el-table-column fixed="right" align="center" label="操作" width="150" class="itemAction">
            <template slot-scope="scope">
              <el-button @click="edit(scope.row.id)" type="text" >编辑</el-button>
              <el-button @click="detailFun(scope.row.id)" type="text" >预览</el-button>
              <el-button @click="detailFun(scope.row.id)" type="text" >下载</el-button>
            </template>
          </el-table-column>
        </el-table> -->
      </div>

      <div>
        <el-table :data="list" style="width: 100%" border>
          <el-table-column prop="licenseType.desc" label="证件类型"></el-table-column>
          <el-table-column prop="certificateNumber" label="证件号">
            <template slot-scope="{ row }">
              <el-input v-if="row.isEdit" placeholder="请输入证件号" v-model="row.certificateNumber"></el-input>
              <span v-else>{{ row.certificateNumber }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="licenseEndDate" label="过期时间">
            <template slot-scope="{ row }">
              <el-date-picker v-if="row.isEdit" v-model="row.licenseEndDate" type="datetime" style="width: 240px" placeholder="选择日期" value-format="yyyy-MM-dd HH:mm:ss"></el-date-picker>
              <span v-else>{{ row.licenseEndDate }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="fileIds" label="附件" class-name="img-cell">
            <template slot-scope="{ row }">
              <el-upload v-if="row.isEdit"  ref="uploadlisence" :limit="3" :file-list="row.filePathList" accept=".jpg,.png,.bmp,.jpeg"
                :action="$uploadUrl" :data="insertProgram" :headers="headersProgram" list-type="picture-card" :on-remove="handleRemove" :on-success="uploadSuccess" :before-upload="beforeUpload">
                <i class="el-icon-plus"></i>
              </el-upload>

              <span v-else>
                <img v-for="file in row.filePathList" :key="file.url" class="el-upload-list__item-thumbnail" :src="file.url" alt="" style="contain:cover;width:50px;height:50px;margin-right:5px">
              </span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="180">
            <template slot-scope="scope">
              <el-row class="table-edit-row">
                <span v-show="!scope.row.isEdit" class="table-edit-row-item">
                  <el-button type="text"  @click="editLisenceFun(scope.row,scope.$index)">编辑</el-button>
                </span>
                <span v-show="scope.row.isEdit" class="table-edit-row-item">
                  <el-button type="text"  @click="cancelLisenceEdit(scope.row,scope.$index)">取消</el-button>
                </span>
                <span v-show="scope.row.isEdit" class="table-edit-row-item">
                  <el-button type="text"  @click="confirmLisenceEdit(scope.row,scope.$index)">确定</el-button>
                </span>
                <span v-if="!!scope.row.fileIds" v-show="!scope.row.isEdit" class="table-edit-row-item">
                  <el-button type="text"  @click="showBigPic(scope.row)">预览</el-button>
                </span>
                <el-image :ref="`ref${scope.row.licenseId}`" style="width:0;height:0" :src="imagesSplitList[0]" :preview-src-list="imagesSplitList"></el-image>
                <span class="table-edit-row-item">
                  <DowloadButton :size="'small'" :buttonType="'text'" :key="scope.row.licenseBaseId" :imgList="getimgList(scope.row.fileIds)"></DowloadButton>
                </span>
              </el-row>
            </template>
          </el-table-column>
        </el-table>
        <el-dialog title="资质预览" append-to-body :visible.sync="dialogStatus" :before-close="closeDialogFun">
          <el-carousel arrow="always" height="50vh" :autoplay="false">
            <el-carousel-item v-for="(item, index) in getsrc(showlisenceItem.fileIds)" :key="index">
              <el-image style="width:100%;height:100%;" :fit="'contain'" :src="item.url"></el-image>
            </el-carousel-item>
          </el-carousel>
          <div slot="footer">
            <el-button @click="closeDialogFun">取 消</el-button>
            <DowloadButton v-if="showlisenceItem.fileIds" :buttonType="'primary'" :imgList="getimgList(showlisenceItem.fileIds)"></DowloadButton>
          </div>
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>
import DowloadButton from "@/components/eyaolink/DowloadButton";
import {
  getitem,
  resetLicenseRel,
} from "@/api/massCenter/productQualification/index";
import { getToken } from "@/utils/auth";
export default {
  name: "productQualificationdetail",
  components: {
    DowloadButton
  },
  data() {
    return {
      item: {},
      tabflag: "productLicenseRelVoList",
      list: [],
      dialogStatus: false,
      imagesSplitList:[],
      tableTitle: {
        productLicenseRelVoList: [
          {
            label: "证件类型",
            name: "licenseType",
          },
          {
            label: "证件号",
            name: "certificateNumber",
          },
          {
            label: "过期时间",
            name: "licenseEndDate",
          },
          {
            label: "附件",
            name: "fileIds",
          },
        ],
        qualityReportRelVoList: [
          {
            label: "证件类型",
            name: "licenseType",
          },
          {
            label: "证件号",
            name: "certificateNumber",
          },
          {
            label: "过期时间",
            name: "licenseEndDate",
          },
          {
            label: "附件",
            name: "fileIds",
          },
        ],
      },
      showlisenceItem: {},
      insertProgram: {
        folderId: 0,
      },
      headersProgram: {
        token: getToken(),
        Authorization: "Basic YWRtaW5fdWk6YWRtaW5fdWlfc2VjcmV0",
      },
      editLisenceItem: {}
    };
  },
  methods: {
    closeDialogFun() {
      this.dialogStatus = false;
    },
    getimgList(row) {
      console.log(row)
      let arr = [];
      row.split(",").forEach((item) => {
        arr.push({
          fileIds: item,
        });
      });
      return arr;
    },
    cancelLisenceEdit(row, index) {
      row = this.editLisenceItem;
      row.isEdit = false
      this.$set(this.list, index, this.editLisenceItem);
    },
    showBigPic(row) {
      this.showlisenceItem = row;
      console.log('row',row);
      this.imagesSplitList = row.filePathList.map(item=>{
        return item.url;
      })
      this.$refs[`ref${row.licenseId}`].showViewer = true;
    },
    beforeUpload(file) {
      let fileTypeList = [
        "image/png",
        "image/pjpeg",
        "image/jpeg",
        "image/bmp",
      ];
      const isJPG = fileTypeList.indexOf(file.type) > -1;
      const isLt2M = file.size / 1024 / 1024 < 5;

      if (!isJPG) {
        this.$message.error("上传图片格式错误!");
      }
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 2MB!");
      }
      return isJPG && isLt2M;
    },
    getFilePath(fileList) {
      let str = "";
      fileList.forEach((item) => {
        let url = item.response.data.url;
        str += item.response.data.url + ",";
      });
      return str.substr(0, str.length - 1);
    },
    uploadSuccess(res, file, fileList) {
      this.list.forEach((item, index) => {
        if (item.id == this.editLisenceItem.id) {
          item.fileIds = this.getFilePath(fileList);
          item.filePathList = fileList;
        }
      });
    },
    handleRemove(file, fileList) {
      this.list.forEach((item, index) => {
        if (item.licenseBaseId == this.editLisenceItem.licenseBaseId) {
          item.fileIds = this.getFilePath(fileList);
          item.filePathList = fileList;
        }
      });
    },
    editLisenceFun(row, index) {
      let that = this
      this.list.forEach((item, index) => {
        if (item.isEdit) {
          that.$set(that.list, index, that.editLisenceItem);
          item.isEdit = false;
        }
      });
      row.isEdit = true
      this.editLisenceItem = JSON.parse(JSON.stringify(row));
    },
    async confirmLisenceEdit(row) {
      row.isEdit = false;
      if (!row.filePathList) {
        this.$message.error("请上传资质");
        return false;
      }
      row.filePathList.forEach((item) => {
        item.url = item.response.data.url;
      });
      row.reminderDateType= "A_MONTH_AGO"
      let res = await resetLicenseRel(row);
      if(res.isSuccess){
        this.$message.success("修改资质成功");
      }
    },
    closeDialogFun() {
      this.dialogStatus = false;
    },
    getsrc(str) {
      console.log(!str)
      if (!str) {
        return [];
      } else {
        let arr = str.split(",");
        let list = [];
        arr.forEach((item) => {
          let obj = {
            response: {
              data: {
                url: "",
              },
            },
          };
          obj.response.data.url = item;
          obj.url = item;
          list.push(obj);
        });
        return list;
      }
    },
    back() {
      this.$store.dispatch("tagsView/delView", this.$route);
      this.$router.push("/massCenter/productQualification");
    },
    edit(row) {},
    async getlist() {
      let { data } = await getitem(this.$route.query.id);
      this.item = data;
      this.chageTabsFun();
    },
    chageTabsFun() {
      let arr = this.item[this.tabflag] || [];
      arr.forEach((item) => {
        item.filePathList = this.getsrc(item.fileIds);
        item.isEdit = false;
        console.log(item);
      });
      this.list = arr
    },
    renderHeader(h, { column }) {
      return (
        <div style="position:relative">
          <i class="el-icon-menu" />
        </div>
      );
    },
  },
  created() {
    this.getlist();
  },
};
</script>

<style lang="less" scoped>
.detail {
  padding: 0;
  background-color: #f2f3f4;
  .head {
    background-color: #fff;
    padding: 0 20px;
    .head_title {
      font-size: 22px;
      line-height: 56px;
      border-bottom: 1px solid #eeeeee;
    }
    .detail {
      padding: 10px 0;
      line-height: 30px;
      color: #505465;
      background-color: #fff;
      font-size: 14px;
      b {
        color: #232333;
      }
    }
  }
  .content {
    margin-top: 20px;
    padding: 20px;
    background-color: #fff;
    .title {
      padding-bottom: 20px;
      span {
        margin-bottom: -2px;
        padding: 0 15px;
        height: 40px;
        line-height: 30px;
        display: block;
        background: rgba(255, 255, 255, 0);
        border-bottom: 2px solid rgb(64, 158, 255);
        font-size: 16px;
        font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
          "PingFang SC"-400;
        font-weight: 400;
        color: rgb(64, 158, 255);
      }
    }
  }
  /deep/ .el-upload {
    width: 50px;
    height: 50px;
    position: relative;
  }
  /deep/ .el-upload > i {
    font-size: 16px;
    position: absolute;
    left: 50%;
    top: 50%;
    -webkit-transform: translateX(-50%) translateY(-50%);
    transform: translateX(-50%) translateY(-50%);
  }
  /deep/ .el-upload-list .el-upload-list__item {
    width: 50px;
    height: 50px;
  }
  /deep/ .hide .el-upload--picture-card {
    display: none;
  }
}
</style>
