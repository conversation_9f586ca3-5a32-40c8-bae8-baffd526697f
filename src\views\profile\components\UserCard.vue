<template>
  <el-card style="margin-bottom:20px;">
    <div class="clearfix" slot="header">
      <span>关于我</span>
    </div>
    <div class="user-profile">
      <div class="box-center">
        <pan-thumb :height="'100px'" :hoverable="false" :image="user.avatar" :width="'100px'">
          <el-link @click="openAvatar" class="change-avatar" type="primary">更换头像</el-link>
        </pan-thumb>
      </div>
      <div class="box-center">
        <div class="user-name text-center">{{ user.name }}</div>
        <div class="user-role text-center text-muted">
          <span>{{ user.email }}</span> ·
          <span>{{ user.mobile  }}</span>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script>
import PanThumb from '@/components/PanThumb'

export default {
  components: { PanThumb },
  props: {
    user: {
      type: Object,
      default: () => {
        return {
        }
      }
    }
  },
  data () {
    return {
      dialogVisible: false,
      logo: [
        { img: 'gitee.png', name: 'gitee', bind: false, radius: true },
        { img: 'github.png', name: 'github', bind: false, radius: true },
        { img: 'tencent_cloud.png', name: 'tencent_cloud', bind: false, radius: true },
        { img: 'qq.png', name: 'qq', bind: false, radius: false },
        { img: 'dingtalk.png', name: 'dingtalk', bind: false, radius: true },
        { img: 'microsoft.png', name: 'microsoft', bind: false, radius: false }
      ],
      oauthType: '',
      page: {
        width: window.screen.width * 0.5,
        height: window.screen.height * 0.5
      }
    }
  },
  mounted () {
    
  },
  methods: {
    openAvatar () {
      this.dialogVisible = true
      // this.$refs.myAvatar.init()
    },
  }
}
</script>

<style lang="scss" scoped>
.box-center {
  margin: 0 auto;
  display: table;
}

.text-muted {
  color: #777;
}

.user-profile {
  .user-name {
    font-weight: bold;
  }

  .box-center {
    padding-top: 10px;
  }

  .user-role {
    padding-top: 10px;
    font-weight: 400;
    font-size: 14px;
  }

  .box-social {
    padding-top: 30px;

    .el-table {
      border-top: 1px solid #dfe6ec;
    }
  }

  .user-follow {
    padding-top: 20px;
  }
}

.user-bio {
  margin-top: 20px;
  color: #606266;

  span {
    padding-left: 4px;
  }
  .logo-wrapper {
    display: inline-block;
    margin: 10px 0;
    img {
      width: 1.4rem;
      display: inline-block;
      margin: 0 0.6rem;
      cursor: pointer;
      &.unbind {
        -webkit-filter: grayscale(100%);
        -moz-filter: grayscale(100%);
        -o-filter: grayscale(100%);
        filter: grayscale(100%);
      }
      &.radius {
        border-radius: 50%;
      }
    }
  }
  .user-bio-section {
    font-size: 14px;
    padding: 15px 0;

    .user-bio-section-header {
      border-bottom: 1px solid #dfe6ec;
      padding-bottom: 10px;
      margin-bottom: 10px;
      font-weight: bold;
    }
  }
}
</style>
