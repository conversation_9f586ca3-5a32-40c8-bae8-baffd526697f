<template>
  <div class="editContent">
    <div style="position:absolute;top:10px; right:15px; background:#fff; height:38px;" >
      <div>
          <el-button @click="clearFun()">取 消</el-button>
          <el-button type="primary" @click="submitFun('ruleForm')"
            >确 定</el-button
          >
      </div>
    </div> 
    <el-form class="form" :inline="true"  :model="query" ref="ruleForm" label-width="100px">
      <input type="hidden" v-model="query.parentId" />
      <input type="hidden" v-model="query.id" />
      <el-form-item
        class="formItem"
        prop="label"
        label="名称:"
        :rules="[{ required: true, message: '请填写名称' }]"
      >
        <el-input
          clearable
          style="width:350px"
          v-model="query.label"
          placeholder="请填写名称"
        ></el-input>
      </el-form-item>
      <el-form-item
        class="formItem"
        prop="code"
        label="编码:"
        :rules="[{ required: true, message: '请填写编码' }]"
      >
        <el-input
          clearable
          style="width:350px"
          v-model="query.code"
          placeholder="请填写编码"
        ></el-input>
      </el-form-item>
      <el-form-item
        class="formItem"
        label="全名:"
      >
        <el-input
          clearable
          style="width:350px"
          v-model="query.fullName"
          placeholder="请填写全名"
        ></el-input>
      </el-form-item>
      <el-form-item
        class="formItem"
        label="经度:"
      >
        <el-input
          clearable
          style="width:350px"
          v-model="query.longitude"
          placeholder="请填写经度"
        ></el-input>
      </el-form-item>
      <el-form-item
        class="formItem"
        label="维度:"
        
      >
        <el-input
          clearable
          style="width:350px"
          v-model="query.latitude"
          placeholder="请填写维度"
        ></el-input>
      </el-form-item>
    
      <el-form-item
        class="formItem"
        label="数据来源:"
      >
        <el-input
          clearable
          style="width:350px"
          v-model="query.source"
          placeholder="请填写数据来源"
        ></el-input>
      </el-form-item>
      <el-form-item
        class="formItem"
        prop="sortValue"
        label="排序:"
        :rules="[{ required: true, message: '请填写排序' }]"
      >
        <el-input-number
          clearable
          style="width:350px"
          v-model="query.sortValue"
          placeholder="请填写排序"
        ></el-input-number>
      </el-form-item>
    

        <el-form-item  class="formItem" prop="level" label="行政区级:" :rules="[{ required: true, message: '请选中行政区级',trigger: 'blur' }]" >
          <el-radio-group v-model="query.level"  @change="$forceUpdate()">
          <el-radio-button label="COUNTRY">国家</el-radio-button>
          <el-radio-button label="PROVINCE">省份/直辖市</el-radio-button>
          <el-radio-button label="CITY">地市</el-radio-button>
          <el-radio-button label="COUNTY">区县</el-radio-button>
          <el-radio-button label="TOWNS">乡镇</el-radio-button>
        </el-radio-group>
      </el-form-item>
    
       
       
      <!-- <el-form-item>
        <el-button @click="clearFun()">取 消</el-button>
        <el-button type="primary" @click="submitFun('ruleForm')"
          >确 定</el-button
        >
      </el-form-item> -->
    </el-form>
  </div>
</template>
<script>
// import rules from '@/utils/rules'
import { editArea }  from "@/api/setting/area";
export default {
  data() {
    return {
      query: {},
      level:{
        COUNTRY:{
          "key": "COUNTRY",
          "data": "国家"
        },
        PROVINCE:{
          "key": "PROVINCE",
          "data": "省份/直辖市"
        },
        CITY:{
          "key": "CITY",
          "data": "地市"
        },
        COUNTY:{
          "key": "COUNTY",
          "data": "区县"
        },
        TOWNS:{
          "key": "TOWNS",
          "data": "乡镇"
        },
        
      },
    };
  },
  props: {
    row: {
      type: Object
    },
    visible: {
      type: Boolean,
      default: false,
      required: true
    },
    isReload: {
      type: Boolean,
      default: false,
      required: true
    },
    actionItem: {
      required: true
    }
  },
  methods: {
    clearFun: function() {
      this.$emit("update:visible", false);
      this.$emit("update:row", {});
    },
    submitFun: function(businessScopeForm) {
      var _this=this;
      _this.$refs[businessScopeForm].validate(async valid => {
        if (valid) {
          var query= JSON.parse(JSON.stringify(this.query))
          query.level=_this.level[query.level]
          var data= await editArea(query)
           if(data.code==0){

             _this.$emit("update:actionItem", this.query);
             _this.$emit("update:visible", false);
             _this.$emit("update:isReload", true);
           }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    changeProductTypeFun(val){
      console.info(val)
    },
  },
  mounted() {
    this.query = this.row;
    if(this.query.id>0){
      this.query.level=this.query.level.key
    }else{
       this.query.level='COUNTRY'
    }
  },
  beforeDestroy() {}
};
</script>
<style lang="less" scoped>
.editContent {
  margin: -30px -20px;
  border-top: 1px solid #ebecee;
  padding: 30px 20px;
.avatar-uploader {
    width: 120px;
    height: 120px;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 120px;
    height: 120px;
    line-height: 120px;
    text-align: center;
  }
  .avatar {
    width:120px;
    height: 120px;
    display: block;
  }
}
</style>
