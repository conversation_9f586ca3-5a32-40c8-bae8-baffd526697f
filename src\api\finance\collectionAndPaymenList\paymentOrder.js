import requestAxios from '@/utils/requestAxios'

// 获取列表数据
export function list(data) {
  return requestAxios({
    url: "/api/finance/admin/financePay/page",
    method: 'post',
    data
  })
}

export function detail(data) {
  return requestAxios({
    url: "/api/finance/admin/financePay/" + data,
    method: 'get',
  })
}

// 确认支付
export function confirmPay(data) {
  return requestAxios({
    url: "/api/finance/admin/financePay/confirmPay",
    method: 'post',
    data:data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 重新支付
export function rePay(data) {
  return requestAxios({
    url: "/api/finance/admin/financePay/loadPay/" + data,
    method: 'get',
  })
}

  // /api/finance/admin/financeCollect/page
