import requestAxios from '@/utils/requestAxios'
import request from '@/utils/request'
 
export function list(data) {
    return requestAxios({
        url: '/api/msgs/smsTask/page',
        method: 'post',
        data
    })
}


export function clearSmsTask(id) {
    return requestAxios({
        url: '/api/msgs/smsTask/' + id,
        method: "get"
    })
}


export function detail(id) {
    return requestAxios({
        url: '/api/msgs/smsTask/'+id,
        method:"get"
    })
}


export function exportExcel(data) {
    return requestAxios({
        url: '/api/msgs/smsTask/export',
        method: 'post',
        data
    })
}
