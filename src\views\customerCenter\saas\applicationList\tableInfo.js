import { deepClone } from "@/utils";

const initTableItem = [
    {   key: '0.0',
      label: '商家名称',
      name: "saleMerchantName",
      width:'240px',
      disabled: true
    },
    {   key: 0,
      label: '客户名称',
      name: "name",
      width:'240px',
      disabled: true
    },
    {   key: 2,
      label: '企业类型',
      name: "merchantTypeName",
      width:'136px',
      disabled: true
    },
    {   key: 4,
      label: '负责人',
      name: "ceoName",
      width:'110px'
    },
    {   key: 6,
      label: '所在区域/注册地址',
      name: "region",
      width:'224px'
    },
    {   key: '6.1',
      label: '企业资质',
      name: "merchantLicenseFileList",
      width:'160px'
    },
    {   key: 7,
      label: '客户来源',
      name: "customerSource",
      width:'144px'
    },
    {   key: 8,
      label: '所属业务员',
      name: "salesman<PERSON><PERSON>",
      width:'144px'
    },
    {   key: 9,
      label: '操作时间',
      name: "createTime",
      width:'224px'
    },
  ]

export default {
  ACCEPTED: deepClone(initTableItem),
  PENDING: deepClone(initTableItem),
  REJECTED: deepClone(initTableItem)
}
