<template>
  <div>
    <im-search-pad
      :is-expand.sync="isExpand"
      :model="model"
      @reset="reload"
      @search="searchLoad"
    >
      <im-search-pad-item prop="id">
        <el-input v-model="model.id" placeholder="请输入账单编号" />
      </im-search-pad-item>
      <im-search-pad-item prop="orderNumber">
        <el-input v-model="model.orderNumber" placeholder="请输入订单/退款单编号" />
      </im-search-pad-item>
      <im-search-pad-item prop="productSn">
        <el-input v-model="model.productSn" placeholder="请输入商品编码" />
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="productName">
        <el-input v-model="model.productName" placeholder="请输入商品名称" />
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="type">
        <el-select placeholder="账单类型" v-model="model.type" clearable>
          <el-option label="销售账单" value="SALE"></el-option>
          <el-option label="退款账单" value="REFUND"></el-option>
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="during">
        <el-date-picker
          type="daterange"
          range-separator="至"
          v-model="during"
          value-format="yyyy-MM-dd"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </im-search-pad-item>
    </im-search-pad>

    <div class="tab_bg">
      <div class="varietiesBan-list-container">
        <div class="varietiesBan-list-tabs-wrapper">
          <div class="varietiesBan-list-tabs">
            <template v-for="(tab, index) in tabs">
              <div v-if="!item.hide" class="tab" :class="{'active': currentTab == index}" 
                :key="index"
                @click="handleChangeTab(index,tab.value)">
                {{ tab.name }}（{{tab.count}}）
              </div>
            </template>
          </div>
          <div class="operations">
            <el-button  @click="$router.push({path: '/finance/businessStatement/salesman/list'})">返回</el-button>
            <!-- <el-button  v-if="checkPermission(['admin', 'salesmanDetail:export'])">导出</el-button> -->
            <el-button  @click="reload">刷新</el-button>
            <el-button  v-if="model.merchantsStatus!='UNSETTLED'&&checkPermission(['admin', 'admin-finance-salesmanBilling:application'])" type="primary" @click="handleBatch">+批量申请结算</el-button>
          </div>
        </div>
      </div>
      <div class="todo-settlement" v-if="model.merchantsStatus==='WAIT'">
        {{todoName}}的账单列表：已选<span class="text-primary">{{selectNumber}}</span>条账单，结算金额：<span class="text-warning">{{settleMoney}}</span>元，该业务员还有<span class="text-primary">{{toSelect}}</span>条账单可结算</span>
      </div>
      <table-pager ref="todoTable" :options="tableTitle" :remote-method="load" :data.sync="tableData" :pageSize="pageSize" :selection="true" :isNeedButton="false"  @selection-change="handleSelectionChange" @selection-all="handleSelectAll">
        <template slot="settlementOrderId" v-if="model.merchantsStatus===2||model.merchantsStatus===1">
          <el-table-column label="结算单号" width="110">
            <slot slot-scope="{row}">
              <span class="text-primary">{{row.settlementOrderId}}</span>
            </slot>
          </el-table-column>
        </template>
        <template slot="settlementSerialNumber" v-if="model.merchantsStatus===2">
          <el-table-column label="结款流水编号" width="110">
            <slot slot-scope="{row}">
              <span class="text-primary">{{row.settlementSerialNumber}}</span>
            </slot>
          </el-table-column>
        </template>
        <template slot="confSettlementTime" v-if="model.merchantsStatus===2">
          <el-table-column label="结算时间" width="170">
            <slot slot-scope="{row}">
              <span class="text-primary">{{row.confSettlementTime}}</span>
            </slot>
          </el-table-column>
        </template>
        <template slot="promotionAmount">
          <el-table-column label="单品推广金额（元）" width="150">
            <slot slot-scope="{row}">
              {{row.promotionAmount|getDecimals}}
            </slot>
          </el-table-column>
        </template>
        <template slot="promotionCountAmount">
          <el-table-column label="推广佣金合计（元）" width="150">
            <slot slot-scope="{row}">
              {{row.promotionCountAmount|getDecimals}}
            </slot>
          </el-table-column>
        </template>
      </table-pager>
    </div>
  </div>
</template>

<script>
  const TableColumns = [
    { label: "账单类型", name: "type.desc",prop: "type.desc",width: "150"},
    { label: "账单编号", name: "id", prop:"id",width: "170" },
    { label: "订单/退款单编号", name: "orderNumber", prop:"orderNumber",width: "170" },
    { label: "结算单号", name: "settlementOrderId", prop:"settlementOrderId",slot: true },
    { label: "结款流水编号", name: "settlementSerialNumber", prop:"settlementSerialNumber",slot: true },
    { label: "结算时间", name: "confSettlementTime", prop:"confSettlementTime",slot: true },
    { label: "商品编码", name: "productSn", prop:"productSn",width: "150" },
    { label: "推广商品", name: "productName", prop:"productName",width: "150" },
    { label: "生产厂家", name: "manufactorName", prop:"manufactorName",width: "150" },
    { label: "规格", name: "productSku", prop:"productSku",width: "150" },
    { label: "单品推广金额（元）", name: "promotionAmount", prop:"promotionAmount",width: "150",slot:true },
    { label: "订单商品数量", name: "quantity", prop:"quantity",width: "110" },
    { label: "推广佣金合计（元）", name: "promotionCountAmount",prop: 'promotionCountAmount',width: '150',slot:true },
    { label: "业务员编码", name: "salesmanSn", prop:'salesmanSn',width: '135' },
    { label: "业务员姓名", name: "name", prop:'name',width: '140'},
    { label: "业务员手机号", name: "contact",prop: 'contact',width: '110'},
    { label: "订单支付时间", name: "orderPaymentTime",prop:'orderPaymentTime',width: "170" },
    { label: "制单人", name: "createUserName",prop:'createUserName',width: "100" },
    { label: "制单时间", name: "createTime",prop:'createTime',width: "170" }
  ];
  const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({ key: i, ...TableColumns[i] });
  }
  import checkPermission from '../../../utils/permission';
  import { fbpDetailBatchSettlement,financeBillProductDetail,fbpDetailCountStatus,salesmanCountSettlement,fbpCount } from '@/api/finance'
  import SearchPad from '@/components/searchPad'
  export default {
    components: {
      SearchPad
    },
    data () {
      return {
        isExpand: false,
        loading: '',
        currentTab: 0,
        tabs: [
          { name: '可结算', value: 'WAIT',count: 0,hide: !checkPermission(['admin', 'admin-finance-salesmanBilling:canHandledView']) },
          { name: '结算中', value: 'PROCESS',count: 0,hide: !checkPermission(['admin', 'admin-finance-salesmanBilling:handlingView']) },
          { name: '已结算', value: 'FINISH',count: 0,hide: !checkPermission(['admin', 'admin-finance-salesmanBilling:handledView']) },
          { name: '未结算', value: 'UNSETTLED',count: 0,hide: !checkPermission(['admin', 'admin-finance-salesmanBilling:unhandledView']) }
        ],
        tableData: [],
        page: 1,
        pageSize: 10,
        totalPage: 0,
        total: 0,
        tableTitle: TableColumnList,
        ids: [],
        during: '',
        model: {
          id: '',
          type: '',
          orderNumber: '',
          merchantsStatus: 'WAIT',
          productSn: '',
          productName: ''
        },
        todoName: '',
        selectNumber: 0,
        settleMoney: 0,
        toSelect: 0,
        isShow: false,
        isCheckAll: false,
      }
    },
    mounted() {
      this.getCount()
      this.$nextTick(()=> {
        if (this.$route.query.currentTab) {
          this.currentTab = this.$route.query.currentTab
          this.handleChangeTab(this.currentTab,'FINISH')
        }
      })
    },
    methods: {
      checkPermission,
      //获取退款账单数
      async getFbCount() {
        const query = {
          salesmanId: this.$route.query.salesmanId
        }
        const {data} = await fbpCount(query)
        this.selectNumber = data.num
        this.settleMoney = data.amount
        this.toSelect = this.total - data.num
      },
      async getCount() {
        const query = {
          salesmanId: this.$route.query.salesmanId
        }
        const {data} = await fbpDetailCountStatus(query)
        this.tabs.forEach(item=>{
          item.count = data[item.value.toLowerCase()]
        })
      },
      handleSelectAll(val) {
        console.log('val----->',val);
        this.settleMoney = 0
        this.ids = []
        if (!this.isCheckAll) {
          this.tableData.map((item, index) => {
            this.ids.push(item.id)
          })
          this.isCheckAll = true
        } else {
          val.map((item, index) => {
            this.ids.push(item.id)
          })
          this.isCheckAll = false
        }
        this.selectNumber = Array.from(new Set(this.ids)).length
        this.toSelect = this.tabs[0].count - this.selectNumber
        if (this.ids.length > 0) {
          this.getSettleAmount()
        }
      },
      handleSelectionChange(val) {
        this.settleMoney = 0
        this.ids = []
        val.map((item, index) => {
          this.ids.push(item.id)
          this.settleMoney += item.settlementAmount
        })
        this.selectNumber = Array.from(new Set(this.ids)).length
        this.toSelect = this.tabs[0].count - this.selectNumber
        if (this.ids.length > 0) {
          this.getSettleAmount()
        }

      },
      async getSettleAmount() {
        const data = await salesmanCountSettlement({
          ids: Array.from(new Set(this.ids)),
          salesmanId: this.$route.query.salesmanId
        })
        if (data.code === 0) {
          this.settleMoney = data.data
        }
      },
      handleBatch() {
        if(this.ids.length === 0) {
          this.$message.warning('请至少选择一项进行操作！')
          return
        }
        this.$confirm('确定结算所选账单？','提示').then(_ => {
          fbpDetailBatchSettlement({
            ids: this.ids
          }).then(res=>{
            if(res.code === 0) {
              this.$message.success('操作成功！')
              this.ids = []
              this.handleRefresh({
                page: 1,
                pageSize: 10
              })
              this.getCount()
            }
          })

        }).catch(_ => {});
      },
      async load(params) {
        const listQuery = {
          model: {
            ...this.model,
            salesmanId: this.$route.query.salesmanId,
            startTime: this.during[0],
            endTime: this.during[1]
          }
        }
        Object.assign(listQuery, params)
        this.loading = true
        const {data} = await financeBillProductDetail(listQuery)
        this.todoName = data.records[0].name
        data.records.forEach((item,index)=>{
          if (item.type.code ==='REFUND' || item.merchantsStatus.code!=='WAIT') {
            item.selectable = true
            if (item.type.code ==='REFUND') {
              this.$nextTick(()=> {
                this.$refs.todoTable.$refs.table.toggleRowSelection(item,true)
                this.ids.push(item.id)
              })
            }
            // item.selectable = true
          } else {
            item.selectable = false
          }
        })
        this.total = data.total
        this.getFbCount();
        this.tableData = data.records
        this.loading = false
        return { data }

      },
      searchLoad() {
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      reload() {
        this.model={
          ...this.model,...{
            id: '',
            type: '',
            orderNumber: '',
            productSn: '',
            productName: ''
          }
        }
        this.during = ''
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      handleChangeTab (index,value) {
        this.currentTab = index
        this.model.merchantsStatus = value
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      handleRefresh(pageParams) {
        this.$refs.todoTable.doRefresh(pageParams)
      }
    }
  }
</script>

<style lang="less" scoped>
  .todo-settlement {
    width: 100%;
    height: 42px;
    margin-bottom: 20px;
    background-color: #effaff;
    border: 1px solid #81d3f8;
    padding: 0 12px;
    line-height: 42px;
    font-size: 14px;
  }
</style>
