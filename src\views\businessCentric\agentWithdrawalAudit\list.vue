<template>
  <div class="archivesPageContent">
    <im-search-pad
      :is-expand.sync="isExpand"
      :model="listQuery"
      @reset="resetForm('searchForm')"
      @search="onSearchSubmitFun"
    >
      <im-search-pad-item prop="salesmanCode">
        <el-input v-model="listQuery.model.salesmanCode" placeholder="请输入业务员编码" />
      </im-search-pad-item>
      <im-search-pad-item prop="realName">
        <el-input v-model="listQuery.model.realName" placeholder="请输入业务员姓名" />
      </im-search-pad-item>
      <im-search-pad-item prop="productCode">
        <el-input v-model="listQuery.model.productCode" placeholder="请输入商品编码" />
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="productName">
        <el-input v-model="listQuery.model.productName" placeholder="请输入商品名称" />
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="manufacturer">
        <el-input v-model="listQuery.model.manufacturer" placeholder="请输入生产厂家" />
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="cityValue">
        <el-cascader
          placeholder="请选择所在区域"

          v-model.trim="cityValue"
          :props="props"
          @change="cityChange"
          clearable
        >
        </el-cascader>
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        v-model.trim="listQuery.model.publishStatusEnum"
        :tabs="approvalStatusList"
        @change="chageTabsFun"
      >
        <template slot="button">
          <div></div>
        </template>
      </tabs-layout>
      <div class="table">
        <el-table
          ref="table"
          v-if="list"
          @selection-change="selectTableItemFun"
          v-loading="listLoading"
          :data="list"
          row-key="agencyAreaId"
          border
          fit
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column
            align="center"
            width="65"
            :render-header="renderHeader"
            fixed
          >
            <template slot-scope="scope">
              <span>{{ scope.$index + 1 }} </span>
            </template>
          </el-table-column>
          <el-table-column
            type="selection"
            width="55"
            align="center"
            fixed
          ></el-table-column>
          <el-table-column
            v-for="(item, index) in tableTitle"
            :key="index"
            :min-width="item.width ? item.width : '350px'"
            :label="item.label"
            show-overflow-tooltip
            align="left"
          >
            <template slot-scope="{ row }">
              <img
                v-if="item.name == 'pictIdS'"
                :src="row.pictIdS | imgFilter"
                width="50px"
              />
              <span v-else>{{ row[item.name] }}</span>
            </template>
          </el-table-column>

          <el-table-column
            fixed="right"
            align="center"
            label="操作"
            width="150"
            class="itemAction"
            v-if="listQuery.model.publishStatusEnum == 'PENDING'"
          >
            <template slot-scope="{ row }">
              <el-row class="table-edit-row">
                <span class="table-edit-row-item">
                  <el-button type="text"  @click="setAgree(row)">同意</el-button>
                </span>
                <span class="table-edit-row-item">
                  <el-button type="text"  @click="rejectFun(row)">驳回</el-button>
                </span>
              </el-row>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-if="total > 0"
          :pageSizes="[10, 20, 50, 100]"
          :total="total"
          :page.sync="listQuery.current"
          :limit.sync="listQuery.size"
          @pagination="getlist"
        />
      </div>
    </div>
    <el-dialog
      append-to-body
      title="驳回理由"
      :visible.sync="rejectFunFlag"
      width="400px"
      @close="close"
      :close-on-click-modal="false"
    >
      <!-- <el-form ref="rejectFun" :model="rejectFunForm">
        <el-form-item
          class="formItem"
          prop="remarks"
          label=""
          :rules="[
            { required: true, message: '请输入驳回理由', trigger: 'blur' },
          ]"
        > -->
      <el-input
        style="min-width: 200px"
        type="textarea"
        rows="4"
        v-model.trim="rejectFunForm"
        placeholder="请输入驳回理由"
      >
      </el-input>
      <!-- </el-form-item>
      </el-form> -->
      <span slot="footer" class="dialog-footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="rejectFunFormFun">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from "@/components/Pagination";
import { areas } from "@/api/enterprise";
import {
  getList,
  agree,
  reject,
} from "@/api/businessCentric/agentWithdrawalAudit";
import tableInfo from "@/views/businessCentric/agentWithdrawalAudit/tableInfo";
import TabsLayout from '@/components/TabsLayout'
export default {
  components: {
    Pagination,
    TabsLayout
  },
  data() {
    return {
      isExpand: false,
      listLoading: false,
      list: [],
      tabType: "list",
      listQuery: {
        current: 1,
        size: 10,
        model: {
          publishStatusEnum: "PENDING"
        },
      },
      total: 100,
      cityValue: [],
      tableTitle: [],
      rejectFunFlag: false,
      rejectFunForm: "",
      tableSelectTitle: [],
      multipleSelection: [],
      multipleSelectionId: [],
      showSelectTitle: false,
      props: {
        checkStrictly: true,
        lazy: true,
        async lazyLoad(node, resolve) {
          const { level } = node;
          let id = node.data ? node.data.id : "";
          let res = await areas({ parentId: id });
          let list = res.data;
          list.forEach((item) => {
            item.value = item.id;
            item.leaf = level >= 2;
          });
          resolve(list);
        },
      },
    };
  },
  computed: {
    approvalStatusList() {
      return [
        {
          name: '待审核',
          value: 'PENDING'
        },
        {
          name: '已撤销',
          value: 'REPEAL'
        },
        {
          name: '已驳回',
          value: 'REJECTED'
        }
      ]
    }
  },
  methods: {
    resetForm() {
      this.list = [];
      this.listQuery = {
        current: 1,
        size: 10,
        model: {
          publishStatusEnum: this.listQuery.model.publishStatusEnum,
        },
      };
      this.getlist()
    },
    async setAgree(row) {
      let { data } = await agree(row.salesmanProductId);
      if (data) {
        this.$message.success("已通过该业务员的撤销申请");
        this.listQuery = {
          current: 1,
          size: 10,
          model: {
            publishStatusEnum: "REPEAL",
          },
        };
        this.initTbaleTitle()
        this.getlist();
      }
    },
    splitString(val) {
      if (!val) {
        return "";
      }
      return val.split(",");
    },
    chageTabsFun() {
      this.list = [];
      this.listQuery.current = 1
      this.initTbaleTitle();
      this.getlist();
    },
    close() {
      this.rejectFunFlag = false;
    },
    rejectFun(row) {
      this.rejectFunFlag = true;
      this.row = row;
    },
    async rejectFunFormFun() {
      if (this.rejectFunForm.length == 0) {
        this.$message.error("请输入驳回原因");
        return;
      }
      let { data } = await reject({
        id: this.row.salesmanProductId,
        reject: this.rejectFunForm
      });
      this.row = {};
      this.rejectFunForm = "";
      this.rejectFunFlag = false;
      if (data) {
        this.$message.success("已驳回该业务员的撤销申请");
        this.listQuery = {
          current: 1,
          size: 10,
          model: {
            publishStatusEnum: "REJECTED",
          },
        };
        this.initTbaleTitle()
        this.getlist();
      }
    },
    cityChange(e) {
      this.listQuery.model.provinceId = e[0];
      this.listQuery.model.cityId = e[1];
      this.listQuery.model.countyId = e[2];
    },
    selectTableItemFun: function (val) {
      // let arr = [];
      // val.forEach((item) => {
      //   arr.push(item.id);
      // });
      // this.multipleSelection = val;
      // this.multipleSelectionId = arr;
    },
    onSearchSubmitFun() {
      this.list = [];
      this.getlist();
    },
    async getlist() {
      this.listLoading = true;
      let { data } = await getList(this.listQuery);
      this.listLoading = false;
      this.list = data.records;
      this.total = data.total;
    },
    initTbaleTitle() {
      this.tableSelectTitle = [];
      this.tableTitle = tableInfo[this.listQuery.model.publishStatusEnum];
    },
    renderHeader(h, { column }) {
      var titles = tableInfo[this.listQuery.model.publishStatusEnum];
      var titlesName = ["显示字段项", "隐藏字段项"];
      return (
        <div style="position:relative">
          <div onClick={this.showHeaer}>
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="640px"
            center
            append-to-body={true}
          >
            <el-transfer
              vModel={this.tableSelectTitle}
              data={titles}
              titles={titlesName}
              onChange={this.setleftTitleFun}
            ></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button onClick={this.closeHeaer}>取消</el-button>
              <el-button type="primary" onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      );
    },
    setleftTitleFun(val) {
      this.tableSelectTitle = val;
    },
    showHeaer: function () {
      this.showSelectTitle = true;
    },
    closeHeaer: function () {
      this.showSelectTitle = false;
      this.tableSelectTitle = [];
    },
    setHeaer: function () {
      var titles = tableInfo[this.listQuery.model.publishStatusEnum];
      var listinfo = titles.filter((element, index, self) => {
        return !this.tableSelectTitle.includes(element.key);
      });
      this.tableTitle = listinfo;
      this.showSelectTitle = !this.showSelectTitle;
    },
  },
  created() {
    this.initTbaleTitle();
    this.getlist();
  }
};
</script>


<style lang="less" scoped>
.archivesPageContent {
  padding: 0;
  .temp_searchBox {
    height: 64px;
    overflow: hidden;
    margin-bottom: 0;
  }
  .form-inline {
    height: 60px;
    overflow: hidden;
  }
  .title {
    border-bottom: 2px solid #ebecee;
    margin-bottom: 16px;
    span {
      margin-bottom: -2px;
      padding: 0 15px;
      height: 40px;
      line-height: 30px;
      display: block;
      background: rgba(255, 255, 255, 0);
      border-bottom: 2px solid rgb(64, 158, 255);
      font-size: 16px;
      font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
        "PingFang SC"-400;
      font-weight: 400;
      color: rgb(64, 158, 255);
    }
  }

  .formItem {
    width: 586px;
  }
  .line {
    color: #dfe6ec;
    margin: 0 6px;
  }
  .typeTabs {
    height: 40px;
    margin-bottom: -2px;
  }
}
/deep/ .el-dialog__footer {
  margin-top: -30px;
  border-top: 1px solid #ddd;
}
</style>
