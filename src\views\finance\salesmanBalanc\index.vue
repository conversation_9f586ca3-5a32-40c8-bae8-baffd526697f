<template>
  <div class="archivesPageContent">
    <im-search-pad
      :has-expand="isExpand"
      :model="listQuery"
      @reset="resetForm"
      @search="onSearchSubmitFun"
    >
      <im-search-pad-item prop="salesmanCode">
        <el-input v-model="listQuery.model.salesmanCode" placeholder="请输入业务员编码" />
      </im-search-pad-item>
      <im-search-pad-item prop="realName">
        <el-input v-model="listQuery.model.realName" placeholder="请输入业务员姓名" />
      </im-search-pad-item>
      <im-search-pad-item prop="contactNumber">
        <el-input v-model="listQuery.model.contactNumber" placeholder="请输入业务联系方式" />
      </im-search-pad-item>
      <im-search-pad-item prop="positionStatus">
        <el-select
          v-model="listQuery.model.positionStatus"
          placeholder="请选择在职状态"
        >
          <!-- <el-option label="全部" value=""></el-option> -->
          <el-option label="在职" value="WORKING"></el-option>
          <el-option label="离职" value="QUIT"></el-option>
        </el-select>
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        :tabs="[ { name: '业务员余额' } ]"
      >
        <template slot="button">
          <div></div>
        </template>
      </tabs-layout>
      <div class="table">
        <el-table
          ref="table"
          v-if="list"
          @selection-change="selectTableItemFun"
          v-loading="listLoading"
          :data="list"
          row-key="id"
          border
          fit
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column
            align="center"
            width="65"
            :render-header="renderHeader"
            fixed
          >
            <template slot-scope="scope">
              <span>{{ scope.$index + 1 }} </span>
            </template>
          </el-table-column>
          <el-table-column type="selection" width="55" fixed></el-table-column>
          <el-table-column
            v-for="(item, index) in tableTitle"
            :key="index"
            :min-width="item.width ? item.width : '350px'"
            :label="item.label"
            show-overflow-tooltip
            align="left"
          >
            <template slot-scope="{ row }">
            <span v-if="item.name == 'amount'">
              <span v-if="row[item.name] > 0" style="color: #70b603">{{
                  row[item.name]|getDecimals
                }}</span>
              <span v-else style="color: #f59a23">{{ row[item.name]|getDecimals }}</span>
            </span>
              <span
                v-else-if="item.name == 'status'"
                :style="row.salesman[item.name].code != 'ENABLED' ? 'color:#ff0066' : ''"
              >
              {{ row.salesman[item.name].code == "ENABLED" ? "已启用" : "已冻结" }}
            </span>
              <!-- <span v-else-if="item.name == 'status'">已{{row.salesman[item.name].desc}}</span> -->
              <span v-else-if="item.name == 'positionStatus'">{{row.salesman[item.name].desc}}</span>
              <span v-else>{{ row.salesman[item.name] }}</span>
            </template>
          </el-table-column>

          <el-table-column
            fixed="right"
            align="center"
            label="操作"
            width="80"
            class="itemAction"
          >
            <template slot-scope="{ row }">
              <el-row class="table-edit-row">
                <span class="table-edit-row-item" v-if="checkPermission(['admin', 'admin-finance-salesmanAccount:detail'])">
                  <el-button type="text"  @click="detailFun(row)">查看详情</el-button>
                </span>
              </el-row>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-if="total > 0"
          :pageSizes="[10, 20, 50, 100]"
          :total="total"
          :page.sync="listQuery.current"
          :limit.sync="listQuery.size"
          @pagination="getlist"
        />
      </div>
    </div>
  </div>
</template>

<script>
import Pagination from "@/components/Pagination";
import { areas } from "@/api/enterprise";
import { list } from "@/api/finance/salesmanBalanc/index";
import tableInfo from "@/views/finance/salesmanBalanc/tableInfo";
import { setContextData, getContextData } from "@/utils/auth";
import TabsLayout from '@/components/TabsLayout'
import checkPermission from '@/utils/permission';

export default {
  data() {
    return {
      isExpand: false,
      listLoading: false,
      list: [],
      tabType: "list",
      listQuery: {
        current: 1,
        size: 10,
        model: {
          salesmanCode:"",
          realName:"",
          contactNumber:"",
          positionStatus:"WORKING"
        },
      },
      total: 100,
      cityValue: [],
      tableTitle: [],
      tableSelectTitle: [0, 1, 2, 3],
      multipleSelection: [],
      multipleSelectionId: [],
      showSelectTitle: false,
      props: {
        lazy: true,
        async lazyLoad(node, resolve) {
          const { level } = node;
          let id = node.data ? node.data.id : "";
          let res = await areas({ parentId: id });
          let list = res.data;
          list.forEach((item) => {
            item.value = item.id;
            item.leaf = level >= 2;
          });
          resolve(list);
        },
      },
    };
  },
  methods: {
    checkPermission,
    resetForm() {
      this.listQuery = {
        current: 1,
        size: 10,
        model: {
          salesmanCode:"",
          realName:"",
          contactNumber:"",
          positionStatus:"WORKING"
        }
      };
      this.getlist()
    },
    detailFun(row) {
      console.log(row);
      setContextData("salesmanBalanc_detail", this.listQuery);
      this.$router.push({
        path: "/finance/salesmanBalanc/detail",
        query: {
          id: row.salesmanId,
          name: row.salesman.realName,
        },
      });
    },
    cityChange(e) {
      this.listQuery.model.provinceId = e[0];
      this.listQuery.model.cityId = e[1];
      this.listQuery.model.countyId = e[2];
    },
    selectTableItemFun: function (val) {
      // let arr = [];
      // val.forEach((item) => {
      //   arr.push(item.id);
      // });
      // this.multipleSelection = val;
      // this.multipleSelectionId = arr;
    },
    onSearchSubmitFun() {
      this.getlist();
    },
    async getlist() {
      this.listLoading = true;
      let { data } = await list(this.listQuery);
      this.listLoading = false;
      this.total = data.total;
      this.list = data.records;
    },

    initTbaleTitle() {
      this.tableSelectTitle = [];
      this.tableTitle = tableInfo[this.tabType];
    },
    renderHeader(h, { column }) {
      var titles = tableInfo[this.tabType];
      var titlesName = ["显示字段项", "隐藏字段项"];
      return (
        <div style="position:relative">
          <div onClick={this.showHeaer}>
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="640px"
            center
            append-to-body={true}
          >
            <el-transfer
              vModel={this.tableSelectTitle}
              data={titles}
              titles={titlesName}
              onChange={this.setleftTitleFun}
            ></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button onClick={this.closeHeaer}>取消</el-button>
              <el-button type="primary" onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      );
    },
    setleftTitleFun(val) {
      this.tableSelectTitle = val;
    },
    showHeaer: function () {
      this.showSelectTitle = true;
    },
    closeHeaer: function () {
      this.showSelectTitle = false;
      this.tableSelectTitle = [];
    },
    setHeaer: function () {
      var titles = tableInfo[this.tabType];
      var listinfo = titles.filter((element, index, self) => {
        return !this.tableSelectTitle.includes(element.key);
      });
      this.tableTitle = listinfo;
      this.showSelectTitle = !this.showSelectTitle;
    },
  },
  created() {
    this.initTbaleTitle();
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (from.path == "/finance/salesmanBalanc/detail") {
        if (getContextData("salesmanBalanc_detail") != "") {
          vm.listQuery = getContextData("salesmanBalanc_detail");
        }
      }
      vm.initTbaleTitle();
      vm.getlist();
    });
  },
  components: {
    Pagination,
    TabsLayout
  },
};
</script>


<style lang="scss" scoped>
.archivesPageContent {
  padding: 0;
  .temp_searchBox {
    height: 64px;
    overflow: hidden;
    margin-bottom: 0;
  }
  .form-inline {
    height: 60px;
    overflow: hidden;
  }
  .title {
    margin-bottom: 16px;
    span {
      margin-bottom: -2px;
      padding: 0 15px;
      height: 40px;
      line-height: 30px;
      display: block;
      background: rgba(255, 255, 255, 0);
      border-bottom: 2px solid rgb(64, 158, 255);
      font-size: 16px;
      font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
        "PingFang SC"-400;
      font-weight: 400;
      color: rgb(64, 158, 255);
    }
  }

  .formItem {
    width: 586px;
  }
  .line {
    color: #dfe6ec;
    margin: 0 6px;
  }
  .typeTabs {
    height: 40px;
    margin-bottom: -2px;
  }
}
</style>
