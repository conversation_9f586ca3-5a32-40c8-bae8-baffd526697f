<template>
    <div class="temp_bannerTableButton">
        <el-button style="width:100px;padding:0 -10px; text-indent: -10px;" @click="showBannerTableDialog=true" >从品牌库添加</el-button>
        <el-dialog append-to-body title="品牌库" :visible.sync="showBannerTableDialog" :before-close="closeDialogFun" :width="'60%'">
            <div class="showBannerTable">
              <im-search-pad
                :has-expand="false"
                :model="listQuery"
                @reset="resetForm('searchForm')"
                @search="getList"
              >
                <im-search-pad-item prop="brandName">
                  <el-input v-model="listQuery.model.brandName" placeholder="请输入品牌名称" />
                </im-search-pad-item>
              </im-search-pad>
                <div class="table">
                    <el-table ref="table" v-loading="listLoading"  :data="list"   border fit highlight-current-row  style="width: 100%" >
                        <el-table-column  align="center" width="80" label="LOGO" show-overflow-tooltip class-name="img-cell">
                            <template slot-scope="{ row }">
                                <el-popover  v-if="row.pictIdS!=''" placement="right" trigger="hover" >
                                    <el-image style="width: 200px; height: 200px" fit="contain" :src="row.brandLogo"/>
                                    <el-image slot="reference" style="width: 50px; height: 50px" fit="cover" :src="row.brandLogo"/>
                                </el-popover>
                            </template>
                        </el-table-column>
                        <el-table-column  align="left" prop="brandName" width="250" label="品牌" show-overflow-tooltip></el-table-column>
                        <el-table-column  align="left" prop="manufacturer"  label="生产厂家" show-overflow-tooltip></el-table-column>
                        <el-table-column fixed="right" align="center" label="操作" width="100" class="itemAction">
                            <template slot-scope="{ row }">
                                <el-button  @click="selectItem(row)" type="text" >选中</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    <pagination v-show="total>0" :total="total" :page.sync="listQuery.current" :limit.sync="listQuery.size" @pagination="getList" />
                </div>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import { list } from '@/api/products/brand'
export default {
    data() {
        return {
            showBannerTableDialog:false,
            listQuery: {
                model:{},
                current: 1,
                size: 10
            },
            list: [],
            total: 0,
            listLoading: true,
        };
    },
    components:{
        Pagination
    },
    computed: {
        selectRowVal: {
            get() {
               this.selectRowVal
            },
            set(val) {
                console.info(val)
                this.$emit('update:selectRowId', val.id)
                this.$emit('update:selectRowName', val.brandName)
            }
        }
    },
    props: {
        selectRowId:{
            type:String,
            default:"",
            required: true
        },
        selectRowName:{
            type:String,
            default:"",
            required: true
        },
    },
    methods: {
        async getList() {
        this.listLoading = true
        const { data } = await list(this.listQuery)
        if(data!=null){
            this.list = data.records
            this.total = data.total
        }
        this.listLoading = false
        },
        closeDialogFun(){
            this.showBannerTableDialog=false;
        },
        selectItem(row){
            this.showBannerTableDialog=false;
            this.selectRowVal=row;
        }
    },
    mounted() {
        this.getList()
    },
    beforeDestroy() {}
};
</script>
<style lang="less" scoped>
.temp_bannerTableButton{width:100%;

}
.showBannerTable{
    margin: -30px -20px;
    border-top: 1px solid #ebecee;
    padding: 10px 20px;
}
.temp_searchBox{height:45px;overflow: hidden; margin-bottom: 0;  padding:0; border-left:none;}

</style>
