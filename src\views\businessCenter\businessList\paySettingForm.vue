<template>
  <div class="form_wrapper">
    <div class="form" v-loading="getSetitngLoading">
      <el-form :model="form[formKey]" :rules="rules[formKey]" ref="form" label-width="120px">
        <el-form-item label="支付方式：">
          <el-select v-model="formKey" placeholder="请选择支付方式" @change="changeFormKey">
            <el-option
              v-for="item in payModeList[paySource]"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="启用状态：" prop="whetherEnabled">
          <el-radio-group v-model="form[formKey].whetherEnabled">
            <el-radio label="Y">启用</el-radio>
            <el-radio label="N">停用</el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- 微信支付 -->
        <template v-if="['1', '3'].includes(formKey)">
          <el-form-item label="商户号：" prop="mchId">
            <el-input v-model="form[formKey].mchId" placeholder="请输入商户号mchId" />
          </el-form-item>
          <el-form-item label="APPID：" prop="appId">
            <el-input v-model="form[formKey].appId" placeholder="请输入微信appid" />
          </el-form-item>
          <el-form-item label="支付密钥：" prop="mchKey">
            <el-input v-model="form[formKey].mchKey" placeholder="请输入微信mchKey" />
          </el-form-item>
          <el-form-item label="签名地址：" prop="keyPath">
            <el-input v-model="form[formKey].keyPath" placeholder="请输入微信keyPath" />
          </el-form-item>
        </template>
        <!-- 银联支付(PC端) -->
        <template v-else-if="formKey === '0'">
          <el-form-item label="终端号：" prop="tid">
            <el-input v-model="form[formKey].tid" placeholder="请输入终端号tid" />
          </el-form-item>
          <el-form-item label="商户号：" prop="mchId">
            <el-input v-model="form[formKey].mchId" placeholder="请输入商户号mchid" />
          </el-form-item>
          <el-form-item label="APPID：" prop="subAppId">
            <el-input v-model="form[formKey].subAppId" placeholder="请输入微信商户appid" />
          </el-form-item>
          <el-form-item label="子APPID：" prop="subMid">
            <el-input v-model="form[formKey].subMid" placeholder="请输入子微信商户appid" />
          </el-form-item>
          <el-form-item label="MD5密钥：" prop="md5">
            <el-input v-model="form[formKey].md5" placeholder="请输入MD5密钥" />
          </el-form-item>
          <el-form-item label="来源编号：" prop="sourceNo">
            <el-input v-model="form[formKey].sourceNo" placeholder="请输入来源编号" />
          </el-form-item>
        </template>
        <!-- 银联支付(小程序端) -->
        <template v-else-if="formKey === '5'">
          <el-form-item label="终端号：" prop="tid">
            <el-input v-model="form[formKey].tid" placeholder="请输入终端号tid" />
          </el-form-item>
          <el-form-item label="商户号：" prop="mchId">
            <el-input v-model="form[formKey].mchId" placeholder="请输入微信appid" />
          </el-form-item>
          <el-form-item label="采购小程序：" prop="subAppId">
            <el-input v-model="form[formKey].subAppId" placeholder="请输入采购商小程序appid" />
          </el-form-item>
          <el-form-item label="助手小程序：" prop="saleAppId">
            <el-input v-model="form[formKey].saleAppId" placeholder="请输入业务员小程序appid" />
          </el-form-item>
          <el-form-item label="MD5密钥：" prop="md5">
            <el-input v-model="form[formKey].md5" placeholder="请输入MD5密钥" />
          </el-form-item>
          <el-form-item label="来源编号：" prop="sourceNo">
            <el-input v-model="form[formKey].sourceNo" placeholder="请输入来源编号" />
          </el-form-item>
        </template>
        <!-- 银行汇款 -->
        <template v-else-if="formKey === '4'">
          <el-form-item label="银行账户：" prop="bankAccount">
           <span>{{ bankAccount }}</span>
          </el-form-item>
          <el-form-item label="银行账号：" prop="bankNumber">
            <span>{{ bankNumber }}</span>
          </el-form-item>
          <el-form-item label="开户银行：" prop="bankName">
            <span>{{ bankName }}</span>
          </el-form-item>
        </template>
      </el-form>
    </div>
    <div class="footer">
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import { getPaySetting, savePaySetting } from '@/api/businessCenter/businessList'

const initForm = {
  whetherEnabled: 'N' // 是否启用
}
const commPayModeList = [
  {
    label: '银行汇款',
    value: '4'
  },
  { label: '银行承兑', value: '8' },
  { label: '货到付款', value: '7' }
]
const wxRules = {
  mchId: [{ required: true, message: '请输入商户号mchId', trigger: 'blur'}],
  appId: [{ required: true, message: '请输入微信appid', trigger: 'blur'}],
  mchKey: [{ required: true, message: '请输入支付密钥mchKey', trigger: 'blur'}],
  keyPath: [{ required: true, message: '请输入签名地址keyPath', trigger: 'blur'}],
}
const ylRules = {
  mchId: [{ required: true, message: '请输入商户号mchId', trigger: 'blur'}],
  tid: [{ required: true, message: '请输入终端号tid', trigger: 'blur'}],
  md5: [{ required: true, message: '请输入MD5密钥', trigger: 'blur'}],
  sourceNo: [{ required: true, message: '请输入来源编号', trigger: 'blur'}],
}
const getInitPaySettingObj = () => {
  return {
    3: null, // 微信(pc)
    1: null, // 微信(小程序,)
    0: null, // 银联支付(pc),
    5: null, // 银联支付(小程序),
    4: null, // 银行汇款,
    7: null, // 货到付款
    8: null // 银行承兑
  }
}

export default {
  name: 'paySettingForm',
  props: {
    paySource: {
      type: String,
      default: '' // PC | WECHAT
    },
  },
  computed: {
    bankAccount() {
      return this.paramListDataVal(4, 'bankAccount')
    },
    bankNumber() {
      return this.paramListDataVal(4, 'bankNumber')
    },
    bankName() {
      return this.paramListDataVal(4, 'bankName')
    }
  },
  watch: {
    paySource(val) {
      if (val === '') return
      const formKey = val === 'PC' ? '3' : '1'
      this.formKey = formKey
      this.setPaySetting(formKey, this.saleMerchantId)
    }
  },
  data() {
    return {
      getSetitngLoading: false, // 获取支付设置loading
      loading: false, // 确定loading
      // 商家id
      saleMerchantId: '',
      // 支付选择列表
      payModeList: {
        PC: [
          {
            label: '微信（PC端）',
            value: '3'
          },
          {
            label: '银联支付（PC端）',
            value: '0'
          },
          ...commPayModeList
        ],
        WECHAT: [
          {
            label: '微信（小程序端）',
            value: '1'
          },
          {
            label: '银联支付（小程序端）',
            value: '5'
          },
          ...commPayModeList
        ]
      },
      // 支付设置
      paySettingObj: getInitPaySettingObj(),
      // 当前选择支付方式
      formKey: '3',
      form: {
        // 微信(pc/小程序)
        1: {
          ...initForm,
          appId: '',
          mchId: '',
          mchKey: '',
          keyPath: ''
        },
        3: {
          ...initForm,
          appId: '',
          mchId: '',
          mchKey: '',
          keyPath: ''
        },
        // 银联(pc)
        0: {
          ...initForm,
          mchId: '',
          tid: '',
          subAppId: '',
          subMid: '',
          md5: '',
          sourceNo: ''
        },
        // 银联(小程序)
        5: {
          ...initForm,
          mchId: '',
          tid: '',
          subAppId: '',
          saleAppId: '',
          md5: '',
          sourceNo: ''
        },
        // 银行汇款(pc/小程序)
        4: {
          ...initForm
        },
        // 银行承兑(pc/小程序)
        8: {
          ...initForm
        },
        // 货到付款(pc/小程序)
        7: {
          ...initForm,
        }
      },
      // 校验规则
      rules: {
        1: wxRules,
        3: wxRules,
        0: {
          ...ylRules,
          subAppId: [{ required: true, message: '请输入微信商户appid', trigger: 'blur'}],
          subMid: [{ required: true, message: '请输入微信子商户appid', trigger: 'blur'}]
        },
        5: {
          ...ylRules,
          subAppId: [{ required: true, message: '请输入采购商小程序appid', trigger: 'blur'}],
          saleAppId: [{ required: true, message: '请输入业务员小程序appid', trigger: 'blur'}]
        }
      }
    }
  },
  methods: {
    // paramList存在并且有数据
    paramListDataVal(id, key) {
      let val = '-'
      const currentPaySetting = this.paySettingObj[id]
      if (!currentPaySetting) return val
      if (!Array.isArray(currentPaySetting.paramList) || Array.isArray(currentPaySetting.paramList) && currentPaySetting.paramList.length === 0) return val
      const item = currentPaySetting.paramList.find(item => item.key === key)
      if (!item) return val
      val = item.val || '-'
      return val
    },
    // 点击取消
    cancel() {
      this.$emit('cancel')
    },
    // 点击确定
    confirm() {
      this.$refs.form.validate((valid) => {
        if (!valid) return
        const data = this.getSubmitData()
        this.loading = true
        savePaySetting(data).then(()=>{
          this.$message.success("修改成功")
          this.$emit('confirm')
        }).finally(()=>{
          this.loading = false
        })
      })
    },
    // 初始化
    init(id, saleMerchantId) {
      if (!saleMerchantId) return
      this.formKey = '3'
      this.saleMerchantId = saleMerchantId
      this.paySettingObj = getInitPaySettingObj();
      if (this.$refs.form) {
        this.$refs.form.resetFields()
      }
      this.setPaySetting(id, saleMerchantId)
    },
    // 设置支付设置
    setPaySetting(id, saleMerchantId) {
      if (this.paySettingObj[id]) return
      this.getSetitngLoading = true
      getPaySetting(id, saleMerchantId).then((res) => {
        this.paySettingObj[id] = res
        let paramList = res.paramList
        if (!Array.isArray(paramList) || Array.isArray(paramList) && paramList.length === 0) {
          this.$message.warning(`id为${id}的paramList异常,请联系管理员`); return
        }
        let currentForm = this.form[id]
        currentForm.whetherEnabled = res.whetherEnabled.code || 'N'
        paramList.forEach(item => {
          if (currentForm.hasOwnProperty(item.key)) {
            currentForm[item.key] = item.val
          }
        })
      }).finally(()=>{
        this.getSetitngLoading = false
      })
    },
    // 改变支付方式
    changeFormKey(val) {
      this.$nextTick(()=>{
        this.$refs.form.clearValidate()
        this.setPaySetting(val, this.saleMerchantId)
      })
    },
    // 获取提交数据
    getSubmitData() {
      const { formKey } = this
      const currentPaySetting = this.paySettingObj[formKey]
      if (!currentPaySetting) {
        this.$message.warning("数据未成功加载, 请重新尝试"); return
      }
      let paramList = currentPaySetting.paramList
      if (!Array.isArray(paramList) || Array.isArray(paramList) && paramList.length === 0) {
        this.$message.warning(`id为${formKey}的paramList异常,请联系管理员`); return
      }
      // 银行汇款和货到付款回传原本的paramList
      if (!['4', '7'].includes(formKey)) {
        const currentForm = this.form[formKey]
        paramList.forEach(item => {
          if (currentForm.hasOwnProperty(item.key)) {
            item.val = currentForm[item.key]
          }
        })
      }
      return {
        id: formKey,
        paramList: paramList,
        saleMerchantId: this.saleMerchantId,
        whetherEnabled: this.form[formKey].whetherEnabled
      }
    }
  }
}
</script>

<style lang="less" scoped>
.form_wrapper {
  box-sizing: border-box;
  padding-bottom: 15px;
  .form {
    box-sizing: border-box;
    padding-right: 20px;
  }
  .footer {
    text-align: right;
    box-sizing: border-box;
    padding-right: 20px;
    padding-top: 15px;
    border-top: 1px solid #ddd;
  }
}
</style>
