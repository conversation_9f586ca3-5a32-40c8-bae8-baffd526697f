<template>
  <el-dialog v-loading="loading" title="选择区域" :visible.sync="visible" width="30%" append-to-body :before-close="handleClose">
    <div class="dialogContent">
      <el-tree ref="areaTree" 
        :data="areaData" 
        show-checkbox 
        @check="handleCheck"
        :default-checked-keys="defaultCheckedKeys" 
        node-key="id">
      </el-tree>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" :loading="loading" @click="handleConfirm">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import _ from "lodash";
  import { mapState,mapGetters,mapActions} from 'vuex'
  import {getTreeNew,orgaStructure} from "@/api/organization/index";

  export default {
    props: {
      title: {
        type: String,
        default: "选择区域",
      },
      regionList: {
        type: Array,
        default: [],
      }
    },
    data() {
      return {
        loading: false,
        visible: false,
        currentTree: "", //当前选中的树
        checkList: [],
        defaultCheckedKeys: [], //默认勾选的树节点
        treeData: [],
        areaData:[],
      };
    },
    computed: {
      // 计算属性computed :
      // 1. 支持缓存，只有依赖数据发生改变，才会重新进行计算
      // 2. 不支持异步，当computed内有异步操作时无效，无法监听数据的变化
      // 3. computed 属性值会默认走缓存，计算属性是基于它们的响应式依赖进行缓存的，也就是基于data中声明过或者父组件传递的props中的数据通过计算得到的值
      // 4. 如果一个属性是由其他属性计算而来的，这个属性依赖其他属性，是一个多对一或者一对一，一般用computed
      // 5.如果computed属性属性值是函数，那么默认会走get方法；函数的返回值就是属性的属性值；在computed中的，属性都有一个get和一个set方法，当数据变化时，调用set方法。
        ...mapGetters([
        'organizationNavNode',
        "organizationInfo",
        "paremDepartmentInfo",
        "departmentInfo",
      ]),
    
    },
    mounted () {
      
    },
    watch:{
      regionList(newVal,oldVla){
        this.defaultCheckedKeys = newVal;
      },
      visible(val){
        if(!val) {
          this.$refs.areaTree.setCheckedKeys([]);
        }
      },
    },
    methods: {
     async show() {
        let _this=this;
        this.visible = true;
        await  this.load()
        this.$nextTick(function(){
          _this.$refs.areaTree.setCheckedKeys(_this.regionList);
        })
      },
      async load () {
        this.loading = true
        const { data } = await getTreeNew({
          organizationId:this.organizationInfo.id,
           // 上级部门id	
          departmentId: (this.paremDepartmentInfo==null||this.organizationInfo.id == this.paremDepartmentInfo.id)?null:this.paremDepartmentInfo.id,
          // 当前部门id	
          presentDepartmentId:(this.organizationNavNode.length>=2)? this.departmentInfo.id:null,
          tag:this.organizationNavNode.length==1?1:0,
        })
        this.loading = false
        this.areaData = data || [];
      },
      handleClose() {
        this.visible = false
      },
      handleCheck(node, tree) {},

      checkDataDeal() {
        const categories = _.cloneDeep(this.$refs.areaTree.getCheckedNodes().concat(this.$refs.areaTree
          .getHalfCheckedNodes()));
        console.info(" 获取选中的节点",this.$refs.areaTree.getCheckedNodes())
        console.info("目前半选中的节点所组成的数组",this.$refs.areaTree.getHalfCheckedNodes())

        console.info(categories)

        _(categories).forEach(f => {
          f.children = _(categories).filter(g => g.parentId === f.id).value()
        })
        this.treeData = _(categories).filter(f => f.parentId === null||f.parentId === '0').value()
        console.info("this.treeData",this.treeData)

        // this.treeData
        let list = JSON.parse(JSON.stringify(this.treeData));
        list = list.map(item => {
          // console.info("item",item)
          let obj = this.areaData.find(i => i.id == item.id);
          // console.log('obj', obj);
          let cityObj = item;
          if (obj!=undefined&&obj.children.length>1&&item.children.length == obj.children.length) {
             // 默认值 为全选
            cityObj.isAll = true; 
            cityObj.children = cityObj.children.map(o => {
              o.isAll = true;
              return o;
            })
            item.children.forEach(function(item){
              obj.children.forEach(function(objitem){
                if(objitem.id==item.id){
                  if(objitem.children.length != item.children.length){
                        console.info(objitem.children,item.children)
                        // 判断children 是否相同 
                         cityObj.isAll = false;
                          cityObj.children = cityObj.children.map(o => {
                            if(o.id==item.id){
                              o.isAll = false;
                            }
                            return o;
                          })
                      }
                    }
                  })  
             })   
          }else  if (obj!=undefined&&obj.children.length==1&&item.children[0].children.length == obj.children[0].children.length) {
            // 单个市是全部
            cityObj.isAll = true;
            cityObj.children = cityObj.children.map(o => {
              o.isAll = true;
              return o;
            })
          } else {
            cityObj.isAll = false;
            cityObj.children = cityObj.children.map(o => {
              let chilObj = obj.children.find(_o => _o.id == o.id);
              if (o.children.length == chilObj.children.length) {
                o.isAll = true;
              } else {
                o.isAll = false;
              }
              return o;
            })
          };
          return cityObj;
        });
        return list;
      },
      handleConfirm() {
        let checkedKeysTrue = this.$refs["areaTree"].getCheckedKeys(true);
        // if()
        let isAll = checkedKeysTrue.findIndex(item => item == '0')
        console.log('isAll', isAll);
        // return
        if (isAll != -1) {
          // 有全国
          if (checkedKeysTrue.length > 1) {
            // 有全国 有混合
            this.$message.warning('全国和其他省不能同时选择');
            return
          } else {
            // 只有全国
            this.$emit('on-confirm', [],true);  
          }
        } else {
          // 没有全国
          let dataList = this.checkDataDeal();
          console.log('没有全国',dataList);
          this.$emit('on-confirm', dataList,false);
        }
        this.visible = false
      },
      
    },
  };

</script>

<style scoped>
.dialogContent {
  max-height: 50vh;
  white-space: nowrap;
  overflow-x: auto;
}
</style>
