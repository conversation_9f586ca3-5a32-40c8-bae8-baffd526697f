import Layout from '@/layout'

const purchasingAgent = {
  path: '/purchasingAgent',
  name: 'purchasingAgent',
  redirect: 'purchasingAgent/registerCheck',
  meta: {
    title: '客户中心',
    icon: 'component'
  },
  component: Layout,
  children: [
    {
      path: 'registerCheck',
      name: 'registerCheck',
      meta: { title: '注册审批' },
      component: () => import('@/views/purchasingAgent/registerCheck/list')
    },
    {
      path: 'registerCheck/detail',
      name: 'registerCheckDetail',
      hidden: true ,
      meta: { title: '采购商注册档案', activeMenu: '/purchasingAgent/registerCheck' },
      component: () => import('@/views/purchasingAgent/registerCheck/detail')
    },
    {
      path: 'purchasingAgentlist',
      name: 'purchasingAgentList',
      meta: { title: '客户档案列表' },
      component: () => import('@/views/purchasingAgent/archivesList/list')
    },
    {
      path: 'purchasingAgentlist/detail',
      name: 'purchasingAgentDetail',
      hidden: true ,
      meta: { title: '查看采购商档案', activeMenu: '/purchasingAgent/purchasingAgentlist' },
      component: () => import('@/views/purchasingAgent/archivesList/detail')
    },
    {
      path: 'purchasingAgentlist/editItem',
      name: 'purchasingAgenteditItem',
      hidden: true ,
      meta: { title: '查看采购商档案', activeMenu: '/purchasingAgent/purchasingAgentlist' },
      component: () => import('@/views/purchasingAgent/archivesList/editItem')
    }
  ]
}

export default purchasingAgent