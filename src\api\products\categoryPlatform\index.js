import requestAxios from '@/utils/requestAxios'

export function list(data) {
    return requestAxios({
        url: '/api/product/admin/categoryPlatform/page',
        method: 'post',
        data
    })
}

// 获取平台分类
export function query(data) {
    return requestAxios({
        url: '/api/product/admin/categoryPlatform/query',
        method: 'post',
        data
    })
}


export function editApi(data) {
    return requestAxios({
        url: '/api/product/admin/categoryPlatform',
        method: data.id > 0 ? 'put' : 'post',
        data
    })
}
 

export function deleteApi(id) {
    return requestAxios({
        url: '/api/product/admin/categoryPlatform?ids[]=' + id,
        method: 'delete'
    })
}


export function addChild(data) {
    return requestAxios({
        url: '/api/product/admin/categoryPlatform/addChild',
        method: 'post',
        data
    })
}

export function getChildren(data) {
    return requestAxios({
        url: '/api/product/admin/categoryPlatform/listChildren',
        headers: { 'content-type': 'application/x-www-form-urlencoded' },
        method: 'post',
        data
    })
}

 

export function editCategoryPlatformExtensionFieldApi(data) {
    return requestAxios({
        url: '/api/product/admin/categoryPlatformExtensionField',
        method: data.id > 0 ? 'put' : 'post',
        data
    })
}

export function deleteCategoryPlatformExtensionFieldApi(id) {
    return requestAxios({
        url: '/api/product/admin/categoryPlatformExtensionField?ids[]=' + id,
        method: 'delete'
    })
}

export function queryCategoryPlatformExtensionFieldApi(data) {
    return requestAxios({
        url: '/api/product/admin/categoryPlatformExtensionField/page',
        method: 'post',
        data
    })
}
