<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
import { faviconName } from './settings';
export default {
  name: 'App',
  created() {
    this.setFavicon()
  },
  methods: {
    setFavicon() {
      let favicon = document.querySelector('link[rel="icon"]')
      if (favicon) return
      favicon = document.createElement('link')
      favicon.rel = 'icon'
      favicon.href = faviconName
      document.head.appendChild(favicon)
    }
  }
}
</script>
