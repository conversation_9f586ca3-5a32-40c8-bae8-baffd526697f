<template>
  <im-dialog :title="title" :visible.sync="visibleDialog" :width="width" :append-to-body="true" @confirm="confirm">
    <div class="dialog-tips">
    </div>
    <el-table
      ref="tableData"
      :data="tableData"
      border
    >
      <el-table-column label="序号" type="index" width="54" align="center" />
      <el-table-column label="模块标题" prop="moduleNameOne">
        <template slot-scope="scope">
          <el-input v-model="scope.row.moduleNameOne" placeholder="请输入模块标题" class="el-input-none-border" />
        </template>
      </el-table-column>
      <el-table-column label="模块副标题" prop="linkParam">
        <template slot-scope="scope">
          <el-input v-model="scope.row.linkParam" placeholder="请输入模块副标题" class="el-input-none-border" />
          <span style="display: none;">{{ scope.row.sortValue = scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="220" align="center">
        <template slot-scope="scope">
          <el-row class="table-edit-row">
            <span v-if="scope.row.id" class="table-edit-row-item">
              <el-button type="text" @click="addUrl(scope.row, scope.$index)">添加活动</el-button>
            </span>
            <span class="table-edit-row-item">
              <el-button type="text" @click="handleUp(scope.$index)">上移</el-button>
            </span>
            <span class="table-edit-row-item">
              <el-button type="text" @click="handleDown(scope.$index)">下移</el-button>
            </span>
            <span class="table-edit-row-item">
              <el-button type="text" @click="handleDel(scope.$index)">删除</el-button>
            </span>
          </el-row>
        </template>
      </el-table-column>
    </el-table>
    <div v-if="tableData.length < 4" class="add-button" @click="add"><span>添加模块</span></div>
    <im-dialog :title="titleUrl" :visible.sync="visible" width="1000px" append-to-body class="decor-footer-dialog-link" @confirm="confirmLinkBUtton">
      <el-table
        ref="tableData"
        :data="tableDataUrl"
        border
      >
        <el-table-column label="序号" type="index" width="54" align="center" />
        <el-table-column label="图片" prop="name" width="200">
          <template slot-scope="scope">
            <div class="file-list" :class="{ 'file-length': scope.row.fileList.length>=1 }">
              <el-upload
                class="upload-demo-table"
                :action="$uploadUrl"
                :limit="1"
                :file-list="scope.row.fileList"
                :headers="headersProgram"
                :data="insertProgram"
                :on-change="handleChange"
                :on-success="uploadSuccess"
                accept=".jpg,.png"
                :before-upload="beforeUpload"
              >
                <el-button type="text" @click="fileIndex(scope.$index)">上传图片</el-button>
              </el-upload>
              <div class="upload-delete" @click="handleRemove(scope.row)"><i class="el-icon-close" /></div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="链接类型" prop="linkType" width="180">
          <template slot-scope="scope">
            <el-select v-model="scope.row.linkType" placeholder="请选择链接类型" @change="changeLinkFun($event, scope.$index, scope.row)">
              <el-option
                v-for="itemInfo in selectList"
                :key="itemInfo.code"
                :label="itemInfo.name"
                :value="itemInfo.code"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="链接目标" prop="linkUrl" min-width="300">
          <template slot-scope="scope">
            <el-input v-model="scope.row.linkUrl" placeholder="链接目标">
              <ProductItemTable v-if="scope.row.linkType==='PRODUCT_DETAIL'" slot="append" :width="'100px'" :select-items.sync="scope.row.linkParam" @confirm="confirmLink(scope.row)" />
              <StoreListTable v-if="scope.row.linkType==='STORE_INDEX'" slot="append" :width="'100px'" :select-items.sync="scope.row.linkParam" @confirm="confirmLink(scope.row)" />
              <ProductTypeItemTable v-if="scope.row.linkType==='CATEGORYTYPE_LIST'" slot="append" :width="'100px'" :select-items.sync="scope.row.linkParam" @confirm="confirmLink(scope.row)" />
              <HelpCenter v-if="scope.row.linkType==='HELP_CENTER'" slot="append" :width="'100px'" :select-items.sync="scope.row.linkParam" @confirm="confirmLink(scope.row)" />
            </el-input>
            <span style="display: none;">{{ scope.row.sortValue = scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" align="center">
          <template slot-scope="scope">
            <el-row class="table-edit-row">
              <span class="table-edit-row-item">
                <el-button type="text" @click="handleUpUrl(scope.$index)">上移</el-button>
              </span>
              <span class="table-edit-row-item">
                <el-button type="text" @click="handleDownUrl(scope.$index)">下移</el-button>
              </span>
              <span class="table-edit-row-item">
                <el-button type="text" @click="handleDelUrl(scope.$index)">删除</el-button>
              </span>
            </el-row>
          </template>
        </el-table-column>
      </el-table>
      <div v-if="tableDataUrl.length < 2" class="add-button" @click="addUrlButton"><span>添加链接</span></div>
    </im-dialog>
  </im-dialog>
</template>

<script>
import { getToken } from '@/utils/auth'
import {advUpdate, batchSaveUpdate, batchSaveAd} from '@/views/fitment/pcmall/components/index'
import { query, getAllAd } from '@/api/setting/data/dictionaryItem'

import ProductItemTable from '@/components/eyaolink/Product/productItemCodeTable'
import ProductTypeItemTable from '@/components/eyaolink/Product/ProductTypeItemTable'
import StoreListTable from '@/components/eyaolink/Store/listTable'
import HelpCenter from '@/components/eyaolink/help'

export default {
  name: 'MallNavigation',
  components: {
    ProductItemTable,
    ProductTypeItemTable,
    StoreListTable,
    HelpCenter
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '1000px'
    },
    selectList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      visibleDialog: false,
      tableData: [],
      fileList: [],
      headersProgram: {
        token: getToken(),
        Authorization: 'Basic YWRtaW5fdWk6YWRtaW5fdWlfc2VjcmV0'
      },
      insertProgram: {
        folderId: 0
      },
      scopeIndex: '',
      titleUrl: '',
      tableDataUrl: [],
      visible: false
    }
  },
  methods: {
    init(data) {
      this.tableData = []
      if (data && data.length > 0) {
        data.map(val => {
          if (val.moduleName && val.moduleName !== '') {
            let name = val.moduleName.split(':')
            this.tableData.push({
              moduleNameOne: name[0] ? name[0] : '',
              linkParam: name[1] ? name[1] : '',
              id: val.id,
              linkNameList: val.linkNameList && val.linkNameList.length > 0 ? val.linkNameList : []
            })
          } else {
            this.tableData.push({
              moduleNameOne: '',
              linkParam: '',
              id: val.id,
              linkNameList: val.linkNameList && val.linkNameList.length > 0 ? val.linkNameList : []
            })
          }
        })
      }
      this.visibleDialog = true
    },
    confirmLinkBUtton() {
      this.tableDataUrl.map(val => {
        val.image =val.fileList[0].response!=undefined ? val.fileList[0].response.data.url :  val.image
        val.linkName = val.fileList[0].response!=undefined ? val.fileList[0].response.data.submittedFileName : val.fileList[0].name
        return val
      })
      batchSaveAd(this.tableDataUrl, this.linkId).then(res => {
        if (res.code == 0) {
          getAllAd().then(res => {
            const data = res.data
            this.tableData = []
            if (data && data.length > 0) {
              data.map(val => {
                if (val.moduleName && val.moduleName !== '') {
                  let name = val.moduleName.split(':')
                  this.tableData.push({
                    moduleNameOne: name[0] ? name[0] : '',
                    linkParam: name[1] ? name[1] : '',
                    id: val.id,
                    linkNameList: val.linkNameList && val.linkNameList.length > 0 ? val.linkNameList : []
                  })
                } else {
                  this.tableData.push({
                    moduleNameOne: '',
                    linkParam: '',
                    id: val.id,
                    linkNameList: val.linkNameList && val.linkNameList.length > 0 ? val.linkNameList : []
                  })
                }
              })
            }
          }).then(() => {
            this.$message.success('添加链接成功')
            this.visible = false
          })
        }
      })
    },
    add() {
      this.tableData.push({
        moduleNameOne: '',
        linkParam: '',
        linkNameList: [],
        sortValue: 0
      })
    },
    change(e) {
      this.$forceUpdate()
    },
    confirmLink(row) {
      let index = 0
      if (row.linkUrl.lastIndexOf('=') === -1) {
        index = row.linkUrl.lastIndexOf('/')
      } else {
        index = row.linkUrl.lastIndexOf('=')
      }
      const str = row.linkUrl.substring(index + 1, row.linkUrl.length)
      row.linkUrl = row.linkUrl.replace(str, row.linkParam)
    },
    changeLinkFun(val, index, row) {
      row.linkParam = ''
      this.selectList.map(item => {
        if (item.code === val) {
          row.linkUrl = item.describe
        }
      })
    },
    addUrl(row, index) {
      this.titleUrl = row.name
      this.linkIndex = index
      this.linkId = row.id
      if (this.tableData[index].linkNameList && this.tableData[index].linkNameList.length > 0) {
        this.tableDataUrl = this.tableData[index].linkNameList.map(val => {
          for (const key in val.linkNameList) {
            val.linkUrl = val.linkNameList[key]
            val.fileList = [{ url: val.image, name: key }]
            val.id = ''
          }
          return val
        })
      }
      this.tableDataUrl = this.tableData[index].linkNameList ? this.tableData[index].linkNameList : []
      this.visible = true
    },
    addUrlButton() {
      this.tableDataUrl.push({
        linkName: '',
        image: '',
        fileList: [],
        linkType: '',
        linkUrl: '',
        linkParam: ''
      })
    },
    confirm() {
      const data = []
      this.tableData.map(val => {
        val.linkNameList && val.linkNameList.length > 0 ? val.linkNameList.map(item => {
          item.id = ''
          return item
        }) : ''
        data.push({
          moduleName: val.moduleNameOne + ':' + val.linkParam,
          id: val.id ? val.id : '',
          sortValue: val.sortValue,
          linkNameList: val.linkNameList
        })
      })
      batchSaveAd(data, 0).then(res => {
        if (res.code == 0) {
          this.$message.success('编辑广告成功')
          this.$emit('confirm')
          this.visibleDialog = false
        }
      })
    },
    handleUp(index) {
      if (index !== 0) {
        this.tableData[index] = this.tableData.splice(index - 1, 1, this.tableData[index])[0]
      }
    },
    handleDown(index) {
      if (index !== this.tableData.length - 1) {
        this.tableData[index] = this.tableData.splice(index + 1, 1, this.tableData[index])[0]
      }
    },
    handleDel(index) {
      this.tableData.splice(index, 1)
    },
    handleRemove(row) {
      const rowIndex = this.tableDataUrl.indexOf(row)
      const data = []
      this.tableDataUrl.map((val, index) => {
        if (index === rowIndex) {
          val.fileList = []
        }
        data.push(val)
      })
      this.tableDataUrl = data
    },
    handleChange(file, fileList) {
      this.tableDataUrl[this.scopeIndex].fileList = fileList
    },
    fileIndex(index) {
      this.scopeIndex = index
    },
    beforeUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < 2
      if (!isLt2M) {
        this.$message({
          message: '上传文件大小不能超过 2MB!',
          type: 'warning'
        })
        return false
      }
    },
    uploadSuccess(res, file) {
      const data = []
      this.tableDataUrl.map((val, index) => {
        data.push(val)
      })
      this.tableDataUrl = data
    },
    handleUpUrl(index) {
      if (index !== 0) {
        this.tableDataUrl[index] = this.tableDataUrl.splice(index - 1, 1, this.tableDataUrl[index])[0]
      }
    },
    handleDownUrl(index) {
      if (index !== this.tableDataUrl.length - 1) {
        this.tableDataUrl[index] = this.tableDataUrl.splice(index + 1, 1, this.tableDataUrl[index])[0]
      }
    },
    handleDelUrl(index) {
      this.tableDataUrl.splice(index, 1)
    }
  }
}
</script>

<style lang="less">
  .upload-delete{
    display: none
  }

  .file-length{
    position: relative;
    .el-upload--text{
      display: none;
    }
    .el-icon-document{
      display: none;
    }
    .upload-delete{
      position: absolute;
      right: 5px;
      top: 4px;
      width: 14px;
      height: 14px;
      border-radius: 50%;
      background-color: #979797;
      color: rgba(235,236,238,1);
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      font-size: 12px;
    }
    .el-progress__text{
      display: none;
    }
  }

</style>
