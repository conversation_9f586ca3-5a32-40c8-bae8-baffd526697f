import requestAxios from '@/utils/requestAxios'
import requestExport from '@/utils/requestExport'

//  记录列表
export function list(data) {
  return requestAxios({
    url: "/api/merchant/admin/purMerchantUpload/page",
    method: 'post',
    data
  })
}

// 日志下载
export function exportProductLog(data) {
  return requestExport({
    url: "/api/merchant/admin/purMerchantUploadLog/export",
    method: 'post',
    data,
    headers: { responseType: 'blob'  }
  })
}

// 删除记录
export function importProductDelete(data) {
  return requestAxios({
    url: "/api/merchant/admin/purMerchantUpload",
    method: 'DELETE',
    params: data
  })
}


