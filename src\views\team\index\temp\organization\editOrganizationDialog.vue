<<template>
    <div class="container" >
    <!-- 编辑组织弹出 -->
    <el-dialog
      title="编辑组织"
      :visible.sync="visible"
      width="30%"
      class="editOrganizationDialog"
      :show-close="true"
      :close-on-click-modal="false"
      :before-close="close"
    >
      <div class="editOrganization">
        <el-form ref="form" :model="editOrganizationForm" label-width="90px">
          <el-form-item
            label="组织名称:"
            prop="name"
            :rules="[
              {
                required: true,
                message: '请输入组织名称',
                trigger: 'blur',
              },
            ]"
          >
            <el-input maxlength="20" show-word-limit v-model="editOrganizationForm.name"></el-input>
          </el-form-item>
          <el-form-item
            label="主管:"
            prop="salesmanId"
            :rules="[
              {
                required: true,
                message: '请选择主管',
                trigger: 'change',
              },
              { validator: checkSalesManByIdFun, trigger: 'change', required: true }
            ]"
          >
            <el-select filterable  v-model="editOrganizationForm.salesmanId" placeholder="请选择">
                <!-- <el-option
                  label="请选择"
                  value="">
                  <span style="float: left">请选择</span>
                </el-option> -->
                <el-option
                v-for="(item,index) in salesmanList"
                :key="index"
                :label="`${item.salesmanName}(${item.mobile})`"
                :value="item.id"
              >
                <span style="float: left">{{ item.salesmanName }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{item.mobile}}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="销售区域:"
            prop="notSetSale"
          >
            <el-radio-group class="sellAreaClass" text-color="#409eff" v-model="editOrganizationForm.notSetSale">
              <el-radio label="N">未设置销售区域，则全国不可销售</el-radio>
              <el-radio label="Y">未设置销售区域，则全国可销售</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            label="销售品种:"
            prop="sellType"
          >
            <el-radio-group class="sellTypeClass" v-model="editOrganizationForm.notSetVariety">
              <el-radio label="N">未设置销售品种，则所有品种不可销售</el-radio>
              <el-radio label="Y">未设置销售品种，则所有品种可销售</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="close()">取 消</el-button>
        <el-button type="primary"  v-throttle  @click="submitEditFun()">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 编辑组织弹出 end -->  
    </div>
</template>
<script>
// import { query } from '@/api/';
import { orgaStructure,getSalesManList,checkSalesManById } from '@/api/organization/index';
import { mapState,mapGetters,mapActions} from 'vuex'
export default {
  data() {
    return {
        // 组织显示
      showEditOrganization:false,
      editOrganizationForm:{
        "name": "",
        "notSetSale": "N",
        "notSetVariety": "N",
        "orgaAreaDto": {
          "commonAreaIds": []
        },
        "salesmanId": ''
      },
      
      salesmanList:null
    };
  },
  props:{
    /**
    * @description  编辑Form 对象  显示
    * @param {Boolean} visible  
    */
    visible:{
        type:Boolean,
        default:false
    },
    editForm: {
        type:Object,
        default:function(){
            return {
              "name": "",
              "notSetSale": "N",
              "notSetVariety": "N",
              "orgaAreaDto": {
                "commonAreaIds": []
              },
              "salesmanId": ''
            };
        }
    }
  },
  components: {
    // 组件
  },
  computed: {
    // 计算属性computed :
    // 1. 支持缓存，只有依赖数据发生改变，才会重新进行计算
    // 2. 不支持异步，当computed内有异步操作时无效，无法监听数据的变化
    // 3. computed 属性值会默认走缓存，计算属性是基于它们的响应式依赖进行缓存的，也就是基于data中声明过或者父组件传递的props中的数据通过计算得到的值
    // 4. 如果一个属性是由其他属性计算而来的，这个属性依赖其他属性，是一个多对一或者一对一，一般用computed
    // 5.如果computed属性属性值是函数，那么默认会走get方法；函数的返回值就是属性的属性值；在computed中的，属性都有一个get和一个set方法，当数据变化时，调用set方法。
    ...mapGetters([
      "organizationInfo",
      "paremDepartmentInfo",
      "departmentInfo",
    ]),
  
  },
  filters: {},
  watch: {
    // 监听属性watch：
    // 1. 不支持缓存，数据变，直接会触发相应的操作；
    // 2. watch支持异步；
    // 3. 监听的函数接收两个参数，第一个参数是最新的值；第二个参数是输入之前的值；
    // 4. 当一个属性发生变化时，需要执行对应的操作；一对多；
    // 5. 监听数据必须是data中声明过或者父组件传递过来的props中的数据，当数据变化时，触发其他操作，函数有两个参数，
    // 　　immediate：组件加载立即触发回调函数执行，
    // 　　deep: 深度监听，为了发现对象内部值的变化，复杂类型的数据时使用，例如数组中的对象内容的改变，注意监听数组的变动不需要这么做。注意：deep无法监听到数组的变动和对象的新增，参考vue数组变异,只有以响应式的方式触发才会被监听到。
    // 　　deepdemo：
    //      obj:{
    //          handler(){
    //              console.log('obj 变了')
    //          },
    //          deep:true
    //      }
    editForm:{
        handler(val){
            this.editOrganizationForm= Object.assign({}, val) 
        },
        deep:true
    }
  },
  methods: {
    //方法集合
    checkSalesManByIdFun(rule, value, callback){
      checkSalesManById(value).then(res=>{
        let {code,data,msg} = res;
        if (code == 0 && data != null&& data.organizationId != this.organizationInfo.id&&data.isLeader.code=='Y') {
          this.$message.warning('当前业务员已是其他组织主管')
          // return callback(new Error('当前业务员已是其他组织主管'))
        } else if (code == 0 && data != null&& data.organizationId != this.organizationInfo.id) {
           this.$message.warning('当前业务员是其他组织成员')
          // return callback(new Error('当前业务员是其他组织成员'))
        } else if (code == 0 && data != null&& data.organizationId == this.organizationInfo.id&&data.isLeader.code=='Y') {
          this.$message.warning('当前业务员是其他部门主管')
          // return callback(new Error('当前业务员是其他部门主管'))
        } else if (code == 0 && data != null&& data.organizationId == this.organizationInfo.id) {
           this.$message.warning('当前业务员是其他部门成员')
          // return callback(new Error('当前业务员是其他部门成员'))
        }  
        callback()
         
      })
    },
    show(){
      // this.showEditOrganization =true;
      // this.getSalesManListFun()
      // if(this.editForm.id!=null){
      //     this.editOrganizationForm = Object.assign({}, JSON.parse(JSON.stringify(this.editForm)))
      // }
      
    },
    close(){
        this.editOrganizationForm={
          "name": "",
          "notSetSale": "N",
          "notSetVariety": "N",
          "orgaAreaDto": {
            "commonAreaIds": []
          },
          "salesmanId": ''
        }
        // this.showEditOrganization =false;
        this.$emit("update:visible",false);
    },
    // 查询业务员列表
    getSalesManListFun(){
      getSalesManList(null).then().then(res => {
        let {code,data,msg} = res;
          if(code == 0 ){
            this.salesmanList=data;
          } 
      })
    },
    // 提交编辑
    async submitEditFun(){
        this.$refs.form.validate(async (valid) => {
          if (valid) {
            let method= (this.editOrganizationForm.id!=undefined&&this.editOrganizationForm.id!=null)?'PUT':'POST'
            let {code,data,msg} = await orgaStructure(this.editOrganizationForm,method);
            if(code == 0 ){
              // 提交回调 
              this.$emit("confirm",this.editOrganizationForm)
              this.$emit("update:visible",false);
            }
          }
        });
        
       
    },
    
  },
  mounted() {
    // 方法调用
    this.getSalesManListFun()
    if(this.editForm.id!=null){
        let editForm= Object.assign({}, JSON.parse(JSON.stringify(this.editForm)))
        editForm.notSetSale = editForm.notSetSale.code;
        editForm.notSetVariety = editForm.notSetVariety.code ;
        this.editOrganizationForm = editForm
    }
  },
  beforeDestroy() {
      this.editOrganizationForm={
        "name": "",
        "notSetSale": "N",
        "notSetVariety": "N",
        "orgaAreaDto": {
          "commonAreaIds": []
        },
        "salesmanId": ''
      }
  },
};
</script>

<style lang="less" scoped>
::v-deep .editOrganizationDialog .el-dialog{
  min-width: 400px;
}

::v-deep .editOrganizationDialog .el-dialog__body{
  padding: 30px 20px !important;
}
::v-deep .editOrganizationDialog .el-dialog__body{
  padding: 30px 20px !important;
}
  .editOrganization{
    ::v-deep .el-select{width: 100%;}
    ::v-deep .sellAreaClass .el-radio{ padding-top: 10px;}
    ::v-deep .sellTypeClass .el-radio{ padding-top: 10px;}

  }

</style>
