const isKz = process.env.VUE_APP_KZ === 'true'
const title = process.env.VUE_APP_TITLE
const faviconName = process.env.VUE_APP_ICON_NAME
const logo = process.env.VUE_APP_LOGO
const menuLogo = process.env.VUE_APP_MENU_LOGO

module.exports = {
  isKz,
  
  logo,

  menuLogo,

  title: (title || '康缘') + '平台管理后台',

  faviconName: (faviconName || 'favicon') + '.ico',

  /**
   * @type {boolean} true | false
   * @description Whether show the settings right-panel
   */
  showSettings: false,

  /**
   * @type {boolean} true | false
   * @description Whether need tagsView
   */
  tagsView: true,

  /**
   * @type {boolean} true | false
   * @description Whether fix the header
   */
  fixedHeader: false,

  /**
   * @type {boolean} true | false
   * @description Whether show the logo in sidebar
   */
  sidebarLogo: true,

  /**
   * @type {string | array} 'production' | ['production', 'development']
   * @description Need show err logs component.
   * The default is only used in the production env
   * If you want to also use it in dev, you can pass ['production', 'development']
   */
  errorLog: 'production'
}
