<template>
  <div class="archivesEditContent">
    <el-form :inline="true" label-width="140px" >
      <page-title title="商品详情"></page-title>
      <div class="item">
        <page-module-title title="基础信息"/>
        <el-row>
          <el-col :span="6">
            <el-form-item class="formItem" label="商品编码:">
              {{pageDate.productCode}}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem"  label="经营类目:">
                <template v-if="pageDate.businessRangeId!=undefined">
                        {{pageDate.businessRangeId|getBusinessRangeListFilter(vueInfo)}}
                </template>
             
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem"  label="平台分类:">
              {{platformCategoryPath}}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" label="商品分类:">
              {{ productCategoryPath }}
            </el-form-item>
          </el-col>
         
          <el-col :span="6">
            <el-form-item class="formItem" label="商品名称:">
              {{pageDate.productName}}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" label="通用名称:">
              {{pageDate.drugName}}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem"  label="商品标题:">
              {{pageDate.title}}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" label="品牌:">
              {{ brandName }}
            </el-form-item>
          </el-col>
          
          <el-col :span="6">
            <el-form-item class="formItem" label="规格:">
              {{ pageDate.spec }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" label="剂型:">
              {{ pageDate.agentiaType }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" label="计量单位:">
              {{ pageDate.measurement && JSON.parse(pageDate.measurement).code }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" label="包装单位:">
              {{ pageDate.unit }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" label="生产厂家:">
              {{pageDate.manufacturer}}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" label="产地:">
              {{ pageDate.area }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" label="批准文号:">
              {{pageDate.approvalNumber}}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" label="中包装:">
              {{pageDate.midPackTotal}}
            </el-form-item>
          </el-col>
          
          <!-- <el-col :span="6">
            <el-form-item class="formItem" label="中包装:">
              {{pageDate.socialCreditCode}}
            </el-form-item>
          </el-col> -->
          <el-col :span="6">
            <el-form-item class="formItem" label="件包装:">
              {{pageDate.packTotal}}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" label="本位码:">
              {{pageDate.standardCode}}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" label="条形码:">
              {{pageDate.barCode}}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" label="处方类型:">
              {{pageDate.otcType && pageDate.otcType.desc}}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" label="助记码:">
              {{pageDate.mnemonicCode}}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" label="ERP编码:">
              {{pageDate.erpCode}}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" label="效期:">
              {{(pageDate.skuList && pageDate.skuList[0])?pageDate.skuList[0].expDate:''}}
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    


      <div class="item" v-if="pageDate.productExtensionFieldList && pageDate.productExtensionFieldList.length>0">
        <page-module-title title="其他属性"/>
        <el-row>
          <el-col :span="6" v-for="(item,index) in pageDate.productExtensionFieldList" :key="index">
            <el-form-item class="formItem" :label="item.name">
              {{ item.value }}
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <div class="item">
        <page-module-title title="经营参数"/>
        <el-row>
          <el-col :span="6">
            <el-form-item class="formItem" label="用券:">
              {{(pageDate.whetherUseCoupon && pageDate.whetherUseCoupon.code =='Y') ? "允许使用优惠券":"不可用券"}}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" label="现款交易商品:">
              {{ (pageDate.whetherCashTransaction && pageDate.whetherCashTransaction.code =='Y') ? "仅支持现款交易，不可赊销":"" }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" label="含麻黄碱商品:">
              {{ (pageDate.whetherContainEphedrine && pageDate.whetherContainEphedrine.code =='Y') ? "含麻黄碱商品，不可现金交易":"" }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" label="销售方式:">
              {{ pageDate.marketing && pageDate.marketing.desc }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" label="是否共享:">
              {{ (pageDate.whetherShare && pageDate.whetherShare.code =='Y') ? "可共享":"不可共享" }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" label="拆销:">
              {{ (pageDate.whetherUnbundled && pageDate.whetherUnbundled.code =='Y') ? "可拆零":"不可拆零" }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" label="库存:">
              {{ (pageDate.skuList && pageDate.skuList[0])?pageDate.skuList[0].realStockQuantity:'' }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" label="每单限购数量:">
              {{ (pageDate.skuList && pageDate.skuList[0])?pageDate.skuList[0].limitQuantity:'' }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" label="每单起订数量:">
              {{pageDate.minBuyQuantity}}
            </el-form-item>
          </el-col>
        </el-row>
      </div>


      <div class="item">
        <page-module-title title="价格信息"/>
        <el-row>
          <el-col :span="6">
            <el-form-item class="formItem" label="分销供货价:">
              {{ (pageDate.skuList && pageDate.skuList[0])?pageDate.skuList[0].supplyPrice:'' }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" label="建议批发价:">
              {{ (pageDate.skuList && pageDate.skuList[0])?pageDate.skuList[0].wholesalePrice:'' }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" label="统一销售价:">
              {{ (pageDate.skuList && pageDate.skuList[0])?pageDate.skuList[0].salePrice:'' }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" label="成本价:">
              {{ (pageDate.skuList && pageDate.skuList[0])?pageDate.skuList[0].costPrice:'' }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" label="医保价:">
              {{ (pageDate.skuList && pageDate.skuList[0])?pageDate.skuList[0].medicarePrice:'' }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" label="零售价:">
              {{ (pageDate.skuList && pageDate.skuList[0])?pageDate.skuList[0].retailPrice:'' }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>

        </el-row>
      </div>

       
       <div class="item">
        <page-module-title title="内容信息"/>
        <el-row>
          <el-col :span="24">
            <el-form-item v-if="pageDate.pictIdS!=null && pageDate.pictIdS!=''" class="formItem" label="产品图片:">
              <el-image
                v-for="(item,index) in pageDate.pictIdS.split(',')"
                :key="index"
                style="width: 150px; height: 150px"
                :src="item" 
                :preview-src-list="pageDate.pictIdS.split(',')">
              </el-image>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item class="formItem" label="产品详情:">
              <span v-html="pageDate.productDescription"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item class="formItem" label="说明书:">
              <div v-for="(item,index) in instructions" :key="index">
                <span>{{item.title}}:{{item.description}}</span>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- <div class="item">
        <page-module-title title="商品资质"/>
        <el-row>
          <el-col :span="24">
            <el-form-item class="formItem" label="产品图片:">
              {{pageDate.socialCreditCode}}
            </el-form-item>
          </el-col>
        </el-row>
      </div> -->

      <div class="item">
        <page-module-title title="GPS质量"/>
        <el-row>
          <el-col :span="6">
            <el-form-item class="formItem" label="养护类型:">
              {{ (pageDate.curingType && pageDate.curingType.code == "STRONG")?'重点养护':(pageDate.curingType && pageDate.curingType.code == "GENERAL")?'常规养护':'' }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" label="养护周期:">
              {{ pageDate.curingCycle }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" label="存储方式:">
              {{ keepWay(pageDate.keepWay) }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" label="存储条件:">
              {{ pageDate.keepCondition }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" label="是否医保:">
              {{ (pageDate.whetherMedicareVariety && pageDate.whetherMedicareVariety.code == "Y")?'医保':(pageDate.whetherMedicareVariety && pageDate.whetherMedicareVariety.code == "N")?'非医保':'' }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" label="医保号:">
              {{pageDate.medicareNum}}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" label="特殊管理药品:">
              {{specialMngMedicinal(pageDate.specialMngMedicinal)}}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" label="是否冷藏冷链:">
              {{pageDate.whetherColdChain && pageDate.whetherColdChain.desc}}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item class="formItem" label="性能:">
              {{pageDate.performance}}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item class="formItem" label="用途:">
              {{pageDate.purpose}}
            </el-form-item>
          </el-col>
        </el-row>
      </div>


    </el-form>
<!--    <es-table-pager></es-table-pager>-->
  </div>
</template>
<script>
  import checkPermission from "@/utils/permission";
  import {
     getDetailApi
  } from "@/api/products/product";
  import {
    getApi
  } from "@/api/products/brand"
  import {
     listBusinessCategory
  } from "@/api/businessCenter/businessList";
  import {
     query
  } from "@/api/products/categoryPlatform";
 import {
     categoryQuery
  } from "@/api/category";

  export default {
    data() {
      return {
        vueInfo:this,
        platformCategoryPath:"",
        platformCategoryList:[],
        businessRangeList:[],
        pageDate:{},
        productCategoryPath:[],
        productRangeList:[],
        brandName:'',
        instructions:[],
      }
    },
    filters:{
        getBusinessRangeListFilter(value,vueInfo){
            let objList= [];
            if(vueInfo.businessRangeList.length>0){
                 objList= vueInfo.businessRangeList.filter(function(item,index){
                    return value==item.id
                 })
                console.info("objList",objList)
            }
            return objList.length==0?'': objList[0].name;
        }   
    },
    methods: {
        // 平台分类
        async getCategoryPlatformList() {
            const { code, data } = await query({})
            if(code==0)
                this.platformCategoryList = data
        },
        getPlatformCategoryPath(nowItem,pathName=''){
            // 父节点 大于0
            let selectItme = this.platformCategoryList.filter(item=>{
                return  item.id==nowItem.parentId
            })
            if(selectItme.length>0){
                if(Number(nowItem.parentId)!=0){
                    return  this.getPlatformCategoryPath(selectItme[0],selectItme[0].label+"/"+ pathName )
                }else{
                    return pathName
                }
            }else{
                return pathName
            }
        },
        async getBusinessRangeList() {
            const { code, data } = await listBusinessCategory()
            if(code==0)
                this.businessRangeList = data
        },
        async getCategoryQuery() {
            const { code, data } = await categoryQuery({});
            if(code==0){
              this.productRangeList = data
            }
        },
        // 商品分类数据处理
        getProductCategoryPath(nowItem,pathName=''){
            // 父节点 大于0
            let selectItme = this.productRangeList.filter(item=>{
                return  item.id==nowItem.parentId
            })
            if(selectItme.length>0){
                if(Number(nowItem.parentId)!=0){
                    return  this.getProductCategoryPath(selectItme[0],selectItme[0].label+"/"+ pathName )
                }else{
                    return pathName
                }
            }else{
                return pathName
            }
        },
        // 存储方式
       keepWay(obj){
         if(obj==null || obj==''){
           return ''
         } else if(obj.code=='NORMAL'){
           return '常温'
         } else if(obj.code=='SHADE') {
           return '阴冷'
         } else if(obj.code =='COOL'){
           return '冷藏'
         } else {
           return ''
         }
       },
      //  特殊管理药品
      specialMngMedicinal(obj){
        if(obj==null || obj==''){
           return ''
         } else if(obj.code=='NONSPECIFIC'){
           return '非特殊药品'
         } else if(obj.code=='SPIRIT') {
           return '精神药物'
         } else if(obj.code =='ANESTHESIA'){
           return '麻醉药品'
         }  else if(obj.code =='MEDICINAL_TOXIC'){
           return '医疗用毒性药品'
         } else if(obj.code =='RADIOACTIVITY'){
           return '放射性药品'
         } else {
           return ''
         }
      },
    },
    async created() {
        await this.getCategoryPlatformList();
        await this.getBusinessRangeList();
        await this.getCategoryQuery();
        let {code,data} =  await getDetailApi(this.$route.query.id);
        if(code==0){
            this.pageDate= data
            //  平台分类
            let selectItme = this.platformCategoryList.filter(item=>{
                return item.id==data.platformCategoryId
            })
            if(selectItme.length>0){
                 this.platformCategoryPath=this.getPlatformCategoryPath(selectItme[0],selectItme[0].label )
            }
           
           //  商品分类
            let productSelectItme = this.productRangeList.filter(item=>{
                return item.id==data.categoryId
            })
            if(productSelectItme.length>0){
                 this.productCategoryPath=this.getProductCategoryPath(productSelectItme[0],productSelectItme[0].label )
            }
            // 品牌
            getApi(data.brandId).then(res=>{
              if(res.code == 0){
                this.brandName = res.data.brandName;
              }
            });
            // 说明书
            console.log(JSON.parse(data.instructions))
            console.log('--',Object.keys(JSON.parse(data.instructions)));
            this.instructions = JSON.parse(data.instructions).map(item=>{
               let ret = {
                title: '', description: '' 
              }
              for (let k in item) {
                ret.title = k
                ret.description = item[k]
              }
              return ret;
            });
        }
      
       
    },
  
    mounted() {
    },
    beforeDestroy() {
    },
  };
</script>
<style lang="less" scoped>
.archivesEditContent {
    background:#fff;
     border-top: 1px solid #ebecee;
    padding: 0 20px;
    .item {
        width: 100%;
        margin-bottom: 30px;
        border-bottom: 1px solid #eeeeee;
    }
}
.formItem {
  display: flex;
  // max-width: 20%;
  // overflow:hidden;
  // text-overflow:ellipsis;
  // -o-text-overflow:ellipsis;
  // -webkit-text-overflow:ellipsis;
  // -moz-text-overflow:ellipsis;
  // white-space:nowrap;
}
</style>
