<template>
  <div class="main">
    <!-- <div class="el-dialog__header">
      <div class="el-dialog__title">编辑资讯</div>
    </div> -->
    <div class="title"><span>资讯信息</span></div>
    <el-form ref="form" :model="form" label-width="170px" style="padding: 20px;">
      <el-form-item label="标题：" prop="title" :rules="[{required: true,min: 2, message: '请至少输入2个字', trigger: 'blur'}]">
        <el-input v-model="form.title" style="width:500px"></el-input>
      </el-form-item>
      <el-form-item label="副标题标题：">
        <el-input type="textarea" v-model="form.subtitle" style="width:500px"></el-input>
      </el-form-item>
      <!-- <el-form-item label="资讯标签：">
        <el-input v-model="form.articleLabel" style="width:500px"></el-input><span class="upload_ms" style="padding-left:20px">多个标签用 ， 分割</span>
      </el-form-item> -->
      <el-form-item label="封面：" >
        <el-upload class="avatar-uploader" :action="$uploadUrl" accept=".jpg,.png,.bmp,.jpeg" :data="insertProgram" :headers="headersProgram" :show-file-list="false" :on-success="handleAvatarSuccess" :before-upload="beforeUpload">
          <el-image style="width: 120px; height: 120px" v-if="form.coverImg!=''" :src="form.coverImg" class="avatar" fit="contain"></el-image>
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          <div class="el-upload__tip" slot="tip">
            <div>- 支持JPG、JPEG、PNG、BMP格式，大小不超过2M</div>
            <div>- 请保证请保证图片质量，分辨率至少为600*600</div>
          </div>
        </el-upload>

      </el-form-item>
      <el-form-item label="资讯分类：" prop="articleCategoryId" :rules="[{required: true,message: '请选择资讯分类'}]">
        <el-select v-model="form.articleCategoryId" placeholder="请选择分类" style="width:500px">
          <el-option v-for="item in options" :key="item.id" :label="item.label" :value="item.id"></el-option>
        </el-select>
        <el-button type="text" style="margin-left:15px" @click="$router.push('/contentCenter/informationCategory')">去添加</el-button>
      </el-form-item>

      <el-form-item label="资讯内容：" prop="content" :rules="[{required: true,min: 20,message: '请输入资讯内容！'}]">
        <Tinymce v-model="form.content" :width="1100" :height="200" />
      </el-form-item>

      <el-form-item label="创建人：">
        <el-input :disabled='true' :value="$store.state.user.name" style="width:500px"></el-input>
      </el-form-item>

      <div class="title"><span>其它</span></div>

      <el-form-item label="是否发布："  prop="publishStatus" :rules="[{required: true, message: '请至少选择是否发布'}]">
        <el-radio-group v-model="form.publishStatus">
          <el-radio-button :label="'Y'" >是</el-radio-button>
          <el-radio-button :label="'N'" >否</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="是否新窗口打开："  prop="isOpenWindow"  :rules="[{required: true, message: '请选择是否新窗口打开'}]" >
        <el-radio-group v-model="form.isOpenWindow">
          <el-radio-button :label="'Y'" >是</el-radio-button>
          <el-radio-button :label="'N'" >否</el-radio-button>
        </el-radio-group>
      </el-form-item>

      <div style="
        position: absolute;
        top: 2vh;
        right: 15px;
        background: #fff;
        height: 38px;
      ">
        <el-button @click="cancel">取消</el-button>
        <!-- <el-button>商城端预览</el-button> -->
        <el-button type="primary" @click="onSubmit">保存</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import Tinymce from "@/components/Tinymce";
import { add, getitem, putItem } from "@/api/contentCenter/informationList/index";
export default {
  data() {
    return {
      form: {
        coverImg: "",
        isOpenWindow: 'Y',
        publishStatus: 'Y'
      },
      imageUrl: "",
      insertProgram: {
        folderId: 0,
      },
      headersProgram: {
        token: getToken(),
        Authorization: "Basic YWRtaW5fdWk6YWRtaW5fdWlfc2VjcmV0",
      },
      options: [],
      submitReload: "",
      insertProgram: {
        folderId: 0,
      },
    };
  },
  methods: {
    onSubmit() {
      this.$refs.form.validate( async (valid) => {
        if(valid) {
          if(this.row.id) {
            this.form.publish_status = this.form.publishStatus
            let {data} = await putItem(this.form)
            this.$emit('update:visible', false)
            this.$emit('update:isReload' , true)
          } else {
            let {data} = await add(this.form)
            this.$emit('update:visible', false)
            this.$emit('update:isReload' , true)
          }
        } else {
        }
      })
    },
    cancel() {
      this.$emit('update:visible', false)
      this.row = {}
    },
    handleAvatarSuccess(res, file) {
      console.log(res.data.url);
      this.form.coverImg = res.data.url;
      console.log(this.form.coverImg)
    },
    beforeUpload(file) {
      console.log(file);
      // const isJPG = file.type === 'image/jpeg';
      const isJPG = true;
      const isLt2M = file.size / 1024 / 1024 < 5;

      if (!isJPG) {
        this.$message.error("上传头像图片只能是 JPG 格式!");
      }
      if (!isLt2M) {
        this.$message.error("上传头像图片大小不能超过 2MB!");
      }
      return isJPG && isLt2M;
    },
    add() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          let { data } = await add(this.form);
        }
      });
    },
    async getitem() {
      let {data} = await getitem(this.row.id)
      console.log(data.publishStatus)
      data.publishStatus = data.publish_status.code
      data.isOpenWindow = data.isOpenWindow.code
      data.coverImg = 'http://eyaolink-dev-bucket.oss-cn-shenzhen.aliyuncs.com'+ data.coverImg
      //  form.articleCategoryId
      //  console.log(data.articleCategoryId)
      this.form = data
      this.$set(this.form, 'articleCategoryId', data.articleCategoryId)
    }
    // async getCategory() {
    //   let {data} = await category()
    //   this.options = data
    //   console.log(data)
    // }
  },
  components: {
    Tinymce,
  },
  created() {
    // console.log(this.categoryList)
    if(this.row.id) {
      this.getitem(this.row.id)
    }
    this.options = this.categoryList;
  },
  props: {
    categoryList: {
      type: Array,
      required: true,
    },
    row: {
      type: Object,
      required: true
    }
  },
};
</script>

<style lang="less" scoped>
.main {
  padding: 30px 0px;
  // overflow: scroll;
  margin: -30px -20px;
  border-top: 1px solid #ebecee;
  padding: 30px 20px;
  .title {
    padding: 20px;
    span {
      font-size: 16px;
      padding-left: 10px;
      border-left: 4px solid rgba(64, 158, 255, 1);
    }
  }
  .upload_ms {
    color: #8c939d;
    line-height: 10px;
  }
  /deep/.avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  /deep/.avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }
  /deep/.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 120px;
    height: 120px;
    line-height: 120px;
    text-align: center;
  }
  /deep/.avatar {
    width: 120px;
    height: 120px;
    display: block;
  }
}
</style>
