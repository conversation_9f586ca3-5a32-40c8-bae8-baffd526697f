<template>
  <div>
    <im-search-pad
      :has-expand="false"
      :model="model"
      @reset="reload"
      @search="searchLoad"
    >
      <im-search-pad-item prop="businessNo">
        <el-input v-model="model.businessNo" placeholder="请输入业务单号" />
      </im-search-pad-item>
      <im-search-pad-item prop="applicantUserName">
        <el-input v-model="model.applicantUserName" placeholder="请输入申请人" />
      </im-search-pad-item>
      <im-search-pad-item prop="during">
        <el-date-picker
          v-model="during"
          type="datetimerange"
          align="right"
          unlink-panels
          range-separator="至"
          start-placeholder="起始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd hh:mm:ss"
        >
        </el-date-picker>
      </im-search-pad-item>
    </im-search-pad>

    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        :tabs="tabList"
        @change="handleChangeTab"
      >
        <template slot="button">
          <!-- <el-button  v-if="checkPermission(['admin','pay:export'])">导出</el-button> -->
          <el-button  @click="reload">刷新</el-button>
        </template>
      </tabs-layout>
      <table-pager ref="bussinessTabel" :options="tableTitle" :remote-method="load" :data.sync="tableData" :pageSize="pageSize" :isNeedButton="isWait"  @selection-change="handleSelectionChange">
        <template slot="amount">
          <el-table-column label="金额（元）" width="100">
            <slot slot-scope="{row}">
              {{row.amount|getDecimals}}
            </slot>
          </el-table-column>
        </template>
        <template slot="payCollectId" v-if="model.businessStatus==='PROCESS'||model.businessStatus==='FINISH'">
          <el-table-column label="收款单号" width="160">
            <slot slot-scope="{row}">
              <span class="text-primary">{{row.payCollectId}}</span>
            </slot>
          </el-table-column>
        </template>
        <template slot="reviewUser" v-if="model.businessStatus==='CANCEL'">
          <el-table-column label="取消人" width="160" prop="reviewUser" />
        </template>
        <template slot="reviewTime" v-if="model.businessStatus==='CANCEL'">
          <el-table-column label="取消时间" width="160" prop="reviewTime" />
        </template>
        <template slot="remarks" v-if="model.businessStatus==='CANCEL'">
          <el-table-column label="取消备注" width="160" prop="remarks" />
        </template>
        <div slot-scope="props">
          <el-row class="table-edit-row">
            <span v-if="checkPermission(['admin','admin-finance-merchantMargin:cancel'])" class="table-edit-row-item">
              <el-button type="text" @click="handleVerify(props.row.id)">取消</el-button>
            </span>
          </el-row>
        </div>
      </table-pager>
    </div>
    <el-dialog title="取消保证金缴纳" :visible.sync="verifyVisible" width="500px" @close="handleClose">
      <el-form :model="vForm" ref="vForm">
        <el-form-item prop="remarks" :rules="[{required: true,message: '请输入备注',trigger: 'blur'}]">
          <el-input placeholder="请输入备注信息" v-model="vForm.remarks" type="textarea" rows="4"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submit()">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  const TableColumns = [
    { label: "业务单号", name: "id",prop: "id",width: "170"},
    { label: "业务类型", name: "type.desc", prop:"type.desc",width: "170" },
    { label: "业务单状态", name: "businessStatus.desc", prop:"businessStatus.desc",width: "98" },
    { label: "申请人", name: "applicantUserName", prop:"applicantUserName",width: '220' },
    { label: "金额（元）", name: "amount", prop:"amount",slot: true  },
    { label: "制单人", name: "createUserName",prop:'createUserName',width: "150" },
    { label: "制单时间", name: "createTime",prop:'createTime',width: "170" },
    { label: "支付方式", name: "method.desc", prop:"method.desc"  },
    { label: "收款单号", name: "payCollectId", prop:"payCollectId",slot: true },
    { label: "取消人", name: "reviewUserName", prop:"reviewUserName",slot: true  },
    { label: "取消时间", name: "reviewTime", prop:"reviewTime",slot: true  },
    { label: "取消原因", name: "remarks", prop:"remarks",slot: true  },
  ];
  const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({ key: i, ...TableColumns[i] });
  }
  import checkPermission from "../../../utils/permission";
  import { merchantsStatistics,list,cancel } from '@/api/finance/deposit/index.js'
  import TabsLayout from '@/components/TabsLayout'

  export default {
    components: {
      TabsLayout
    },
    data () {
      return {
        loading: '',
        currentTab: 0,
        tabs: [
          { name: '缴纳中', value: 'PROCESS',count: 0,permission: 'pay-process:view' },
          { name: '缴纳成功', value: 'FINISH',count: 0,permission: 'pay-finish:view'  },
          { name: '缴纳取消', value: 'CANAEL',count: 0,permission: 'pay-cancel:view'  },
        ],
        tableData: [],
        page: 1,
        pageSize: 10,
        totalPage: 0,
        total: 0,
        tableTitle: TableColumnList,
        ids: [],
        during: '',
        model: {
          businessNo: '',
          businessStatus: 'PROCESS',
          applicantUserName: '',
          type: 'COLLECT'
        },
        verifyVisible: false,
        isWait: true,//操作列
        vForm: {
          remarks: '',
          'ids[]': ''
        }
      }
    },
    computed: {
      tabList() {
        return [
          { name: `缴纳中（${this.tabs[0].count}）`, value: 'PROCESS', hide: !checkPermission(['admin', 'admin-finance-merchantMargin:handingView']) },
          { name: `缴纳成功（${this.tabs[1].count}）`, value: 'FINISH', hide: !checkPermission(['admin', 'admin-finance-merchantMargin:successView']) },
          { name: `缴纳取消（${this.tabs[2].count}）`, value: 'CANAEL', hide: !checkPermission(['admin', 'admin-finance-merchantMargin:cancelView'])  }
        ]
      }
    },
    mounted() {
      this.getCount()
    },
    methods: {
      checkPermission,
      async getCount() {
        const query = {
          type: 'COLLECT'
        }
        const {data} = await merchantsStatistics(query)
        this.tabs.forEach(item=>{
          item.count = data[item.value.toLowerCase()]
        })
      },
      handleSelectionChange(val) {

      },
      handleVerify(id) {
        this.vForm['ids[]'] = id
        this.verifyVisible = true
      },
      handleClose() {
        this.verifyVisible = false
        this.$refs['vForm'].resetFields();
      },

      submit() {
        this.$refs['vForm'].validate(async valid => {
          if (valid) {
            await cancel(this.vForm).then(res => {
              if (res.code === 0) {
                this.$message.success('取消成功！')
                this.verifyVisible = false
                this.getCount()
                this.handleRefresh({
                  page: 1,
                  pageSize: 10
                })
              }
            })
          } else {
            return false
          }
        })
      },
      async load(params) {
        const listQuery = {
          model: {
            ...this.model,
            startTime: this.during[0],
            endTime: this.during[1]
          }
        }
        Object.assign(listQuery, params)
        this.loading = true
        const {data} = await list(listQuery)
        this.todoName = data.records[0].merchantName
        this.loading = false
        return { data }
      },
      searchLoad() {
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      reload() {
        this.model={
          ...this.model,
          ...{ 
            businessNo: '',
            applicantUserName: ''
          }
        }
        this.during = ''
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      handleChangeTab(tab) {
        this.model.businessStatus = tab.value
        if(tab.value !== 'PROCESS'){
          this.isWait = false
        } else {
          this.isWait = true
        }
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      handleRefresh(pageParams) {
        this.$refs.bussinessTabel.doRefresh(pageParams)
      }
    }
  }
</script>
