<template>
  <div>
    <im-search-pad
      :has-expand="false"
      :model="model"
      @reset="reload"
      @search="searchLoad"
    >
      <im-search-pad-item prop="contact">
        <el-input v-model="model.contact" placeholder="请输入业务员手机" />
      </im-search-pad-item>
      <im-search-pad-item prop="salesmanSn">
        <el-input v-model="model.salesmanSn" placeholder="请输入业务员编码" />
      </im-search-pad-item>
    </im-search-pad>

    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        :tabs="tabs"
        @change="handleChangeTab"
      >
        <template slot="button">
          <el-button  type="primary" v-if="checkPermission(['admin','admin-finance-salesmanBilling:application'])" @click="handleBatch">+批量申请结算</el-button>
          <!-- <el-button  v-if="checkPermission(['admin','salesman:export'])">导出</el-button> -->
          <el-button  @click="reload">刷新</el-button>
        </template>
      </tabs-layout>
      <table-pager ref="todoTable" :options="tableTitle" :remote-method="load" :data.sync="tableData" :operation-width="160" :selection="true" :pageSize="pageSize" @selection-change="handleSelectionChange">
        <template slot="promotionCountAmount">
          <el-table-column label="可结算金额（元）" width="145">
            <slot slot-scope="{row}">
              {{row.promotionCountAmount|getDecimals}}
            </slot>
          </el-table-column>
        </template>
        <template slot="beCountSettlement">
          <el-table-column label="业务账单数" width="130">
            <slot slot-scope="{row}">
              <el-row class="table-edit-row">
                <span class="table-edit-row-item">
                  <el-button type="text" @click="$router.push({path: '/finance/businessStatement/salesman/detail',query:{salesmanId: row.salesmanId}})">{{row.beCountSettlement}}</el-button>
                </span>
              </el-row>
            </slot>
          </el-table-column>
        </template>
        <div slot-scope="props">
          <el-row class="table-edit-row">
            <span v-if="checkPermission(['admin','admin-finance-salesmanBilling:view'])" class="table-edit-row-item">
              <el-button type="text" @click="$router.push({path: '/finance/businessStatement/salesman/detail',query:{salesmanId: props.row.salesmanId}})">查看详情</el-button>
            </span>
            <span v-if="props.row.beCountSettlement !==0&&checkPermission(['admin','admin-finance-salesmanBilling:clearing'])" class="table-edit-row-item">
              <el-button type="text" @click="handleSettle(props.row)">一键结算</el-button>
            </span>
          </el-row>
        </div>
      </table-pager>
    </div>
  </div>
</template>

<script>
  const TableColumns = [
    { label: "业务员编码", name: "salesmanSn",prop: "salesmanSn",width: "170"},
    { label: "业务员姓名", name: "name", prop:"name" },
    { label: "业务员手机号", name: "contact",prop: 'contact',width: '120' },
    { label: "可结算金额（元）", name: "promotionCountAmount", prop:'promotionCountAmount',slot:true },
    { label: "业务账单数", name: "beCountSettlement",prop:'beCountSettlement',slot:true }
  ];
  const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({ key: i, ...TableColumns[i] });
  }
  import checkPermission from '../../../utils/permission';
  import { financeBillProduct,saleSettlement,productBatchSettlement } from '@/api/finance'
  import TabsLayout from '@/components/TabsLayout'

  export default {
    components: {
      TabsLayout
    },
    data () {
      return {
        loading: '',
        currentTab: 0,
        tabs: [
          { name: '业务员账单' },
        ],
        tableData: [],
        page: 1,
        pageSize: 10,
        totalPage: 0,
        total: 0,
        tableTitle: TableColumnList,
        model: {
          contact: '',
          salesmanSn: ''
        },
        salesmanIds: []
      }
    },
    mounted() {
    },
    methods: {
      checkPermission,
      handleSelectionChange(val) {
        this.salesmanIds = val.map(function(item,index) {
          return item.salesmanIds;
        })

      },
      handleBatch() {
        if(this.salesmanIds.length === 0) {
          this.$message.warning('请至少选择一项进行操作！')
          return
        }
        this.$confirm('确定结算所选账单？','提示').then(_ => {
          productBatchSettlement({
            'salesmanIds[]': this.salesmanIds.toString()
          }).then(res=>{
            if(res.code == 0) {
              this.$message.success('操作成功！');
              this.merchantsIds =[];
              this.handleRefresh({
                page: 1,
                pageSize: 10
              })
            }
          })

        }).catch(_ => {});
      },
      handleSettle(row) {
        saleSettlement(row.salesmanId).then(res=>{
          if(res.code == 0) {
            this.$message.success('结算成功');
            this.reload();
          }
        })
      },
      async load(params) {
        const listQuery = {
          model: {...this.model}
        }
        Object.assign(listQuery, params)
        this.loading = true
        const { data } = await financeBillProduct(listQuery)
        data.records.forEach((item,index)=>{
          if (item.settlementAmount < 0||item.beCountSettlement===0) {
            item.selectable = true
          } else {
            item.selectable = false
          }
        })
        return { data }
        this.loading = false
      },
      searchLoad() {
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      reload() {
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      handleChangeTab (tab) {
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      handleRefresh(pageParams) {
        this.$refs.todoTable.doRefresh(pageParams)
      }
    }
  }
</script>
