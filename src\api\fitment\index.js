import requestAxios from '@/utils/requestAxios'



//页面服务->頁面管理   查询
export function pageInfo(code) {
    return requestAxios({
        url: '/api/merchant/admin/page/' + code ,
        method: 'GET'
    })
}

//页面服务->组件   页面组件 分页列表查询
export function pageComponentList(data) {
    return requestAxios({
        url: '/api/merchant/admin/pageComponent/page' ,
        method: 'post',
        data
    })
}
//页面服务->组件  页面组件 新增
export function pageComponentAdd(data) {
    return requestAxios({
        url: '/api/merchant/admin/pageComponent',
        method: 'POST',
        data
    })
}
//页面服务->组件  页面组件 批量新增
export function pageComponentListAdd(data) {
    return requestAxios({
        url: '/api/merchant/admin/pageComponent/batch',
        method: 'POST',
        data
    })
}
//页面服务->组件 页面组件 删除
export function pageComponentDel(componentId) {
    return requestAxios({
        url: '/api/merchant/admin/pageComponent?ids[]=' + componentId ,
        method: 'DELETE'
    })
} 

//页面服务->组件 页面组件 修改
export function pageComponentUpdate(data) {
    return requestAxios({
        url: '/api/merchant/admin/pageComponent',
        method: 'PUT',
        data
    })
} 





//页面服务->广告   广告 分页列表查询
export function pageADList(data) {
    return requestAxios({
        url: '/api/merchant/admin/pageAd/page',
        method: 'post',
        data
    })
}
//页面服务->广告  广告 新增
export function pageADAdd(data) {
    return requestAxios({
        url: '/api/merchant/admin/pageAd',
        method: 'POST',
        data
    })
}


//页面服务->广告  广告 根据组件ID批量删除广告
export function deleteByPageComponentId(pageComponentId) {
    return requestAxios({
        url: '/api/merchant/admin/pageAd/deleteByPageComponentId?pageComponentId=' + pageComponentId,
        method: 'delete'
    })
}


//页面服务->广告  广告 批量新增
export function pageADListAdd(data) {
    return requestAxios({
        url: '/api/merchant/admin/pageAd/batch',
        method: 'POST',
        data
    })
}

//页面服务->广告  广告 编辑
export function pageADEdit(data) {
    return requestAxios({
        url: '/api/merchant/admin/pageAd',
        method: 'PUT',
        data
    })
}


//页面服务->广告 广告 删除
export function pageADDel(adId) {
    return requestAxios({
        url: '/api/merchant/admin/pageAd?ids[]=' + adId,
        method: 'DELETE'
    })
} 