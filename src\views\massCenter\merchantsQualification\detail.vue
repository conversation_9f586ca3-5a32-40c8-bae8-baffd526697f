<template>
  <div class="archivesEditContent" v-if="listLoading">
    <page-title :title="query.name"/>
    <div class="item">
      <page-module-title title="基础信息" />
      <el-row :gutter="20">
        <el-col :span="6">
          <div><span style="color:#232333">客户编码:</span> {{query.code || '无'}}</div>
        </el-col>
        <el-col :span="6">
          <div><span style="color:#232333">客户名称:</span> {{query.name || '无'}}</div>
        </el-col>
        <el-col :span="6">
          <div><span style="color:#232333">统一社会信用代码:</span> {{query.socialCreditCode || '无'}}</div>
        </el-col>
        <el-col :span="6">
          <div><span style="color:#232333">法人代表:</span> {{query.legalPerson || '无'}}</div>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="6">
          <div><span style="color:#232333">企业类型:</span> {{query.merchantType || '无'}}</div>
        </el-col>
        <el-col :span="6">
          <div><span style="color:#232333">联系人:</span> {{query.ceoName || '无'}}</div>
        </el-col>
        <el-col :span="6">
          <div><span style="color:#232333">联系电话:</span> {{query.ceoMobile || '无'}}</div>
        </el-col>
        <el-col :span="6">
          <div><span style="color:#232333">所在区域:</span> {{query.region || '无'}}</div>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="6">
          <div><span style="color:#232333">注册地址:</span> {{query.registerAddress || '无'}}</div>
        </el-col>
        <el-col :span="6">
          <div><span style="color:#232333">创建时间:</span> {{query.createTime || '无'}}</div>
        </el-col>
      </el-row>
    </div>
    <div class="item">
      <page-module-title title="经营范围" />
      <div class="cateGory">
        <el-row :gutter="20" v-if="cateGory" style="width:100%">
          <el-col v-for="(ids, keys) in cateGory" :key="keys" :span="8" style="width:33%;flex:1;display:flex;flex-wrap:wrap;">
            <span>{{keys + ':'}}</span> <span style="white-space: nowrap;color:#aaaaaa" v-for="ite in ids" :key="ite.id">{{ite.label + '、'}}</span>
          </el-col>
        </el-row>
        <span style="padding-left:30px;color:#a9a9ac" v-else>无</span>
      </div>
    </div>
    <div class="item">
      <page-module-title title="客商资质" />
      <div class="content">
        <div style="padding-left: 10px; height: 30px; line-height: 30px; font-size:14px">企业类型： {{query.merchantType}}</div>
        <div class="table">
          <lisence-table v-if="lisenceTableDate.length!=0" :lisenceTableDate.sync="lisenceTableDate" :edit="['admin','merchantsQualification:edit']" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import LisenceTable from "@/views/businessCenter/businessList/lisenceTable";
import { getitem } from "@/api/massCenter/merchantsQualification/index";
import {
  listByLicenseBaseType,
  MerchantTypeDetailsById,
} from "@/api/purchasingAgent/archivesList";
export default {
  components: {
    LisenceTable
  },
  data() {
    return {
      listLoading: false,
      query: {},
      item: {},
      list: [],
      cateGory: {},
      lisenceTableDate: [],
      tableTitle: [
        {
          label: "证件类型",
          name: "label",
        },
        {
          label: "证件号",
          name: "licenseNumber",
        },
        {
          label: "过期时间",
          name: "licenseEndTime",
        },
        {
          label: "附件",
          name: "fileIds",
        },
      ],
    };
  },
  methods: {
    async getMerchantTypeDetailsById() {
      this.lisenceTableDate = [];
      let { data } = await MerchantTypeDetailsById(this.query.merchantTypeId);
      let tableDate = data.licenseBases;
      tableDate.forEach((item) => {
        let obj = {
          licenseBaseId: item.id,
          licenseEndTime: "",
          filePath: "",
          isForever: "",
          licenseNumber: "",
          label: item.name,
          isEdit: false,
          limit: item.multiple.code == "Y" ? 5 : 1,
        };
        this.query.merchantLicenses.find((ids) => {
          if (item.id == ids.licenseBaseId) {
            obj.licenseEndTime = ids.isForever.code === 'Y' ? '' :ids.licenseEndTime;
            obj.filePath = ids.filePath;
            obj.filePathList = this.getsrc(ids.filePath);
            obj.licenseNumber = ids.licenseNumber;
            obj.label = item.name;
            obj.merchantId = ids.merchantId;
            obj.id = ids.id;
            obj.isForever = ids.isForever.code === 'Y'
          }
        });
        this.lisenceTableDate.push(obj);
      });
    },
    back() {
      this.$store.dispatch("tagsView/delView", this.$route);
      this.$router.go(-1);
    },
    getsrc(str) {
      if (!str) {
        return [];
      } else {
        let arr = str.split(",");
        let list = [];
        arr.forEach((item) => {
          let obj = {
            response: {
              data: {
                url: "",
              },
            },
          };
          obj.response.data.url =
            "http://eyaolink-dev-bucket.oss-cn-shenzhen.aliyuncs.com/" + item;
          obj.url =
            "http://eyaolink-dev-bucket.oss-cn-shenzhen.aliyuncs.com/" + item;
          list.push(obj);
        });
        return list;
      }
    },
    async getitem() {
      let { data } = await getitem(this.$route.query.id);
      this.listLoading = true;
      this.query = data;
      let obj = {};

      if (!data.businessCategoryDetailList) {
        this.cateGory = false;
      } else {
        data.businessCategoryDetailList.forEach((item) => {
          if (!obj[item.parentName]) {
            obj[item.parentName] = [];
            obj[item.parentName].push(item);
          } else {
            obj[item.parentName].push(item);
          }
        });
        this.cateGory = obj;
      }
      // console.log(data)
      console.log(this.query.merchantLicenses);
      this.getMerchantTypeDetailsById(this.query.merchantLicenses);

      // let lisenceData = (await listByLicenseBaseType()).data;
      // lisenceData.forEach((item) => {
      //   let obj = {
      //     licenseBaseId: item.id,
      //     licenseEndTime: "",
      //     filePath: "",
      //     isForever: "",
      //     licenseNumber: "",
      //     label: item.name,
      //     isEdit: false,
      //     limit: item.multiple.code == "Y" ? 5 : 1,
      //   };
      //   data.merchantLicenses.find((ids) => {
      //     if (item.id == ids.licenseBaseId) {
      //       obj.licenseEndTime = ids.licenseEndTime;
      //       obj.filePath = ids.filePath;
      //       obj.licenseNumber = ids.licenseNumber;
      //       obj.filePathList = this.getsrc(ids.filePath);
      //       obj.label = item.name;
      //       obj.merchantId = ids.merchantId;
      //       obj.id = ids.id;
      //     }
      //   });
      //   this.lisenceTableDate.push(obj);
      // });
    },
    editLisenceFun(row) {
      row.isEdit = true;
      this.editLisenceItem = JSON.parse(JSON.stringify(row));
    },
    getsrc(str) {
      console.log(str);
      if (!str) {
        return [];
      } else {
        let arr = str.split(",");
        let list = [];
        arr.forEach((item) => {
          let obj = {
            response: {
              data: {
                url: "",
              },
            },
          };
          obj.response.data.url = item;
          obj.url = item;
          list.push(obj);
        });
        return list;
      }
    },
  },
  created() {
    this.getitem();
  },
  mounted() {
    this.query = this.row;
  }
};
</script>

<style lang="less" scoped>
.archivesEditContent {
  border-top: 1px solid #ebecee;
  padding: 0 20px;
  background-color: #fff;
  .item {
    width: 100%;
    margin-bottom: 30px;
    padding-bottom: 30px;
    border-bottom: 1px solid #eeeeee;
    &:last-child{
      margin-bottom: 0;
    }
    .cateGory {
      display: flex;
      padding: 0 30px;
      .el-col-8 {
        font-size: 14px;
        line-height: 20px;
        padding-bottom: 10px;
        text-overflow: wrap;
      }
    }
    .title {
      padding: 0 0 30px;
      span {
        font-size: 16px;
        padding-left: 10px;
        border-left: 4px solid rgba(64, 158, 255, 1);
      }
    }
    .formItem {
      width: 300px;
    }
  }
  .top_title {
    height: 56px;
    line-height: 56px;
    font-family: "PingFangSC-Regular", "PingFang SC", sans-serif;
    font-size: 18px;
    text-align: left;
    border-bottom: 1px solid #eeeeee;
    margin-bottom: 20px;
    .el-button {
      margin-left: 10px;
    }
  }
  .el-row {
    margin-bottom: 30px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .el-col {
    border-radius: 4px;
    color: #a9a9ac;
    font-size: 14px;
    span {
      color: #333 ;
      padding-right: 10px ;
    }
  }
  .grid-content {
    border-radius: 4px;
    min-height: 36px;
  }
  .row-bg {
    padding: 10px 0;
    background-color: #f9fafc;
  }
  .productPicContent .text p {
    font-family: "PingFangSC-Regular", "PingFang SC", sans-serif,
      "PingFangSC-Regular", "PingFang SC", sans-serif-400;
    font-weight: 400;
    color: #aaaaaa;
    line-height: 20px;
    font-size: 13px;
    margin: 0;
  }
  .content {
    // padding: 20px;
    background-color: #fff;
    .title {
      padding-bottom: 20px;
      span {
        margin-bottom: -2px;
        padding: 0 15px;
        height: 40px;
        line-height: 30px;
        display: block;
        background: rgba(255, 255, 255, 0);
        border-bottom: 2px solid rgb(64, 158, 255);
        font-size: 16px;
        font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
          "PingFang SC"-400;
        font-weight: 400;
        color: rgb(64, 158, 255);
      }
    }
  }
  .detailMsg {
    font-family: "PingFangSC-Regular", "PingFang SC", sans-serif,
      "PingFangSC-Regular", "PingFang SC", sans-serif-400;
    font-weight: 400;
    color: #aaaaaa;
    line-height: 20px;
    padding-bottom: 20px;
    font-size: 13px;
  }
  /deep/ .el-input.is-disabled .el-input__inner {
    color: #04060c;
    background-color: #eaf7e7;
  }
  // /deep/ .is-checked .is-disabled .el-checkbox__inner {
  //   color: #2fa338;
  //   background-color: #1b9e38;
  // }
  /deep/ .is-checked .el-checkbox__label {
    color: #04060c;
  }
  /deep/ .is-disabled .is-checked .el-radio__inner {
    background-color: #1b9e38;
  }
  /deep/ .is-disabled.is-checked.el-radio > .el-radio__label {
    color: #04060c;
  }
  /deep/ .el-col .el-col-6 span {
    padding-left: 100px;
  }
  /deep/ .el-upload {
    width: 40px;
    height: 40px;
    position: relative;
  }
  /deep/ .el-upload > i {
    font-size: 16px;
    position: absolute;
    left: 50%;
    top: 50%;
    -webkit-transform: translateX(-50%) translateY(-50%);
    transform: translateX(-50%) translateY(-50%);
  }
  /deep/ .el-upload-list .el-upload-list__item {
    width: 40px;
    height: 40px;
  }
  /deep/ .hide .el-upload--picture-card {
    display: none;
  }
}
</style>
