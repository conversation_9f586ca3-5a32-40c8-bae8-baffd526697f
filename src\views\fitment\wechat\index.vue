<template>
  <div
    class="wechatIndex"
    v-if="checkPermission(['admin', 'fitment-wechat:view'])"
  >
    <div class="pageBox">
      <div class="pageTitle">基药云</div>
      <div class="">
        <wechatSearch
          v-if="pagePlateId != ''"
          :pagePlateId="pagePlateId"
        ></wechatSearch>
        <hot-search-words
          v-if="pagePlateId != ''"
          :pagePlateId="pagePlateId"
        ></hot-search-words>
        <banner v-if="pagePlateId != ''" :pagePlateId="pagePlateId"></banner>
        <nav-taps
          v-if="pagePlateId != ''"
          :pagePlateId="pagePlateId"
          :navTapCompomentObj="navTapCompomentObj"
        ></nav-taps>
        <AnnouncementBox
          v-if="pagePlateId != ''"
          :pagePlateId="pagePlateId"
        ></AnnouncementBox>
        <capsule-banner
          v-if="pagePlateId != ''"
          :pagePlateId="pagePlateId"
        ></capsule-banner>
        <Discount
          v-if="pagePlateId != ''"
          :pagePlateId="pagePlateId"
        ></Discount>
        <product-types-tabs
          v-if="pagePlateId != ''"
          :pagePlateId="pagePlateId"
          :floorsCompomentObject="floorsCompomentObject"
        ></product-types-tabs>
      </div>
    </div>
  </div>
</template>
<script>
import checkPermission from "@/utils/permission";
import { pageInfo } from "@/api/fitment";
import wechatSearch from "../commons/wechat/Search";
import HotSearchWords from "../commons/wechat/HotSearchWords";
import Banner from "../commons/wechat/Banner";
import NavTaps from "../commons/wechat/NavTaps";
import AnnouncementBox from "../commons/wechat/AnnouncementBox";
import CapsuleBanner from "../commons/wechat/CapsuleBanner";
import Discount from "../commons/wechat/Discount";
import ProductTypesTabs from "../commons/wechat/ProductTypesTabs";
export default {
  data() {
    return {
      pagePlateId: "",
      navTapCompomentObj: null,
      floorsCompomentObject: null
    };
  },
  props: {},
  components: {
    wechatSearch,
    HotSearchWords,
    Banner,
    NavTaps,
    CapsuleBanner,
    Discount,
    AnnouncementBox,
    ProductTypesTabs
  },
  methods: {
    checkPermission,
    async pageInfoFun() {
      let { data } = await pageInfo("WeChatHome");
      if (data != null) {
        this.pagePlateId = data.id;
        data.pageComponentList.forEach(item => {
          if (item.componentCode == "main") {
            this.navTapCompomentObj = item;
          }
          if (item.componentCode == "floors") {
            this.floorsCompomentObject = item;
          }
        });
      }
    }
  },
  mounted() {
    this.pageInfoFun();
  },
  beforeDestroy() {}
};
</script>
<style lang="less" scoped>
.wechatIndex {
  background: #f2f3f4;
  padding: 25px;
  .items {
    border: 1px dashed red;
    margin-bottom: 12px;
    cursor: pointer;
  }
}
.wechatIndex .pageBox {
  background: #fff;
  padding: 25px;
  width: 425px;
  .pageTitle {
    height: 44px;
    line-height: 44px;
    font-size: 16px;
    font-weight: bold;
    text-align: center;
  }
}
</style>
