const Mock = require('mockjs')
const { param2Obj } = require('./utils')

const user = require('./user')
const article = require('./article')
const search = require('./remote-search')




// const role = require('./role')
// const businessCategory = require('./setting/businessCategory')
// const bill = require('./setting/card/bill')
// const cards = require('./setting/card/cards')
// const sms = require('./setting/sms/sms')
// const smsTemp = require('./setting/sms/temp')
// const smsTask = require('./setting/sms/smsTask')
// const staff = require('./setting/staff')
// const archives = require('./products/archives')
// const brand = require('./products/brand')
// //
// const business = require('./business/list')
// const purchasingAgent = require('./purchasingAgent/registercheck')
// 




const mocks = [
  ...article,
  ...search,
  ...user
  // ...role,
  // ...cards,
  // ...businessCategory,
  // ...bill,
  // ...sms,
  // ...staff,
  // ...archives,
  // ...brand,
  // ...smsTemp,
  // ...smsTask,
  // // 
  // ...business,
  // ...purchasingAgent
  // 
]

// for front mock
// please use it cautiously, it will redefine XMLHttpRequest,
// which will cause many of your third-party libraries to be invalidated(like progress event).
function mockXHR() {
  // mock patch
  // https://github.com/nuysoft/Mock/issues/300
  Mock.XHR.prototype.proxy_send = Mock.XHR.prototype.send
  Mock.XHR.prototype.send = function() {
    if (this.custom.xhr) {
      this.custom.xhr.withCredentials = this.withCredentials || false

      if (this.responseType) {
        this.custom.xhr.responseType = this.responseType
      }
    }
    this.proxy_send(...arguments)
  }

  function XHR2ExpressReqWrap(respond) {
    return function(options) {
      let result = null
      if (respond instanceof Function) {
        const { body, type, url } = options
        // https://expressjs.com/en/4x/api.html#req
        result = respond({
          method: type,
          body: JSON.parse(body),
          query: param2Obj(url)
        })
      } else {
        result = respond
      }
      return Mock.mock(result)
    }
  }

  for (const i of mocks) {
    Mock.mock(new RegExp(i.url), i.type || 'get', XHR2ExpressReqWrap(i.response))
  }
}

module.exports = {
  mocks,
  mockXHR
}
