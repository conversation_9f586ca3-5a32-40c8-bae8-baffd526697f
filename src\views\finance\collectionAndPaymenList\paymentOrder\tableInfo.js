export default {
  WAIT: [
    {
      key: 0,
      label: '付款单类型',
      name: "type",
      width: '140px',
      disabled: true
    },
    {
      key: 1,
      label: '付款单单号',
      name: "id",
      width: '200px',
      disabled: true
    },
    {
      key: 2,
      label: '关联业务单号',
      name: "businessNo",
      width: '170px',
      disabled: true
    },
    {
      key: 3,
      label: '收款方类型',
      name: "payerType",
      width: '170px'
    },
    {
      key: 4,
      label: '收款方',
      name: "payerName",
      width: '170px'
    },
    {
      key: 5,
      label: '应付金额（元）',
      name: "paymentAmount",
      width: '160px'
    },
    {
      key: 6,
      label: '制单人',
      name: "createUserName",
      width: '120px'
    },
    {
      key: 7,
      label: '制单时间',
      name: "createTime",
      width: '170px'
    }
  ],
  FINISH: [
    {
      key: 0,
      label: '付款单类型',
      name: "type",
      width: '140px',
      disabled: true
    },
    {
      key: 1,
      label: '付款单单号',
      name: "id",
      width: '200px',
      disabled: true
    },
    {
      key: 2,
      label: '关联业务单号',
      name: "businessNo",
      width: '170px',
      disabled: true
    },
    {
      key: 3,
      label: '收款方类型',
      name: "payerType",
      width: '170px'
    },
    {
      key: 4,
      label: '收款方',
      name: "payerName",
      width: '170px'
    },
    {
      key: 5,
      label: '已付金额（元）',
      name: "paymentAmount",
      width: '160px'
    },
    {
      key: 6,
      label: '制单人',
      name: "createUserName",
      width: '120px'
    },
    {
      key: 7,
      label: '制单时间',
      name: "createTime",
      width: '170px'
    },
    {
      key: 8,
      label: '出纳员',
      name: "cashierName",
      width: '100px'
    },
    {
      key: 9,
      label: '支付方式',
      name: "flowOnline",
      width: '100px'
    },
    {
      key: 10,
      label: '支付时间',
      name: "paymentTime",
      width: '170px'
    }
  ],
  COSE: [
    {
      key: 0,
      label: '付款单类型',
      name: "type",
      width: '140px',
      disabled: true
    },
    {
      key: 1,
      label: '付款单单号',
      name: "id",
      width: '200px',
      disabled: true
    },
    {
      key: 2,
      label: '关联业务单号',
      name: "businessNo",
      width: '170px',
      disabled: true
    },
    {
      key: 3,
      label: '付款方类型',
      name: "payerType",
      width: '170px'
    },
    {
      key: 4,
      label: '付款方',
      name: "payerId",
      width: '170px'
    },
    {
      key: 5,
      label: '应付金额（元）',
      name: "paymentAmount",
      width: '160px'
    },
    {
      key: 6,
      label: '制单人',
      name: "createUserName",
      width: '120px'
    },
    {
      key: 7,
      label: '制单时间',
      name: "createTime",
      width: '170px'
    },
    {
      key: 8,
      label: '出纳员',
      name: "cashierName",
      width: '170px'
    },
    {
      key: 8,
      label: '支付时间',
      name: "paymentTime",
      width: '170px'
    },
    {
      key: 8,
      label: '异常原因',
      name: "remarks",
      width: '170px'
    }
  ],
  ERROR: [
    {
      key: 0,
      label: '付款单类型',
      name: "type",
      width: '140px',
      disabled: true
    },
    {
      key: 1,
      label: '付款单单号',
      name: "id",
      width: '200px',
      disabled: true
    },
    {
      key: 2,
      label: '关联业务单号',
      name: "businessNo",
      width: '170px',
      disabled: true
    },
    {
      key: 3,
      label: '付款方类型',
      name: "payerType",
      width: '170px'
    },
    {
      key: 4,
      label: '收款方',
      name: "payerName",
      width: '170px'
    },
    {
      key: 5,
      label: '应付金额（元）',
      name: "paymentAmount",
      width: '160px'
    },
    {
      key: 6,
      label: '制单人',
      name: "createUserName",
      width: '120px'
    },
    {
      key: 7,
      label: '制单时间',
      name: "createTime",
      width: '170px'
    },
    {
      key: 8,
      label: '出纳员',
      name: "cashierName",
      width: '170px'
    },
    {
      key: 8,
      label: '支付时间',
      name: "paymentTime",
      width: '170px'
    },
    {
      key: 8,
      label: '异常原因',
      name: "remarks",
      width: '170px'
    }
  ],
  PROCESS:[
    {
      key: 0,
      label: '付款单类型',
      name: "type",
      width: '140px',
      disabled: true
    },
    {
      key: 1,
      label: '付款单单号',
      name: "id",
      width: '200px',
      disabled: true
    },
    {
      key: 2,
      label: '关联业务单号',
      name: "businessNo",
      width: '170px',
      disabled: true
    },
    {
      key: 3,
      label: '收款方类型',
      name: "payerType",
      width: '170px'
    },
    {
      key: 4,
      label: '收款方',
      name: "payerName",
      width: '170px'
    },
    {
      key: 5,
      label: '应付金额（元）',
      name: "paymentAmount",
      width: '160px'
    },
    {
      key: 6,
      label: '制单人',
      name: "createUserName",
      width: '120px'
    },
    {
      key: 7,
      label: '制单时间',
      name: "createTime",
      width: '170px'
    }
  ]
}
