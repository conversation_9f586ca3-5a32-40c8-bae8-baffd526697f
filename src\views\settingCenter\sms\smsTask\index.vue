<template>
  <div class="smsSituationPageContent">
    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        v-model="activeId"
        :tabs="tabs"
        @change="chageTabsFun"
      >
        <template slot="button">
        </template>
      </tabs-layout>
      <div class="table">
        <el-table ref="table"   @selection-change="selectionChangeFun" v-loading="listLoading" :data="list" row-key="id"  border fit highlight-current-row  style="width: 100%" >
          <el-table-column
            type="selection"
            width="55"
            :reserve-selection="true"
            fixed
          >
          </el-table-column>
          <el-table-column
            v-for="(item, index) in tableTitle"
            :key="index"
            :width="item.width"
            :min-width="(item.width?item.width:'350px')"
            :label="item.label"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              <span v-if="item.name=='status'">{{ row[item.name].code|sendStatusFilter }}</span>
              <span v-else>{{ row[item.name] }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="checkPermission(['admin','admin-setting-sms:cancel'])"  fixed="right" align="center" label="操作" width="80" class="itemAction">
            <template slot-scope="scope">
              <el-row class="table-edit-row">
                <span class="table-edit-row-item">
                  <el-button @click="clearItemFun(scope.row)" type="text" >取消</el-button>
                </span>
              </el-row>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="listQuery.current" :limit.sync="listQuery.size" @pagination="getList" />
      </div>
    </div>

    <!-- 设置 编辑 -->
    <el-dialog v-if="showEdit" :title="'短信详情'" :visible.sync="showEdit" width="450" >
        <detail :visible.sync="showEdit" :isReload.sync="submitReload" :row.sync="row"></detail>
    </el-dialog>
    <!-- 设置 编辑 -->
  </div>
</template>
<script>
import checkPermission from '@/utils/permission'
import { list,clearSmsTask,exportExcel } from '@/api/setting/sms/smsTask'
import detail from '@/views/settingCenter/sms/smsTask/detail'
import Pagination from '@/components/Pagination'
import {downloadFile} from '@/utils/commons'
import TabsLayout from '@/components/TabsLayout'
export default {
  data() {
    return {
      activeId:"all",
      showEdit:false,
      row:{
          id:0,
          type:"",
          name:"",
      },
      multipleSelection:[],
      tableTitle:[
          {
              label: '主题',
              name: "topic",
              width: '150px'
          },
          {
              label: '接收者手机号码',
              name: "receiver",
              width: '180px'
          },
          {
              label: '发送时间',
              name: "sendTime",
              width: '180px'
          },
          {
              label: '发送内容',
              name: "content",
          },
          {
              label: '发送状态',
              name: "status",
              width: '150px'
          }
      ],
      submitReload:false,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        model: {
          status: ""
        },
        current: 1,
        size: 10
      },
      tabs: [
        {
          name: '全部',
          value: 'all',
          hide: !checkPermission(['admin', 'admin-setting-sms:addView'])
        },
        {
          name: '短信发送中',
          value: 'WAITING',
          hide: !checkPermission(['admin', 'admin-setting-sms:handlingView'])
        },
        {
          name: '短信成功',
          value: 'SUCCESS',
          hide: !checkPermission(['admin', 'admin-setting-sms:successView'])
        },
        {
          name: '发送失败',
          value: 'FAIL',
          hide: !checkPermission(['admin', 'admin-setting-sms:failView'])
        }
      ]
    };
  },
  watch:  {
    submitReload:function(newVal,oldVal){
      if(newVal){
        this.submitReload=false;
        console.info("重新加载")
      }
    }
  },
  components: {
    Pagination,
    detail,
    TabsLayout
  },
  methods: {
    checkPermission,
    async outExcel() {
      const tHeader = ['主题', '接收者手机号码', '发送时间','发送内容','来源类型', '发送状态']
      const filterVal = ['topic', 'receiver', 'sendTime', 'content','sourceType', 'status']
      // let {data} = await list({...this.listQuery, ...{size: 10000}})
      let exportData= this.formatJson(this.multipleSelection,filterVal)
      downloadFile({tHeader:tHeader,exportData:exportData})
    },
    formatJson(dataList,filterVal) {
      return dataList.map(v => filterVal.map(j => {
        if (j === 'status') {
          return v[j].desc
        } else if (j === 'sourceType') {
          return v[j].desc
        } else {
          return v[j]
        }
      }))
    },
    taskDetail:function(row){
    },
    clearItemFun:function(row){
      clearSmsTask(row.id)
      this.getList()
    },
    selectionChangeFun:function(val) {
      this.multipleSelection = val
    },
    chageTabsFun:function() {
      this.$refs.table.clearSelection()
      this.list = []
      this.listQuery.current = 1
      this.getList()
    },
    async getList() {
      this.listLoading = true
      if(this.activeId === 'all')  this.listQuery.model.status = ''
      else  this.listQuery.model.status = this.activeId
      const { data } = await list(this.listQuery)
      this.list = data.records
      this.total = data.total
      this.listLoading = false
    }
  },
  mounted() {
      this.getList()
  },
  beforeDestroy() {}
};
</script>
<style lang="scss" scoped>
@import "@/styles/element-variables.scss";

.smsSituationPageContent {
  padding: 0;
  .title{
        border-bottom:2px solid #EBECEE;
         margin-bottom: 16px;
      span{
        margin-bottom: -2px;
        padding:0 15px;
        height: 40px;
        line-height: 40px;
        display:block;
        background: rgba(255,255,255,0);
        border-bottom:2px solid rgb(64, 158, 255);
        font-size: 16px;
        font-family: 'PingFangSC-Regular', 'PingFang SC', 'PingFangSC-Regular', 'PingFang SC'-400;
        font-weight: 400;
        color:rgb(64, 158, 255);
      }
  }
  .formItem{width:586px;}
  .line{color:#dfe6ec; margin:0 6px;}

  .typeTabs{height: 40px;margin-bottom: -2px; margin-left:10px;}

}
</style>
<style lang="less" scoped>
/deep/ .el-table-column--selection {text-align: center}
</style>
