<template>
  <div class="table">
    <el-table
      ref="table"
      v-if="list"
      v-loading="listLoading"
      :data="list"
      row-key="id"
      border
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column align="center" width="80" :render-header="renderHeader">
        <template slot-scope="scope">
          <span>{{ scope.$index + 1 }} </span>
        </template>
      </el-table-column>
      <el-table-column
        v-for="(item, index) in tableTitle"
        :key="index"
        :min-width="item.width ? item.width : '350px'"
        :label="item.label"
        show-overflow-tooltip
        align="left"
      >
        <template slot-scope="{ row }">
          <span>{{ row[item.name] }}</span>
        </template>
      </el-table-column>
<!--
      <el-table-column
        fixed="right"
        align="center"
        label="操作"
        width="150"
        class="itemAction"
      >
        <template slot-scope="{ row }">
          <el-button type="text"  @click="addFun(row)"
            >充值</el-button
          >
          <el-button type="text"  @click="reduceFun(row)"
            >扣减</el-button
          >
          <el-button type="text" >查看记录</el-button>
        </template>
      </el-table-column> -->
    </el-table>
    <pagination
      v-if="total > 0"
      :pageSizes="[10, 20, 50, 100]"
      :total="total"
      :page.sync="listQuery.current"
      :limit.sync="listQuery.size"
      @pagination="getlist"
    />
  </div>
</template>

<script>
import Pagination from "@/components/Pagination";
import tableInfo from "@/views/finance/merchantsBalance/tableInfo";
import { detaillist } from "@/api/finance/merchantsBalance";
import { getitem } from "@/api/finance/merchantsBalance";
export default {
  data() {
    return {
      list: [],
      total: 0,
      tableTitle: tableInfo['tableTitleInfo'],
      listQuery: {
        current: 1,
        size: 10,
        model: {},
      },
    };
  },
  methods: {
    async getlist() {
      let {data} = await  detaillist(this.row.id)
      console.log(data)
    },
    renderHeader(h, { column }) {
      return (
        <div style="position:relative">
          <i class="el-icon-menu" />
        </div>
      );
    },
  },
  props: {
    row: {
      type: Object,
      required: true
    },
    visible: {
      type: Boolean,
      required: true
    }
  },
  components: {
    Pagination,
  },
};
</script>

<style>
</style>
