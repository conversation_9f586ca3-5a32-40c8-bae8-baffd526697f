import requestAxios from '@/utils/requestAxios'

export function login(data) {
  return requestAxios({
    url: '/api/oauth/anno/token',
    method: 'post',
    data
  })
}

export function sendPhoneValidateCode(data) {
  return requestAxios({
    url: '/api/oauth/anno/sendPhoneValidateCode',
    method: 'post',
    data
  })
}
export function secondaryVerificationEnabled(data) {
  return requestAxios({
    url: '/api/oauth/anno/secondaryVerificationEnabled',
    method: 'get',
    data
  })
}
export function reloadToken() {
  return requestAxios({
    url: '/api/oauth/anno/verify',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    params: {
    }
  })
}

export function getInfo(token) {
  return requestAxios({
    url: '/vue-element-admin/user/info',
    method: 'get',
    params: { token }
  })
}

export function logout() {
  return requestAxios({
    url: '/vue-element-admin/user/logout',
    method: 'post'
  })
}

export function getUserMenuRouter(userId) {
  return requestAxios({
    url: '/api/oauth/menu/router',
    method: 'get',
    // params: {
    //   userId: userId
    //  }
  })
}

export function getUserResourceVisible(userId) {
  return requestAxios({
    url: '/api/oauth/resource/visible',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    params: {
      userId: userId
    }
  })
}
