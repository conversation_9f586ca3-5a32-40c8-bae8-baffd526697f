<template>
  <div class="siteManagementEditContent">
    <page-title :title="$route.query.id == null ? '新增站点' : '编辑站点'">
      <template>
        <el-button type="primary" @click="submitFun('ruleForm')">提 交</el-button>
      </template>
    </page-title>
    <el-form
      ref="ruleForm"
      :inline="true"
      class="form"
      :model="query"
      :rules="rules"
      label-width="90px"
    >
      <div class="item">
        <module-title title="站点信息" />
        <div>
          <el-row>
            <el-col :span="6">
              <el-form-item class="formItem minW" label="站点编码:">
                <el-input
                  v-model="query.siteNum"
                  :disabled="true"
                  clearable
                  placeholder="系统自动生成编码"
                  style="width: 100%"
                />
                <!-- :disabled="query.role.id > 0" -->
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item
                class="formItem minW"
                prop="siteName"
                label="站点名称:"
              >
                <el-input
                  v-model="query.siteName"
                  clearable
                  placeholder="请填写站点名称"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item class="formItem minW" label="负责人姓名:">
                <el-input
                  v-model="query.managerName"
                  clearable
                  placeholder="请填写负责人姓名"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item class="formItem minW" prop="managerMobile" label="负责人手机:">
                <el-input
                  v-model="query.managerMobile"
                  clearable
                  placeholder="请填写负责人手机"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>

      <div class="item">
        <module-title title="账号信息" />
        <div>
          <el-row>
            <el-col :span="6">
              <el-form-item
                class="formItem minW"
                prop="account"
                label="登录账号:"
              >
                <el-input
                  v-model="query.account"
                  :disabled="isEdit"
                  clearable
                  placeholder="请填写登录账号"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col v-if="!this.$route.query.id" :span="6">
              <el-form-item class="formItem minW" prop="password" label="登录密码:">
                <el-input
                  v-model="query.password"
                  clearable
                  placeholder="请填写登录密码"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>

            <el-col v-if="!this.$route.query.id" :span="6">
              <el-form-item
                class="formItem minW"
                prop="checkPass"
                label="确认密码:"
              >
                <el-input
                  v-model="query.checkPass"
                  clearable
                  placeholder="请确认密码"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item class="formItem minW" prop="userMobile" label="登录手机号:">
                <el-input
                  v-model="query.userMobile"
                  clearable
                  :disabled="isEdit"
                  placeholder="请填写登录手机号"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>

      <div class="item">
        <module-title title="管辖地区" />
        <div>
          <areaTree :selected-data.sync="query.provinceIds" @onConfirm="onConfirm" />
        </div>
      </div>
    </el-form>
  </div>
</template>
<script>
import {
  adminSite,
  feignListByUserId
} from '@/api/setting/permission/userRole'
import ModuleTitle from '@/components/PageModuleTitle'
import areaTree from '@/components/areaTree'
import PageTitle from '../../../components/PageTitle/index'

export default {
  components: {
    PageTitle,
    ModuleTitle,
    areaTree
  },
  data() {
    var validatePass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入登录密码'))
      } else {
        if (this.query.checkPass !== '') {
          this.$refs.ruleForm.validateField('checkPass')
        }
        callback()
      }
    }
    var validatePass2 = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.query.password) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }

    return {
      query: {
        account: '',
        managerMobile: '',
        managerName: '',
        password: '',
        checkPass: '',
        provinceIds: [],
        publishStatus: 'Y',
        siteName: '',
        userId: '',
        userMobile: ''
      },
      selectedData: [],
      isEdit: false,
      check: [],
      data: [],
      role: {},
      id: '',
      appList: [],
      rules: {
        siteName: [
          {
            required: true,
            message: '请填写站点名称',
            trigger: 'blur'
          }
        ],
        account: [
          {
            required: true,
            message: '请填写登录账号',
            trigger: 'blur'
          }
        ],
        managerMobile: [
          { min: 11, max: 11, message: '请输入11位手机号码', trigger: 'blur' },
          {
            pattern: /^1\d{10}$/,
            message: '请输入正确的手机号码'
          }
        ],
        userMobile: [
          { min: 11, max: 11, message: '请输入11位手机号码', trigger: 'blur' },
          {
            pattern: /^1\d{10}$/,
            message: '请输入正确的手机号码'
          }
        ],
        password: [{ required: true, validator: validatePass, trigger: 'blur' }],
        checkPass: [
          { required: true, validator: validatePass2, trigger: 'blur' }
        ]
      }
    }
  },
  async mounted() {
    if (this.$route.query.id > 0) {
      this.isEdit = true
      await this.getFun()
    } else {
    }
  },
  beforeDestroy() {},

  methods: {
    goBackFun() {
      this.$router.go(-1)
      this.$store.dispatch('tagsView/delView', this.$route)
    },
    onConfirm(e) {
      // this.query.provinceIds = e.provinceId.split(",");
      this.query.provinceIds = e.checkedKeys
    },
    clearFun: function() {
      var _this = this
      _this.goBackFun()
    },
    submitFun(formName) {
      const _this = this
      this.$refs[formName].validate(async(valid) => {
        if (valid) {
          if (this.query.provinceIds && this.query.provinceIds.length === 0) {
            this.$message.error('请选择管辖地区!')
            return
          }
          if (this.$route.query.id === null) {
            await adminSite(_this.query).then(res => {
              if (res.code === 0) {
                _this.goBackFun()
              }
            })
          } else {
            const obj = {
              ..._this.query,
              id: _this.$route.query.id
            }

            await adminSite(obj).then(res => {
              if (res.code === 0) {
                _this.goBackFun()
              }
            })
          }
        } else {
          return false
        }
      })
    },
    async getFun() {
      // const { data } = await getApi(this.$route.query.id);
      // this.query.role = Object.assign(this.query.role, data);
      this.getFindAuthorityIdByRoleId()
    },
    async getFindAuthorityIdByRoleId() {
      const { data } = await feignListByUserId(this.$route.query.id)
      this.query = Object.assign(data)
      this.query.provinceIds = JSON.parse(data.province)
    }
  }
}
</script>
<style lang="less" scoped>
.siteManagementEditContent {
  // margin: -30px -20px;
  border-top: 1px solid #ebecee;
  padding: 0px 20px 20px;
  background-color: #fff;
  .formItem {
    text-align: left;
  }

  .item {
    width: 100%;
    margin-bottom: 30px;
    // border-bottom: 1px solid #eeeeee;
    &:last-child {
      margin-bottom: 0;
    }
    .formItem {
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
    }
  }
}
/deep/ .minW .el-form-item__content {
  width: calc(100% - (100px + 15px));
}
</style>
