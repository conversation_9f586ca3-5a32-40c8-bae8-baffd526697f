<template>
  <div class="goods">
    <im-search-pad
      :has-expand="false"
      :model="listQuery"
      @reset="resetFun"
      @search="searchFun"
    >
      <im-search-pad-item prop="purMerchantCode">
        <el-input v-model="listQuery.model.purMerchantCode" placeholder="请输入客户编码" />
      </im-search-pad-item>
      <im-search-pad-item prop="purMerchantName">
        <el-input v-model="listQuery.model.purMerchantName" placeholder="请输入客户名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="cityValue">
        <el-cascader
          placeholder="请选择所在区域"
          v-model="cityValue"
          :props="props"
          @change="cityChange"
          clearable
        >
        </el-cascader>
      </im-search-pad-item>
    </im-search-pad>
    <el-table
      v-loading="isLoading"
      @selection-change="selectTableItemFun"
      border
      fit
      :data="list"
      style="width: 100%"
      max-height="450px"
    >
      <el-table-column
        type="selection"
        width="55"
        align="center"
        :selectable="checkSelect"
        v-if="isAll"
      ></el-table-column>
      <el-table-column
        v-else
        type="selection"
        width="55"
        align="center"
        fixed
      ></el-table-column>
      <el-table-column
        v-for="(item, index) in tableTitle"
        :key="index"
        :min-width="item.width ? item.width : '350px'"
        :label="item.label"
        show-overflow-tooltip
        align="left"
      >
        <template slot-scope="{ row }">
          <span>{{ row[item.name] }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="isAll"
        fixed="right"
        align="center"
        label="操作"
        width="80"
        class="itemAction"
      >
        <template slot-scope="{ row }">
          <el-button
            type="text"

            @click="bindMerchant(row)"
            v-if="row.isBinding == '0'"
            >绑定</el-button
          >
          <el-button v-else disabled type="text" >已绑</el-button>
        </template>
      </el-table-column>

      <el-table-column
        v-if="!isAll"
        fixed="right"
        align="center"
        label="操作"
        width="80"
        class="itemAction"
      >
        <template slot-scope="{ row }">
          <el-button type="text"  @click="unboundMerchant(row)"
            >解绑</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="flex_between_center">
      <div>
        当前页已选择
        <span style="font-size: 20px; color: #00f; margin-right: 10px">{{
          this.selectTableArr.length
        }}</span>
        <el-button
          v-if="isAll"
          :disabled="selectTableArr.length == 0"
          @click="bindAll"
          >批量绑定</el-button
        >
        <el-button
          v-else
          :disabled="selectTableArr.length == 0"
          @click="unBindAll"
          >批量解绑</el-button
        >
      </div>
      <pagination
        v-if="totals > 0 && !isAll"
        :pageSizes="[2, 10, 20, 50]"
        :total="totals"
        :page.sync="listQuery.current"
        :limit.sync="listQuery.size"
        @pagination="getlist"
      />
      <pagination
        v-if="totals > 0 && isAll"
        :pageSizes="[2, 10, 20, 50]"
        :total="totals"
        :page.sync="listQuery.current"
        :limit.sync="listQuery.size"
        @pagination="getAll"
      />
    </div>
    <!-- <div class="dialog-footer">
      <el-button @click="cancelFun">取 消</el-button>
      <el-button type="primary" @click="confirmFun">确 定</el-button>
    </div> -->
  </div>
</template>

<script>
const tableInfo = [
  {
    key: 0,
    name: "code",
    label: "客户编码",
    width: "120px",
  },
  {
    key: 1,
    name: "name",
    label: "客户名称",
    width: "170px",
  },
  {
    key: 2,
    name: "merchantType",
    label: "企业类型",
    width: "170px",
  },
  {
    key: 3,
    name: "ceoName",
    label: "负责人",
    width: "120px",
  },
  {
    key: 4,
    name: "ceoMobile",
    label: "联系电话",
    width: "170px",
  },
  {
    key: 5,
    name: "region",
    label: "所在地区",
    width: "200px",
  },
];
import Pagination from "@/components/Pagination";
import checkPermission from "@/utils/permission";
import { areas } from "@/api/enterprise";
import {
  salesManMerchant,
  allMerchant,
  bindMerchant,
  unboundMerchant,
} from "@/api/businessCentric/salesmanList";
export default {
  data() {
    return {
      isLoading: false,
      tableTitle: tableInfo,
      totals: 11,
      list: [],
      listQuery: {
        current: 1,
        size: 10,
        model: {},
      },
      tableSelectTitle: [],
      showSelectTitle: false,
      selectTableArr: [],
      cityValue: [],
      props: {
        lazy: true,
        checkStrictly: true,
        async lazyLoad(node, resolve) {
          const { level } = node;
          let id = node.data ? node.data.id : "";
          let res = await areas({ parentId: id });
          let list = res.data;
          list.forEach((item) => {
            item.value = item.id;
            item.leaf = level >= 2;
          });
          resolve(list);
        },
      },
    };
  },
  methods: {
    async searchFun() {
      this.list = [];
      if (this.isAll) {
        this.getAll();
      } else {
        this.getlist();
      }
    },
    resetFun() {
      this.list = [];
      this.listQuery = {
        current: 1,
        size: 10,
        model: {},
      };
      this.cityValue = []
      if (this.isAll) {
        this.getAll();
      } else {
        this.getlist();
      }
    },
    cityChange(e) {
      this.listQuery.model.provinceId = e[0];
      this.listQuery.model.cityId = e[1];
      this.listQuery.model.countyId = e[2];
    },
    async unboundMerchant(row) {
      let { data } = await unboundMerchant({ id: this.row.id, data: [row.id] });
      if (data) {
        this.$message.success("解除绑定成功");
        let ind;
        this.list.find((item, index) => {
          if (item.id == row.id) {
            ind = index;
          }
        });
        this.list.splice(ind, 1);
        let _row = JSON.parse(JSON.stringify(this.row));
        _row.customersNumber -= 1;
        this.$emit("update:row", _row);
      }
    },
    async unBindAll() {
      let arr = [];
      this.selectTableArr.forEach((item) => {
        arr.push(item.id);
      });
      let { data } = await unboundMerchant({ id: this.row.id, data: arr });
      let _row = JSON.parse(JSON.stringify(this.row));
      if (data) {
        _row.customersNumber -= arr.length;
        this.$emit("update:row", _row);
        this.$message.success("批量解除绑定成功");
        this.getlist();
      }
    },
    checkSelect(row, index) {
      let isChecked = true;
      if (row.isBinding == 0) {
        isChecked = true;
      } else {
        isChecked = false;
      }
      return isChecked;
    },
    async bindMerchant(row) {
      let { data } = await bindMerchant({ id: this.row.id, data: [row.id] });
      if (data) {
        this.$message.success("绑定客户成功");
        row.isBinding = 1;
        let _row = JSON.parse(JSON.stringify(this.row));
        _row.customersNumber += 1;
        this.$emit("update:row", _row);
        // this.$emit("update:visible", false);
      }
    },
    async bindAll() {
      let arr = [];
      this.selectTableArr.forEach((item) => {
        arr.push(item.id);
      });
      let _row = JSON.parse(JSON.stringify(this.row));
      let { data } = await bindMerchant({ id: this.row.id, data: arr });
      if (data) {
        this.$message.success("批量绑定客户成功");
        _row.customersNumber += arr.length;
        this.$emit("update:row", _row);
        this.getAll();
        this.selectTableArr.forEach((item) => {
          item.isBinding = 1;
        });
      }
    },
    selectTableItemFun(row) {
      this.selectTableArr = row;
    },
    async getAll() {
      this.isLoading = true;
      let { data } = await allMerchant({
        id: this.row.id,
        data: this.listQuery,
      });
      this.list = data.records;
      this.isLoading = false;
      this.totals = data.total;
    },
    async getlist() {
      this.isLoading = true;
      let { data } = await salesManMerchant({
        data: this.listQuery,
        id: this.row.id,
      });
      if (data) {
        this.list = data.records;
        this.totals = data.total;
        this.$emit("update:merchantNum", data.total);
      }
      this.isLoading = false;
    },
    confirmFun() {},
    cancelFun() {
      this.$emit("update:visible", false);
    },
    renderHeader(h, { column }) {
      return (
        <div style="position:relative">
          <i class="el-icon-menu" />
        </div>
      );
    },
    checkPermission,
  },
  created() {
    if (this.isAll) {
      this.getAll();
    } else {
      this.getlist();
    }
  },
  components: {
    Pagination,
  },
  props: {
    row: {
      type: Object,
      required: true,
    },
    visible: {
      type: Boolean,
      default: false,
      required: true,
    },
    merchantNum: {
      type: Number,
      default: 0,
    },
    isAll: {
      type: Boolean,
      required: true,
    },
  },
};
</script>

<style lang="less" scoped>
.goods {
  .dialog-footer {
    border-top: 1px solid #efefef;
    margin: -30px -20px;
    margin-top: 30px;
    padding: 20px;
    padding-top: 10px;
    text-align: right;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
  }
}
</style>
