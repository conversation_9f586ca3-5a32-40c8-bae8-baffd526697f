<template>
  <div class="archivesPageContent">
    <im-search-pad
      :has-expand="false"
      :model="listQuery"
      @reset="resetForm"
      @search="onSearchSubmitFun"
    >
      <im-search-pad-item prop="productCode">
        <el-input v-model="listQuery.model.productCode" placeholder="请输入商品编码" />
      </im-search-pad-item>
      <im-search-pad-item prop="productName">
        <el-input v-model="listQuery.model.productName" placeholder="请输入商品名称" />
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        :tabs="[{ name: '商品资质' }]"
      >
        <template slot="button">
          <div></div>
        </template>
      </tabs-layout>
      <div class="table">
        <el-table v-if="list" v-loading="listLoading" :data="list" row-key="id" border fit highlight-current-row style="width: 100%">
          <el-table-column align="center" :render-header="renderHeader" width="65" fixed>
            <template slot-scope="scope">
              {{ scope.$index+1 }}
            </template>
          </el-table-column>
          <el-table-column label="主图" prop="pictIdS" class-name="img-cell">
            <template slot-scope="scope">
              <el-popover placement="right" trigger="hover">
                <img :src="scope.row.pictIdS | imgFilter" alt="" width="200" height="200">
                <img slot="reference" :src="scope.row.pictIdS | imgFilter" alt="" width="50" height="50">
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column
            v-for="(item, index) in tableTitle"
            :key="index"
            :min-width="(item.width?item.width:'350px')"
            :label="item.label"
            :show-overflow-tooltip="item.name!='qualityReportRelVoList'"
            align="left"
          >
            <template slot-scope="{row}">
              <template v-if="item.name=='qualityReportRelVoList'">
                <el-button v-for="ite in row[item.name]" :key="ite.id" type="text" style="color:#409EFF">{{ ite.licenseId }}</el-button>
              </template>

              <template v-else-if="item.name=='productLicenseRelVoList'">
                <el-button v-if="row[item.name]" type="text" style="color:#409EFF" @click="detailFun(row)">已上传（{{ row[item.name].length }}）</el-button>
                <el-button v-else type="text" style="color:#409EFF">未上传</el-button>
              </template>
              <el-button v-else-if="item.name=='publishStatus'&&row[item.name].code=='1'" type="text" style="color:#409EFF">已启用</el-button>
              <el-button v-else-if="item.name=='publishStatus'&&row[item.name].code=='0'" type="text" style="color:#FF3C54">已冻结</el-button>
              <span v-else>{{ row[item.name] }}</span>
            </template>
          </el-table-column>

          <el-table-column fixed="right" align="center" label="操作" width="120" class="itemAction">
            <template slot-scope="scope">
              <el-button type="text" v-if="checkPermission(['admin', 'admin-product-qualification:detail'])" @click="detailFun(scope.row)">查看详情</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-if="total > 0" :total="total" :page.sync="listQuery.current" :limit.sync="listQuery.size" @pagination="getlist" />
      </div>
    </div>
    <el-dialog v-if="showEditPage" :title="row.name" :visible.sync="showEditPage" width="80%">
      <edit :visible.sync="showEditPage" :is-reload.sync="submitReload" :row.sync="row" />
    </el-dialog>
  </div>
</template>

<script>
import { setContextData, getContextData } from '@/utils/auth'
import tableInfo from '@/views/massCenter/productQualification/tableInfo'
import Pagination from '@/components/Pagination'
import { list } from '@/api/massCenter/productQualification'
import edit from '@/views/massCenter/productQualification/detail'
import TabsLayout from '@/components/TabsLayout'
import checkPermission from '@/utils/permission'
export default {
  components: {
    Pagination,
    edit,
    TabsLayout
  },
  data() {
    return {
      listQuery: {
        current: 1,
        size: 10,
        model: {}
      },
      list: [],
      listLoading: false,
      tableTitle: tableInfo,
      total: 0,
      tableSelectTitle: [0, 1, 2, 3],
      showSelectTitle: false,
      showEditPage: false,
      row: {},
      submitReload: ''
    }
  },
  methods: {
    checkPermission,
    async getlist() {
      this.listLoading = true
      const { data } = await list(this.listQuery)
      if (data) {
        this.total = data.total
        data.records.forEach((item) => {
          item.pictIdS ? item.pictIdS = item.pictIdS.split(',')[0] : item.pictIdS = ''
        })
        this.list = data.records
      } else {
        this.list = []
        this.total = 0
      }
      this.listLoading = false
    },
    detailFun(row) {
      setContextData('productQualification_detail', this.listQuery)
      this.$router.push({
        path: '/massCenter/productQualification/detail',
        query: {
          id: row.id
        }
      })
      // this.showEditPage = true;
      // this.row = row;
    },
    onSearchSubmitFun() {
      this.list = []
      this.getlist()
    },
    resetForm() {
      this.listQuery = {
        current: 1,
        size: 10,
        model: {}
      }
      this.getlist()
    },
    renderHeader(h, { column }) {
      var titles = tableInfo
      var titlesName = ['显示字段项', '隐藏字段项']
      return (
        <div style='position:relative'>
          <div onClick={this.showHeaer}>
            <i class='el-icon-menu' />
          </div>
          <el-dialog
            title='设置显示列表'
            showClose={false}
            visible={this.showSelectTitle}
            width='640px'
            center
            append-to-body={true}
          >
            <el-transfer
              vModel={this.tableSelectTitle}
              data={titles}
              titles={titlesName}
              onChange={this.setleftTitleFun}
            ></el-transfer>
            <div style='margin-top: 25px;text-align: center;'>
              <el-button onClick={this.closeHeaer}>取消</el-button>
              <el-button type='primary' onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      )
    },
    showHeaer: function() {
      this.showSelectTitle = true
    },
    setleftTitleFun(val) {
      this.tableSelectTitle = val
    },
    closeHeaer: function() {
      this.showSelectTitle = false
      this.tableSelectTitle = []
    },
    setHeaer: function() {
      var titles = tableInfo
      var listinfo = titles.filter((element, index, self) => {
        return !this.tableSelectTitle.includes(element.key)
      })
      this.tableTitle = listinfo
      this.showSelectTitle = !this.showSelectTitle
    },
    initTbaleTitle() {
      this.tableTitle = tableInfo
      this.tableSelectTitle = []
    }
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (from.path == '/massCenter/productQualification/detail') {
        if (getContextData('productQualification_detail') != '') {
          vm.listQuery = getContextData('productQualification_detail')
        }
      }
      vm.initTbaleTitle()
      vm.getlist()
    })
  }
}
</script>

<style lang="scss" scoped>
.archivesPageContent {
  padding: 0;
  background-color: #f2f3f4;
  .temp_searchBox {
    height: 64px;
    overflow: hidden;
    margin-bottom: 0;
  }
  .form-inline {
    height: 60px;
    overflow: hidden;
  }
  .title {
    margin-bottom: 16px;
    span {
      margin-bottom: -2px;
      padding: 0 15px;
      height: 40px;
      line-height: 30px;
      display: block;
      background: rgba(255, 255, 255, 0);
      border-bottom: 2px solid rgb(64, 158, 255);
      font-size: 16px;
      font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
        "PingFang SC"-400;
      font-weight: 400;
      color: rgb(64, 158, 255);
    }
  }

  .formItem {
    width: 586px;
  }
  .line {
    color: #dfe6ec;
    margin: 0 6px;
  }
  .typeTabs {
    height: 40px;
    margin-bottom: -2px;
    margin-left: 10px;
  }
}
</style>
