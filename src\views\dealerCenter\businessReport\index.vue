<template>
  <div class="archivesEditContent">
    <im-search-pad :is-expand.sync="isExpand" @reset="resetForm" @search="onSearchSubmitFun">
      <im-search-pad-item prop="productName">
      <el-date-picker
        v-model="listQuery.model.timeRange"
          range-separator="-"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          start-placeholder="开始时间"
          :default-time="['00:00:00', '23:59:59']"
          unlink-panels
          style="width: 100%"
          end-placeholder="结束时间"
        />
      </im-search-pad-item>
      <im-search-pad-item prop="agencyName">
        <el-input v-model="listQuery.model.agencyName" placeholder="请输入经销商名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="orgName">
        <el-input v-model="listQuery.model.orgName" placeholder="请输入机构名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="schoolName">
        <el-input v-model="listQuery.model.schoolName" placeholder="请输入学校名称" />
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout ref="tabs-layout" :tabs="approvalStatusList" v-model="listQuery.model.approvalStatus">
        <template slot="button">
          <el-button @click="exportExcel" :loading="exportLoading">导出筛选结果</el-button>
        </template>
      </tabs-layout>
      <div style="background-color: #fff; margin-bottom: 20px">
        <div class="statistics">
          <div class="statistics_item">
            <span class="text">机构数量（个）</span>
            <span class="num">{{ listTotal.orgTotal }}</span>
          </div>
          <div class="statistics_item">
            <span class="text">学校数量（个）</span>
            <span class="num">{{ listTotal.schoolTotal }}</span>
          </div>
          <div class="statistics_item">
            <span class="text">服务人数（人）</span>
            <span class="num">{{ listTotal.persions }}</span>
          </div>
        </div>
      </div>
      <div class="table">
        <el-table
          v-if="list"
          ref="table"
          @select="onSelect"
          @select-all="onAllSelect"
          v-loading="listLoading"
          :data="list"
          row-key="id"
          border
          fit
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column align="center" width="65" show-overflow-tooltip :render-header="renderHeader" fixed>
            <template slot-scope="scope">
              <span>{{ scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column :reserve-selection="true" align="center" type="selection" width="55" fixed>
          </el-table-column>
          <el-table-column
            v-for="(item, index) in tableTitle"
            :key="index"
            :min-width="item.width ? item.width : '180px'"
            :width="item.name == 'pictIdS' ? item.width : ''"
            :label="item.label"
          >
            <template slot-scope="{ row }">
              <span v-if="item.name === 'accountTotal'" style="color: #0056e5">
                {{ row[item.name] }}
              </span>
              <span v-else-if="item.name === 'status'">
                {{ row[item.name] ? row[item.name].desc : '' }}
              </span>
              <span v-else-if="item.name === 'beginTime'">
                {{ row['beginTime'] + ' 至 ' + row['endTime'] }}
              </span>
              <span v-else>{{ row[item.name] }}</span>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="listQuery.current"
          :limit.sync="listQuery.size"
          @pagination="getList"
        />
      </div>
    </div>
  </div>
</template>
<script>
import { getBusinessReportList, getBusinessReportTotal, businessReportExport } from '@/api/dealerManagement'
import ExamineButton from '@/views/products/product/ExamineButton'
import tableInfo from './tableInfo'
import Pagination from '@/components/Pagination'
import TabsLayout from '@/components/TabsLayout'
import { downloadFile2Url } from '@/utils/commons'

export default {
  name: 'businessReport',
  components: {
    Pagination,
    ExamineButton,
    TabsLayout
  },
  watch: {
    'listQuery.model.timeRange'(val) {
      if (Array.isArray(val) && val.length > 0) {
        this.listQuery.model.beginTime = val[0]
        this.listQuery.model.endTime = val[1]
      } else {
        this.listQuery.model.beginTime = ''
        this.listQuery.model.endTime = ''
      }
    }
  },
  data() {
    return {
      isExpand: false,
      exportLoading: false,
      tableTitle: [],
      list: [],
      listTotal: {
        orgTotal: 0,
        persions: 0,
        schoolTotal: 0
      },
      total: 0,
      listLoading: true,
      tableSelectTitle: [0, 1, 2, 3],
      listQuery: {
        model: {
          agencyId: '',
          agencyName: '',
          beginTime: '',
          className: '',
          endTime: '',
          orgId: '',
          orgName: '',
          schoolName: '',
          timeRange: []
        },
        current: 1,
        size: 10
      },
      routerQuery: {
        total: 0,
        usedTotal: 0,
        id: '',
        agencyName: ''
      }
    }
  },
  computed: {
    approvalStatusList() {
      return [
        {
          name: '业务报表',
          value: 'ALL'
        }
      ]
    }
  },
  created() {
    Object.keys(this.routerQuery).forEach((key) => {
      const queryItem = this.$route.query[key]
      if (queryItem || queryItem == 0) this.routerQuery[key] = queryItem
    })
  },
  methods: {
    exportExcel() {
      this.exportLoading = true
      businessReportExport(this.listQuery).then(res => {
        if (res.code !== 0) return
        downloadFile2Url(res.data, '业务报表')
      }).finally(() => {
        this.exportLoading = false
      })
    },
    renderHeader(h, { column }) {
      var titles = tableInfo[this.listQuery.model.approvalStatus]
      var titlesName = ['显示字段项', '隐藏字段项']
      return (
        <div style="position:relative">
          <div onClick={this.showHeaer}>
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            append-to-body
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="640px"
            center
          >
            <el-transfer
              vModel={this.tableSelectTitle}
              onChange={this.setleftTitleFun}
              data={titles}
              titles={titlesName}
            ></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button onClick={this.closeHeaer}>取消</el-button>
              <el-button type="primary" onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      )
    },
    setleftTitleFun(val) {
      this.tableSelectTitle = val
    },
    setHeaer: function () {
      var titles = tableInfo[0]
      var listinfo = titles.filter((element, index, self) => {
        return !this.tableSelectTitle.includes(element.key)
      })
      this.tableTitle = listinfo
      this.showSelectTitle = !this.showSelectTitle
    },
    showHeaer: function () {
      this.showSelectTitle = true
    },
    closeHeaer: function () {
      this.showSelectTitle = false
      this.tableSelectTitle = []
    },
    getListTotal() {
      getBusinessReportTotal(this.listQuery.model).then((res) => {
        if (res.code !== 0) return
        this.listTotal = res.data
      })
    },
    async getList() {
      this.listLoading = true
      const { data } = await getBusinessReportList(this.listQuery)
      this.list = data.records
      this.total = data.total
      this.listLoading = false
      this.getListTotal()
    },
    onSearchSubmitFun: function () {
      this.list = []
      this.getList()
    },
    resetForm() {
      Object.assign(this.$data.listQuery, this.$options.data().listQuery)
      this.getList()
    },
    initTbaleTitle() {
      this.tableSelectTitle = [99]
      this.tableTitle = tableInfo.ALL
      this.tableTitle = this.tableTitle.filter((item) => {
        return item.key != 99
      })
    },
    // table 选中
    onAllSelect(selection) {
      this.onSelect(selection)
    },
    onSelect: function (val) {
      this.multipleSelection = val
    }
  },
  mounted() {
    this.initTbaleTitle()
    this.getList()
  }
}
</script>
<style lang="less" scoped>
.archivesEditContent {
  .statistics {
    display: flex;
    background-color: #f8f8f8;
    box-sizing: border-box;
    padding: 20px 30px;
    .statistics_item {
      display: flex;
      flex-direction: column;
      margin-right: 80px;
      .text {
        color: #666;
      }
      .num {
        margin-top: 15px;
        font-size: 24px;
      }
    }
  }
}
</style>
