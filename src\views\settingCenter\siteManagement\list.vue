<template>
  <div class="archivesPageContent">
    <im-search-pad
      :is-expand.sync="isExpand"
      :model="searchForm"
      @reset="resetForm('searchForm')"
      @search="onSearchSubmitFun"
    >
      <im-search-pad-item prop="siteName">
        <el-input v-model="searchForm.siteName" placeholder="请输入站点名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="account">
        <el-input v-model="searchForm.account" placeholder="请输入登录账号" />
      </im-search-pad-item>
      <im-search-pad-item prop="userMobile">
        <el-input
          v-model="searchForm.userMobile"
          placeholder="请输入注册手机号"
        />
      </im-search-pad-item>
    </im-search-pad>

    <div class="tab_bg">
      <tabs-layout ref="tabs-layout" :tabs="[{ name: '站点列表', value: '' }]">
        <template slot="button">
          <div>
            <el-button
              v-if="checkPermission(['admin', 'admin-setting-site:release'])"
              :disabled="multipleSelection.length == 0"
              @click="updateSalePublishStatus"
              >批量启动</el-button
            >
            <el-button
              v-if="checkPermission(['admin', 'admin-setting-site:freeze'])"
              :disabled="multipleSelection.length == 0"
              @click="updateSaleMerchantRejected"
              >批量冻结</el-button
            >

            <el-button @click="reloadPageFun">刷新</el-button>
            <el-button
              type="primary"
              @click="newFun"
              v-if="checkPermission(['admin', 'admin-setting-site:add'])"
              >+新增站点</el-button
            >
          </div>
        </template>
      </tabs-layout>
      <table-pager
        ref="pager-table"
        :options="tableTitle"
        :data.sync="list"
        :selection="true"
        @selection-change="onSelect"
        @selection-all="onAllSelect"
        :remoteMethod="getList"
        :operationWidth="150"
        @change-page="multipleSelection = []"
      >
        <template slot="publishStatus.code">
          <el-table-column label="账号状态" width="150">
            <slot slot-scope="{ row }">
              <span
                :style="row.publishStatus.code == 'N' ? 'color:#ff0066' : ''"
              >
                {{ row.publishStatus.code == "Y" ? "已启用" : "已冻结" }}
              </span>
            </slot>
          </el-table-column>
        </template>

        <template slot="provinceIds">
          <el-table-column label="站点区域" width="150">
            <slot slot-scope="{ row }">
              {{ row.provinceIds.join(",") }}
            </slot>
          </el-table-column>
        </template>

        <div
          slot-scope="scope"
          v-if="checkPermission(['admin', 'admin-setting-site:edit'])"
        >
          <el-row class="table-edit-row">
            <span class="table-edit-row-item">
              <el-button @click="editFun(scope.row)" type="text"
                >编辑</el-button
              >
            </span>

            <span
              v-if="checkPermission(['admin', 'admin-setting-site:password'])"
              class="table-edit-row-item"
            >
              <el-button @click="resetFun(scope.row)" type="text"
                >重置密码</el-button
              >
            </span>
          </el-row>
        </div>
      </table-pager>
    </div>
    <reset v-if="showReset" :visible.sync="showReset" :row.sync="row"></reset>
  </div>
</template>
<script>
import { setContextData, getContextData } from "@/utils/auth"; //sessionStorage存值 取值
import checkPermission from "@/utils/permission"; //权限
import {
  list,
  pageCount,
  siteBatchEnable,
  siteBatchFrozen,
} from "@/api/setting/siteManagement/index";
import tableInfo from "./tableInfo";
import Pagination from "@/components/Pagination";
import TabsLayout from "@/components/TabsLayout";
import reset from "@/views/settingCenter/siteManagement/reset";

export default {
  components: {
    TabsLayout,
    Pagination,
    reset,
  },
  data() {
    return {
      isExpand: false,
      showReset: false,
      row: {},
      pageCountInfo: {
        acceptedCount: "0",
        pendingCount: "0",
        rejectedCount: "0",
      },
      multipleSelection: [],
      multipleSelectionId: [],
      tableSelectTitle: [0, 1, 2, 3],
      tableTitle: [],
      submitReload: false,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        model: {},
        current: 1,
        size: 10,
      },
      searchForm: {
        siteName: "",
        account: "",
        userMobile: "",
      },
    };
  },
  computed: {},
  watch: {
    submitReload: function (newVal, oldVal) {
      if (newVal) {
        this.submitReload = false;
        this.getPageCount();
        this.$refs["pager-table"].doRefresh();
      }
    },
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (from.path == "/settingCenter/siteManagement/edit") {
        if (getContextData("siteManagement_list") != "") {
          vm.listQuery = getContextData("siteManagement_list");
        }
      }
      vm.initTbaleTitle();
      vm.getPageCount();
      vm.$refs["pager-table"].doRefresh();
    });
  },
  methods: {
    checkPermission,
    resetFun: function (row) {
      this.row = row;
      this.showReset = true;
    },
    checkSelect() {
      if (this.multipleSelection.length < 1) {
        this.$alert("请至少选择一个站点");
        return true;
      }
    },

    newFun: function () {
      setContextData("siteManagement_list", this.listQuery);
      this.$router.push({
        path: "/settingCenter/siteManagement/edit",
      });
    },
    editFun(row) {
      setContextData("siteManagement_list", this.listQuery);
      this.$router.push({
        path: "/settingCenter/siteManagement/edit",
        query: {
          id: row.id,
        },
      });
    },
    async getList(params) {
      this.listLoading = true;
      Object.assign(this.listQuery, params);
      return await list(this.listQuery);
    },
    async getPageCount() {
      const { data } = await pageCount({
        current: 1,
        map: {},
        model: {},
        size: 10,
      });
      this.pageCountInfo = Object.assign(this.pageCountInfo, data);
    },
    // 搜索
    onSearchSubmitFun() {
      this.listQuery.current = 1;
      this.listQuery.model["siteName"] = this.searchForm.siteName;
      this.listQuery.model["account"] = this.searchForm.account;
      this.listQuery.model["userMobile"] = this.searchForm.userMobile;
      this.$refs["pager-table"].doRefresh();
    },
    // 重置搜索
    resetForm(formName) {
      this.searchForm = {
        siteName: "",
        account: "",
        userMobile: "",
      };
      this.onSearchSubmitFun();
    },
    initTbaleTitle() {
      this.tableSelectTitle = [];
      this.tableTitle = tableInfo["SITE"];
    },
    // table 全选
    onAllSelect(selection) {
      this.onSelect(selection);
    },
    // table 选中
    onSelect(val) {
      let arr = [];
      val.forEach((item) => {
        arr.push(item.id);
      });
      this.multipleSelection = val;
      this.multipleSelectionId = arr;
    },

    // 批量启用
    async updateSalePublishStatus() {
      if (this.checkSelect()) return 0;
      let data = await siteBatchEnable({
        ids: this.multipleSelectionId,
      });
      this.reloadPageFun();
    },
    // 批量冻结
    async updateSaleMerchantRejected() {
      if (this.checkSelect()) return 0;
      let data = await siteBatchFrozen({
        ids: this.multipleSelectionId,
      });
      this.reloadPageFun();
    },

    //页面刷新
    reloadPageFun() {
      this.getPageCount();
      this.$refs["pager-table"].doRefresh();
    },
  },
  mounted() {},
  beforeDestroy() {},
};
</script>
<style lang="scss" scoped>
@import "@/styles/element-variables.scss";
</style>
