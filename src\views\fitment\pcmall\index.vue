<template>
  <div class="main" v-loading="loading" id="mallContainer">
    <page-title title="商城装修" :show-back="false">
      <el-button  @click="preview">预览</el-button>
      <!--<el-button  type="primary">保存并发布</el-button>-->
    </page-title>
    <div id="mallInnerContainer">
      <!-- 搜索、导航栏 -->
      <Search @active-edit="activeEdit" />
      <!-- 头部轮播 -->
      <div class="header-container">
        <!-- 中间部分 -->
        <div class="nav-body">
          <!-- 中间部分 -->
          <div class="nav-content">
            <!-- 轮播图 -->
            <div class="nav-carousel">
              <div class="edit-posi" style="height: 370px;">
                <img style="width: 100%;height: 100%" src="../../../assets/img/index/banner1.png" alt>
                <div class="edit-pop" @click="activeEdit('banner')">点击编辑轮播广告</div>
              </div>
              <div class="nav-show edit-posi">
                <img :src="require('@/assets/img/index/banner2.png')" alt class="nav-show-left">
                <img :src="require('@/assets/img/index/banner3.png')" alt class="nav-show-right">
                <div class="edit-pop" @click="activeEdit('banner-fixed')">点击编辑固定广告</div>
              </div>
            </div>
            <!-- 个人中心 -->
            <div v-if="false" class="nav-person">
              <div class="nav-person-img2">
                <div>
                  <p>Hi，{{ buyersVo.buyersNm }}</p>
                  <p v-if="userDetail.memberDetailsVo.isCredit === 'Y'" class="last">
                    可用额度：¥{{ userDetail.memberDetailsVo.availableCredit || 0 }}</p>
                </div>
              </div>
              <div v-if="userDetail.orderStateVos" class="nav-person-list">
                <div class="list-button" @click="jumpOrder('WAIT_PAY')">
                  <p class="num">{{ userDetail.orderStateVos.toBePaid || 0 }}</p>
                  <p class="text">待付款</p>
                </div>
                <div class="list-button" @click="jumpOrder('WAIT_SEND')">
                  <p class="num">{{ userDetail.orderStateVos.toBeDelivered || 0 }}</p>
                  <p class="text">待发货</p>
                </div>
                <div class="list-button" @click="jumpOrder('SEND')">
                  <p class="num">{{ userDetail.orderStateVos.toBeReceived || 0 }}</p>
                  <p class="text">待收货</p>
                </div>
                <div class="list-button" @click="jumpOrder('WAIT_APPROVE')">
                  <p class="num">{{ userDetail.orderStateVos.toBConfirmed || 0 }}</p>
                  <p class="text">待确认</p>
                </div>
              </div>
            </div>
            <div v-else class="nav-person">
              <div class="nav-person-img">
                <img src="../../../assets/img/index/user_purchaser.png">
                <p>Hi~欢迎来到药链商城</p>
              </div>
              <div class="nav-person-button">
                <router-link to="">
                  <el-button  round>登录</el-button>
                </router-link>
                <router-link to="" class="last">
                  <el-button  round>注册</el-button>
                </router-link>
              </div>
            </div>
            <!-- 公告栏 -->
            <div class="nav-notice">
              <div class="notice-list quick">
                <div class="quick-title">快捷入口</div>
                <div class="quick-content">
                  <div class="quick-list">
                    <img src="../../../assets/img/index/my_btn_payment.png" class="icon">
                    <p>待付款</p>
                  </div>
                  <div class="quick-list">
                    <img src="../../../assets/img/index/my_btn_shipments.png" class="icon">
                    <p>待发货</p>
                  </div>
                  <div class="quick-list">
                    <img src="../../../assets/img/index/my_btn_receipt.png" class="icon">
                    <p>待收货</p>
                  </div>
                </div>
              </div>
              <div class="notice-list notice">
                <el-tabs v-model="activeNotice">
                  <el-tab-pane label="商城公告" name="first">
                    <ul>
                      <li> · 111国家药品监督管理局关于印发中...</li>
                      <li> · 国家药品监督管理局关于印发中...</li>
                      <li> · 国家药品监督管理局关于印发中...</li>
                    </ul>
                  </el-tab-pane>
                  <el-tab-pane label="健康资讯" name="second">
                    <ul>
                      <li> · 2222国家药品监督管理局关于印发中...</li>
                      <li> · 国家药品监督管理局关于印发中...</li>
                      <li> · 国家药品监督管理局关于印发中...</li>
                    </ul>
                  </el-tab-pane>
                  <el-tab-pane label="医药财经" name="third">
                    <ul>
                      <li> · 3333国家药品监督管理局关于印发中...</li>
                      <li> · 国家药品监督管理局关于印发中...</li>
                      <li> · 国家药品监督管理局关于印发中...</li>
                    </ul>
                  </el-tab-pane>
                </el-tabs>
              </div>
              <div class="notice-list qual">
                <div class="qual-list">
                  <img src="../../../assets/img/index/Icon_guarantee.png" class="icon">
                  <p>正品保障</p>
                </div>
                <div class="qual-list">
                  <img src="../../../assets/img/index/Icon_qualifications.png" class="icon">
                  <p>资质完备</p>
                </div>
                <div class="qual-list">
                  <img src="../../../assets/img/index/Icon_complete.png" class="icon">
                  <p>药品齐全</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 内容 -->
      <div id="content" class="content">
        <!-- 折扣 -->
        <div class="offer aaa scroll-item">
          <div class="commodity">
            <!-- 头部 -->
            <div class="commodity-head">
              <div class="commodity-text">
                <div>
                  <span class="commodity-title">优惠专区</span>
                  <span class="commodity-remarks">优惠商品低价购</span>
                </div>
              </div>
            </div>
            <!-- 内容 -->
            <div class="commodity-content">
              <div class="discount">
                <div class="discount-lt edit-posi">
                  <img src="../../../assets/img/index/discount1.png" alt>
                  <img src="../../../assets/img/index/discount2.png" alt>
                  <div class="edit-pop" @click="activeEdit('advert-lt')">点击编辑广告</div>
                </div>
                <div class="discount-rt edit-posi">
                  <div class="edit-pop" @click="activeEdit('advert-rt')">点击编辑广告</div>
                  <div class="discount-rt-content">
                    <div class="discount-list">
                      <div class="discount-title">
                        助力抗疫
                        <span>抗疫精选</span>
                      </div>
                      <div class="discount-content">
                        <img src="../../../assets/img/index/discount3.png" alt>
                        <img src="../../../assets/img/index/discount4.png" alt>
                      </div>
                    </div>
                    <div class="discount-list">
                      <div class="discount-title">
                        助力抗疫
                        <span>抗疫精选</span>
                      </div>
                      <div class="discount-content">
                        <img src="../../../assets/img/index/discount5.png" alt>
                        <img src="../../../assets/img/index/discount6.png" alt>
                      </div>
                    </div>
                    <div class="discount-list">
                      <div class="discount-title">
                        助力抗疫
                        <span>抗疫精选</span>
                      </div>
                      <div class="discount-content">
                        <img src="../../../assets/img/index/discount7.png" alt>
                        <img src="../../../assets/img/index/discount8.png" alt>
                      </div>
                    </div>
                    <div class="discount-list">
                      <div class="discount-title">
                        助力抗疫
                        <span>抗疫精选</span>
                      </div>
                      <div class="discount-content">
                        <img src="../../../assets/img/index/discount9.png" alt>
                        <img src="../../../assets/img/index/discount10.png" alt>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>

        <!-- 精选推荐 -->
        <div class="commodity">
          <!-- 头部 -->
          <div class="commodity-head">
            <div class="commodity-text">
              <div>
                <span class="commodity-title">精选推荐</span>
                <span class="commodity-remarks">精选好物超值购</span>
              </div>
            </div>
          </div>
          <!-- 内容 -->
          <div class="commodity-content">
            <div class="reco-title edit-posi">
              <div class="edit-pop" @click="activeEdit('product')">点击编辑商品分组</div>
              <div class="title-list active">
                <p>热销品种</p>
                <span>热门推荐</span>
              </div>
              <div class="title-list">
                <p>热销品种</p>
                <span>热门推荐</span>
              </div>
              <div class="title-list">
                <p>热销品种</p>
                <span>热门推荐</span>
              </div>
              <div class="title-list">
                <p>热销品种</p>
                <span>热门推荐</span>
              </div>
              <div class="title-list">
                <p>热销品种</p>
                <span>热门推荐</span>
              </div>
              <div class="title-list">
                <p>热销品种</p>
                <span>热门推荐</span>
              </div>
            </div>
            <div class="reco-content">
              <div class="reco-list">
                <img class="reco-logo" src="../../../assets/img/index/banner1.png">
                <div class="name">盐酸多西环素肠溶胶囊</div>
                <div class="spec">0.1g*20</div>
                <div class="spec">永信药品工业(昆山)有限公司</div>
                <div class="price">
                  <span>￥</span>25.00
                  <!--                <p>价格登录可见</p>-->
                </div>
                <div class="shop">
                  <img class="icon-shop" src="../../../assets/img/index/icon_shop.png" alt>
                  <span>石药大药房旗舰店</span>
                  <img class="icon-arrow" src="../../../assets/img/index/icon-arrow-right-black.png" alt>
                </div>
              </div>
              <div class="reco-list">
                <img class="reco-logo" src="../../../assets/img/index/banner1.png">
                <div class="name">盐酸多西环素肠溶胶囊</div>
                <div class="spec">0.1g*20</div>
                <div class="spec">永信药品工业(昆山)有限公司</div>
                <div class="price">
                  <span>￥</span>25.00
                  <!--                <p>价格登录可见</p>-->
                </div>
                <div class="shop">
                  <img class="icon-shop" src="../../../assets/img/index/icon_shop.png" alt>
                  <span>石药大药房旗舰店</span>
                  <img class="icon-arrow" src="../../../assets/img/index/icon-arrow-right-black.png" alt>
                </div>
              </div>
              <div class="reco-list">
                <img class="reco-logo" src="../../../assets/img/index/banner1.png">
                <div class="name">盐酸多西环素肠溶胶囊</div>
                <div class="spec">0.1g*20</div>
                <div class="spec">永信药品工业(昆山)有限公司</div>
                <div class="price">
                  <span>￥</span>25.00
                  <!--                <p>价格登录可见</p>-->
                </div>
                <div class="shop">
                  <img class="icon-shop" src="../../../assets/img/index/icon_shop.png" alt>
                  <span>石药大药房旗舰店</span>
                  <img class="icon-arrow" src="../../../assets/img/index/icon-arrow-right-black.png" alt>
                </div>
              </div>
              <div class="reco-list">
                <img class="reco-logo" src="../../../assets/img/index/banner1.png">
                <div class="name">盐酸多西环素肠溶胶囊</div>
                <div class="spec">0.1g*20</div>
                <div class="spec">永信药品工业(昆山)有限公司</div>
                <div class="price">
                  <span>￥</span>25.00
                  <!--                <p>价格登录可见</p>-->
                </div>
                <div class="shop">
                  <img class="icon-shop" src="../../../assets/img/index/icon_shop.png" alt>
                  <span>石药大药房旗舰店</span>
                  <img class="icon-arrow" src="../../../assets/img/index/icon-arrow-right-black.png" alt>
                </div>
              </div>
              <div class="reco-list">
                <img class="reco-logo" src="../../../assets/img/index/banner1.png">
                <div class="name">盐酸多西环素肠溶胶囊</div>
                <div class="spec">0.1g*20</div>
                <div class="spec">永信药品工业(昆山)有限公司</div>
                <div class="price">
                  <span>￥</span>25.00
                  <!--                <p>价格登录可见</p>-->
                </div>
                <div class="shop">
                  <img class="icon-shop" src="../../../assets/img/index/icon_shop.png" alt>
                  <span>石药大药房旗舰店</span>
                  <img class="icon-arrow" src="../../../assets/img/index/icon-arrow-right-black.png" alt>
                </div>
              </div>
              <div class="reco-list">
                <img class="reco-logo" src="../../../assets/img/index/banner1.png">
                <div class="name">盐酸多西环素肠溶胶囊</div>
                <div class="spec">0.1g*20</div>
                <div class="spec">永信药品工业(昆山)有限公司</div>
                <div class="price">
                  <span>￥</span>25.00
                  <!--                <p>价格登录可见</p>-->
                </div>
                <div class="shop">
                  <img class="icon-shop" src="../../../assets/img/index/icon_shop.png" alt>
                  <span>石药大药房旗舰店</span>
                  <img class="icon-arrow" src="../../../assets/img/index/icon-arrow-right-black.png" alt>
                </div>
              </div>
              <div class="reco-list">
                <img class="reco-logo" src="../../../assets/img/index/banner1.png">
                <div class="name">盐酸多西环素肠溶胶囊</div>
                <div class="spec">0.1g*20</div>
                <div class="spec">永信药品工业(昆山)有限公司</div>
                <div class="price">
                  <span>￥</span>25.00
                  <!--                <p>价格登录可见</p>-->
                </div>
                <div class="shop">
                  <img class="icon-shop" src="../../../assets/img/index/icon_shop.png" alt>
                  <span>石药大药房旗舰店</span>
                  <img class="icon-arrow" src="../../../assets/img/index/icon-arrow-right-black.png" alt>
                </div>
              </div>
              <div class="reco-list">
                <img class="reco-logo" src="../../../assets/img/index/banner1.png">
                <div class="name">盐酸多西环素肠溶胶囊</div>
                <div class="spec">0.1g*20</div>
                <div class="spec">永信药品工业(昆山)有限公司</div>
                <div class="price">
                  <span>￥</span>25.00
                  <!--                <p>价格登录可见</p>-->
                </div>
                <div class="shop">
                  <img class="icon-shop" src="../../../assets/img/index/icon_shop.png" alt>
                  <span>石药大药房旗舰店</span>
                  <img class="icon-arrow" src="../../../assets/img/index/icon-arrow-right-black.png" alt>
                </div>
              </div>
              <div class="reco-list">
                <img class="reco-logo" src="../../../assets/img/index/banner1.png">
                <div class="name">盐酸多西环素肠溶胶囊</div>
                <div class="spec">0.1g*20</div>
                <div class="spec">永信药品工业(昆山)有限公司</div>
                <div class="price">
                  <span>￥</span>25.00
                  <!--                <p>价格登录可见</p>-->
                </div>
                <div class="shop">
                  <img class="icon-shop" src="../../../assets/img/index/icon_shop.png" alt>
                  <span>石药大药房旗舰店</span>
                  <img class="icon-arrow" src="../../../assets/img/index/icon-arrow-right-black.png" alt>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!--      <div class="no-login">-->
        <!--        <div class="w-1200">-->
        <!--          <div class="lt">-->
        <!--            <img src="../../../assets/img/index/home_toast_login.png" />-->
        <!--            <div>-->
        <!--              <p>您尚未登录</p>-->
        <!--              <span>登录后才可以查看商品价格</span>-->
        <!--            </div>-->
        <!--          </div>-->
        <!--          <div class="immediately-button">立即登陆</div>-->
        <!--        </div>-->
        <!--      </div>-->

        <!-- 加载过程 -->
        <!--      <div v-if="showIndex" v-loading="showIndex" class="show_loading"></div>-->

        <!--      导入图片 商城LOGO/图片广告-->
        <import-image ref="importImage" :title="this.activeName" :type="activeType" @confirm="confirm" />
        <!--      商城导航-->
        <mall-navigation ref="mallNavigation" :select-list="selectList" :title="this.activeName" :type="activeType" @confirm="confirm" />
        <!--      搜索热词-->
        <hot-words ref="hotWords" :title="this.activeName" :type="activeType" width="500px" @confirm="confirm" />
        <!--      轮播广告-->
        <carousel-advertising ref="carouselAdvertising" :select-list="selectList" :title="this.activeName" :type="activeType" @confirm="confirm" />
        <!--      固定广告-->
        <fixed-advertising ref="fixedAdvertising" :select-list="selectList" :title="this.activeName" :type="activeType" @confirm="confirm" />
        <!--      优惠专区广告 左 -->
        <special-zone ref="specialZone" :select-list="selectList" :title="this.activeName" :type="activeType" @confirm="confirm" />
        <!--      优惠专区广告 右-->
        <special-zone-rt ref="specialZoneRt" :select-list="selectList" :title="this.activeName" :type="activeType" @confirm="confirm" />
        <!--      商品分组-->
        <product-group ref="productGroup" :title="this.activeName" :type="activeType" width="800px" @confirm="confirm" />
        <!--      页脚装修-->
        <decoration-footers ref="decorationFooters" :select-list="selectList" :title="this.activeName" :type="activeType" @confirm="confirm" />

        <Footer @active-edit="activeEdit" />
      </div>
    </div>
  </div>
</template>

<script>
import Search from './components/Search'
import Footer from './components/Footer'
import importImage from './components/importImage'
import mallNavigation from './components/mallNavigation'
import hotWords from './components/hotWords'
import carouselAdvertising from './components/carouselAdvertising'
import fixedAdvertising from './components/fixedAdvertising'
import specialZone from './components/specialZone'
import specialZoneRt from './components/specialZoneRt'
import productGroup from './components/productGroup'
import decorationFooters from './components/decorationFooters'
import { getPageData, copyrightPage, listByParentId } from './components'
import { query, getAllAd } from '@/api/setting/data/dictionaryItem'
export default {
  name: 'Index',
  components: {
    Search,
    Footer,
    mallNavigation,
    importImage,
    hotWords,
    carouselAdvertising,
    fixedAdvertising,
    specialZone,
    specialZoneRt,
    productGroup,
    decorationFooters
  },
  data() {
    return {
      loading: false,
      page: {
        pageDataAdVoMap: {
          PC_LOGO: [],
          PC_APPLETS: [],
          PC_ADV_CAR: [],
          PC_ADV_FIXED: [],
          PC_ADV_DISCOUNT_LT: [],
          PC_ADV_DISCOUNT_RT: [],
          PC_MALL_NAVIGATION: [],
          PC_SERVICE_CENTER: []
        },
        pageDataHotWordVoList: [],
        pageDataProductGroupVoList: []
      },
      pageDataFooter: {
        content: '',
        id: '',
        PC_SERVICE_CENTER: []
      },
      listByParent: [],
      activeNotice: 'first',
      activeName: '',
      activeType: '',
      selectList: [],
      specialZoneList: []
    }
  },
  mounted() {
    this.init()
    this.resizeContainer()
  },
  methods: {
    init() {
      this.loading = true
      getPageData().then(res => {
        if (res.code == 0) {
          this.page = res.data
        }
      }).then(() => {
        copyrightPage().then(res => {
          console.log(res)
          this.pageDataFooter.content = res.data.content
          this.pageDataFooter.id = res.data.id
          this.pageDataFooter.PC_SERVICE_CENTER = this.page.pageDataAdVoMap.PC_SERVICE_CENTER
        })
      }).then(res => {
        query({ dictionaryId: '93299716304928817' }).then(res => {
          this.selectList = res.data
        })
      }).then(res => {
        getAllAd().then(res => {
          this.specialZoneList = res.data
        })
      }).finally(() => {
        this.loading = false
      })
    },
    // 快速实现外部内容适配
    resizeContainer() {
      const maineContainer = document.querySelector('#mallContainer')
      const innerContainer = document.querySelector('#mallInnerContainer')
      const { width } = maineContainer.getBoundingClientRect()
      const PADDING = 40
      innerContainer.style.zoom = (width - PADDING) / 1200 > 1 ? 1 : (width - PADDING) / 1200
    },
    activeEdit(type) {
      this.$nextTick(() => {
        this.activeType = type
        switch (type) {
          case 'logo':
          case 'code':
            this.activeName = type === 'logo' ? '商城LOGO' : '图片广告'
            this.$refs.importImage.init(type === 'logo' ? this.page.pageDataAdVoMap.PC_LOGO : this.page.pageDataAdVoMap.PC_APPLETS)
            break
          case 'hot-words':
            this.activeName = '搜索热词'
            this.$refs.hotWords.init(this.page.pageDataHotWordVoList)
            break
          case 'nav':
            this.activeName = '商城导航'
            this.$refs.mallNavigation.init(this.page.pageDataAdVoMap.PC_MALL_NAVIGATION)
            break
          case 'banner':
            this.activeName = '轮播广告'
            this.$refs.carouselAdvertising.init(this.page.pageDataAdVoMap.PC_ADV_CAR)
            break
          case 'banner-fixed':
            this.activeName = '固定广告'
            this.$refs.fixedAdvertising.init(this.page.pageDataAdVoMap.PC_ADV_FIXED)
            break
          case 'advert-lt':
            this.activeName = '优惠专区广告'
            this.$refs.specialZone.init(this.page.pageDataAdVoMap)
            break
          case 'advert-rt':
            this.activeName = '优惠专区广告'
            this.$refs.specialZoneRt.init(this.specialZoneList)
            break
          case 'product':
            this.activeName = '商品分组'
            this.$refs.productGroup.init(this.page.pageDataProductGroupVoList)
            break
          case 'footer':
            this.activeName = '页脚装修'
            this.$refs.decorationFooters.init(this.pageDataFooter)
            break
        }
      })
    },
    confirm() {
      this.init()
    },
    preview() {
      query({code:'pcweb_link'}).then(res=>{
        if(res.code==0 && res.msg == 'ok' && res.data.length>0) {
          let link = res.data[0].describe;
          window.open(link);
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/home.scss';
.main {
  padding: 0 20px;
  background: #fff;
  //height: calc(100vh - 118px);
  overflow-x: hidden;
}
</style>

<style lang="less">
.edit-posi{
  position: relative;
  cursor: pointer;
  .edit-pop{
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: rgba(3,10,33,0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-size: 14px;
    z-index: 6;
  }
}
.nav-notice {
  .notice-list{
    &.notice{
      .el-tabs__item{
        padding: 0;
        height: 12px;
        line-height: 12px;
        border: 0;
      }
      .el-tabs__nav-wrap::after{
        height: 0;
      }
      .el-tabs__active-bar{
        height: 0;
      }
      .el-tabs__nav{
        display: flex;
        justify-content: space-between;
        width: 100%;
      }
      .el-tabs__header{
        margin-bottom: 4px;
      }
    }
  }
}
.header-container {
  .nav-body {
    .nav-content {
      .nav-carousel {
        .el-carousel__button{
          width: 8px;
          height: 8px;
          border-radius: 50%;
        }
        .el-carousel__indicator.is-active button{
          width: 30px;
          border-radius: 20px;
        }
      }
    }
  }
}
</style>
