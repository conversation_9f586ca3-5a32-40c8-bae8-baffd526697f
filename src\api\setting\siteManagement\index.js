import requestAxios from '@/utils/requestAxios'
import requestExport from '@/utils/requestExport'

export function list(data) {
  return requestAxios({
    method: 'post',
    url: '/api/merchant/admin/site/page',
    data
  })
}

export function resetApi(data) {
  return requestAxios({
    url: '/api/authority/user/reset',
    method: 'post',
    data
  })
}

//批量启动
export function siteBatchEnable(data) {
    return requestAxios({
      url: '/api/merchant/admin/site/batchEnable',
      method: 'post',
      params: data
    })
  }


  //批量冻结
  export function siteBatchFrozen(data) {
    return requestAxios({
      url: '/api/merchant/admin/site/batchFrozen',
      method: 'post',
      params: data
    })
  }


export function deleteApi(id) {
  return requestAxios({
    url: '/api/product/admin/product?ids[]=' + id,
    method: 'delete'
  })
}

export function getDetailApi(id) {
  return requestAxios({
    url: '/api/product/admin/product/detail/' + id,
    method: 'get'
  })
}

export function getExportApi() {
  return requestAxios({
    url: '/api/product/admin/product/export' + id,
    method: 'get'
  })
}

export function pageCount(data) {
  return requestAxios({
    method: 'post',
    url: '/api/product/admin/product/pageCount',
    data
  })
}

// 驳回
export function setBatchRejectProduct(data) {
  return requestAxios({
    method: 'post',
    url: '/api/product/admin/product/batchRejectProduct',
    data
  })
}

// 审核通过
export function setBatchAcceptProduct(data) {
  return requestAxios({
    method: 'post',
    url: '/api/product/admin/product/batchAcceptProduct',
    data
  })
}
// 审核通过
export function bannerProductList(data) {
  return requestAxios({
    method: 'post',
    url: '/api/product/admin/product/searchListByBrand',
    data
  })
}

// 产品导入记录接口
export function importProductList(data) {
  return requestAxios({
    method: 'post',
    url: '/api/product/admin/productUpload/page',
    data
  })
}

// 产品导入接口
export function importProductFile(data) {
  return requestAxios({
    method: 'post',
    url: '/api/product/admin/productPlatform/import',
    data
  })
}

// 产品导入记录删除接口
export function importProductDelete(data) {
  return requestAxios({
    method: 'post',
    url: '/api/product/admin/productPlatform/import/delete',
    data
  })
}



