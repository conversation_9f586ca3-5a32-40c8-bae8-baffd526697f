<template>
  <div class="archivesEditContent">
    <!-- 显示上传大图 -->
    <el-dialog append-to-body title="" :visible.sync="isShowBigPic" width="30%">
      <img width="100%" :src="dialogImageUrl" alt="" srcset="" />
    </el-dialog>
    <!-- 显示上传大图 end-->
    <div class="top_title flex_between_center">
      <div>{{ this.$route.query.id == null ? '新增' : '编辑' }}产品档案</div>
      <div>
        <!-- 未进入编辑状态 -->
        <el-button @click="clearFun" v-if="isUpdate == false">返 回</el-button>
        <!-- 未进入编辑状态 end-->
        <!-- 进入编辑状态 -->
        <el-popover v-else v-model="rejectFlag" placement="bottom-end" title="取消提醒" width="300" trigger="click">
          <el-button slot="reference">取消编辑</el-button>
          确定取消编辑?取消后编辑内容将不被保存!
          <div style="text-align: right; margin: 0;padding-top:14px">
            <el-button size="mini" @click="rejectFlag = false">取 消</el-button>
            <el-button type="primary" size="mini" @click="isUpdate = false">确 定</el-button>
          </div>
        </el-popover>
        <el-button type="primary" @click="submitFun('ruleForm')"
          v-if="checkPermission(['admin', 'admin-platformProduct:edit']) && this.$route.query.id != null && isUpdate == true">提 交
        </el-button>
        <!-- 进入编辑状态 end -->
        <el-button type="primary" @click="createProductPlatformFun('ruleForm')"
          v-if="checkPermission(['admin', 'admin-platformProduct:addArchives']) && this.$route.query.id == null">生成档案</el-button>
        <el-button @click="downFun()"
          v-else-if="checkPermission(['admin', 'admin-platformProduct:off']) && this.$route.query.id != null && query.approvalStatus == 'ACCEPTED' && isUpdate == false">
          撤销审核</el-button>
        <el-button @click="downFun()"
          v-else-if="checkPermission(['admin', 'admin-platformProduct:off']) && this.$route.query.id != null && query.approvalStatus == 'REJECTED' && isUpdate == false">
          撤销审核</el-button>
        <el-button @click="toEditFun()"
          v-if="checkPermission(['admin', 'admin-platformProduct:edit']) && this.$route.query.id != null && query.approvalStatus == 'ACCEPTED' && isUpdate == false">
          编 辑</el-button>
        <el-button @click="toEditFun()"
          v-if="checkPermission(['admin', 'admin-platformProduct:edit']) && this.$route.query.id != null && query.approvalStatus == 'PENDING' && isUpdate == false">
          编 辑</el-button>
        <el-button @click="toEditFun()"
          v-if="checkPermission(['admin', 'admin-platformProduct:edit']) && this.$route.query.id != null && query.approvalStatus == 'REJECTED' && isUpdate == false">
          编 辑</el-button>
        <!-- PENDING,ACCEPTED,REJECTED -->

        <el-button type="primary" @click="submitProductPlatformPassFun()"
          v-if="checkPermission(['admin', 'admin-platformProduct:accept']) && this.$route.query.id != null && this.$route.query.tabType == 'PENDING' && isUpdate == false">
          通 过</el-button>

        <el-popover placement="top" width="250" v-model="showSetRejectReasonStatus"
          v-if="this.$route.query.id != null && this.$route.query.tabType == 'PENDING' && isUpdate == false">
          <div style="line-height:36px;">驳回理由</div>
          <div>
            <el-input type="textarea" v-model="query.rejectReason" placeholder="" rows="4"></el-input>
          </div>
          <div style="text-align: right; margin: 0;padding-top:10px;">
            <el-button type="text" @click="showSetRejectReasonStatus = false">取 消</el-button>
            <el-button type="primary" @click="setRejectReasonFun()">确 认</el-button>
          </div>
          <el-button slot="reference" v-if="checkPermission(['admin', 'admin-platformProduct:reject'])" style="margin-left:10px;">驳 回</el-button>
        </el-popover>
        <!--  -->
        <el-popover placement="top" width="250" v-model="showRejectReasonStatus"
          v-if="this.$route.query.id != null && query.approvalStatus == 'REJECTED' && isUpdate == false">
          <div style="line-height:36px;">驳回理由</div>
          <div>
            {{ query.rejectReason }}
          </div>
          <div style="text-align: right; margin: 0;padding-top:10px;">
            <el-button type="text" @click="showRejectReasonStatus = false">知道了</el-button>
          </div>
          <el-button slot="reference" style="margin-left:10px;">驳回理由</el-button>
        </el-popover>
      </div>
    </div>
    <el-form :inline="true" label-width="100px" :model="query" ref="ruleForm" :size="'small'">
      <div class="item">
        <module-title title="基础信息" />
        <div>
          <el-row>
            <el-col :span="6">
              <el-form-item class="formItem minW" prop="productCode" label="产品编码:">
                <span v-if="this.$route.query.id != null && isUpdate == false" class="detailSpan">{{ query.productCode
                }}</span>
                <el-input v-else :disabled="true" clearable v-model="query.productCode" placeholder="请填写产品编码"
                  style="width:100%;"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item class="formItem minW" prop="productName" label="产品名称:" :rules="[
                { required: true, message: '请填写产品名称', trigger: 'blur' }
              ]">
                <span v-if="this.$route.query.id != null && isUpdate == false" class="detailSpan">{{ query.productName
                }}</span>
                <el-input v-else clearable v-model="query.productName" placeholder="请填写产品名称" style="width:100%;">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item class="formItem minW" prop="mnemonicCode" label="助记码:">
                <span v-if="this.$route.query.id != null && isUpdate == false" class="detailSpan">{{ query.mnemonicCode
                }}</span>
                <el-input v-else :disabled="true" clearable v-model="query.mnemonicCode" placeholder="请填写助记码"
                  style="width:100%;"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item class="formItem minW" prop="businessRangeId" label="经营类目:" :label-width="'120px'" :rules="[
                { required: true, message: '请选择经营类目', trigger: 'blur' }
              ]">
                <span v-if="this.$route.query.id != null && isUpdate == false" class="detailSpan">{{
                    query.businessRangeName
                }}</span>
                <CascaderOfBusinessCategory :width="'100%'" v-else :selectId.sync="query.businessRangeId">
                </CascaderOfBusinessCategory>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="6">
              <el-form-item class="formItem minW" prop="drugName" label="通用名称:">
                <span v-if="this.$route.query.id != null && isUpdate == false" class="detailSpan">{{ query.drugName
                }}</span>
                <el-input v-else clearable v-model="query.drugName" placeholder="请填写通用名称" style="width:100%;">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item class="formItem minW" prop="categoryId" label="产品分类:" :rules="[
                { required: true, message: '请填写产品分类', trigger: 'blur' }
              ]">
                <span v-if="this.$route.query.id != null && isUpdate == false" class="detailSpan">{{
                    query.categoryPathName
                }}</span>
                <CascaderOfProductType v-else :selectId.sync="query.categoryId" :width="'100%'"></CascaderOfProductType>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item class="formItem minW" prop="brandId" label="品牌:" :rules="[
                { required: true, message: '请填写产品品牌', trigger: 'blur' }
              ]">
                <span v-if="this.$route.query.id != null && isUpdate == false" class="detailSpan">{{ query.brandName
                }}</span>
                <el-input v-else disabled clearable v-model="query.brandName" placeholder="请填写产品品牌">
                  <BannerTableButton slot="append" :selectRowId.sync="query.brandId"
                    :selectRowName.sync="query.brandName"></BannerTableButton>
                </el-input>
              </el-form-item>
            </el-col>


            <!--  <el-col :span="6">
              <el-form-item class="formItem"    prop="brandId" label="品牌:" :rules="[{ required: true, message: '请填写产品品牌', trigger: 'blur' }]">
                <span v-if="this.$route.query.id !=null && isUpdate == false"  class="detailSpan"  >{{ query.brandName }}</span>
                <el-input
                  v-else
                  placeholder="请填写产品品牌"
                  v-model="query.brandName"
                  disabled
                  :width="'100%'"
                >
                  <BannerTableButton
                    slot="append"
                    :selectRowId.sync="query.brandId"
                    :selectRowName.sync="query.brandName"
                  ></BannerTableButton>
                </el-input>
              </el-form-item>
            </el-col> -->
            <el-col :span="6">
              <el-form-item class="formItem minW" prop="spec" :label-width="'120px'" label="规格:" :rules="[
                { required: true, message: '请填写产品规格', trigger: 'blur' }
              ]">
                <span v-if="this.$route.query.id != null && isUpdate == false" class="detailSpan">{{ query.spec
                }}</span>
                <el-input v-else clearable v-model="query.spec" style="width:100%;" placeholder="请填写产品规格"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item class="formItem minW" prop="agentiaType" label="剂型:">
                <span v-if="this.$route.query.id != null && isUpdate == false" class="detailSpan" disabled>{{
                    query.agentiaType
                }}</span>
                <el-select v-else v-model="query.agentiaType" placeholder="请填写剂型" style="100%">
                  <el-option v-for="item in dosageList" :key="item.id" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item class="formItem minW" prop="measurement.val" label="计量单位:">
                <span v-if="this.$route.query.id != null && isUpdate == false" class="detailSpan">{{
                    query.measurement.val == undefined ? '' : query.measurement.val
                }}{{ (query.measurement.code ==
    undefined ? '' : query.measurement.code)
}}</span>
                <el-input v-else v-model="query.measurement.val" placeholder="请填计量值,如:5">
                  <el-select v-model="query.measurement.code" slot="append" style="width:60px">
                    <el-option label="g" value="g"></el-option>
                    <el-option label="对" value="对"></el-option>
                    <el-option label="扎" value="扎"></el-option>
                    <el-option label="KG" value="KG"></el-option>
                    <el-option label="条" value="条"></el-option>
                    <el-option label="只" value="只"></el-option>
                    <el-option label="头" value="头"></el-option>
                    <el-option label="个" value="个"></el-option>
                    <el-option label="枝" value="枝"></el-option>
                    <el-option label="包" value="包"></el-option>
                    <el-option label="粒" value="粒"></el-option>
                    <el-option label="张" value="张"></el-option>
                    <el-option label="盒" value="盒"></el-option>
                    <el-option label="捆" value="捆"></el-option>
                    <el-option label="支" value="支"></el-option>
                    <el-option label="朵" value="朵"></el-option>
                  </el-select>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item class="formItem minW" prop="unit" label="包装单位:">
                <span v-if="this.$route.query.id != null && isUpdate == false" class="detailSpan" disabled>{{ query.unit
                }}</span>
                <el-select v-else v-model="query.unit" style="width:100%;" placeholder="请选包装单位">
                  <el-option v-for="item in packageList" :key="item.id" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item class="formItem minW" prop="manufacturer" :label-width="'120px'" label="生产厂家:" :rules="[
                { required: true, message: '请填写生产厂家', trigger: 'blur' }
              ]">
                <span v-if="this.$route.query.id != null && isUpdate == false" class="detailSpan">{{ query.manufacturer
                }}</span>
                <el-input v-else clearable style="width:100%;" v-model="query.manufacturer" placeholder="请填写生产厂家">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item class="formItem minW" prop="area" label="产地:">
                <span v-if="this.$route.query.id != null && isUpdate == false" class="detailSpan">{{ query.area
                }}</span>
                <el-input v-else clearable v-model="query.area" style="width:100%;" placeholder="请填写产地"></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item class="formItem minW" prop="approvalNumber" label="批准文号:">
                <span v-if="this.$route.query.id != null && isUpdate == false" class="detailSpan">{{
                    query.approvalNumber
                }}</span>
                <el-input v-else clearable v-model="query.approvalNumber" placeholder="请填写批准文号" style="width:100%;">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item class="formItem minW" prop="midPackTotal" label="中包装:">
                <span v-if="this.$route.query.id != null && isUpdate == false" class="detailSpan">{{ query.midPackTotal
                }}</span>
                <el-input v-else clearable v-model="query.midPackTotal" placeholder="请填写中包装" style="width:100%;">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item class="formItem minW" prop="packTotal" label="件包装:" :label-width="'120px'">
                <span v-if="this.$route.query.id != null && isUpdate == false" class="detailSpan">{{ query.packTotal
                }}</span>
                <el-input v-else clearable style="width:100%;" v-model="query.packTotal" placeholder="请填写件包装">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item class="formItem minW" prop="otcType" label="处方类型:"
                :rules="[{ required: true, message: '请选择处方类型', trigger: 'blur' }]">
                <span v-if="this.$route.query.id != null && isUpdate == false" class="detailSpan">{{ query.otcType !=
                    null ? query.otcType.desc : ""
                }}</span>
                <el-select v-else v-model="query.otcType.code" style="width:100%;" placeholder="请选择处方类型">
                  <el-option value="" label="请选择"></el-option>
                  <el-option label="其它" value="OTHER"></el-option>
                  <el-option label="RX" value="RX"></el-option>
                  <el-option label="OTC甲" value="A_OTC"></el-option>
                  <el-option label="OTC乙" value="B_OTC"></el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item class="formItem minW" prop="barCode" label="条形码:"
                :rules="[{ message: '请填写条形码', trigger: 'blur' }]">
                <span v-if="this.$route.query.id != null && isUpdate == false" class="detailSpan">{{ query.barCode
                }}</span>
                <el-input v-else clearable v-model="query.barCode" style="width:100%;" placeholder="请填写条形码"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item class="formItem minW" prop="standardCode" label="本位码:"
                :rules="[{ message: '请填写本位码', trigger: 'blur' }]">
                <span v-if="this.$route.query.id != null && isUpdate == false" class="detailSpan">{{ query.standardCode
                }}</span>
                <el-input v-else clearable v-model="query.standardCode" style="width:100%;" placeholder="请填写本位码">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item class="formItem minW" prop="licenseHolders" :label-width="'120px'" label="上市许可持有人:"
                :rules="[{ message: '请填写上市许可持有人', trigger: 'blur' }]">
                <span v-if="this.$route.query.id != null && isUpdate == false" class="detailSpan">{{
                    query.licenseHolders
                }}</span>
                <el-input v-else clearable v-model="query.licenseHolders" placeholder="请填写上市许可持有人"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item class="formItem minW" prop="referenceMarketPrice" :label-width="'100px'" label="市场价:"
                :rules="[{ message: '请填写市场价', trigger: 'blur' }]">
                <span v-if="this.$route.query.id != null && isUpdate == false" class="detailSpan">{{
                    query.referenceMarketPrice
                }}</span>
                <el-input v-else clearable v-model="query.referenceMarketPrice" placeholder="请填写市场价"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>
      <div class="item">
        <module-title title="其他信息" />
        <el-row>
          <el-col :span="18">
            <el-form-item class="formItem minW" prop="applicable" :label-width="'100px'" label="商品适用:"
              :rules="[{ message: '请填写商品适用', trigger: 'blur' }]">
              <span v-if="this.$route.query.id != null && isUpdate == false" class="detailSpan">{{
                  query.applicable
              }}</span>
              <el-input maxlength="200" show-word-limit type="textarea" v-else clearable v-model="query.applicable" placeholder="请填写商品适用">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="18">
            <el-form-item class="formItem minW" prop="material" :label-width="'100px'" label="成分原料:"
              :rules="[{ message: '请填写成分原料', trigger: 'blur' }]">
              <span v-if="this.$route.query.id != null && isUpdate == false" class="detailSpan">{{
                  query.material
              }}</span>
              <el-input maxlength="200" show-word-limit type="textarea" v-else clearable v-model="query.material" placeholder="请填写成分原料">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="18">
            <el-form-item class="formItem minW" prop="useDosage" :label-width="'100px'" label="用药用量:"
              :rules="[{ message: '请填写用药用量', trigger: 'blur' }]">
              <span v-if="this.$route.query.id != null && isUpdate == false" class="detailSpan">{{
                  query.useDosage
              }}</span>
              <el-input maxlength="200" show-word-limit type="textarea" v-else clearable v-model="query.useDosage" placeholder="请填写用药用量">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="18">
            <el-form-item class="formItem minW" prop="useGuide" :label-width="'100px'" label="用药指南:"
              :rules="[{ message: '请填写用药指南', trigger: 'blur' }]">
              <span v-if="this.$route.query.id != null && isUpdate == false" class="detailSpan">{{
                  query.useGuide
              }}</span>
              <el-input maxlength="200" show-word-limit type="textarea" v-else clearable v-model="query.useGuide" placeholder="请填写用药指南">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <div class="item">
        <module-title title="内容信息" />
        <el-form-item label="产品图片" style="width:100%;">
          <div class="productPicContent">
            <div class="flex_start_center">
              <!-- <div v-if="query.pictIdS.length==0&&isUpdate == false">无上传图片</div> -->
              <div v-if="query.pictIdS instanceof Array" class="productPicContentItem"
                v-for="(item, index) in query.pictIdS" :key="index">
                <img :src="item" alt="" />
                <span v-if="query.id == 0 || isUpdate == true" class="picitem-actions flex_center_center">
                  <i class="el-icon-zoom-in" @click="showBigPic(item)"></i>
                  <i class="el-icon-delete" @click="picRemove(item)"></i>
                </span>
                <p class="setMainPic" v-if="query.id == 0 || isUpdate == true" @click="setMainPicFun(index, item)">设置为主图
                </p>
              </div>

              <div v-if="query.id == 0 || isUpdate == true">
                <el-upload list-type="picture-card" :action="$uploadUrl" :data="insertProgram" :headers="headersProgram"
                  :on-success="uploadSuccess" :before-upload="beforeUpload" :show-file-list="false" multiple :limit="5"
                  :class="{ hide: query.pictIdS.length >= 5 }" accept=".jpg,.png,.bmp,.jpeg" style="overflow: hidden;">
                  <div slot="default" class="uploadPic">
                    <div>
                      <span class="el-icon-plus"></span>
                    </div>
                    <div>
                      <span>上传({{ query.pictIdS.length }}/5)</span>
                    </div>
                  </div>
                </el-upload>
              </div>
            </div>

            <div class="text">
              <p>- 支持JPG、JPEG、PNG、BMP格式，大小不超过2M</p>
              <p>- 请保证请保证图片质量，分辨率至少为600*600</p>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="产品详情">
          <div v-if="this.$route.query.id != null && isUpdate == false"
            style="#CDCED3; max-height:200px; overflow: auto;  max-width:1000px;" v-html="query.productDescription">
          </div>
          <!-- <tinymce
            v-show="query.id == 0 || (this.$route.query.id !=null && isUpdate == true)"
            v-model="query.productDescription"
            :width="1000"
            :height="200"
          /> -->

        </el-form-item>
        <el-form-item label="说明书" style="width: 100%">
          <el-table :data="instructions" border fit highlight-current-row style="width: 1000px;">
            <el-table-column show-overflow-tooltip label="标题" width="300" align="left">
              <template slot-scope="{ row }">

                <el-input v-if="query.id == 0 || isUpdate == true" v-model="row.title"></el-input>
                <span v-else>{{ row.title }}</span>
              </template>
            </el-table-column>
            <el-table-column label="描述" min-width="300" align="left">
              <template slot-scope="{ row }">

                <el-input v-if="query.id == 0 || isUpdate == true" v-model="row.detail" :rows="1" type="textarea">
                </el-input>
                <span v-else>{{ row.detail }}</span>
              </template>
            </el-table-column>

            <el-table-column v-if="query.id == 0 || isUpdate == true" fixed="right" align="center" label="操作"
              width="150" class="itemAction">
              <template slot-scope="{ row }">
                <!-- <el-button
                  v-if="row.edit == false"
                  @click="editInstructionsFun(row)"
                  type="text"
                  >编辑</el-button
                >
                <el-button
                  v-if="row.edit == true"
                  @click="submitInstructionsFun(row)"
                  type="text"

                  >确定</el-button
                > -->
                <el-button @click="delInstructionsFun(row)" type="text">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="addInstructionsItemBtn" v-if="query.id == 0 || isUpdate == true">
            <span class="addInstructionsItem" @click="addInstructionsItemFun">+添加</span>
          </div>
        </el-form-item>
      </div>

      <div class="item">
        <module-title title="GSP质量" />
        <el-row>
          <el-col :span="6">
            <el-form-item label-width="120px" class="formItem minW" prop="curingType" label="养护类型:">
              <span v-if="this.$route.query.id != null && isUpdate == false" class="detailSpan">{{ query.curingType |
                  curingTypeVal
              }}</span>
              <el-select v-else v-model="query.curingType" placeholder="请选择养护类型">
                <el-option value="" label="请选择"></el-option>
                <el-option value="GENERAL" label="常规养护"></el-option>
                <el-option value="STRONG" label="重点养护"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem minW" prop="curingCycle" label="养护周期:" label-width="120px">
              <span v-if="this.$route.query.id != null && isUpdate == false" class="detailSpan">{{
                  query.curingCycle
              }}</span>
              <el-input v-else clearable v-model="query.curingCycle" placeholder="请填写养护周期"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem minW" prop="keepWay" label="存储方式:">
              <span v-if="this.$route.query.id != null && isUpdate == false" class="detailSpan">{{ query.keepWay |
                  keepWayVal
              }}</span>
              <el-select v-else v-model="query.keepWay" placeholder="请选择存储方式">
                <el-option value="" label="请选择"></el-option>
                <el-option value="NORMAL" label="常温"></el-option>
                <el-option value="SHADE" label="阴冷"></el-option>
                <el-option value="COOL" label="冷藏"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem minW" prop="keepCondition" label="存储条件:" :rules="[
              {
                max: 10,
                message: '存储条件最多10位',
                trigger: ['blur', 'change']
              }
            ]">
              <span v-if="this.$route.query.id != null && isUpdate == false" class="detailSpan">{{ query.keepCondition
              }}</span>
              <el-input clearable v-else style="width:100%;" v-model="query.keepCondition" placeholder="请填写存储条件">
              </el-input>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item class="formItem minW" label-width="120px" prop="medicareNum" label="医保号:">
              <span v-if="this.$route.query.id != null && isUpdate == false" class="detailSpan">{{
                  query.medicareNum
              }}</span>
              <el-input v-else clearable v-model="query.medicareNum" placeholder="请填写医保号"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem minW" prop="specialMngMedicinal" label-width="120px" label="特殊管理药品:">
              <span v-if="this.$route.query.id != null && isUpdate == false" class="detailSpan">{{
                  query.specialMngMedicinal | specialMngMedicinalVal
              }}</span>
              <el-select v-else v-model="query.specialMngMedicinal" placeholder="请选择特殊管理药品">
                <el-option value="" label="请选择"></el-option>
                <el-option value="NONSPECIFIC" label="非特殊药品"></el-option>
                <el-option value="SPIRIT" label="精神药物"></el-option>
                <el-option value="ANESTHESIA" label="麻醉药品"></el-option>
                <el-option value="MEDICINAL_TOXIC" label="医疗用毒性药品"></el-option>
                <el-option value="RADIOACTIVITY" label="放射性药品"></el-option>
              </el-select>
            </el-form-item>

          </el-col>

          <el-col :span="6">
            <el-form-item class="formItem minW" prop="performance" label="性能:">
              <span v-if="this.$route.query.id != null && isUpdate == false" class="detailSpan">{{
                  query.performance
              }}</span>
              <el-input clearable v-else v-model="query.performance" placeholder="请填写性能"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem minW" prop="purpose" label="用途:">
              <span v-if="this.$route.query.id != null && isUpdate == false" class="detailSpan">{{ query.purpose
              }}</span>
              <el-input clearable v-else style="width:100%" v-model="query.purpose" placeholder="请填写用途"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem  minW2" prop="whetherColdChain" label-width="120px" label="是否冷藏冷链:">
              <span v-if="this.$route.query.id != null && isUpdate == false" class="detailSpan">{{
                  query.whetherColdChain
                    == "Y" ? "是" : "否"
              }}</span>
              <el-radio-group v-else v-model="query.whetherColdChain">
                <el-radio-button label="Y">是</el-radio-button>
                <el-radio-button label="N">否</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem " label-width="120px" prop="whetherMedicareVariety" label="是否医保:">

              <span v-if="this.$route.query.id != null && isUpdate == false" class="detailSpan">{{
                  query.whetherMedicareVariety == "Y" ? "医保" : "非医保"
              }}</span>
              <el-radio-group v-else v-model="query.whetherMedicareVariety">
                <el-radio-button label="Y">医保</el-radio-button>
                <el-radio-button label="N">非医保</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </div>


      <!--
      <div class="item">
        <div class="title"><span>商品资质</span></div>
        <div>
          <div class="detailMsg">上传材料为复印件加盖企业原印公章且或质管章，支持JPG、JPEG、PNG、BMP格式，大小不超过2M</div>
          <el-table :data="productLicenseBase"  border fit highlight-current-row class="productLicenseBaseTable" style="width: 100%" >
            <el-table-column label="证件类型" width="150" align="center">
              <template slot-scope="{row}">
                  <span >{{row.name}}</span>
              </template>
            </el-table-column>
            <el-table-column label="过期时间"  width="200" align="center">
              <template slot-scope="{row}">
                  <span v-if="row.edit==false">{{row.endTime}}</span>
                   <el-date-picker
                    v-else v-model="row.endTime"
                    type="date"
                    style="width:150px;"
                    value-format="yyyy-MM-dd"
                    placeholder="选择日期">
                  </el-date-picker>
              </template>
            </el-table-column>
            <el-table-column
              label="附件"
              align="left"
            >
              <template slot-scope="{row}">
                  <div class="flex_start_center " v-if="row.edit==true">
                        <div class="productLicenseBaseItem" v-for="(item,index) in row.pictIdS" :key="index">
                            <img  :src="item" alt="" >
                            <span class="picitem-actions flex_center_center">
                                <i class="el-icon-delete"  @click="removeProductQualificationFun(row,item)"></i>
                            </span>
                        </div>
                        <div>
                          <el-upload
                            list-type="picture-card"
                            :action="$uploadUrl"
                            :data="insertProgram"
                            :headers="headersProgram"
                            :on-success="uploadLicenseBaseImgSuccess"
                            :before-upload="beforeUpload"
                            :show-file-list="false"
                            multiple
                            :limit="5"
                             :class="{hide:row.pictIdS.length>=5}"
                            style="overflow: hidden; height:30px" >
                            <div slot="default">
                                <div>
                                  <span class="el-icon-plus"></span>
                                </div>
                                <div>
                                  <span>上传({{row.pictIdS.length}}/5)</span>
                                </div>
                            </div>
                          </el-upload>
                        </div>
                  </div>
                  <div  v-else>
                      <el-popover placement="right" trigger="hover" v-for="(item,index) in row.pictIdS" :key="index"  style="margin:0;padding:0;" >
                          <el-image
                          style="width: 200px; height: 200px"
                          fit="contain"
                          :src="item"
                          ></el-image>
                          <el-image
                          slot="reference"
                          style="width: 30px; height: 30px;margin-right:15px;"
                          fit="cover"
                          :src="item"
                          ></el-image>
                      </el-popover>
                  </div>
              </template>
            </el-table-column>
            <el-table-column fixed="right" align="center" label="操作" width="150" >
                <template slot-scope="scope">
                  <el-button v-if="scope.row.edit==false"  @click="editProductQualificationFun(scope.row)" type="text" >编辑</el-button>
                  <el-button v-else  @click="submitProductQualificationFun(scope.row)" type="text" >确定</el-button>
                </template>
            </el-table-column>
          </el-table>
        </div>
      </div> -->
    </el-form>


  </div>
</template>
<script>
import checkPermission from '@/utils/permission'
import { uploadFile } from "@/api/file";
import { getToken } from "@/utils/auth";
import { parseTime } from "@/utils/index";
import { getLicenseBaseByType } from "@/api/setting/merchantType";
import {
  getApi,
  editApi,
  acceptProductPlatformApi,
  rejectProductPlatformApi,
  editProductStatusApi
} from "@/api/products/archives";
import tableInfo from '@/views/products/archives/tableInfo'

import BannerTableButton from "@/components/eyaolink/BannerTableButton";
import CascaderOfProductType from "@/components/eyaolink/CascaderOfProductType";
import CascaderOfBusinessCategory from "@/components/eyaolink/CascaderOfBusinessCategory";
// import Tinymce from "@/components/Tinymce";
import ModuleTitle from '@/components/PageModuleTitle'
const instructionsTemplate = ['【药品名称】', '【药品成份】', '【主要作用/适应症】', '【性 状】', '【用量用法】', '【规格包装】', '【禁忌使用】', '【不良反应】', '【儿童用药】', '【注意事项】', '【孕妇及哺乳期妇女用药】', '【老年患者用药】', '【药物过量】', '【药物相互作用】', '【药代动力学】', '【药理毒理】', '【包装型号】', '【贮藏方式】', '【批准文号】', '【有 效 期】', '【生产厂家】']
export default {
  components: {
    // Tinymce,
    CascaderOfProductType,
    BannerTableButton,
    CascaderOfBusinessCategory,
    ModuleTitle
  },
  data() {
    return {
      rejectFlag: false,
      defaultQuery: null,
      isUpdate: false,
      rowItem: {},
      headersProgram: {
        token: getToken(),
        Authorization: "Basic YWRtaW5fdWk6YWRtaW5fdWlfc2VjcmV0"
      },
      insertProgram: {
        folderId: 0
      },
      dosageList: tableInfo.dosageList,
      packageList: tableInfo.packageList,
      isOTC: "N",
      editCertificates: false,
      dialogImageUrl: "",
      isShowBigPic: false,
      certificatesForm: {
        id: 1,
        certificatesType: "",
        outTime: "",
        annex: ""
      },
      productLicenseBase: [],
      editLicenseBasePicList: [],
      productImages: [], //产品图片
      query: this.initQuery(),
      instructions: [],

      showSetRejectReasonStatus: false, // 是否显示驳回录入
      showRejectReasonStatus: false // 是否显示驳回原因
    };
  },
  filters: {
    curingTypeVal: function (val) {
      let reurnVal = "";
      switch (val) {
        case "GENERAL":
          reurnVal = "常规养护";
          break;
        case "STRONG":
          reurnVal = "重点养护";
          break;
      }
      return reurnVal;
    },
    keepWayVal: function (val) {
      let reurnVal = "";
      switch (val) {
        case "NORMAL":
          reurnVal = "阴冷";
          break;
        case "SHADE":
          reurnVal = "阴冷";
          break;
        case "COOL":
          reurnVal = "冷藏";
          break;
      }
      return reurnVal;
    },
    specialMngMedicinalVal: function (val) {
      let reurnVal = "";
      switch (val) {
        case "NONSPECIFIC":
          reurnVal = "非特殊药品";
          break;
        case "SPIRIT":
          reurnVal = "精神药物";
          break;
        case "ANESTHESIA":
          reurnVal = "麻醉药品";
          break;
        case "MEDICINAL_TOXIC":
          reurnVal = "医疗用毒性药品";
          break;
        case "RADIOACTIVITY":
          reurnVal = "放射性药品";
          break;
      }
      return reurnVal;
    }
  },
  watch: {
    // isOTC: function(newVal, oldVal) {
    //   if (newVal == "N") {
    //     this.query.otcType = "OTHER";
    //   }
    // }
  },
  props: {
  },
  methods: {
    checkPermission,
    initQuery() {
      return {
        id: 0,
        agentiaType: "",
        approvalNumber: "",
        approvalStatus: "PENDING",
        area: "",
        barCode: "",
        brandId: "",
        businessRangeId: "",
        categoryId: "",
        curingCycle: "",
        curingType: "",
        drugName: "",
        licenseHolders: "",
        instructions: [],
        keepCondition: "",
        keepWay: "",
        manufacturer: "",
        measurement: {
          val: "",
          code: "g"
        },
        medicareNum: "",
        midPackTotal: 1,
        mnemonicCode: "",
        operation: "",
        otcType: { desc: "", code: "" },
        packTotal: 1,
        performance: "",
        pictIdS: [],
        productCode: "",
        productDescription: "",
        productName: "",
        purpose: "",
        rejectReason: "",
        saleMerchantId: 0,
        spec: "",
        specialMngMedicinal: "",
        standardCode: "",
        unit: "盒",
        whetherColdChain: "N",
        whetherEnabled: "Y",
        whetherMedicareVariety: "N",
        referenceMarketPrice: "",
        applicable: "",
        material: "",
        useDosage: "",
        useGuide: ""
      };
    },
    goBackFun() {
      this.$router.go(-1)
      this.$store.dispatch("tagsView/delView", this.$route);
    },
    async getFun() {
      const { data } = await getApi(this.$route.query.id);
      data.pictIdS = (data.pictIdS == null || data.pictIdS == "") ? [] : data.pictIdS.split(",");
      this.query = Object.assign(this.query, data);
      this.query.otcType = data.otcType == null ? { desc: "", code: "" } : data.otcType;
      this.query.approvalStatus =
        data.approvalStatus == undefined ? "PENDING" : data.approvalStatus.code;
      this.query.curingType =
        data.curingType == null ? "" : data.curingType.code;
      this.query.keepWay =
        data.keepWay == null ? "" : data.keepWay.code;
      this.query.specialMngMedicinal =
        data.specialMngMedicinal == null ? "" : data.specialMngMedicinal.code;
      this.query.whetherColdChain =
        data.whetherColdChain != undefined ? data.whetherColdChain.code : "N";
      this.query.whetherMedicareVariety =
        data.whetherMedicareVariety != undefined
          ? data.whetherMedicareVariety.code
          : "N";
      this.query.whetherEnabled =
        data.whetherEnabled != undefined ? data.whetherEnabled.code : "N";
      var instr = data.instructions && JSON.parse(data.instructions) || []
      instr.forEach(item => {
        item.edit = false
      })
      this.query.instructions = instr;
      try {
        this.query.measurement = data.measurement && JSON.parse(data.measurement) || { val: "", code: "g" };
      } catch (e) {
        this.query.measurement = { val: "", code: "g" }
      }

      this.instructions = instr;
    },
    toEditFun() {
      this.rejectFlag = false;
      this.isUpdate = true;
      this.defaultQuery = JSON.parse(JSON.stringify(this.query))

    },
    toClearEditFun() {
      this.isUpdate = false;
      this.query = Object.assign(this.defaultQuery, {});
      this.defaultQuery = null
    },
    beforeUpload(file) {
      let fileTypeList = ["image/png", "image/pjpeg", "image/jpeg", "image/bmp"]
      const isJPG = fileTypeList.indexOf(file.type) > -1;
      const isLt2M = file.size / 1024 / 1024 < 5;

      if (!isJPG) {
        this.$message.error('上传图片格式错误!');
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!');
      }
      return isJPG && isLt2M;
    },
    uploadSuccess(res, file) {
      this.query.pictIdS.push(res.data.url);
    },
    picRemove(file) {
      var pictIdS = [];
      console.info(file);
      this.query.pictIdS.forEach(item => {
        if (item != file) {
          pictIdS.push(item);
        }
      });
      this.productImages = [...[], ...pictIdS];
      this.query.pictIdS = [...[], ...pictIdS];
    },
    showBigPic(file) {
      this.isShowBigPic = true;
      this.dialogImageUrl = file;
    },
    showBannerTableDialog: function () {
      console.info("showBannerTableDialog");
    },
    setMainPicFun(index, item) {
      this.query.pictIdS.splice(index, 1)
      this.query.pictIdS = [...[item], ...this.query.pictIdS];
      console.info(this.defaultQuery)
    },
    // 说明书
    editInstructionsFun(row) {
      row.edit = true;
    },
    delInstructionsFun(row) {
      var items = this.instructions.filter(item => {
        return item.title != row.title;
      });
      this.instructions = items;
    },
    submitInstructionsFun(row) {
      row.edit = false;
    },
    addInstructionsItemFun() {
      this.instructions.push({
        title: "",
        detail: "",
        edit: true
      });
    },
    // 说明书end
    //商品资质

    editProductQualificationFun(row) {
      this.productLicenseBase.forEach(item => {
        item.edit = false;
        if (row.id == item.id) {
          item.edit = true;
          this.editLicenseBasePicList = [...[], ...item.pictIdS];
        }
      });
      this.$set(this.productLicenseBase, this.productLicenseBase);
    },
    removeProductQualificationFun(row, url) {
      var pictIdS = [];
      row.pictIdS.forEach(item => {
        if (item != url) {
          pictIdS.push(item);
        }
      });
      row.pictIdS = pictIdS;
    },
    submitProductQualificationFun(row) {
      row.edit = false;
    },
    async getLicenseBaseTypes() {
      //商品资质
      var { data } = await getLicenseBaseByType("PRODUCT");
      var licenseBase = [];
      data.forEach(item => {
        licenseBase.push({
          id: item.id,
          name: item.name,
          pictIdS: [],
          endTime: "",
          edit: false
        });
      });
      this.productLicenseBase = licenseBase;
    },
    uploadLicenseBaseImgSuccess(res, file) {
      this.productLicenseBase.forEach(item => {
        if (item.edit == true) {
          item.pictIdS.push(res.data.url);
        }
      });
    },
    //商品资质 end

    // 功能方法
    clearFun: function () {
      //关闭页面
      this.goBackFun();
    },
    clearEditFun: function () {
      //关闭编辑页面
      this.goBackFun();
    },
    submitFun: function (ruleForm) {
      // 编辑->提交
      let _this = this;
      _this.$refs[ruleForm].validate(async valid => {
        if (valid) {
          let instructionsEdit = false;
          _this.instructions.forEach(item => {
            if (item.edit) {
              instructionsEdit = true;
            }
          })
          if (instructionsEdit) {
            _this.$message.warning('说明书请点击确定');
            return
          }
          var otcType = _this.query.otcType
          _this.query.otcType = JSON.parse(JSON.stringify(_this.query.otcType.code))
          _this.query.instructions = JSON.stringify(_this.instructions);
          _this.query.measurement = JSON.stringify(_this.query.measurement);
          if (_this.query.pictIdS.constructor == Array) {
            _this.query.pictIdS = _this.query.pictIdS.join(",");
          }
          var data = await editApi(_this.query);
          if (data.code == 0) {
            _this.goBackFun();
          } else {
            _this.query.otcType = otcType
            _this.$message.error("档案提交失败！");
          }
        } else {
          return false;
        }
      });
    },
    async downFun() {
      // 下架提交
      let _this = this;
      let subObj = {
        list: [
          {
            id: _this.query.id,
            approvalStatus: "PENDING",
            rejectReason: "下架"
          }
        ]
      };
      var data = await acceptProductPlatformApi(subObj);
      if (data.code == 0) {
        _this.goBackFun();
      } else {
        _this.$message.error("提交下架失败!");
      }
    },
    createProductPlatformFun(ruleForm) {
      // 创建档案
      let _this = this;
      console.log('__this.instructions', _this.instructions);
      _this.$refs[ruleForm].validate(async valid => {
        if (valid) {
          let instructionsEdit = false;
          _this.instructions.forEach(item => {
            if (item.edit) {
              instructionsEdit = true;
            }
          })
          if (instructionsEdit) {
            _this.$message.warning('说明书请点击确定');
            return
          }
          _this.query.otcType = JSON.parse(JSON.stringify(_this.query.otcType.code))
          _this.query.instructions = JSON.stringify(_this.instructions);
          _this.query.measurement = JSON.stringify(_this.query.measurement);
          if (_this.query.pictIdS.constructor == Array) {
            _this.query.pictIdS = _this.query.pictIdS.join(",");
          }
          var data = await editApi(_this.query);
          if (data.code == 0) {
            _this.goBackFun();
          } else {
            _this.query.otcType = otcType
            _this.$message.error("创建档案失败！");
          }
        } else {
          return false;
        }
      });
    },
    // 通过
    async submitProductPlatformPassFun() {
      let _this = this;
      let subObj = {
        list: [
          {
            id: _this.query.id,
            approvalStatus: "ACCEPTED",
            rejectReason: "通过"
          }
        ]
      };
      var data = await acceptProductPlatformApi(subObj);
      if (data.code == 0) {
        _this.goBackFun();
      } else {
        _this.$message.error("创建档案失败!");
      }
    },
    async setRejectReasonFun() {
      //驳回提交
      let _this = this;
      let subObj = {
        list: [
          {
            id: _this.query.id,
            approvalStatus: "REJECTED",
            rejectReason: _this.query.rejectReason
          }
        ]
      };
      var data = await rejectProductPlatformApi(subObj);
      if (data.code == 0) {
        _this.goBackFun();
      } else {
        _this.$message.error("驳回提交失败!");
      }
    }
    // 提交方法 end
  },
  mounted() {
    console.log('mounted')
    this.getLicenseBaseTypes();
    if (this.$route.query.id > 0) {
      this.getFun();
    } else {
      this.instructions = instructionsTemplate.map(title => ({ title, detail: '' }))
    }
  },
  beforeDestroy() { }
};
</script>
<style lang="less" scoped>
.archivesEditContent {
  // margin: -30px -20px;
  border-top: 1px solid #ebecee;
  padding: 0px 20px;
  background-color: #fff;

  .top_title {
    height: 56px;
    line-height: 56px;
    font-family: "PingFangSC-Regular", "PingFang SC", sans-serif;
    font-size: 18px;
    text-align: left;
    border-bottom: 1px solid #eeeeee;
    margin-bottom: 20px;

    .el-button {
      margin-left: 10px;
    }
  }

  .item {
    width: 100%;
    margin-bottom: 30px;
    border-bottom: 1px solid #eeeeee;

    &:last-child {
      margin-bottom: 0;
    }

    .formItem {
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      // display: flex;
      // justify-content: space-between;
      // align-items: flex-start;
    }
  }

  .productPicContentItem,
  .productLicenseBaseItem {
    background-color: #fbfdff;
    border: 1px dashed #c0ccda;
    border-radius: 6px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 148px;
    height: 148px;
    cursor: pointer;
    line-height: 146px;
    vertical-align: top;
    overflow: hidden;
    border-radius: 5px;
    margin-right: 5px;
    position: relative;

    .setMainPic {
      color: #fff;
      background: rgba(0, 0, 0, 1);
      position: absolute;
      z-index: 999;
      left: 0;
      bottom: 0;
      height: 30px;
      margin: 0;
      line-height: 30px;
      text-align: center;
      width: 100%;
    }

    img {
      width: 148px;
      height: 148px;
    }

    & .picitem-actions {
      position: absolute;
      z-index: 999;
      width: 148px;
      height: 148px;
      left: 0;
      top: 0;
      cursor: default;
      text-align: center;
      color: #fff;
      opacity: 0;
      font-size: 20px;
      background-color: rgba(0, 0, 0, 0.5);

      i {
        margin: 5px;
        cursor: pointer;
      }

    }

    &:hover .picitem-actions {
      opacity: 1;
    }

    &:hover .setMainPic {
      opacity: 1;
    }

  }

  .productLicenseBaseItem {
    width: 30px;
    height: 30px;
    line-height: 28px;

    img {
      width: 30px;
      height: 30px;
    }

    & .picitem-actions {
      width: 30px;
      height: 30px;
    }
  }

  .uploadPic {
    padding-bottom: 100%;
    margin-bottom: -100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;

    >div {
      min-width: 100%;
      height: 25px;
    }
  }

  .productPicContent .text p {
    font-family: "PingFangSC-Regular", "PingFang SC", sans-serif,
      "PingFangSC-Regular", "PingFang SC", sans-serif-400;
    font-weight: 400;
    color: #aaaaaa;
    line-height: 20px;
    font-size: 13px;
    margin: 0;
  }

  .detailMsg {
    font-family: "PingFangSC-Regular", "PingFang SC", sans-serif,
      "PingFangSC-Regular", "PingFang SC", sans-serif-400;
    font-weight: 400;
    color: #aaaaaa;
    line-height: 20px;
    padding-bottom: 20px;

    font-size: 13px;
  }

  /deep/ .hide .el-upload--picture-card {
    display: none;
  }

  /deep/ .productLicenseBaseTable .el-upload--picture-card {
    height: 30px;
    width: 30px;
    padding: 0;
    margin: 0;
  }

  /deep/ .productLicenseBaseTable .el-upload--picture-card .el-icon-plus {
    height: 30px;
    display: block;
    line-height: 30px;
    width: 30px;
    padding: 0;
    margin: 0;
  }

  /deep/ .productLicenseBaseTable .el-upload-list--picture-card .el-upload-list__item {
    height: 30px;
    line-height: 30px;
    width: 30px;
  }

  /deep/ .productLicenseBaseTable .el-upload-list--picture-card .el-upload-list__item-actions {
    height: 30px;
    line-height: 30px;
    width: 30px;
  }

  .addInstructionsItemBtn {
    height: 38px;
    line-height: 38px;
    text-align: center;
    border: 1px solid #dfe6ec;
    border-top: none;
    color: #409eff;
    cursor: pointer;
  }

  /deep/ .minW .el-form-item__content {
    width: calc(100% - (100px + 15px));
  }

  /deep/ .minW .el-form-item__content .el-select {
    width: 100%;
  }

  /deep/ .minW .el-form-item__content .el-select>.el-input {
    width: 100%
  }

  /deep/ .minW2 .el-form-item__content {
    width: calc(100% - (120px + 15px));
  }

  /deep/ .minW3 .el-form-item__content {
    width: calc(100% - (160px + 15px));
  }
}
</style>
