<template>
  <div class="detail-wrapper" v-loading="loading">
    <div v-if="detail">
      <div class="detail-title flex_between_center" style="margin-bottom: 30px">
        付款单详情
        <div>
          <el-button
            type="primary"
            @click="(acceptedFlag = true), (acceptedForm.id = $route.query.id)"
            v-if="detail.paymentStatus.code == 'WAIT' && checkPermission(['admin', 'admin-finance-payments:confirm'])"
          >
            确认支付</el-button
          >
          <el-button @click="backlist">返回列表</el-button>
        </div>
      </div>
      <div class="detail-items">
        <div class="item">
          <div class="title"><span>付款单信息</span></div>
          <div class="ntable">
            <div class="nitem">
              <div>付款单类型</div>
              <div>
                <span v-if="detail.type">{{ detail.type.desc }}</span
                ><span v-else>{{ detail.type }}</span>
              </div>
              <div>付款单状态</div>
              <div>
                <span v-if="detail.paymentStatus">{{
                  detail.paymentStatus.desc
                }}</span
                ><span v-else>{{ detail.paymentStatus }}</span>
              </div>
            </div>
            <div class="nitem">
              <div>付款单单号</div>
              <div>{{ detail.id }}</div>
              <div>关联业务单号</div>
              <div>{{ detail.businessNo }}</div>
            </div>
            <div class="nitem">
              <div>收款方类型</div>
              <div>
                <span v-if="detail.payerType">{{ detail.payerType.desc }}</span
                ><span v-else>{{ detail.payerType }}</span>
              </div>
              <div>收款方</div>
              <div>{{ detail.payerName }}</div>
            </div>
            <div class="nitem">
              <div>应付金额（元）</div>
              <div>
                <span>
                  <span
                    v-if="detail.paymentAmount >= 0"
                    style="color: #70b603"
                    >{{ detail.paymentAmount | getDecimals }}</span
                  >
                  <span v-else style="color: #f59a23">{{
                    detail.paymentAmount | getDecimals
                  }}</span>
                </span>
              </div>
              <div>支付方式</div>
              <div>{{ detail.flowOnline.desc }}</div>
            </div>
            <div class="nitem">
              <div>制单人</div>
              <div>{{ detail.createUserName }}</div>
              <div>制单时间</div>
              <div>{{ detail.createTime }}</div>
            </div>
            <div class="nitem">
              <div>收款方开户名</div>
              <div>{{ detail.collectAccountName }}</div>
              <div>收款方账号</div>
              <div>{{ detail.collectAccount }}</div>
            </div>
            <div class="nitem">
              <div>收款方开户行</div>
              <div>{{ detail.collectAccountBank }}</div>
              <!-- <div>收款方开户行</div>
              <div>{{ detail.collectAccountBank }}</div> -->
            </div>
          </div>
        </div>
      </div>

      <div
        class="detail-items"
        v-if="
          detail.flowOnline.code == 'ON' && detail.paymentStatus.code != 'WAIT'
        "
      >
        <div class="item">
          <div class="title"><span>付款回单 （银行回调信息）</span></div>
          <div class="ntable">
            <div class="nitem">
              <div>业务类型</div>
              <div>{{ item.flowRemarks }}</div>
              <div>支付日期</div>
              <div>{{ item.createTime.substr(0,10) }}</div>
            </div>
            <div class="nitem">
              <div>付款方账户号</div>
              <div>{{ item.payAccount }}</div>
              <div>付款方户名</div>
              <div>{{ item.payAccountName }}</div>
            </div>
            <div class="nitem">
              <div>金额（小写）</div>
              <div>{{ item.smallAmount }}</div>
              <div>金额（大写）</div>
              <div>{{ item.bigAmount }}</div>
            </div>
            <div class="nitem">
              <div>收款方账户号</div>
              <div>{{ item.collectAccount }}</div>
              <div>收款方开户行</div>
              <div>{{ item.collectAccountBank }}</div>
            </div>
            <div class="nitem">
              <div>收款方户名</div>
              <div>{{ item.collectAccountName }}</div>
              <div></div>
              <div></div>
            </div>
            <div class="nitem">
              <div>流水号</div>
              <div>{{ item.serialNumber }}</div>
              <div>回单编号</div>
              <div>{{ item.receiptNumber }}</div>
            </div>
          </div>
        </div>
      </div>

      <div
        class="detail-items"
        v-if="
          detail.flowOnline.code == 'DOWN' && detail.paymentStatus.code != 'WAIT'
        "
      >
        <div class="item">
          <div class="title"><span>付款回单 （人工支付）</span></div>
          <div class="ntable">
            <div class="nitem">
              <div>日期</div>
              <div>{{ item.createTime }}</div>
              <div>流水号</div>
              <div>{{ item.serialNumber }}</div>
            </div>
            <div class="nitem">
              <div>付款凭证</div>
              <div>
                <el-image
                  style="height: 100px; width: 100px"
                  :src="item.certificatePath"
                  :preview-src-list="[item.certificatePath]"
                  fit="contain"
                >
                </el-image>
                <div style="color: #ccc; padding-top: 20px">点击图片预览</div>
              </div>
              <div>出纳员</div>
              <div>{{ item.createUserName }}</div>
            </div>
          </div>
        </div>
        <!-- <div class="detail-items">
          <div class="item">
            <div class="title"><span>付款回单 （操作人工支付）</span></div>
            <div class="ntable">
              <div class="nitem">
                <div>日期</div>
                <div></div>
                <div>流水号</div>
                <div></div>
              </div>
              <div class="nitem">
                <div>付款凭证</div>
                <div></div>
                <div></div>
                <div></div>
              </div>
              <div class="nitem">
                <div>出纳员</div>
                <div></div>
                <div></div>
                <div></div>
              </div>
            </div>
          </div>
        </div> -->
      </div>
    </div>
    <el-dialog
      append-to-body
      title="确认支付"
      :visible.sync="acceptedFlag"
      width="30%"
      @close="close"
      :close-on-click-modal="false"
    >
      <el-form ref="accepted" :model="acceptedForm" label-width="100px">
        <el-form-item
          class="formItem"
          prop="serialNumber"
          label="银行流水号:"
          :rules="[
            { required: true, message: '请输入银行流水号', trigger: 'blur' },
          ]"
        >
          <el-input
            style="min-width: 200px"
            type="number"
            v-model="acceptedForm.serialNumber"
            placeholder="请输银行流水号"
          >
          </el-input>
        </el-form-item>
        <!-- <el-form-item
          class="formItem"
          prop="remarks"
          label="备注信息:"
          :rules="[
            { required: true, message: '请输入备注信息', trigger: 'blur' },
          ]"
        >
          <el-input
            style="width: 90%; min-width: 200px"
            type="textarea"
            rows="3"
            v-model="acceptedForm.remarks"
            placeholder="请输入备注信息"
          >
          </el-input>
        </el-form-item> -->
        <el-form-item
          class="formItem"
          prop="certificatePath"
          label="付款凭证:"
          :rules="[
            { required: true, message: '请输上传付款凭证', trigger: 'blur' },
          ]"
        >
          <el-upload
            v-if="acceptedForm.id"
            ref="uploadlisence"
            :class="{ hide: !!acceptedForm.certificatePath }"
            :action="$uploadUrl"
            :data="insertProgram"
            :headers="headersProgram"
            list-type="picture-card"
            :on-remove="handleRemove"
            :on-success="uploadSuccess"
            :before-upload="beforeUpload"
          >
            <i class="el-icon-plus"></i>
          </el-upload>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="acceptedFormFun">确认支付</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  detail,
  confirmPay,
} from "@/api/finance/collectionAndPaymenList/paymentOrder";
import qs from "qs";
import { getToken } from "@/utils/auth";
import { setContextData, getContextData } from "@/utils/auth";
import checkPermission from '@/utils/permission';
export default {
  data() {
    return {
      loading: false,
      detail: "",
      item: "",
      acceptedFlag: false,
      acceptedForm: {},
      headersProgram: {
        token: getToken(),
        Authorization: "Basic YWRtaW5fdWk6YWRtaW5fdWlfc2VjcmV0",
      },
      insertProgram: {
        folderId: 0,
      },
    };
  },
  methods: {
    checkPermission,
    backlist() {
      this.$router.go(-1);
    },
    handleRemove(file, fileList) {
      this.acceptedForm.fileList = fileList;
      this.$set(this.acceptedForm, "certificatePath", "");
    },
    uploadSuccess(res, file, fileList) {
      this.$set(this.acceptedForm, "certificatePath", file.response.data.url);
    },
    beforeUpload(file) {
      let fileTypeList = [
        "image/png",
        "image/pjpeg",
        "image/jpeg",
        "image/bmp",
      ];
      const isJPG = fileTypeList.indexOf(file.type) > -1;
      const isLt2M = file.size / 1024 / 1024 < 5;

      if (!isJPG) {
        this.$message.error("上传图片格式错误!");
      }
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 2MB!");
      }
      return isJPG && isLt2M;
    },
    close() {
      this.acceptedFlag = false;
      this.$refs.accepted.resetFields();
      this.acceptedForm.fileList = [];
      this.$set(this.acceptedForm, "fileList", []);
      this.acceptedForm = {};
      console.log(this.acceptedForm);
    },
    acceptedFormFun() {
      console.log(this.acceptedForm);
      this.$refs.accepted.validate(async (valid) => {
        if (valid) {
          let { data } = await confirmPay(qs.stringify(this.acceptedForm));
          console.log(data);
          if (data) {
            this.$message.success("已确认收款该收款单");
            let obj = {
              current: 1,
              size: 10,
              model: {
                paymentStatus: "FINISH",
              },
            };
            setContextData("paymentOrder_detail", obj);
            this.$router.push({
              path: "/finance/collectionAndPaymen/paymentOrder/list",
            });
            this.resetForm();
            this.close();
          }
        } else {
          console.log("sssss");
        }
      });
    },
    async getitem() {
      let { data } = await detail(this.$route.query.id);
      this.detail = data;
      this.item = data.financePaymentFlow;
      this.loading = false;
      console.log(data);
    },
  },
  created() {
    // this.loading = true;
    this.getitem();
  },
  mounted() {},
};
</script>

<style lang="less">
.detail-wrapper {
  .item {
    width: 100%;
    margin-bottom: 30px;
    border-bottom: 1px solid #eeeeee;
    .title {
      padding: 0 0 15px;
      span {
        font-size: 16px;
        padding-left: 10px;
        border-left: 4px solid rgba(64, 158, 255, 1);
      }
    }
  }
  .item:last-of-type {
    border-bottom: none;
  }
  .ntable {
    border: 1px solid #dfe6ec;
    margin-bottom: 30px;
    border-radius: 5px;
    .nitem {
      display: flex;
      & > div {
        padding: 18px 20px;
        border-bottom: 1px solid #dfe6ec;
      }
      & > div:nth-child(odd) {
        color: #323336;
        font-weight: 600;
        font-size: 14px;
        width: 150px;
        text-align: right;
        border-right: 1px solid #dfe6ec;
        background-color: #f7f7f8;
        font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
          Microsoft YaHei, Arial, sans-serif;
      }
      & > div:nth-child(even) {
        flex: 1;
        border-right: 1px solid #dfe6ec;
        font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
          Microsoft YaHei, Arial, sans-serif;
        font-size: 14px;
      }
      & > div:last-child {
        border-right: none;
      }
    }
    .nitem:last-child {
      & > div {
        border-bottom: none;
      }
    }
  }
  /deep/ .el-icon-circle-close {
    color: #fff;
  }
}
.hide /deep/.el-upload--picture-card {
  display: none !important;
}
</style>
