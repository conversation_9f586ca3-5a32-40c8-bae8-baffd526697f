<template>
  <el-dialog :close-on-click-modal="false"   :before-close="clearFun"    :show-close="true" :title="'自定义属性'" :visible.sync="visible" width="35%" >
    <div class="newPropContent">
       <el-table
            :data="query"
            border
            fit
            highlight-current-row
          >
            <el-table-column
              show-overflow-tooltip
              label="属性名称"
              align="left"
            >
              <template slot-scope="scope">
                <span v-if="scope.row.edit == false">{{ scope.row.name }}</span>
                <el-input v-else v-model="scope.row.name "></el-input>
              </template>
            </el-table-column>

            <el-table-column
              fixed="right"
              align="center"
              label="操作"
              width="150"
              v-if="checkPermission(['admin','productType-setting:add','productType-setting:edit','productType-setting:delete'])"
              class="itemAction"
            >
              <template slot-scope="scope">
                <el-button
                  v-if="scope.row.edit == false&&checkPermission(['admin','productType-setting:edit'])"
                  @click="editItemFun(scope.row)"
                  type="text"

                  >编辑</el-button
                >
                <el-button
                  v-if="scope.row.edit == true&&checkPermission(['admin','productType-setting:edit'])"
                  @click="submitItemFun(scope.row)"
                  type="text"

                  >确定</el-button
                >
                <el-button
                  v-if="scope.row.edit == false&&checkPermission(['admin','productType-setting:delete'])"
                  @click="deleteItemFun(scope.row)"
                  type="text"

                  >删除</el-button
                >
                <el-button
                  v-if="scope.row.edit == true&&checkPermission(['admin','productType-setting:edit'])"
                  @click="removeUpdateItem(scope.$index)"
                  type="text"

                  >取消</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <div class="addInstructionsItem"  v-if="checkPermission(['admin','productType-setting:add'])"  @click="addQuery">
            <span >+添加</span>
          </div>
    </div>
    <span slot="footer" class="dialog-footer">
        <el-button @click="clearFun">取 消</el-button>
          <el-button type="primary"  v-if="checkPermission(['admin','productType-setting:add','productType-setting:edit','productType-setting:delete'])" @click="clearFun">确 定</el-button>
      </span>
  </el-dialog>
</template>
<script>
import checkPermission from '@/utils/permission'
import { queryCategoryPlatformExtensionFieldApi,deleteCategoryPlatformExtensionFieldApi,editCategoryPlatformExtensionFieldApi} from "@/api/products/categoryPlatform";
export default {
  data() {
    return {
        isUpdate:false,
        editIndex:null,
        defaultVal:{},
        query: []
    };
  },
  props: {
    row: {
      type: Object
    },
    visible: {
      type: Boolean,
      default: false,
      required: true
    }
  },
  methods: {
    checkPermission,
    addQuery:function(){

        var canNew=true;
        this.query.forEach(item=>{
           if(item.id==0){
               canNew=false
           }
        })
        if(canNew){
             this.query.forEach(item=>{
                item.edit=false
            })
            this.query.push({
                id:0,
                name:"",
                fieldType:"TEXT",
                edit:true,
                categoryId:this.row.id
            })
        }else{
            this.$message.error("请先保存新增数据");
        }
    },
    editItemFun(row){
        var canNew=true;
        this.query.forEach(item=>{
           if(item.id==0){
               canNew=false
           }
        })
        if(canNew){
            this.query.forEach(item=>{
               item.edit=false
           })
           this.defaultVal=JSON.parse(JSON.stringify(row));
           row.edit=true;
           this.isUpdate=true;

        }else{
            this.$message.error("请先保存新增数据");
        }

    },
    removeUpdateItem(index){
        if(this.defaultVal.hasOwnProperty("id")==false){
            console.info("remove",index)
            this.query.splice(index, 1);
        }else{
            this.$set( this.query, index, JSON.parse(JSON.stringify(this.defaultVal)))
            this.defaultVal={}
        }
    },
    deleteItemFun(row){
    var _this=this;
      this.$confirm('此操作将永久删除该信息, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        _this.submitDeleteFun(row)
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    async submitDeleteFun(row) {
       let data = await deleteCategoryPlatformExtensionFieldApi(row.id);
      if(data.code==0){
        this.getList()
       }else{
        this.$message.error("删除失败");
      }
    },
    async submitItemFun(row){
       await editCategoryPlatformExtensionFieldApi(row)
       this.getList()
        this.defaultVal={};
    },
    async getList(){
        var {data} =await queryCategoryPlatformExtensionFieldApi({
                "current": 1,
                "map": {},
                "model": {
                    "categoryId": this.row.id
                },
                "order": "descending",
                "size": 9999,
                "sort": "id"
            })
        data.records.forEach(item=>{
            item.edit=false
        })
        this.query=data.records
    },
    clearFun: function() {
      this.$emit("update:visible", false);
      this.$emit("update:row", {});
    },


  },
  mounted() {
     this.getList()
  },
  beforeDestroy() {}
};
</script>
<style lang="less" scoped>
.newPropContent {
  margin: -30px -20px;
  border-top: 1px solid #ebecee;
  padding: 30px 20px;
  .avatar-uploader {
    width: 120px;
    height: 120px;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 120px;
    height: 120px;
    line-height: 120px;
    text-align: center;
  }
  .avatar {
    width:120px;
    height: 120px;
    display: block;
  }
  .addInstructionsItem{color:#409EFF; width:100%; height:50px; line-height: 50px; text-align: center; border: 1px solid #dfe6ec;cursor: pointer;}
}
</style>
