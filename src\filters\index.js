// import parseTime, formatTime and set to filter
export { parseTime, formatTime } from '@/utils'

/**
 * Show plural label if time is plural number
 * @param {number} time
 * @param {string} label
 * @return {string}
 */
function pluralize(time, label) {
  if (time === 1) {
    return time + label
  }
  return time + label + 's'
}

/**
 * @param {number} time
 */
export function timeAgo(time) {
  const between = Date.now() / 1000 - Number(time)
  if (between < 3600) {
    return pluralize(~~(between / 60), ' minute')
  } else if (between < 86400) {
    return pluralize(~~(between / 3600), ' hour')
  } else {
    return pluralize(~~(between / 86400), ' day')
  }
}

/**
 * Number formatting
 * like 10000 => 10k
 * @param {number} num
 * @param {number} digits
 */
export function numberFormatter(num, digits) {
  const si = [
    { value: 1E18, symbol: 'E' },
    { value: 1E15, symbol: 'P' },
    { value: 1E12, symbol: 'T' },
    { value: 1E9, symbol: 'G' },
    { value: 1E6, symbol: 'M' },
    { value: 1E3, symbol: 'k' }
  ]
  for (let i = 0; i < si.length; i++) {
    if (num >= si[i].value) {
      return (num / si[i].value).toFixed(digits).replace(/\.0+$|(\.[0-9]*[1-9])0+$/, '$1') + si[i].symbol
    }
  }
  return num.toString()
}

/**
 * 10000 => "10,000"
 * @param {number} num
 */
export function toThousandFilter(num) {
  return (+num || 0).toString().replace(/^-?\d+/g, m => m.replace(/(?=(?!\b)(\d{3})+$)/g, ','))
}

/**
 * Upper case first char
 * @param {String} string
 */
export function uppercaseFirst(string) {
  return string.charAt(0).toUpperCase() + string.slice(1)
}


// SMS Template Type
export function smsTempTypeFilter(number) {
  var typeName=""
  switch (number) {
    case 1:
      typeName="验证码"
      break;
    case 2:
      typeName="推广"
      break;
    case 3:
      typeName="短信通知"
      break;
    default:
      typeName="未定义"
      break;
  }
  return typeName;
}

//providerType  ALI,TENCENT,BAIDU
export function providerTypeFilter(code) {
  var typeName = ""
  switch (code) {
    case "ALI":
      typeName = "阿里云短信"
      break;
    case "TENCENT":
      typeName = "腾讯云短信"
      break;
    case "BAIDU":
      typeName = "百度云短信"
      break;
    default:
      typeName = "未定义"
      break;
  }
  return typeName;
}


// SMS Template Type WAITING, SUCCESS, FAIL
export function sendStatusFilter(number) {
  var typeName=""
  switch (number) {
    case 'WAITING':
      typeName="发送中"
      break;
    case 'SUCCESS':
      typeName ="发送成功"
      break;
    case 'FAIL':
      typeName="发送失败"
      break;
    default:
      typeName="未定义"
      break;
  }
  return typeName;
}




export function areaLevelFilter(number) {
  var typeName=""
  switch (number) {
    case 'COUNTRY':
      typeName ="国家"
      break;
    case 'PROVINCE':
      typeName ="省份/直辖市"
      break;
    case 'CITY':
      typeName ="地市"
      break;
    case 'COUNTY':
      typeName ="区县"
      break;
    case 'TOWNS':
      typeName ="乡镇"
      break;
    default:
      typeName="未定义"
      break;
  }
  return typeName;
}

// 默认图片
export function imgFilter(value) {
  if(value != "" && value != null && value != 'null') {
    return value.split(',')[0];
  } else {
    return require('@/assets/img/index/product_default.png');
  }
}



