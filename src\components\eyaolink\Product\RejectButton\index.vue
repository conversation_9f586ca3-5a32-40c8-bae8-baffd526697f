<template>
  <div class="temp_RejectButton">
    <el-button :type="buttonType" @click="showRejectDialog = true"
      >驳回</el-button
    >
    <el-dialog
      :close-on-click-modal="false"
      append-to-body
      title="驳回理由"
      :visible.sync="showRejectDialog"
      :before-close="closeDialogFun"
      width="420px"
    >
    <el-form class="form" :inline="true" :model="query" ref="ruleForm" label-width="20px">
      <el-form-item
        style="width:100%;"
        class="formItem"
        label=""
        prop="rejectValueInfo"
        :rules="[{ required: true, message: '请填写驳回理由',trigger: 'blur' }]"
      >
        <el-input style="width:100%;" type="textarea" v-model="query.rejectValueInfo" placeholder=""></el-input>
      </el-form-item>
    </el-form>
      <div  class="formItem btnsInfo"  > 
        <el-button type="primary" @click="submitFun('ruleForm')"
          >确 定</el-button
        >
        <el-button @click="clearFun()">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  data() {
    return {
        query:{
            rejectValueInfo:""
        },
        showRejectDialog: false
    };
  },
  props: {
    buttonType: {
      default: "primary",
      type: String
    },
    rejectValue: {
      default:"",
      type: String
    },

  },
  methods: {
    submitFun(ruleForm) {
      var _this=this;
      _this.$refs[ruleForm].validate(async (valid) => {
        if (valid) {
            _this.showRejectDialog = false
            _this.$emit('submitCallbackFun',_this.query);
            _this.query.rejectValueInfo="";
        } else {
          return false;
        }
      });
    },
    closeDialogFun(){
      this.showRejectDialog=false;
    },
    clearFun() {
      var _this=this;
      _this.query.rejectValueInfo="";
      _this.showRejectDialog = false;
      _this.$emit('clearCallbackFun');
       
    }
  },
  mounted() {},
  beforeDestroy() {}
};
</script>
<style lang="less" scoped>
.temp_RejectButton {
  margin: 0 8px;
}
.btnsInfo{
  width:100%;
  display:flex;
  justify-content: flex-end;
}
 /deep/ .el-form-item__content{
   width:100%;
 }
</style>
