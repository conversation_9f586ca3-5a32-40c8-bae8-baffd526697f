<template>
  <div class="proxySetings" v-loading="listLading">
    <page-title title="代理设置" :show-back="false">
      <el-button @click="editFun" v-if="!isEdit">编辑</el-button>
      <el-button @click="cancelFun" v-if="isEdit">取消</el-button>
      <el-button
        @click="submitFun"
        :loading="doubleSubmit"
        v-if="isEdit"
        type="primary"
      >保存</el-button>
    </page-title>
    <div class="archivesEditContent">
      <el-form :inline="true" :model="query" ref="editForm" :rules="rules">
        <div class="item">
          <module-title title="默认保证金" tips="未指定地区范围内时启用默认代理保证金"></module-title>
          <el-form-item
            label="业务员每代理一个品种需缴纳保证金:"
            prop="defaultMoney"
          >
            <el-input
              style="width: 100px"
              :disabled="!isEdit"
              v-model="query.defaultMoney"
            ></el-input>
            <span style="color: #505465; padding-left: 10px">元</span>
          </el-form-item>
        </div>
        <div class="item">
          <module-title title="地区设置" tips="指定地区范围内代理保证金"></module-title>
          <el-button
            type="primary"
            @click="newAddrFun"
            v-if="isEdit"

            style="margin: 10px 0 20px 0"
            >+ 新增地区</el-button
          >

          <el-table :data="addrtableDate" style="width: 100%" border>
            <el-table-column width="50" align="center">
              <template slot="header">
                <span><i class="el-icon-menu" /></span>
              </template>
              <template slot-scope="scope">
                <span>{{ scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="provinceName" label="省份" min-width="130">
              <template slot-scope="{ row }">
                <span>{{ row.provinceName }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="cityNames" label="城市" min-width="200">
              <template slot-scope="{ row }">
                <span>{{ row.cityNames }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="districtNames"
              label="区 / 区县"
              min-width="300"
            >
              <template slot-scope="{ row }">
                <span>{{ row.districtNames }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="areaMoney"
              label="代理品种保证金"
              min-width="100"
            >
              <template slot-scope="{ row }">
                <span style="color: #ff6e1b">{{ row.areaMoney }}</span>
              </template>
            </el-table-column>
            <!-- <el-table-column
              prop="publishStatus"
              label="是否启用"
              min-width="60"
              align="center"
            >
              <template slot-scope="{ row }">
                <el-input
                  v-if="row.isEdit"
                  placeholder="请输入联系电话"
                  v-model="row.publishStatus"
                ></el-input>
                <span
                  :style="row.publishStatus.code == 'N' ? 'color:#ff0066' : ''"
                  v-else
                  >{{ row.publishStatus.code == "Y" ? "是" : "否" }}</span
                >
              </template>
            </el-table-column> -->
            <el-table-column
              v-if="isEdit"
              fixed="right"
              align="center"
              label="操作"
              width="130"
              class="itemAction"
            >
              <template slot-scope="scope">
                <el-button
                  type="text"
                  @click="editAddrItem(scope.row, scope.$index)"

                  >编辑</el-button
                >
                <el-button
                  type="text"
                  @click="delAddr(scope.row, scope.$index)"

                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="item">
          <module-title title="放弃代理设置" />
          <el-form-item label="业务员主动放弃代理后:" prop="abandonAnewTime">
            <el-input
              style="width: 100px"
              :disabled="!isEdit"
              value="200"
              v-model="query.abandonAnewTime"
            ></el-input>
            <span style="color: #505465; padding-left: 10px"
              >个月内不允许重新代理放弃的商品</span
            >
          </el-form-item>
        </div>

        <!-- <div class="item">
          <div class="title"><span>业务员用户协议</span></div>
          <Tinymce v-model="query.content" style="width:100%" :height="200" />
        </div>

        <div class="item">
          <div class="title"><span>业务员须知</span></div>
          <Tinymce v-model="query.content" style="width:100%" :height="200" />
        </div> -->
      </el-form>
    </div>
    <el-dialog
      v-if="addrFlag"
      title="添加地区"
      :visible.sync="addrFlag"
      width="70%"
      :show-close="true"
      :close-on-click-modal="false"
      :before-close="addrclose"
    >
      <div class="addrtree">
        <el-tree
          class="addrtrees"
          ref="addrTree"
          :data="addrTree"
          show-checkbox
          node-key="id"
          accordion
          :default-expanded-keys="defaultCheck"
          :default-checked-keys="defaultCheck"
          :props="defaultProps"
          @check="addrChange"
        >
        </el-tree>

        <el-form ref="price" :model="row" inline>
          <el-form-item
            label="代理品种保证金:"
            prop="areaMoney"
            :rules="[
              {
                required: true,
                message: '请输入代理品种保证金',
                trigger: 'blur',
              },
            ]"
          >
            <el-input
              style="width: 200px"
              value="200"
              v-model="row.areaMoney"
            ></el-input>
            元
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="addrclose">取 消</el-button>
        <el-button type="primary" :loading="doubleSubmit" @click="addrConfirm"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Tinymce from "@/components/Tinymce"
import { areas, getitem } from "@/api/businessCenter/businessList"
import {
  checkAddr,
  getItem,
  submit,
  ReSubmit,
  addAddr,
  delAddr,
  putAddr,
} from "@/api/businessCentric/proxySettings";
import { checkNumPot2, checkInterNum } from "@/utils/rules"
import PageTitle from '@/components/PageTitle'
import ModuleTitle from '@/components/PageModuleTitle'
export default {
  components: {
    Tinymce,
    PageTitle,
    ModuleTitle
  },
  data() {
    // var validatePass2 = (rule, value, callback) => {
    //   if (value !== this.query.password) {
    //     callback(new Error("两次输入密码不一致!"));
    //   }
    //   callback();
    // };
    let that = this;
    return {
      rules: {
        defaultMoney: [
          { required: true, message: "请填写默认保证金！", trigger: "blur" },
          { validator: checkNumPot2 },
        ],
        abandonAnewTime: [
          {
            required: true,
            message: "请填写默认不可代理时间",
            trigger: "blur",
          },
          { validator: checkInterNum },
          // { validator: validatePass2, trigger: "blur" },
        ],
      },
      listLading: false,
      addrtableDate: [],
      query: {},
      addrFlag: false,
      addrTree: {},
      id: "",
      defaultProps: {
        children: "children",
        label: "label",
        disabled(data, node) {
          if (that.allCheckIds.indexOf(data.id) !== -1) {
            return true;
          } else {
            return false;
          }
        },
      },
      checkdNodeIds: [],
      defaultCheck: [],
      currentTree: "", //当前选中的树
      isEdit: false,
      allCheckIds: "",
      currentIndex: "",
      row: {},
      doubleSubmit: false,
    };
  },
  methods: {
    editFun() {
      this.oldQuery = JSON.parse(JSON.stringify(this.query));
      this.oldAddrTableDate = JSON.parse(JSON.stringify(this.addrtableDate));
      this.isEdit = true;
    },
    newAddrFun() {
      this.addrFlag = true;
      this.defaultCheck = [];
      let idArr = [];
      this.addrtableDate.forEach((item) => {
        idArr = idArr.concat(item.checkedKeys);
      });
      this.allCheckIds = idArr.toString();
    },
    cancelFun() {
      this.$confirm(
        "取消编辑后，编辑内容将不被保存，您确定要取消编辑吗?",
        "取消提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then((res) => {
          this.isEdit = false;
          this.doubleSubmit = false
        })
        .catch((req) => {

        });
    },
    async getItem(isEdit=false) {
      this.listLading = true;
      this.isEdit = true;
      let { data } = await getItem();
      this.listLading = false;
      this.query.defaultMoney = data.proxyConfigs[0].defaultMoney;
      this.query.abandonAnewTime = data.proxyConfigs[0].abandonAnewTime;
      this.id = data.proxyConfigs[0].id;
      this.query = JSON.parse(JSON.stringify(this.query));
      data.proxyConfigAreas.forEach((item) => {
        item.checkedKeys = JSON.parse(item.checkedKeys);
      });
      this.addrtableDate = data.proxyConfigAreas;
      this.isEdit = isEdit;
    },
    submitFun() {
      this.$refs.editForm.validate(async (valid) => {
        if (valid) {
          this.doubleSubmit = true;
          let obj = JSON.parse(JSON.stringify(this.query));
          if (!this.id) {
            obj.proxyConfigAreaPolicies = this.addrtableDate;
            let { data } = await submit(obj);
            if (data) {
              this.$message.success("保存成功");
              this.isEdit = false;
            }
          } else {
            obj.id = this.id;
            let { data } = await ReSubmit(obj);
            if (data) {
              this.$message.success("保存成功");
              this.isEdit = false;
            }
          }
          this.doubleSubmit = false;
        }
      });
    },
    addrChange(node, tree) {
      if (this.currentTree === "") {
        this.currentTree = node.id.slice(0, 2);
      } else {
        if (node.id.slice(0, 2) !== this.currentTree) {
          this.currentTree = node.id.slice(0, 2);
          this.$refs.addrTree.setCheckedKeys([]);
          this.$refs.addrTree.setCheckedKeys([node.id]);
        } else {
          this.$refs.addrTree.setCurrentKey(node.id);
        }
      }
    },
    async addrConfirm() {
      this.$refs.price.validate(async (valid) => {
        if (valid) {
          let checkedKeys = this.$refs["addrTree"].getCheckedKeys();
          let checkedKeysTrue = this.$refs["addrTree"].getCheckedKeys(true);
          let halfCheckedKeys = this.$refs["addrTree"].getHalfCheckedKeys();
          if (checkedKeys.length == 0) {
            this.$message.error("请选择代理区域");
            return false;
          }
          let { data } = await checkAddr({
            checkedKeys,
            checkedKeysTrue,
            halfCheckedKeys,
          });
          let obj = {
            areaMoney: this.row.areaMoney,
            checkedKeys: data.checkedKeys,
            cityIds: data.cityIdS,
            cityNames: data.cityNames,
            districtIds: data.districtIdS,
            districtNames: data.districtNames,
            provinceId: data.provinceId,
            provinceName: data.provinceName,
          };
          // this.addrtableDate.push(obj);
          if (this.id && this.row.id) {
            obj.id = this.row.id;
            this.doubleSubmit = true;
            let { data } = await putAddr(obj);
            this.doubleSubmit = false;
            if (data) {
              this.addrFlag = false;
              this.row = {};
              this.$set(this.addrtableDate, this.currentIndex, obj);
              this.getItem(true);
              return;
            }
          }
          if (this.id && !this.row.id) {
            this.doubleSubmit = true;
            let { data } = await addAddr(obj);
            this.doubleSubmit = false;
            if (data) {
              this.addrFlag = false;
              this.addrtableDate.push(obj);
              this.row = {};
              this.getItem(true);
              return;
            }
          }
          if (!this.id && this.currentIndex === "") {
            this.addrtableDate.push(obj);
            this.addrFlag = false;
            this.row.areaMoney = "";
            this.$message.success("添加成功");
            this.currentIndex = "";
            return;
          }
          if (!this.id && this.currentIndex !== "") {
            this.$set(this.addrtableDate, this.currentIndex, obj);
            this.addrFlag = false;
            this.row.areaMoney = "";
            this.$message.success("编辑成功");
            this.currentIndex = "";
          }
          this.currentIndex = "";
        }
      });
    },
    async getAddrTree() {
      let { data } = await areas();
      this.addrTree = data;
    },
    addrclose() {
      this.addrFlag = false;
    },
    editAddrItem(row, index) {
      let idArr = [];
      this.addrtableDate.forEach((item, ind) => {
        if (index != ind) {
          idArr = idArr.concat(item.checkedKeys);
        }
      });
      this.defaultCheck = row.checkedKeys;
      this.allCheckIds = idArr.toString();
      this.row = row;
      this.currentIndex = index;
      this.addrFlag = true;
    },
    delAddr(row, index) {
      this.$confirm("您确定删除此条地区设置？", "删除提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async (res) => {
          let { data } = await delAddr(row.id);
          if (data) {
            this.$message.success("删除成功");
            this.addrtableDate.splice(index, 1);
          }
        })
        .catch((req) => {});
    },
  },
  created() {
    this.getAddrTree();
    this.getItem();
  }
}
</script>

<style lang="less" scoped>
.proxySetings {
  padding: 0 20px 20px 20px;
  background: #fff;
  border-top: 1px solid #ebecee;
  .archivesEditContent {
    .item {
      width: 100%;
      margin-bottom: 30px;
      border-bottom: 1px solid #eeeeee;
      .title {
        padding: 0 0 15px;
        span {
          padding: 0 10px;
          border-left: 4px solid rgba(64, 158, 255, 1);
          font-family: "PingFangSC-Semibold", "PingFang SC Semibold",
            "PingFang SC", sans-serif;
          font-weight: 650;
          font-style: normal;
          font-size: 15px;
        }
        i {
          font-style: normal;
          font-size: 14px;
        }
      }
      /deep/.el-form-item--medium .el-form-item__content {
        text-align: left;
        color: #cdced3;
      }
      /deep/ .el-col {
        line-height: unset;
      }
    }
    .textgray {
      font-family: "PingFangSC-Regular", "PingFang SC", sans-serif;
      font-weight: 400;
      font-style: normal;
      font-size: 14px;
      color: #505465;
      text-align: left;
      line-height: 22px;
    }
  }
  .addrtree {
    .addrtrees {
      width: 300px;
      max-height: 50vh;
      overflow-y: scroll;
      margin-right: 100px;
    }
    display: flex;
  }
}
</style>
