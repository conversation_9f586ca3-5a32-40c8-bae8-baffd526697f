<template>
  <el-dialog :close-on-click-modal="false"   :before-close="clearFun"    :show-close="true" v-if="visible" :title="'设置角色'" :visible.sync="visible" width="60%" >
    <div class="setRoleTable">
      <div class="table">
        <el-table
          ref="table"
          @select="onSelect"
          @select-all="onAllSelect"
          v-loading="listLoading"
          :data="list"
          row-key="id"
          border
          fit
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column
              :reserve-selection="true"
              type="selection"
              width="55">
          </el-table-column>
          <el-table-column
            v-for="(item, index) in tableTitle"
            :key="index"
            :width="item.width"
            :min-width="(item.width?item.width:'350px')"
            :label="item.label"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              <span v-if="item.name=='status'">{{ row[item.name]?'启用':'禁用' }}</span>
              <span v-else>{{ row[item.name] }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column
              fixed="right"
              align="center"
              label="操作"
              width="80"
              class="itemAction"
          >
              <template slot-scope="{row}">
                  <el-button v-if="checkPermission(['admin','roleIndex:update'])" @click="selectClickFun(row)" type="text" >选中</el-button>
              </template>
          </el-table-column> -->
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="listQuery.current" :limit.sync="listQuery.size" @pagination="getList" />
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
          <el-button @click="clearFun()">取 消</el-button>
            <el-button type="primary" @click="selectClickFun"
              >确 定</el-button
            >
      </span>
  </el-dialog>
</template>
<script>
import checkPermission from '@/utils/permission'
import { list,deleteApi,setUserRoleApi } from '@/api/setting/permission/userRole'
import Pagination from '@/components/Pagination'
export default {
  data() {
    return {
      selectItems:[],
      tableTitle:[
        {
          label:'角色编码	',
          name: "code",
          width:'150px'
        },
        {
          label:'角色名称	',
          name: "name",
          width:'150px'
        },
        {
          label:'描述',
          name: "describe",
        },
        {
          label:'角色状态	',
          name: "status",
          width:'150px'
        }
      ],
      submitReload:false,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        model:{
          status:true
        },
        current: 1,
        size: 10
      },
    };
  },
   props: {
    row: {
      type: Object
    },
    visible: {
      type: Boolean,
      default: false,
      required: true
    },
    isReload: {
      type: Boolean,
      default: false,
      required: true
    }
  },
  components: { Pagination },
  filters:{

  },
  methods: {
    checkPermission,
    // table 选中
    onAllSelect(selection) {
      this.onSelect(selection);
    },
    onSelect:function(val){
     this.selectItems = val;
    },
    clearFun: function() {
      this.$emit("update:visible", false);
      this.$emit("update:row", {});
    },
    async selectClickFun(row) {
        let _this=this;
        let roleIdList=[]
        this.selectItems.forEach(item=>{
          roleIdList.push(item.id)
        })

        var data = await setUserRoleApi({
          "roleIdList":roleIdList,
          "userId": this.row.userId
        })
        if(data.code==0){
          _this.$emit("update:visible", false);
          _this.$emit("update:isReload", true);
        }else{
          _this.$message.error("提交失败！")
        }
    },

    async getList() {
      this.listLoading = true
      const { data } = await list(this.listQuery)
      this.list = data.records
      this.total = data.total
      this.listLoading = false
      this.displayTable()
    },
    displayTable() {
        const vm = this;
        vm.selectItems.forEach(item => {
        vm.list.forEach(rowItem => {
            if (item.roleId === rowItem.id) {
                  vm.$refs.table.toggleRowSelection(rowItem, true);
                }
            });
        });
    }
  },
  mounted() {
      this.getList()
      this.selectItems=this.row.roleList
  },
  beforeDestroy() {}
};
</script>
<style lang="scss" scoped>
@import "@/styles/element-variables.scss";

.setRoleTable {
    margin: -30px -20px;
    border-top: 1px solid #ebecee;
    padding: 10px 20px;
  .title{
       border-bottom:2px solid #EBECEE;
       margin-bottom:35px;
      span{
        margin-bottom: -2px;
        padding:0 15px;
        height: 40px;
        line-height: 30px;
        display:block;
        background: rgba(255,255,255,0);
        border-bottom:2px solid rgb(64, 158, 255);
        font-size: 16px;
        font-family: 'PingFangSC-Regular', 'PingFang SC', 'PingFangSC-Regular', 'PingFang SC'-400;
        font-weight: 400;
        color:rgb(64, 158, 255);
      }
  }
  .formItem{width:586px;}
  .line{color:#dfe6ec; margin:0 6px;}
}
</style>
