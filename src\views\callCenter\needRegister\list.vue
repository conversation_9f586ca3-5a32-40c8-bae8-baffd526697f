<template>
  <div class="archivesPageContent">
    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        v-model="listQuery.model.handleStatus"
        :tabs="approvalStatusList"
        @change="chageTabsFun"
      />
      <div class="table">
        <el-table v-if="list" v-loading="listLoading" :data="list" row-key="id" border fit highlight-current-row style="width: 100%">
          <el-table-column align="center" width="65" :render-header="renderHeader" fixed>
            <template slot-scope="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column v-for="(item, index) in tableTitle" :key="index" :min-width="(item.width?item.width:'350px')" :label="item.label" show-overflow-tooltip align="left">
            <template slot-scope="{row}">
              
              <el-button v-if="item.name=='publishStatus'&&row[item.name].code=='1'" type="text" style="color:#409EFF">已启用</el-button>
              <el-button v-else-if="item.name=='publishStatus'&&row[item.name].code=='0'" type="text" style="color:#FF3C54">已冻结</el-button>
              <el-button v-else-if="item.name=='approvalNumber'&&row[item.name]=='0'" type="text" > </el-button>
              <span v-else>{{ row[item.name]|''}}</span>
            </template>
          </el-table-column>

          <el-table-column v-if="listQuery.model.handleStatus == 'N'&&checkPermission(['admin', 'admin-demandRegister:processing'])" fixed="right" align="center" label="操作" width="80" class="itemAction">
            <template slot-scope="{row}">
              <el-popover :ref="row.id" placement="bottom-end" title="需求处理" width="300" trigger="click" @hide="pophide">
                <el-row slot="reference" class="table-edit-row">
                  <span class="table-edit-row-item">
                    <el-button type="text">处理</el-button>
                  </span>
                </el-row>
                <!-- <el-form ref="disposeform" :model="disposeText"> -->
                <!-- <el-form-item label="备注" prop="text" :rules="[{required: true, message: '请填写需求处理备注',trigger: 'blur'},{required: true,min:5, message: '请至少填写5个字！',trigger: 'blur'}]"> -->
                <el-input type="textarea" :rows="3" placeholder="请输入需求处理备注" v-model="disposeText">
                </el-input>
                <!-- </el-form-item> -->
                <!-- </el-form> -->
                <div style="text-align: right; margin: 0;padding-top:14px">
                  <el-button size="mini" @click="closePop(row.id)">取消</el-button>
                  <el-button type="primary" size="mini" @click="dispose(row)">确定</el-button>
                </div>
              </el-popover>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="listQuery.current" :limit.sync="listQuery.size" @pagination="getlist" />
      </div>
    </div>
    <el-dialog v-if="showEdit" :title="row.title" :visible.sync="showEdit" width="700px">
      <edit :visible.sync="showEdit" :isReload.sync="submitReload" :row.sync="row"></edit>
    </el-dialog>
  </div>
</template>

<script>
import checkPermission from '@/utils/permission'
import tableInfo from "@/views/callCenter/needRegister/tableInfo";
import Pagination from "@/components/Pagination";
import { list, dispose } from "@/api/callCenter/needRegister/index";
import qs from "qs";
import TabsLayout from '@/components/TabsLayout'
export default {
  data() {
    return {
      listQuery: {
        current: 1,
        size: 10,
        model: {
          handleStatus: "N",
        },
      },
      query: {
        showRefuteContent: false,
      },
      showEdit: false,
      list: [],
      listLoading: false,
      tableTitle: [],
      total: 0,
      row: {},
      disposeText: "",
      tableSelectTitle: [0, 1, 2, 3],
      showSelectTitle: false,
    };
  },
  computed: {
    approvalStatusList() {
      return [
        {
          name: '未处理',
          value: 'N',
          hide: !checkPermission(['admin', 'admin-demandRegister:Unhandled'])
        },
        {
          name: '已处理',
          value: 'Y',
          hide: !checkPermission(['admin', 'admin-demandRegister:handled'])
        }
      ]
    }
  },
  methods: {
    checkPermission,
    closePop(id) {
      this.$refs[id].doClose();
    },
    async dispose(row) {
      if (!this.disposeText) {
        this.$message.error("请填写需求处理备注");
        return;
      }
      let obj = { id: row.id,remark: this.disposeText };
      obj = qs.stringify(obj);
      let { data } = await dispose(obj);
      this.listQuery = {
        current: 1,
        size: 10,
        model: {
          handleStatus: "Y",
        },
      };
      this.disposeText = ''
      this.getlist();
      // this.$refs.disposeform.validate(async (valid) => {
      //   if(valid) {
      //     let {data} = await dispose({
      //       id: row.id,
      //       handleRemarks: this.disposeText.text,
      //       handleStatus: 'Y',
      //     })
      //     this.listQuery = {
      //       current: 1,
      //       size: 10,
      //       model: {
      //         handleStatus:'Y'
      //       }
      //     }
      //     this.getlist()
      //   }
      // })
    },
    async getlist() {
      this.listLoading = true;
      let { data } = await list(this.listQuery);
      this.total = data.total;
      this.list = data.records;
      this.listLoading = false;
    },
    pophide(e) {
      this.disposeText = "";
    },
    chageTabsFun(e) {
      this.list = [];
      this.listQuery.current = 1;
      this.initTbaleTitle();
      this.getlist();
    },
    renderHeader(h, { column }) {
      var titles = tableInfo[this.listQuery.model.handleStatus];
      var titlesName = ["显示字段项", "隐藏字段项"];
      return (
        <div style="position:relative">
          <div onClick={this.showHeaer}>
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="640px"
            center
            append-to-body={true}
          >
            <el-transfer
              vModel={this.tableSelectTitle}
              data={titles}
              titles={titlesName}
              onChange={this.setleftTitleFun}
            ></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button onClick={this.closeHeaer}>取消</el-button>
              <el-button type="primary" onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      );
    },
    setleftTitleFun(val) {
      this.tableSelectTitle = val;
    },
    showHeaer: function () {
      this.showSelectTitle = true;
    },
    closeHeaer: function () {
      this.showSelectTitle = false;
      this.tableSelectTitle = [];
    },
    setHeaer: function () {
      var titles = tableInfo[this.listQuery.model.handleStatus];
      var listinfo = titles.filter((element, index, self) => {
        return !this.tableSelectTitle.includes(element.key);
      });
      this.tableTitle = listinfo;
      this.showSelectTitle = !this.showSelectTitle;
    },
    initTbaleTitle() {
      this.tableTitle = tableInfo[this.listQuery.model.handleStatus];
      this.tableSelectTitle = []
    },
  },
  created() {
    this.getlist();
    this.initTbaleTitle();
  },
  components: {
    Pagination,
    TabsLayout
  },
};
</script>


<style lang="scss" scoped>
.archivesPageContent {
  padding: 0;
  .temp_searchBox {
    height: 64px;
    overflow: hidden;
    margin-bottom: 0;
  }
  .form-inline {
    height: 60px;
    overflow: hidden;
  }
  .title {
    border-bottom: 2px solid #ebecee;
    margin-bottom: 16px;
    span {
      margin-bottom: -2px;
      padding: 0 15px;
      height: 40px;
      line-height: 30px;
      display: block;
      background: rgba(255, 255, 255, 0);
      border-bottom: 2px solid rgb(64, 158, 255);
      font-size: 16px;
      font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
        "PingFang SC"-400;
      font-weight: 400;
      color: rgb(64, 158, 255);
    }
  }
  .formItem {
    width: 586px;
  }
  .line {
    color: #dfe6ec;
    margin: 0 6px;
  }
  .typeTabs {
    height: 40px;
    margin-bottom: -2px;
    margin-left: 10px;
  }
}
</style>
