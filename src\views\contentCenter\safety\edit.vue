<template>
  <div class="main">
    <div class="top_title flex_between_center">
      <div class="el-dialog__title">{{$route.query.id? '编辑' : '新增'}}安全协议</div>
      <div>
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="onSubmit">保存</el-button>
      </div>
    </div>
    <div class="title"><span>文章信息</span></div>
    <el-form ref="form" :model="form" label-width="100px" style="padding: 20px;">
      <el-form-item label="协议类型：" prop="protocolType"
        :rules="[{ required: true, message: '请选择协议类型', trigger: 'change' }]">
        <el-select v-model="form.protocolType" placeholder="请选择协议类型">
          <el-option v-for="(item,index) in protocolTypeList" :key="index" :label="item.desc" :value="item.code"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="应用类型：" prop="terminalType"
        :rules="[{ required: true, message: '请选择应用类型', trigger: 'change' }]">
        <el-select v-model="form.terminalType" placeholder="请选择应用类型">
          <el-option v-for="(item,index) in terminalTypeList" :key="index" :label="item.desc" :value="item.code"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="标题：" prop="title" :rules="[{required: true,min: 2, message: '请至少输入2个字', trigger: 'blur'}]">
        <el-input v-model="form.title" placeholder="请填写标题" style="width:500px"></el-input>
      </el-form-item>

      <el-form-item label="文章内容：" prop="content"
        :rules="[{required: true,message: '请输入资讯内容！', trigger: 'blur'}, {required: true,min:20, message: '内容请输入20字以上'}]">
        <Tinymce v-model="form.content" :width="800" :height="200" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  import {
    getToken
  } from "@/utils/auth";
  import Tinymce from "@/components/Tinymce";
  import {
    securityProtocolDetail,
    securityProtocolAdd,
    securityProtocolEdit,
    listProtocolTypeByMerchantId,
    listTerminalByMerchantId
  } from "@/api/contentCenter/safety/index";
  export default {
    data() {
      return {
        form: {
          protocolType: "",
          terminalType:'',
          title: '',
          content: ''
        },
        terminalTypeList:[],
        protocolTypeList:[],
        imageUrl: "",
        insertProgram: {
          folderId: 0,
        },
        headersProgram: {
          token: getToken(),
          Authorization: "Basic YWRtaW5fdWk6YWRtaW5fdWlfc2VjcmV0",
        },
        submitReload: "",
      };
    },
    methods: {
      cancel() {
        // this.$emit('update:visible', false)
        this.$store.dispatch("tagsView/delView", this.$route);
        this.$router.go(-1);
      },
      onSubmit() {
        this.$refs.form.validate(async (valid) => {
          if (valid) {
            if (this.form.id) {
              securityProtocolEdit(this.form).then(res => {
                if (res.code == 0 && res.msg == 'ok') {
                  this.$message.success('修改成功');
                  this.$router.go(-1);
                }
              })
            } else {
              securityProtocolAdd(this.form).then(res => {
                if (res.code == 0 && res.msg == 'ok') {
                  this.$message.success('新增成功');
                  this.$router.go(-1);
                }
              })
            }
          }
        });
      },
      handleAvatarSuccess(res, file) {
        this.form.coverImg = URL.createObjectURL(file.raw);
      },
      beforeAvatarUpload(file) {
        console.log(file);
        // const isJPG = file.type === 'image/jpeg';
        const isJPG = true;
        const isLt2M = file.size / 1024 / 1024 < 5;

        if (!isJPG) {
          this.$message.error("上传头像图片只能是 JPG 格式!");
        }
        if (!isLt2M) {
          this.$message.error("上传头像图片大小不能超过 2MB!");
        }
        return isJPG && isLt2M;
      },
      async getitem() {
        let {
          data
        } = await securityProtocolDetail(this.$route.query.id);
        this.form = data;
        this.form.protocolType = (data.protocolType && data.protocolType.code) ? data.protocolType.code : ''
        this.form.terminalType = (data.terminalType && data.terminalType.code) ? data.terminalType.code : ''
      },
      getList(){
          listProtocolTypeByMerchantId().then(res=>{
              if(res.code == 0 && res.msg == 'ok') {
                  this.protocolTypeList = res.data;
              }
          });
          listTerminalByMerchantId().then(res=>{
              if(res.code == 0 && res.msg == 'ok') {
                  this.terminalTypeList = res.data;
              }
          })
      },
    },
    components: {
      Tinymce,
    },
    created() {
      this.getList();
      if (this.$route.query.id) {
        this.getitem()
      }
    },
  };

</script>

<style lang="less" scoped>
  .main {
    // overflow: scroll;
    // margin: -30px -20px;
    border-top: 1px solid #ebecee;
    padding: 0px 20px;
    background-color: #fff;

    .top_title {
      height: 56px;
      line-height: 56px;
      font-family: "PingFangSC-Regular", "PingFang SC", sans-serif;
      font-size: 18px;
      text-align: left;
      border-bottom: 1px solid #eeeeee;
      margin-bottom: 20px;

      .el-button {
        margin-left: 10px;
      }
    }

    .title {
      padding: 20px;

      span {
        font-size: 16px;
        padding-left: 10px;
        border-left: 4px solid rgba(64, 158, 255, 1);
      }
    }

    .upload_ms {
      color: #8c939d;
      line-height: 10px;
    }

    /deep/.avatar-uploader .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }

    /deep/.avatar-uploader .el-upload:hover {
      border-color: #409eff;
    }

    /deep/.avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 120px;
      height: 120px;
      line-height: 120px;
      text-align: center;
    }

    /deep/.avatar {
      width: 120px;
      height: 120px;
      display: block;
    }
  }

</style>
