<template>
  <div
    class="items"
    @click="
      checkPermission(['admin', 'fitment-wechat:edit']) && showDrawerFun()
    "
  >
    <div class="announcementBox">
      <div class="announcementInfo">
        <img src="~@/assets/imgs/icon_announcement.png" />
        <div class="newmsgbox">
          <div class="new">最新</div>
          <div class="msg"  v-for="(item, index) in queryNewArchive" :key="index">{{item.name}}</div>
        </div>
      </div>
    </div>
    <el-drawer
        :destroy-on-close="true"
        :size="'345px'"
        append-to-body
        :wrapperClosable="false"
        :visible.sync="drawer"
        :with-header="false">
        <div class="flex_between_center top" >
            <div>最新资讯</div> 
            <div>
                <el-button @click="drawer=false" >取 消</el-button>
                <el-button type="primary"  @click="submitFun()" >提交</el-button>
            </div>
        </div>
        <el-form  label-width="75px" class="fromBox form">
            <el-form-item label="标题" class="hotItem"  v-for="(item,index) in newArchive" :key="index"  >
                <i class="el-icon-error" @click="removeItem(index)"></i>
                <el-input v-model="item.name" placeholder="请设置标题"></el-input>
            </el-form-item>
        </el-form>
        <div class="addbtn" @click="addItemBtnFun" v-if="newArchive.length==0">
                + 添加标题
            </div>
    </el-drawer>
  </div>
</template>
<script>
import checkPermission from '@/utils/permission' 
import { 
    pageComponentList,
    deleteByPageComponentId,
    pageADList,
    pageADAdd,
    pageADListAdd,
    pageADEdit 
}  from "@/api/fitment";
export default {
data() {
    return {
        drawer:false,
        parentId:null,
        queryNewArchive:null,
        newArchive:[]
    };
},
props: {
    pagePlateId:{
        type:String,
        required:true
    }
},
methods: {
    checkPermission,
    showDrawerFun(){
        this.drawer=true
        this.newArchive=[...[],...this.queryNewArchive]
    },
    addItemBtnFun(){
        this.newArchive.push({
          id: "",
          pagePlateId:this.pagePlateId,
          showStatus:"Y",
          pageComponentId:this.pageComponentId,
          name: "",
          sortValue:this.newArchive.length
        })
    },
    removeItem(index){
        this.newArchive.splice(index,1)
    },
    async submitFun(){
        await deleteByPageComponentId(this.pageComponentId)
        console.info(this.newArchive)
        this.newArchive.forEach((item,index)=>{
            item.sortValue=index;
        })
        this.queryNewArchive=[...[],...this.newArchive]
        this.drawer=false
        var data = await pageADListAdd(this.queryNewArchive)
        if (data.code == 0) {
            this.initFun();
        } else {
            this.$message.error("提交失败！");
        }
    },
    async initFun(){
        var {data} = await  pageComponentList({
            "current": 1,
            "map": {},
            "model": {
                "componentCode": "info"
            },
            "order": "descending",
            "size": 10,
            "sort": "id"
        })
        this.pageComponentId=data.records[0].id
        let adList = await  pageADList({
            "current": 1,
            "map": {},
            "model": {
                pageComponentId:data.records[0].id
            },
            "order": "descending",
            "size": 10,
            "sort": "id"
        })
        adList.data.records.forEach(item=>{
            item.showStatus=item.showStatus.code
        })
        this.queryNewArchive=adList.data.records
    }
},
mounted() {
    // 请求热搜接口
    this.initFun()
},
beforeDestroy() {}
};
</script>
<style lang="less" scoped>
.items {
  border: 1px dashed red;
  margin-bottom: 12px;
  cursor: pointer;
  margin-bottom: 48/2px;
}

.announcementBox {
  padding: 6/2px 32/2px;
  background: #fff;
  width: 686/2px;
  margin: 0 auto;
}

.announcementBox .announcementInfo {
  display: flex;
  justify-content: space-between;
  width: 686/2px;
  height: 92/2px;
  align-items: center;
}

.announcementBox .announcementInfo img {
  width: 60/2px;
  height: 28/2px;
}

.announcementBox .announcementInfo .newmsgbox {
  background: linear-gradient(
    270deg,
    rgba(0, 188, 121, 0.1) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  width: 616/2px;
  align-items: center;
  border-radius: 44/2px;
  display: flex;
  justify-content: space-between;
  height: 92/2px;
}

.announcementBox .announcementInfo .newmsgbox .new {
  width: 50/2px;
  line-height: 30/2px;
  height: 30/2px;
  background: rgba(0, 188, 121, 0.1);
  text-align: center;
  border-radius: 4/2px;
  font-size: 18/2px;
  font-weight: 500;
  color: #00bc79;
  margin-left: 20/2px;
}

.announcementBox .announcementInfo .newmsgbox .msg {
  font-size: 26/2px;
  font-weight: 400;
  color: #212e43;
  padding-right: 35/2px;
  position: relative;
}

.announcementBox .announcementInfo .newmsgbox .msg:after {
  content: "";
  display: block;
  width: 20/2px;
  height: 20/2px;
  border-right: 1/2px solid #212e43;
  border-top: 1/2px solid #212e43;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  position: absolute;
  right: 20/2px;
  top: 50%;
  margin-top: -10/2px;
}


.hotItems{ margin: 0 auto;margin-top: 7px; width: 345px ;}
.hotItems span{
    line-height: 24px;
    height: 24px;
    font-size: 12px;
    font-weight: 400;
    text-align: center;
    border-radius: 20px;
}


.fromBox {
  max-height: calc(100vh - 260px);
  overflow-y: auto;
  padding: 20px 15px;
  width: 100%;
}
.top{
    border-bottom: 1px solid #efefef;
    height:60px;
    padding:0 25px;
   
}
.form{
     margin:0 auto;
     margin-top: 16px;
     width:305px;
    .hotItem{
        border: 1px solid #efefef;
        padding:15px 5px 15px 0 ;
        position: relative;
        i{  
            opacity: 0;
            position: absolute;
            right: -15px;
            top: -20px;
            font-size: 20px;
        }
        
    }
    :hover i{  
        opacity: 1;
    }
}
.addbtn{
    cursor: pointer;
    margin:0 auto;
    text-align: center;
    margin-top: 16px;
    width:305px;
    height: 40px;
    line-height: 40px;
    background: #ffffff;
    border: 1px solid #409eff;
    font-size: 14px;
    font-weight: 400;
    color: #409eff;
}
.tipBox{
    margin:0 auto;
    margin-top: 16px;
    width:305px;
    p.title{
        font-size: 14px;
        height: 19px;
        font-size: 14px;
        font-weight: 400;
    }
    p.tip{
        font-size: 14px;
        
        height: 40px;
        font-family:  -400;
        font-weight: 400;
        color: #aaaaaa;
        line-height: 20px;

    }
}
</style>
