<template>
  <im-dialog :title="title" :visible.sync="visibleDialog" :width="width" :append-to-body="true" @confirm="confirm">
    <el-table
      ref="tableData"
      :data="tableData"
      border
    >
      <el-table-column label="序号" type="index" width="54" align="center" />
      <el-table-column label="热词名称" prop="name">
        <template slot-scope="scope">
          <el-input v-model="scope.row.name" placeholder="请输入热词名称" class="el-input-none-border" />
          <span style="display: none;">{{ scope.row.sortValue = scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150" align="center">
        <template slot-scope="scope">
          <el-row class="table-edit-row">
            <span class="table-edit-row-item">
              <el-button type="text" @click="handleUp(scope.$index)">上移</el-button>
            </span>
            <span class="table-edit-row-item">
              <el-button type="text" @click="handleDown(scope.$index)">下移</el-button>
            </span>
            <span class="table-edit-row-item">
              <el-button type="text" @click="handleDel(scope.$index)">删除</el-button>
            </span>
          </el-row>
        </template>
      </el-table-column>
    </el-table>
    <div class="add-button" @click="add"><span>添加热词</span></div>
  </im-dialog>
</template>

<script>
import { hotUpdate } from './index'
export default {
  name: 'MallNavigation',
  props: {
    title: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '720px'
    }
  },
  data() {
    return {
      visibleDialog: false,
      tableData: []
    }
  },
  methods: {
    init(data) {
      this.tableData = data
      this.visibleDialog = true
    },
    confirm() {
      console.log(this.tableData)
      hotUpdate(this.tableData).then(res => {
        this.$message.success('编辑热词成功')
        this.$emit('confirm')
        this.visibleDialog = false
      })
    },
    add() {
      this.tableData.push({
        name: ''
      })
    },
    handleUp(index) {
      if (index !== 0) {
        this.tableData[index] = this.tableData.splice(index - 1, 1, this.tableData[index])[0]
      }
    },
    handleDown(index) {
      if (index !== this.tableData.length - 1) {
        this.tableData[index] = this.tableData.splice(index + 1, 1, this.tableData[index])[0]
      }
    },
    handleDel(index) {
      this.tableData.splice(index, 1)
    }
  }
}
</script>

<style lang="less">
  .add-button{
    border: 1px solid #EBECEE;
    border-top: 0;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    span{
      font-size: 14px;
      color: #0056e5;
      position: relative;
      &:after{
        content: '';
        position: absolute;
        left: -15px;
        top: 50%;
        background-color: #0056E5;
        width: 7px;
        height: 1px;
        margin-top: 0;
      }
      &:before{
        content: '';
        position: absolute;
        left: -12px;
        top: 50%;
        margin-top: -3px;
        background-color: #0056E5;
        width: 1px;
        height: 7px;
      }
    }
  }

</style>
