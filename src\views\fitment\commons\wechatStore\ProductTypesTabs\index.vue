<template>
    <div class="items"  @click.stop="checkPermission(['admin','fitment-wechat:edit'])&&showDrawerFun()">
        <div class=" productGroupItems">
            <el-tabs v-model="editableTabsValue" type="card"  >
                <el-tab-pane v-for="(item,index) in queryProductGroupItems" :key="index"  :name="item.id">
                    <div slot="label" class="productGroupItem">
                        <h4 class="title1">{{item.label}}</h4>
                        <!-- <h5 class="title2">{{item.ext1}}</h5> -->
                    </div>
                </el-tab-pane>
            </el-tabs>
        </div> 
        <el-drawer
            :destroy-on-close="true"
            :size="'345px'"
            append-to-body
            :wrapperClosable="false"
            :visible.sync="drawer"
            :with-header="false">
            <div class="flex_between_center top" >
                <div>商品分组</div> 
                <div>
                    <el-button @click="drawer=false" >取 消</el-button>
                    <el-button type="primary"  @click="submitFun()" >提交</el-button>
                </div>
            </div>
            <div class="tipBox">
                <p class="title">添加分组</p>
                <p class="tip">最多添加10个商品分组，鼠标拖拽可调整分组顺序</p>
            </div>
            <el-form  label-width="90px" class="form">
                 <el-form-item label-width="0" label=" " class="setNavItem"  v-for="(item,index) in productGroupItems" :key="index"  prop="label"  v-dragging="{ item: item, list: productGroupItems, group: 'item' }">
                    <i class="el-icon-error closeI" @click="removeItem(index)"></i>
                    <div>
                        <el-form-item label="分组大标题" class="setNavItemInput">
                            <el-input v-model="item.label"></el-input>
                        </el-form-item>
                        <el-form-item label="分组副标题" class="setNavItemInput">
                            <el-input v-model="item.ext1"></el-input>
                        </el-form-item>
                        <el-form-item label="商品数量" class="setNavItemInput">
                            <el-input :value="item.pageAdList.length" :disabled="true">
                                <ProductTableButton  
                                    slot="append"
                                    :width="'100px'"
                                    :selectItems.sync="item.pageAdList"
                                ></ProductTableButton>
                            </el-input>
                        </el-form-item>
                    </div>
                </el-form-item>
            </el-form>
            <div class="addbtn" @click="addItemBtnFun">
                + 添加分组
            </div>
        </el-drawer>
    </div>
</template>
<script>
import checkPermission from '@/utils/permission' 
import {
 pageComponentListAdd
} from "@/api/fitment";
import ProductTableButton from "./ProductTableButton";
import { numberFormatter } from '@/filters';
export default {
data() {
return {
    drawer:false,
    editableTabsValue:"1",
    productGroupItems:[
    ],
    queryProductGroupItems:[
    ],
};
},
components:{
    ProductTableButton
},
props: {
    floorsCompomentObject:{
      type:Object,
      required: true
    },
    pagePlateId:{
        type:String,
        required:true
    }
},
methods: {
    checkPermission,
    showDrawerFun(){
        this.drawer=true
        this.productGroupItems=JSON.parse(JSON.stringify(this.queryProductGroupItems))
    },
    addItemBtnFun(){
        if(this.productGroupItems.length<8){
            this.productGroupItems.push(
            {
                "adAdd": "N",
                "top": "N",
                "adSearch": "N",
                "adTemplateType": "FIXED",
                "parentId":this.floorsCompomentObject.id,
                "label":"",
                "ext1": "",
                "pageAdList": [],
                "pagePlateId": this.pagePlateId,
		    }
          )
        }
    },
    removeItem(index){
        this.productGroupItems.splice(index,1)
    },
    async submitFun(){
        this.productGroupItems.forEach((item,index)=>{
            item.componentCode=this.floorsCompomentObject.id+"_"+this.floorsCompomentObject.componentCode+"_"+index
            item.sortValue=index;
        })
       this.queryProductGroupItems=[...[],...this.productGroupItems]
       await pageComponentListAdd({
           parentPageComponentId:this.floorsCompomentObject.id,
           list:this.queryProductGroupItems
       })
       this.drawer=false
    },
     handleAvatarSuccess(res, file) {
        this.imageUrl = URL.createObjectURL(file.raw);
      },
      beforeAvatarUpload(file) {
        const isJPG = file.type === 'image/jpeg';
        const isLt2M = file.size / 1024 / 1024 < 5;

        if (!isJPG) {
          this.$message.error('上传头像图片只能是 JPG 格式!');
        }
        if (!isLt2M) {
          this.$message.error('上传头像图片大小不能超过 2MB!');
        }
        return isJPG && isLt2M;
      }
},
mounted() {
    if(this.floorsCompomentObject.children!=null){
        this.floorsCompomentObject.children.forEach(item=>{
            if(item.pageAdList==null){
                item.pageAdList=[]
            }
            this.queryProductGroupItems.push(item)
        })
    }
},
beforeDestroy() {}
};
</script>
<style lang="less" scoped>
.items{
    background: #fff;
    border:1px  dashed  red;
    margin-bottom: 12px;
    cursor: pointer;
} 
.productGroupItems{margin-top:15px;}

.productGroupItems .productGroupItem{ padding:5px 10px;  }
.productGroupItems .productGroupItem .title1{ color: #409EFF;  font-weight: bold; margin:0}
.productGroupItems .productGroupItem .title2{ color: #409EFF; font-weight: 400; margin:0;text-align: center; }

/deep/ .productGroupItems .el-tabs__item{ height:auto; line-height: unset;}


.top{
    border-bottom: 1px solid #efefef;
    height:60px;
    padding:0 15px;
   
}
.fromBox{max-height: calc(100vh - 260px); overflow-y: auto;}
.form{
    max-height: calc(100vh - 260px); overflow-y: auto;
     margin:0 auto;
     margin-top: 16px;
     width:100%;
     padding:10px;
     position: relative;
    //  border: 1px solid #efefef;
    .closeI{  
        opacity: 0;
        position: absolute;
        right: -18px;
        top: -18px;
        font-size: 20px;
        cursor: pointer;
    }
    .setNavItem {border: 1px solid #efefef; padding:8px 10px;}
    .setNavItem .setNavItemInput{margin-bottom: 10px;}
    .setNavItem .setNavItemInput:nth-last-child(1){margin-bottom:0;}
}
.fromBox{max-height: calc(100vh - 260px); overflow-y: auto;}
.form:hover .closeI{  
    opacity: 1;
}
.addbtn{
    cursor: pointer;
    margin:0 auto;
    text-align: center;
    margin-top: 16px;
    width:325px;
    height: 40px;
    line-height: 40px;
    background: #ffffff;
    border: 1px solid #409eff;
    font-size: 14px;
    font-weight: 400;
    color: #409eff;
}
.tipBox{
    margin:0 auto;
    margin-top: 16px;
    width:325px;
    p.title{
        font-size: 14px;
        height: 19px;
        font-size: 14px;
        font-weight: 400;
    }
    p.tip{
        font-size: 14px;
        
        height: 40px;
        font-family:  -400;
        font-weight: 400;
        color: #aaaaaa;
        line-height: 20px;

    }
}


</style>