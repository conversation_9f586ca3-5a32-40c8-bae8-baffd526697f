<template>
  <div class="items" @click.stop="checkPermission(['admin','fitment-wechat:edit'])&&showDrawerFun()" >
    <div class="info">
        <img class="logo" src="http://eyaolink-dev-bucket.oss-cn-shenzhen.aliyuncs.com/wechat/20210113/images/index/toast_login.png"/>
        <div class="title">
            <div class="shopName">华东医药股份有限公司</div>
            <div class="shopInfo">
            <span>上架品种 12720</span>
            <span>起配金额 ￥500</span>
            </div>
        </div>
        <div class="msg">
        <img src="http://eyaolink-dev-bucket.oss-cn-shenzhen.aliyuncs.com/wechat/20210113/images/index/icon_announcement.png"/>
        <span v-for="(item, index) in queryHeaderItems" :key="index">{{item.name}}</span>
        </div>
    </div>

    <el-drawer
            :destroy-on-close="true"
            :size="'345px'"
            append-to-body
            :wrapperClosable="false"
            :visible.sync="drawer"
            :with-header="false">
            <div class="flex_between_center top" >
                <div>店铺公告</div> 
                <div>
                    <el-button @click="drawer=false" >取 消</el-button>
                    <el-button type="primary"  @click="submitFun()" >提交</el-button>
                </div>
            </div>
            <div class="tipBox">
                <p class="title">店铺公告</p>
                <!-- <p class="tip">最多添加20个热词，默认展示前10个热词，其它的热词在搜索页中显示，鼠标拖拽可调热词顺序</p> -->
            </div>
            <el-form  label-width="75px" class="fromBox form">
                <el-form-item label="店铺公告" class="hotItem"  v-for="(item,index) in HeaderItems" :key="index"   v-dragging="{ item: item, list: HeaderItems, group: 'item' }" >
                    <i class="el-icon-error" @click="removeItem(index)"></i>
                    <el-input v-model="item.name" placeholder="请设置提示文字"></el-input>
                </el-form-item>
            </el-form>
            <div class="addbtn" @click="addItemBtnFun">
                + 添加店铺消息
            </div>
        </el-drawer>

  </div>
</template>
<script>
import checkPermission from '@/utils/permission' 
import { 
    pageComponentList,
    deleteByPageComponentId,
    pageADList,
    pageADAdd,
    pageADListAdd,
    pageADEdit 
}  from "@/api/fitment";
export default {
data() {
    return {
        drawer:false,
        parentId:null,
        queryHeaderItems:null,
        HeaderItems:null
    };
},
props: {
    pagePlateId:{
        type:String,
        required:true
    }
},
methods: {
    checkPermission,
    showDrawerFun(){
        this.drawer=true
        this.HeaderItems=[...[],...this.queryHeaderItems]
    },
    addItemBtnFun(){
        this.HeaderItems.push({
          id: "",
          pagePlateId:this.pagePlateId,
          showStatus:"Y",
          pageComponentId:this.pageComponentId,
          name: "",
          sortValue:this.HeaderItems.length
        })
    },
    removeItem(index){
        this.HeaderItems.splice(index,1)
    },
    async submitFun(){
        await deleteByPageComponentId(this.pageComponentId)
        console.info(this.HeaderItems)
        this.HeaderItems.forEach((item,index)=>{
            item.sortValue=index;
        })
        this.queryHeaderItems=[...[],...this.HeaderItems]
        this.drawer=false
        var data = await pageADListAdd(this.queryHeaderItems)
        if (data.code == 0) {
            this.initFun();
        } else {
            this.$message.error("提交失败！");
        }
    },
    async initFun(){
        var {data} = await  pageComponentList({
            "current": 1,
            "map": {},
            "model": {
                "componentCode": "hot_search"
            },
            "order": "descending",
            "size": 10,
            "sort": "id"
        })
        this.pageComponentId=data.records[0].id
        let adList = await  pageADList({
            "current": 1,
            "map": {},
            "model": {
                pageComponentId:data.records[0].id
            },
            "order": "descending",
            "size": 10,
            "sort": "id"
        })
        adList.data.records.forEach(item=>{
            item.showStatus=item.showStatus.code
        })
        this.queryHeaderItems=adList.data.records
    }
},
mounted() {
    // 请求热搜接口
    this.initFun()
},
beforeDestroy() {}
};
</script>
<style lang="less" scoped>
.items {
  margin: 0 auto 50/2px;
  margin-top: 150/2px;
  width: 685/2px; height:200/2px;
  border: 1px dashed red;
  margin-bottom: 12px;
  cursor: pointer;
  margin-bottom: 48/2px;
}

.info{
  width: 685/2px; height:200/2px;
  border-radius: 5/2px;
  background: #fff;
  position: relative;
}

.info .logo{
  position: absolute;
  top: -50/2px;
  left: 25/2px;
  background: #fff;
   width: 150/2px; height: 150/2px; border: 1px solid #eeeeee; border-radius: 5/2px;
}

.info .title{
  padding-left: 190/2px; 
  padding-top: 10/2px;
}
.info .title .shopName{
  color: #364255;
  font-weight: 600;
  line-height: 30px;
  font-size: 30/2px;
}
.info .title .shopInfo{
  color: #838b97;
  font-weight: 400;
  font-size: 24/2px;
}
.info .title .shopInfo span:nth-child(1){margin-right: 10/2px;}
.info .msg{margin-top:30/2px; margin-left: 25/2px; display: flex; justify-content: flex-start; align-items: center;}
.info .msg img{width: 70/2px; height: 35/2px;}
.info .msg span{color: #838b97; margin-left:30/2px;
  font-weight: 400;
  font-size: 28/2px;}




.hotItems{ margin: 0 auto;margin-top: 7px; width: 345px ;}
.hotItems span{
    line-height: 24px;
    height: 24px;
    font-size: 12px;
    font-weight: 400;
    text-align: center;
    border-radius: 20px;
}


.fromBox {
  max-height: calc(100vh - 260px);
  overflow-y: auto;
  padding: 20px 15px;
  width: 100%;
}
.top{
    border-bottom: 1px solid #efefef;
    height:60px;
    padding:0 25px;
   
}
.form{
     margin:0 auto;
     margin-top: 16px;
     width:305px;
    .hotItem{
        border: 1px solid #efefef;
        padding:15px 5px 15px 0 ;
        position: relative;
        i{  
            opacity: 0;
            position: absolute;
            right: -15px;
            top: -20px;
            font-size: 20px;
        }
        
    }
    :hover i{  
        opacity: 1;
    }
}
.addbtn{
    cursor: pointer;
    margin:0 auto;
    text-align: center;
    margin-top: 16px;
    width:305px;
    height: 40px;
    line-height: 40px;
    background: #ffffff;
    border: 1px solid #409eff;
    font-size: 14px;
    font-weight: 400;
    color: #409eff;
}
.tipBox{
    margin:0 auto;
    margin-top: 16px;
    width:305px;
    p.title{
        font-size: 14px;
        height: 19px;
        font-size: 14px;
        font-weight: 400;
    }
    p.tip{
        font-size: 14px;
        
        height: 40px;
        font-family:  -400;
        font-weight: 400;
        color: #aaaaaa;
        line-height: 20px;

    }
}
</style>