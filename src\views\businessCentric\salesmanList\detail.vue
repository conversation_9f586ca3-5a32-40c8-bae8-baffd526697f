<template>
  <div class="salesManDetail" v-loading="loadingFlag" v-if="load">
    <page-title title="业务员档案详情">
      <template slot="icon">
        <img
          v-if="salesManForm.approvalStatus == 'PENDING'"
          src="@/assets/promoteImg/PENDING.png"
        />
        <img
          v-if="salesManForm.approvalStatus == 'REJECTED'"
          src="@/assets/promoteImg/REJECTED.png"
        />
        <img
          v-if="salesManForm.approvalStatus == 'ACCEPTED'"
          src="@/assets/promoteImg/promotion.png"
        />
      </template>
      <template>
        <el-button
          @click="acceptedFun"
          v-if="salesManForm.approvalStatus.code == 'PENDING'"
        >通 过</el-button
        >
        <el-button
          @click="rejectedFun"
          v-if="salesManForm.approvalStatus.code == 'PENDING'"
        >驳 回</el-button
        >
        <el-button type="primary" @click="toEdit">编 辑</el-button>
      </template>
    </page-title>
    <div class="contemt">
      <el-form
        :inline="true"
        :label-width="'140px'"
        :model="salesManForm"
        ref="editForm"
        :rules="rules"
      >
        <div class="item">
          <page-module-title title="基础信息" />
          <div>
            <el-form-item class="formItem" prop="code" label="业务员编码:">
              <span>{{ salesManForm.salesmanCode }}</span>
            </el-form-item>

            <el-form-item
              class="formItem"
              prop="name"
              label="真实姓名:"
              :rules="[
                {
                  required: true,
                  message: '请输入业务员真实姓名',
                  trigger: 'blur',
                },
              ]"
            >
              <span>{{ salesManForm.realName }}</span>
            </el-form-item>

            <!-- <el-form-item class="formItem" prop="idNumber" label="身份证号:" :rules="[{required: true, message: '请输入业务员身份证号', trigger:'blur'}]">
              <span>{{salesManForm.idCardNumber}}</span>
            </el-form-item> -->

            <el-form-item
              class="formItem"
              prop="idNumber"
              label="手机号:"
              :rules="[
                { required: true, message: '请输入联系方式', trigger: 'blur' },
              ]"
            >
              <span>{{ salesManForm.contactNumber }}</span>
            </el-form-item>

            <el-form-item
              class="formItem"
              prop="idNumber"
              label="所在区域:"
              :rules="[
                { required: true, message: '请输入联系方式', trigger: 'blur' },
              ]"
            >
              <span>{{ salesManForm.area }}</span>
            </el-form-item>

            <el-form-item
              class="formItem"
              prop="idNumber"
              label="性别:"
              :rules="[
                {
                  required: true,
                  message: '请选择业务员性别',
                  trigger: 'blur',
                },
              ]"
            >
              <span>{{
                (salesManForm.sex && salesManForm.sex.code=="M")
                  ? "男"
                  : (salesManForm.sex && salesManForm.sex.code == "W")
                  ? "女"
                  : "保密"
              }}</span>
            </el-form-item>

            <el-form-item
              class="formItem"
              prop="idNumber"
              label="在职状态:"
              :rules="[
                {
                  required: true,
                  message: '请选择业务员账户状态',
                  trigger: 'blur',
                },
              ]"
            >
              <span>{{
                salesManForm.positionStatus.code == "WORKING" ? "在职" : "离职"
              }}</span>
            </el-form-item>

            <el-form-item
              v-if="salesManForm.positionStatus == 'QUIT'"
              class="formItem"
              prop="idNumber"
              label="离职时间:"
              :rules="[
                {
                  required: true,
                  message: '请选择业务员在职状态',
                  trigger: 'blur',
                },
              ]"
            >
              <span>{{ salesManForm.dimissionTime }}</span>
            </el-form-item>
          </div>
        </div>
        <div class="item">
          <page-module-title title="账户信息" />
          <el-form-item
            class="formItem"
            prop="idNumber"
            label="登录账号:"
            :rules="[
              {
                required: true,
                message: '请输入业务员登录账号',
                trigger: 'blur',
              },
            ]"
          >
            <span>{{ salesManForm.account }}</span>
          </el-form-item>
          <!--
          <el-form-item class="formItem" prop="idNumber" label="登录密码:" :rules="[{required: true, message: '请输入业务员登录密码', trigger:'blur'}]">
            <span>{{434343}}</span>
          </el-form-item> -->
        </div>
        <div class="item" v-if="salesManForm.bankCard">
          <page-module-title title="银行卡信息" />
          <el-form-item
            class="formItem"
            prop="idNumber"
            label="银行账户:"
            :rules="[
              {
                required: true,
                message: '请输入业务员登录账号',
                trigger: 'blur',
              },
            ]"
          >
            <span>{{ salesManForm.bankCard.bankAccount }}</span>
          </el-form-item>

          <el-form-item
            class="formItem"
            prop="idNumber"
            label="银行账号:"
            :rules="[
              {
                required: true,
                message: '请输入业务员登录密码',
                trigger: 'blur',
              },
            ]"
          >
            <span>{{ salesManForm.bankCard.bankNumber }}</span>
          </el-form-item>

          <el-form-item
            class="formItem"
            prop="idNumber"
            label="开户银行:"
            :rules="[
              {
                required: true,
                message: '请输入业务员登录密码',
                trigger: 'blur',
              },
            ]"
          >
            <span>{{ salesManForm.bankCard.bankName }}</span>
          </el-form-item>
        </div>
        <div class="item">
          <page-module-title title="证件信息" tips="点击图片即可放大预览" />
          <el-table
            v-if="list"
            :data="list"
            row-key="id"
            border
            fit
            highlight-current-row
            style="width: 100%"
          >
            <el-table-column
              prop="label"
              label="证件类型"
              min-width="100px"
            ></el-table-column>
            <el-table-column
              prop="idCardNumber"
              label="证件号"
              min-width="200px"
            >
              <template slot-scope="{ row }">
                <span>{{ row.idCardNumber }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="idCardPastTime"
              label="过期时间"
              min-width="170px"
            >
              <template slot-scope="{ row }">
                <span>{{
                  !row.idCardPastTime ? "长期" : row.idCardPastTime
                }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="idCardFront" label="身份证人像面" class-name="img-cell">
              <template slot-scope="{ row }">
                <span>
                  <el-image
                    v-for="file in row.idCardFrontList"
                    :key="file.url"
                    style="width: 50px; height: 50px"
                    :src="file.url"
                    :preview-src-list="[file.url]"
                  >
                  </el-image>
                  <!-- <img
                    v-for="file in row.idCardFrontList"
                    :key="file.url"
                    class="el-upload-list__item-thumbnail"
                    :src="file.url"
                    alt=""
                    style="
                      contain: cover;
                      width: 40px;
                      height: 40px;
                      margin-right: 5px;
                    "
                  /> -->
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="idCardReverse" label="身份证国徽面" class-name="img-cell">
              <template slot-scope="{ row }">
                <span>
                  <div
                    class="demo-image__preview"
                    v-for="file in row.idCardReverseList"
                    :key="file.url"
                  >
                    <el-image
                      style="width: 50px; height: 50px"
                      :src="file.url"
                      :preview-src-list="[file.url]"
                    >
                    </el-image>
                  </div>
                  <!-- <img
                    v-for="file in row.idCardReverseList"
                    :key="file.url"
                    class="el-upload-list__item-thumbnail"
                    :src="file.url"
                    alt=""
                    style="
                      contain: cover;
                      width: 40px;
                      height: 40px;
                      margin-right: 5px;
                    "
                  /> -->
                </span>
              </template></el-table-column
            >
            <el-table-column label="操作" align="center" width="140px">
              <template slot-scope="{ row }">
                <el-button type="text"  @click="showImgFlag = true"
                  >预 览</el-button
                ></template
              >
            </el-table-column>
          </el-table>
        </div>
        <!--
        <div class="item">
          <div class="title"><span>代理品种</span></div>
          <el-table v-loading="isLoading" border fit :data="goodsList" style="width: 100%">
            <el-table-column align="center" width="80" fixed="left" :render-header="renderHeader">
              <template slot-scope="scope">
                <span>{{scope.$index + 1}} </span>
              </template>
            </el-table-column>
            <el-table-column type="selection" width="55" align="center" fixed="left"></el-table-column>
            <el-table-column v-for="(item, index) in tableTitle" :key="index" :min-width="(item.width?item.width:'350px')"
              :fixed="(item.name == 'publishStatus'||item.name == 'pictIdS' ) ? 'left' : false" :label="item.label" show-overflow-tooltip :align="item.name == 'pictIdS'? 'center': 'left'">
              <template slot-scope="{row}">
                <el-popover v-if="item.name=='pictIdS'" placement="right" trigger="hover">
                  <img src="https://ss1.bdstatic.com/70cFuXSh_Q1YnxGkpoWK1HF6hhy/it/u=1089874897,1268118658&fm=26&gp=0.jpg" alt="" width="300" height="300">
                  <img slot="reference" src="https://ss1.bdstatic.com/70cFuXSh_Q1YnxGkpoWK1HF6hhy/it/u=1089874897,1268118658&fm=26&gp=0.jpg" alt="" width="40" height="40">
                </el-popover>
                <span v-else>{{ row[item.name] }}</span>
              </template>
            </el-table-column>
          </el-table>
          <pagination v-if="goodsTotal>0" :pageSizes="[2, 10, 20, 50]" :total="goodsTotal" :current-change="goodsTop" :page.sync="goodsQuery.current" :limit.sync="goodsQuery.size" @pagination="getlist" />
        </div> -->
        <!-- <div class="client">
          <div class="title flex_between_center">
            <div>
              <el-tabs v-model="clientQuery.model.approvalStatus.code" class="typeTabs" @tab-click="chageTabsFun">
                <el-tab-pane :label="'已绑定客户'" name="Y"></el-tab-pane>
                <el-tab-pane :label="'未绑定客户'" name="N"></el-tab-pane>
              </el-tabs>
            </div>
            <div>
              <el-button>批量绑定</el-button>
              <el-button>批量解绑</el-button>
            </div>
          </div>
          <el-table border fit :data="clientList" style="width: 100%">
            <el-table-column align="center" width="80" fixed="left" :render-header="clientrenderHeader">
              <template slot-scope="scope">
                <span>{{scope.$index + 1}} </span>
              </template>
            </el-table-column>
            <el-table-column type="selection" width="55" align="center" fixed="left"></el-table-column>
            <el-table-column v-for="(item, index) in clientTableTitle" :key="index" :min-width="(item.width?item.width:'350px')"
              :fixed="(item.name == 'publishStatus'||item.name == 'pictIdS' ) ? 'left' : false" :label="item.label" show-overflow-tooltip :align="item.name == 'pictIdS'? 'center': 'left'">
              <template slot-scope="{row}">
                <el-popover v-if="item.name=='pictIdS'" placement="right" trigger="hover">
                  <img src="https://ss1.bdstatic.com/70cFuXSh_Q1YnxGkpoWK1HF6hhy/it/u=1089874897,1268118658&fm=26&gp=0.jpg" alt="" width="300" height="300">
                  <img slot="reference" src="https://ss1.bdstatic.com/70cFuXSh_Q1YnxGkpoWK1HF6hhy/it/u=1089874897,1268118658&fm=26&gp=0.jpg" alt="" width="40" height="40">
                </el-popover>
                <span v-else>{{ row[item.name] }}</span>
                <span style="color: rgb(45, 172, 12);}">4343</span>
              </template>
            </el-table-column>
            <el-table-column fixed="right" align="center" label="操作" width="150" class="itemAction">
              <template slot-scope="scope">
                <el-button type="text"  @click="getlist(scope.row.id)">绑定</el-button>
                <el-button type="text" >解绑</el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination v-if="clientTotal>0" :pageSizes="[2, 10, 20, 50]" :total="clientTotal" :current-change="goodsTop" :page.sync="goodsQuery.current" :limit.sync="goodsQuery.size" @pagination="getlist" />
        </div> -->
      </el-form>
      <div style="height: 100px"></div>
    </div>
    <el-dialog
      title="资质预览"
      append-to-body
      :visible.sync="showImgFlag"
      :before-close="closeDialogFun"
    >
      <el-carousel arrow="always" height="50vh" :autoplay="false">
        <el-carousel-item v-for="(item, index) in showImgList" :key="index">
          <el-image
            style="width: 100%; height: 100%"
            :fit="'contain'"
            :src="item.fileIds"
          ></el-image>
        </el-carousel-item>
      </el-carousel>
      <div slot="footer">
        <el-button @click="closeDialogFun">取 消</el-button>
        <DowloadButton
          :buttonType="'primary'"
          :imgList="showImgList"
        ></DowloadButton>
      </div>
    </el-dialog>
  </div>
</template>

<script>
const tableInfo = [
  {
    key: 0,
    name: "publishStatus",
    label: "商品状态",
    width: "120px",
    disabled: true,
  },
  {
    key: 1,
    name: "pictIdS",
    label: "商品主图",
    width: "80px",
    disabled: true,
  },
  {
    key: 2,
    name: "code",
    label: "商品编码",
    width: "170px",
    disabled: true,
  },
  {
    key: 3,
    name: "name",
    label: "商品名称",
    width: "170px",
    disabled: true,
  },
  {
    key: 4,
    name: "relname",
    label: "通用名称",
    width: "150px",
  },
  {
    key: 5,
    name: "relax",
    label: "规格",
    width: "140px",
  },
  {
    key: 6,
    name: "dw",
    label: "单位",
    width: "80px",
  },
  {
    key: 7,
    name: "price",
    label: "销售价",
    width: "170px",
  },
  {
    key: 8,
    name: "price",
    label: "销售费用",
    width: "140px",
  },
  {
    key: 9,
    name: "price",
    label: "费用率",
    width: "100px",
  },
  {
    key: 10,
    name: "price",
    label: "所属商家",
    width: "140px",
  },
  {
    key: 11,
    name: "price",
    label: "代理区域",
    width: "240px",
  },
  {
    key: 12,
    name: "price",
    label: "代理状态",
    width: "100px",
  },
  {
    key: 13,
    name: "price",
    label: "剂型",
    width: "100px",
  },
  {
    key: 14,
    name: "price",
    label: "生产厂家",
    width: "170px",
  },
  {
    key: 15,
    name: "price",
    label: "平台商品分类",
    width: "240px",
  },
  {
    key: 16,
    name: "price",
    label: "商品条形码",
    width: "170px",
  },
];
const clientTableInfo = [
  {
    key: 0,
    name: "code",
    label: "客户编码",
    width: "170px",
    disabled: true,
  },
  {
    key: 1,
    name: "name",
    label: "客户名称",
    width: "140px",
    disabled: true,
  },
  {
    key: 2,
    name: "socialCode",
    label: "社会统一信用代码",
    width: "180px",
    disabled: true,
  },
  {
    key: 3,
    name: "merchantType",
    label: "企业类型",
    width: "140px",
  },
  {
    key: 4,
    name: "ceoName",
    label: "联系人",
    width: "100px",
  },
  {
    key: 5,
    name: "codeMobile",
    label: "联系电话",
    width: "140px",
  },
  {
    key: 6,
    name: "region",
    label: "所在地区",
    width: "170px",
  },
  {
    key: 7,
    name: "listCategory",
    label: "经营范围",
    width: "200px",
  },
];
import Pagination from "@/components/Pagination";
import {
  getitem,
  accepted,
  rejected,
} from "@/api/businessCentric/salesmanList";
import { setContextData } from "@/utils/auth";
import DowloadButton from "@/components/eyaolink/DowloadButton";
export default {
  data() {
    return {
      rules: {},
      salesManForm: {},
      loadingFlag: false,
      list: [],
      tableSelectTitle: [],
      showSelectTitle: false,
      goodsList: [1, 2],
      tableTitle: tableInfo,
      isLoading: false,
      goodsTotal: 11,
      goodsQuery: {
        current: 1,
        size: 10,
      },
      clientList: [1, 2],
      clientTotal: 11,
      clientQuery: {
        current: 1,
        size: 10,
        model: {
          approvalStatus: {
            code: "Y",
          },
        },
      },
      clientTableTitle: clientTableInfo,
      showImgFlag: false,
      showImgList: [],
      load: false,
    };
  },
  methods: {
    async rejectedFun() {
      let { data } = await rejected([this.salesManForm.id]);
      if (data) {
        this.$message.success("已驳回该业务员");
        setContextData("salesmanList_list", {
          current: 1,
          size: 10,
          model: {
            approvalStatus: "REJECTED",
          },
        });
        this.$router.go(-1);
      }
    },
    async acceptedFun() {
      let { data } = await accepted([this.salesManForm.id]);
      if (data) {
        this.$message.success("已通过该业务员");
        setContextData("salesmanList_list", {
          current: 1,
          size: 10,
          model: {
            approvalStatus: "ACCEPTED",
          },
        });
        this.$router.go(-1);
      }
    },
    closeDialogFun() {
      this.showImgFlag = false;
    },
    showImg() {},
    goodsTop(e) {},
    chageTabsFun() {},
    toEdit() {
      this.$router.push({
        path: "/businessCentric/salesmanListeditItem",
        query: {
          id: this.$route.query.id,
        },
      });
    },
    submit() {
      this.$refs.editForm.validate(async (valid) => {
        if (valid) {
          if (this.$route.query.id) {
          } else {
          }
        } else {
          console.log("submit error!");
        }
      });
    },
    async getitem() {
      this.loadingFlag = true;
      let { data } = await getitem(this.$route.query.id);
      this.load = true;
      this.salesManForm = data;
      let obj = {
        label: "身份证",
        idCardNumber: data.idCardNumber,
        idCardPastTime: data.idCardPastTime
          ? data.idCardPastTime.substr(0, 10)
          : "",
        idCardFront: data.idCardFront,
        idCardFrontList: this.getsrc(data.idCardFront),
        idCardReverse: data.idCardReverse,
        idCardReverseList: this.getsrc(data.idCardReverse),
        isEdit: false,
      };
      this.showImgList = [
        { fileIds: data.idCardFront },
        { fileIds: data.idCardReverse },
      ];
      this.list.push(obj);
      this.loadingFlag = false;
    },
    getsrc(str) {
      if (!str) {
        return [];
      } else {
        let arr = str.split(",");
        let list = [];
        arr.forEach((item) => {
          let obj = {
            response: {
              data: {
                url: "",
              },
            },
          };
          obj.response.data.url = item;
          obj.url = item;
          list.push(obj);
        });
        return list;
      }
    },
    getlist() {},
    initTbaleTitle() {
      this.tableTitle = tableInfo;
      this.tableSelectTitle = [];
    },
    clientrenderHeader() {
      return (
        <div style="position:relative">
          <i class="el-icon-menu" />
        </div>
      );
    },
    renderHeader(h, { column }) {
      var titles = tableInfo;
      var titlesName = ["显示字段项", "隐藏字段项"];
      return (
        <div style="position:relative">
          <div onClick={this.showHeaer}>
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="640px"
            center
            append-to-body={true}
          >
            <el-transfer
              vModel={this.tableSelectTitle}
              data={titles}
              titles={titlesName}
              onChange={this.setleftTitleFun}
            ></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button onClick={this.closeHeaer}>取消</el-button>
              <el-button type="primary" onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      );
    },
    setleftTitleFun(val) {
      this.tableSelectTitle = val;
    },
    showHeaer: function () {
      this.showSelectTitle = true;
    },
    closeHeaer: function () {
      this.showSelectTitle = false;
      this.tableSelectTitle = [];
    },
    setHeaer: function () {
      var titles = tableInfo;
      var listinfo = titles.filter((element, index, self) => {
        return !this.tableSelectTitle.includes(element.key);
      });
      this.tableTitle = listinfo;
      this.showSelectTitle = !this.showSelectTitle;
    }
  },
  created() {
    this.getitem();
  },
  components: {
    Pagination,
    DowloadButton,
  },
};
</script>

<style lang="less" scoped>
.salesManDetail {
  padding: 0 20px;
  border-top: 1px solid #ebecee;
  background-color: #fff;
  .top_title {
    line-height: 56px;
    border-bottom: 1px solid #eeeeee;
    font-size: 18px;
    margin-bottom: 30px;
    .flex_between_center {
      padding: 0 20px;
    }
    img {
      display: inline-block;
      width: 70px;
      position: relative;
      top: 10px;
    }
  }
  .contemt {
    .formItem {
      min-width: 250px;
      span {
        display: inline-block;
        width: 200px;
        color: #cdced3;
      }
    }
    .item {
      width: 100%;
      margin-bottom: 30px;
      border-bottom: 1px solid #eeeeee;
      .title {
        padding: 0 0 15px;
        span {
          font-size: 16px;
          padding-left: 10px;
          border-left: 4px solid rgba(64, 158, 255, 1);
        }
      }
    }
  }
  .client {
    & > .title {
      border-bottom: 2px solid #ebecee;
      margin-bottom: 35px;
      padding-top: 10px;
      .typeTabs {
        transform: translateY(15px);
        margin-left: 30px;
      }
    }
  }
  /deep/ .el-icon-circle-close {
    color: #fff;
  }
}
</style>
