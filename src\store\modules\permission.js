import { asyncRoutes, constantRoutes } from '@/router'
import { getUserMenuRouter } from '@/api/user'
import Cookies from 'js-cookie'
import Layout from '@/layout'
import Nested from '@/views/nested'
/**
 * Use meta.role to determine if the current user has permission
 * @param roles
 * @param route
 */
function hasPermission(roles, route) {
  if (route.meta && route.meta.roles) {
    return roles.some(role => route.meta.roles.includes(role))
  } else {
    return true
  }
}

/**
 * Filter asynchronous routing tables by recursion
 * @param routes asyncRoutes
 * @param roles
 */
export function filterAsyncRoutes(routes, roles) {
  const res = []

  routes.forEach(route => {
    const tmp = { ...route }
    if (hasPermission(roles, tmp)) {
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, roles)
      }
      res.push(tmp)
    }
  })

  return res
}


const state = {
  routes: [],
  addRoutes: [],
  alreadyAddRouts: false
}

const mutations = {
  SET_ROUTES: (state, routes) => {
    state.addRoutes = routes
    state.routes = constantRoutes.concat(routes)
    state.alreadyAddRouts = true
  },
  RESET_ROUTES: (state, routes = []) => {
    state.addRoutes = routes
    state.routes = constantRoutes.concat(routes)
    state.alreadyAddRouts = false
  }
}

const actions = {
  generateRoutes({ commit, rootState  }, roles) {
    return new Promise((resolve, reject) => {
      let accessedRoutes
      // TODO 此为无用逻辑，可删除，初始化环节路由、角色权限全部为后端控制
      if (roles.includes('admin')) {
        accessedRoutes = asyncRoutes || []
        Cookies.set('SET_ROUTES', accessedRoutes)
        commit('SET_ROUTES', accessedRoutes)
        resolve(accessedRoutes)
      } else {
        // 通过userId获取路由权限
        getUserMenuRouter(rootState.user.userId).then(response => {
          const { data } = response
          accessedRoutes = filterAsyncRouter(data)
          Cookies.set('SET_ROUTES', accessedRoutes)
          commit('SET_ROUTES', accessedRoutes)
          resolve(accessedRoutes)
        }).catch(error => {
          reject(error)
        })
      }
    })
  },
  resetRoutes({ commit }, routes) {
    commit('RESET_ROUTES', routes)
  }
}

function filterAsyncRouter(routes, parent) {
  return routes.filter((route) => {
    const component = route.component
    if (component) {
      if (route.component === 'Layout') {
        if (route.children && route.children.length > 0 && parent) {
          route.component = Nested
        } else {
          route.component = Layout
        }
      } else if (typeof component === 'string') {
        route.component = view(component)
      }
      if (route.children && route.children.length) {
        route.children = filterAsyncRouter(route.children, route)
      }
      return true
    }
  })
}

function view(path) {
  return function (resolve) {
    require([`@/views/${path}.vue`], resolve)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
