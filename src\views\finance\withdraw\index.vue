<template>
  <div>
    <im-search-pad
      :has-expand="false"
      :model="model"
      @reset="reload"
      @search="searchLoad"
    >
      <im-search-pad-item prop="businessNo">
        <el-input v-model="model.businessNo" placeholder="请输入业务号" />
      </im-search-pad-item>
      <im-search-pad-item prop="applicantUserName">
        <el-input v-model="model.applicantUserName" placeholder="请输入制单人" />
      </im-search-pad-item>
      <im-search-pad-item prop="applicantUserName">
        <el-date-picker
          v-model="during"
          type="datetimerange"
          align="right"
          unlink-panels
          range-separator="至"
          start-placeholder="起始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd hh:mm:ss"
        >
        </el-date-picker>
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        :tabs="tabList"
        @change="handleChangeTab"
      >
        <template slot="button">
          <!-- <el-button  v-if="checkPermission(['admin','withdraw:export'])">导出</el-button> -->
          <el-button  @click="reload">刷新</el-button>
        </template>
      </tabs-layout>
      <table-pager ref="bussinessTabel" :options="tableTitle" :remote-method="load" :data.sync="tableData" :pageSize="pageSize"  :isNeedButton="isWait"  >
        <template slot="amount">
          <el-table-column label="金额（元）" width="100">
            <slot slot-scope="{row}">
              {{row.amount|getDecimals}}
            </slot>
          </el-table-column>
        </template>
        <template slot="reviewUserName" v-if="model.businessStatus!=='WAIT'">
          <el-table-column label="审核人" width="160" prop="reviewUserName" />
        </template>
        <template slot="reviewTime" v-if="model.businessStatus!=='WAIT'">
          <el-table-column label="审核时间" width="160" prop="reviewTime" />
        </template>
        <template slot="remarks" v-if="model.businessStatus!=='WAIT'">
          <el-table-column label="审核备注"  prop="remarks" />
        </template>
        <template slot="paymentId" v-if="model.businessStatus==='PROCESS'||model.businessStatus==='FINISH'">
          <el-table-column label="付款单号" width="160">
            <slot slot-scope="{row}">
              <span class="text-primary">{{row.paymentId}}</span>
            </slot>
          </el-table-column>
        </template>
        <div slot-scope="props">
          <el-button type="text" v-if="checkPermission(['admin','widthdraw:verify'])" @click="handleVerify(props.row.id)">审核</el-button>
        </div>
      </table-pager>
    </div>
    <el-dialog title="提现单审核" :visible.sync="verifyVisible" width="500px">
      <el-form :model="vForm">
        <el-form-item>
          <el-input placeholder="请输入备注信息" v-model="vForm.remarks" type="textarea" rows="4"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="verifyVisible = false">取 消</el-button>
        <el-button v-if="checkPermission(['admin', 'admin-finance-balanceCashOut:reject'])" @click="submit(3)">驳 回</el-button>
        <el-button v-if="checkPermission(['admin', 'admin-finance-balanceCashOut:accept'])" type="primary" @click="submit(1)">审核通过</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  const TableColumns = [
    { label: "业务单号", name: "id",prop: "id",width: "170"},
    { label: "业务类型", name: "businessType.desc", prop:"businessType.desc",width: "100" },
    { label: "业务单状态", name: "businessStatus.desc", prop:"businessStatus.desc",width: "100" },
    { label: "制单人", name: "applicantUserName", prop:"applicantUserName",width: '170' },
    { label: "金额（元）", name: "amount", prop:"amount",slot: true  },
    { label: "制单人", name: "createUserName",prop:'createUserName',width: "200" },
    { label: "制单时间", name: "createTime",prop:'createTime',width: "170" },
    { label: "审核人", name: "reviewUserName", prop:"reviewUserName",slot: true  },
    { label: "审核时间", name: "reviewTime", prop:"reviewTime",slot: true },
    { label: "审核备注", name: "remarks", prop:"remarks",slot: true  },
    { label: "付款单号", name: "paymentId", prop:"paymentId",slot: true  },
  ];
  const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({ key: i, ...TableColumns[i] });
  }
  import checkPermission from "@/utils/permission";
  import { statistics,list,updateCashRemarks } from '@/api/finance/withdraw/index.js'
  import TabsLayout from '@/components/TabsLayout'

  export default {
    components: {
      TabsLayout
    },
    data () {
      return {
        loading: '',
        currentTab: 0,
        tabs: [
          { name: '待审核', value: 'WAIT',count: 0,hide: !checkPermission(['admin', 'admin-finance-balanceCashOut:pendingView']) },
          { name: '提现中', value: 'PROCESS',count: 0,hide: !checkPermission(['admin', 'admin-finance-balanceCashOut:handingView'])  },
          { name: '已驳回', value: 'REJECT',count: 0,hide: !checkPermission(['admin', 'admin-finance-balanceCashOut:rejectedView'])  },
          { name: '已完成', value: 'FINISH',count: 0,hide: !checkPermission(['admin', 'admin-finance-balanceCashOut:finishedView'])  }
        ],
        tableData: [],
        page: 1,
        pageSize: 10,
        totalPage: 0,
        total: 0,
        tableTitle: TableColumnList,
        ids: [],
        during: '',
        model: {
          businessNo: '',
          businessStatus: 'WAIT',
          applicantUserName: '',
        },
        verifyVisible: false,
        isWait: true,//操作列
        vForm: {
          remarks: '',
          id: ''
        }
      }
    },
    computed: {
      tabList() {
        return [
          { name: `待审核（${this.tabs[0].count}）`, value: 'WAIT', hide: !checkPermission(['admin', 'widthdraw-wait:view']) },
          { name: `提现中（${this.tabs[1].count}）`, value: 'PROCESS', hide: !checkPermission(['admin', 'widthdraw-process:view']) },
          { name: `已驳回（${this.tabs[2].count}）`, value: 'REJECT', hide: !checkPermission(['admin', 'widthdraw-reject:view']) },
          { name: `已完成（${this.tabs[3].count}）`, value: 'FINISH', hide: !checkPermission(['admin', 'widthdraw-finish:view']) }
        ]
      }
    },
    mounted() {
      this.getCount()
    },
    methods: {
      checkPermission,
      async getCount() {
        const query = {
          merchantsId: this.$route.params.merchantsId
        }
        const {data} = await statistics(query)
        this.tabs.forEach(item=>{
          item.count = data[item.value.toLowerCase()]
        })
      },
      handleSelectionChange(val) {

      },
      handleVerify(id) {
        this.vForm.id = id
        this.verifyVisible  = true
      },
      async submit(status) {
        const param = {
          ...this.vForm,
          status: status //1,//状态 1 -> 通过 3 -> 驳回
        }
        const {data} = await updateCashRemarks(param)
        this.$message.success('该提现单审核操作完成！')
        this.verifyVisible = false
        this.getCount()
        this.vForm.remarks = ''
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      async load(params) {
        const listQuery = {
          model: {
            ...this.model,
            startTime: this.during[0],
            endTime: this.during[1]
          }
        }
        Object.assign(listQuery, params)
        this.loading = true
        const {data} = await list(listQuery)
        this.todoName = data.records[0].merchantName
        this.loading = false
        return { data }
      },
      searchLoad() {
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      reload() {
        this.model={
          ...this.model,
          ...{
            businessNo: '',
            applicantUserName: ''
          }
        }
        this.during = ''
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      handleChangeTab(tab) {
        this.model.businessStatus = tab.value
        if (tab.value !== 'WAIT') {
          this.isWait = false
        } else {
          this.isWait = true
        }
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      handleRefresh(pageParams) {
        this.$refs.bussinessTabel.doRefresh(pageParams)
      }
    }
  }
</script>

<style lang="less" scoped>
  .todo-settlement {
    width: 100%;
    height: 42px;
    margin-bottom: 20px;
    background-color: #effaff;
    border: 1px solid #81d3f8;
    padding: 0 12px;
    line-height: 42px;
    font-size: 14px;
  }
</style>
