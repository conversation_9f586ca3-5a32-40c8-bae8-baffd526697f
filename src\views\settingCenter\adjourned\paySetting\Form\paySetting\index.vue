<template>
  <div class="tempPaySettingForm">
    <el-form ref="form" :model="form" label-width="100px" v-if="dialogType!='offline'">
      <div>
        <div class="title">渠道信息</div>
        <el-form-item class="formItem" label="支付方式:">
          <el-input :disabled="true" v-model="form.payName"></el-input>
        </el-form-item>
        <el-form-item class="formItem" label="描述:" required>
          <el-input v-model="form.detail" type="textarea"></el-input>
        </el-form-item>
        <el-form-item class="formItem" label="状态:">
          <el-switch
            v-model="form.start"
            active-text="启动"
            inactive-text="停用"
          ></el-switch>
        </el-form-item>
      </div>
      <div>
        <div class="title">公众号信息</div>
        <el-form-item class="formItem" label="APPID:">
          <el-input v-model="form.payName"></el-input>
        </el-form-item>
        <el-form-item class="formItem" label="AppSecret:">
          <el-input v-model="form.payName"></el-input>
        </el-form-item>
      </div>
      <div>
        <div class="title">商户参数</div>
        <el-form-item class="formItem" label="商户号:">
          <el-input v-model="form.merchantNumber"></el-input>
        </el-form-item>
        <el-form-item class="formItem" label="商户秘钥:">
          <el-input v-model="form.merchantKey"></el-input>
        </el-form-item>
        <el-button type="primary" @click="onSubmit">立即创建</el-button>
        <el-button>取消</el-button>
      </div>
    </el-form>
    <el-form v-else ref='offlineForm' :rules="offlineRules" :model="offlineForm" label-width="100px">
      <el-form-item class="formItem" label="银行账户:" prop="name">
        <el-input v-model="offlineForm.name" placeholder="请输入银行账户"></el-input>
      </el-form-item>
      <el-form-item class="formItem" label="银行账号:" prop="code">
        <el-input v-model="offlineForm.code" placeholder="请输入银行账户"></el-input>
      </el-form-item>
      <el-form-item class="formItem" label="开户银行:" prop="bankName">
        <el-input v-model="offlineForm.bankName" placeholder="请输入开户银行名"></el-input>
      </el-form-item>
      <el-button type="primary" @click="submitOfflineForm('offlineForm')">立即创建</el-button>
      <el-button>取消</el-button>
    </el-form>
  </div>
</template>
<script>
export default {
  data() {
    return {
      form: {
        payName: "银联支付",
        detail: "银联支付",
        start: true,
        APPID: "",
        AppSecret: "",
        merchantNumber: "",
        merchantKey: ""
      },
      offlineForm:{
        name:'',
        code:'',
        bankName:''
      },
      offlineRules: {
        name:[
          { required: true, message: '请输入银行账户', trigger: 'blur' },
        ],
        code:[
          { required: true, message: '请输入银行账户', trigger: 'blur' },
        ],
        bankName:[
          { required: true, message: '请输入开户银行名', trigger: 'blur' },
        ],
      },
    };
  },
  props: {
    cardId: {
      required: true,
      type: Number,
      default: 0
    },
    dialogType:{
      type:String,
      default:'default'
    }
  },
  methods: {
    onSubmit: function() {},
    submitOfflineForm(formName){
      this.$refs[formName].validate((valid) => {
        if(valid){
          console.log('valid--表单验证成功');
        } else {
          console.log('valid--表单验证失败');
        }
      })
    },
  },
  
  mounted() {},
  beforeDestroy() {}
};
</script>
<style lang="scss" scoped>
@import "@/styles/element-variables.scss";
.tempPaySettingForm {
  .title {
    color: #333333;
    font-family: "Arial-BoldMT", "Arial Bold", "Arial";
    font-weight: 700;
    font-style: normal;
    font-size: 15px;
    text-indent: 4px;
    margin-bottom: 20px;
    border-left: 3px solid $--color-primary;
  }
  .formItem{margin-bottom: 16px;}
}
</style>
