<template>
  <div class="container">
    <!-- 下级部门 -->
    <div>
      <div class="tab_bg">
        <tabs-layout :tabs="[{ name: '下级部门' }]">
          <template slot="button">
            <div class="flex_between_center">
              <el-button
                v-if="organizationNavNode.length<5 && checkPermission(['admin', 'admin-team-orgaStructure:addSubDepartment'])"
                size="small"
                type="primary"
                @click="showEditSubDepartmentFun(
                  {
                    id:null,
                    name: '',
                    orgaAreaDto: {
                      commonAreaIds: []
                    },
                    parentId: 0,
                    salesmanId: ''
                  })"
                >+新增下级部门</el-button
              >
            </div>
          </template>
        </tabs-layout>
        <table-pager
          ref="table"
          width="100%"
          :align="'center'"
          :paging="false"
          :options="subDepartmentTableColumnList"
          :data.sync="subDepartmentList"
          :selection="false"
          @selection-change="selectSubDepartmentTableItemFun"
        >
        <!-- <table-pager
          ref="table"
          :options="subDepartmentTableColumnList"
          :remote-method="loadSubDepartment"
          :data.sync="subDepartmentList"
          :selection="false"
          @selection-change="selectSubDepartmentTableItemFun"
        > -->
        <template slot="salesAreaName">
          <el-table-column label="销售区域" min-width="250">
            <slot slot-scope="{ row }">
              <p class="labelInfo flex_between_center"  @click="openAreaDialog()">
                <span>{{ row.salesAreaName || "未设置" }}</span><i class="el-icon-edit"></i>
              </p>
            </slot>
          </el-table-column>
        </template>
        <template slot="totalCount">
          <el-table-column label="销售品种" >
            <slot slot-scope="{ row }">
              <p class="labelInfo flex_between_center">
                <span>{{ row.totalCount || "0" }}</span><i class="el-icon-edit" @click="()=>{this.visibleChooseProduct=true}"></i>
              </p>
            </slot>
          </el-table-column>
        </template>
        <template slot="salesmanName">
          <el-table-column label="主管" min-width="150" >
            <slot slot-scope="{ row }">
              <p class="labelInfo flex_between_center" @click.stop="showEditSubDepartmentFun(row)">
                <span>{{ row.salesmanName || "未设置" }}</span><i class="el-icon-edit" ></i>
              </p>
            </slot>
          </el-table-column>
        </template>
          <div slot-scope="{row}">
            <el-row class="table-edit-row">
              <span class="table-edit-row-item">
                <del-el-button style="margin-left:5px" 
                  v-if="checkPermission(['admin', 'admin-team-orgaStructure:delDepartment'])" 
                  :targetId="row.id" 
                    :btnTxt="delbtnTxt" 
                    :text="delText" 
                    @handleDel="removeSubDepartmentFun">
                </del-el-button>
                <el-link type="primary" :underline="false" class="detailBtn" @click="setTreeNode(row)">查看</el-link>
              </span>
            </el-row>
          </div>
        </table-pager>
      </div>
    </div>
    <!-- 下级部门 end -->

    <EditDepartmentDialog 
      v-if="visible"
      :visible.sync="visible"
      :crateSource="'content'"
      :editForm="editform"
      @confirm="confirmSubmitFun"
      ></EditDepartmentDialog>
    <!-- <EditDepartmentDialog 
      ref="editDepartmentDialogSub" 
      :crateSource="'content'"
      :editForm="editform"
      @confirm="confirmSubmitFun"
      ></EditDepartmentDialog> -->
      <!--   -->
        <!-- 区域 -->
    <area-tree-dialog
      ref="areaDialog"
      @on-confirm="getEreaData"
      :regionList="areaList"
    />
    <!-- 区域 end -->
  </div>
</template>
<script>
// 下级部门
const subDepartmentTableColumns = [
  {
    label: "部门名称",
    name: "name",
    prop: "name",
    width: "150px",
  },
  // {
  //   label: "销售区域",
  //   name: "salesAreaName",
  //   prop: "salesAreaName",
  //   width: "250px",
  //   slot:true
  // },
  // {
  //   label: "销售品种 ",
  //   name: "totalCount",
  //   prop: "totalCount",
  //   width: "100px",
  //   slot:true
  // },
  { label: "主管", name: "salesmanName", prop: "salesmanName", width: "100px",slot:true },
  {
    label: "主管手机",
    name: "mobile",
    prop: "mobile",
    width: "120px",
  },
  {
    label: "部门成员(人)",
    name: "salesmanCount",
    prop: "salesmanCount",
    width: "110px",
  },
];
const subDepartmentTableColumnList = [];
for (let i = 0; i < subDepartmentTableColumns.length; i++) {
  subDepartmentTableColumnList.push({
    key: i,
    ...subDepartmentTableColumns[i],
  });
}
import areaTreeDialog from "@/views/team/index/temp/organization/organization-area-tree";
import { deleteDepartment,getSalesManList } from '@/api/organization/index';
import { mapState,mapGetters,mapActions} from 'vuex'
import { productList } from "@/api/dashboard";
import delElButton from "@/components/eyaolink/delElButton";
import EditDepartmentDialog from '@/views/team/index/temp/organization/editDepartmentDialog';
// import { query } from '@/api/';
export default {
  data() {
    return {
      visible:false,
      // 下级部门内容
      subDepartmentTableColumnList: subDepartmentTableColumnList,
      selectSubDepartmentTableItemIds: null,
      delText:"确定移除该部门吗？移除后，该部门及其下级部门将删除。该操作不可撤销，请谨慎操作。",
      delbtnTxt:"移除",
      // 下级部门内容
      // 新增 下级部门
      editform: {
      },
      areaList:[]
    };
  },
  props: {
    /**
     * @param {string} 子部门列表 
     */
    subDepartmentList: {
        type: Array,
        default: [],
        required: true,
    }
  },
  components: {
    // 组件
    areaTreeDialog,
    EditDepartmentDialog,
    delElButton
  },
  computed: {
    ...mapGetters([
      'organizationNavNode',
      "organizationInfo",
      "paremDepartmentInfo",
      "departmentInfo",
    ]),
  
  },
  filters: {},
  watch: {
    // 监听属性watch：
    // 1. 不支持缓存，数据变，直接会触发相应的操作；
    // 2. watch支持异步；
    // 3. 监听的函数接收两个参数，第一个参数是最新的值；第二个参数是输入之前的值；
    // 4. 当一个属性发生变化时，需要执行对应的操作；一对多；
    // 5. 监听数据必须是data中声明过或者父组件传递过来的props中的数据，当数据变化时，触发其他操作，函数有两个参数，
    // 　　immediate：组件加载立即触发回调函数执行，
    // 　　deep: 深度监听，为了发现对象内部值的变化，复杂类型的数据时使用，例如数组中的对象内容的改变，注意监听数组的变动不需要这么做。注意：deep无法监听到数组的变动和对象的新增，参考vue数组变异,只有以响应式的方式触发才会被监听到。
    // 　　deepdemo：
    //      obj:{
    //          handler(){
    //              console.log('obj 变了')
    //          },
    //          deep:true
    //      }
  },
  methods: {
    
    // 下级部门列表
    
    selectSubDepartmentTableItemFun(row) {
      this.selectSubDepartmentTableItemIds = row;
    },
    async removeSubDepartmentFun(id) {
      console.info(id)
      let {code,data,msg} = await deleteDepartment(id);
      if(code == 0 ){
        // 提交回调 
          this.$emit("confirm",this.editSubDepartmentForm)
          this.visible =false
          this.updateComponentsFun()
      }else{
        this.visible =false
      }
    },
    subDepartmentLoad() {},
    // 下级部门列表



    // 下级部门查看
    setTreeNode(row){
      this.$parent.$parent.setNodeByComponent(row.id)
    },

    // 下级部门编辑
    showEditSubDepartmentFun(row){
     
      if(row.id==null&&this.organizationNavNode!=null&&this.organizationNavNode.length>0){
        row.parentId = this.organizationNavNode[this.organizationNavNode.length-1].id
      }
      this.editform=Object.assign({}, row) 
      console.info("showEditSubDepartmentFun",this.editform)
      this.visible =true
      // this.$refs.editDepartmentDialogSub.show()
     
    },
    confirmSubmitFun(data){
      
      this.visible =false;
      console.info(" this.$parent.$parent.load('Department')")
      // this.$refs.editDepartmentDialogSub.close()
      // 更新 整个页面的
      this.updateComponentsFun()
    },



    // 下级部门编辑 end



    // 销售区域  
    openAreaDialog() {
      this.$refs.areaDialog.show();
    },
     getEreaData(data, flag) {
      if(this.departmentForm.area == undefined) {
        this.departmentForm['area'] =[]
      }
      if (flag) {
        // 全国
        let departmentForm = this.departmentForm;
        let allObj = {
          allType: 1,
          id: 0,
          label: "全国",
          isAllNational: 1, //是全国
        };
        this.sellAreaStr = '全国';
        this.departmentForm.area=[allObj];
        // 设置选中值
        this.areaList =this.getSelectAreaId(this.departmentForm.area)
      } else {
        if (data.length == 0) {
          // 没有选中任何数据
          this.sellAreaStr="";
          this.departmentForm.area = [];
          this.areaList =this.getSelectAreaId(this.departmentForm.area)
          return;
        }
        let str = this.getSelectAreaNames(data);
        this.sellAreaStr = str;
        let list = JSON.parse(JSON.stringify(this.departmentForm.area)).filter((item)=>{item.id!=0});
        let newList = [];
        let oldList = [];
        data.forEach((item) => {
          let index = list.findIndex((_) => _.id == item.id);
          if (index == -1) {
            // 新增的省市区
            let newObj = {
              ...item,
              allType: 1,
              isAllNational: 0, //不是全国
            };
            newList.push(newObj);
          } else {
            // 修改的省市区
            let oldObj = {
              ...item,
              allType: this.departmentForm.area[index].allType,
              isAllNational: 0, //不是全国
            };
            oldList.push(oldObj);
          }
        });
        let listData = [...oldList, ...newList];
        // console.info(this.getSelectAreaId(listData))
        //选中 树形图
        console.info(JSON.stringify(listData))
        this.departmentForm.area = listData
        //选中区
        this.areaList =this.getSelectAreaId(listData)
        // this.departmentForm.area =  this.dataDeal(listData);
      }

      let areaSubmit = {
        "areaNames": this.sellAreaStr,
        "departmentId": 0,
        "isAll": false,  
        "list":[],
        "organizationId": 0
      }
      if(flag){
        areaSubmit.isAll =true
      }
      areaSubmit.list = this.departmentForm.area 
      if(this.departmentInfo==null){
        delete areaSubmit.departmentId;
        areaSubmit.organizationId= this.organizationInfo.id;
        areaSubmit.organizationId= this.organizationInfo.id;
      } else{
        delete areaSubmit.organizationId
          areaSubmit.departmentId= this.departmentInfo.id
      }

      // 提交更新
      areaEdit(areaSubmit).then(res=>{
          let {code,data,msg} = res;
          if(code==0){
            // 回调更新部门信息
            console.info("this.updateComponentsFun()")
            this.updateComponentsFun()
          } 
      });
      

    },
    dataDeal(list) {
      let arr = JSON.parse(JSON.stringify(list));
      arr = arr.map((item) => {
        let obj = {
          ...item,
          label: item.label,
          isAll: item.isAll,
          id: item.id,
        };
        return obj;
      });
      return arr;
    },

    //递归名字
    getSelectAreaNames(list, str = "") {
      let _this = this;
      list.forEach(function (item, index) {
        if (item.isAll == true) {
          if (str != "") {
            str += "、";
          }
          str += item.label;
        } else if (item.children.length > 0) {
          if (str != "") {
            str += "、";
          }
          str += `${item.label}(${_this.getSelectAreaNames(item.children)})`;
        } else {
          if (str != "") {
            str += "、";
          }
          str += item.label;
        }
      });
      return str;
    },
    
     //递归循环区ID
    getSelectAreaId(list, selectList  = []) {
      let _this = this;
      list.forEach(function (item, index) {
        if (item.children!=null&&item.children.length > 0) {
          _this.getSelectAreaId(item.children,selectList)
        }else{
          selectList.push( item.id);
        }
      });
      return selectList;
    },


    // 销售区域 end 




    // 回调更新信息
    updateComponentsFun() {
      this.$parent.$parent.load('Department')
      this.$parent.getDepartmentInfo()
    },
  },
  mounted() {
    // 方法调用
    // let editform =JSON.parse(JSON.stringify(this.editform));
    // editform = {...editform,...{departmentId:this.departmentId}};
    // this.editform = editform
  },
  beforeDestroy() {},
};
</script>

<style lang="scss" scoped>
.container{width: 100%;}
.department {
  font-size: 16px;
  padding: 16px 0 0 16px;
}
.departmentBreadcrumb {
  padding: 16px 0 0 16px;

  ::v-deep .breadcrumbItem {
    & > span {
      color: #0056e5;
    }
    & > i {
      color: #0056e5;
    }
    &:last-child > span {
      color: #303133;
    }
  }
}

.labelInfo{
  color: #409EFF;
  font-size:14px;
  cursor: pointer;
}


::v-deep .es-table-pager .page-row {
  justify-content: flex-end;
}
.memberSearchInput {
  margin-right: 10px;
}

.detailBtn{ 
  margin-left: 10px;
   font-size:14px;
}

</style>
