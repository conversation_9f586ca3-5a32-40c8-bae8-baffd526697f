<template>
    <el-dialog append-to-body title="批量转移" width="500px" :visible="visible" @open="handleOpen" @close="handleClose">
        <el-form ref="form" :model="model" :rules="rules" label-width="120px">
            <el-form-item prop="id" label="接收业务员：">
                <el-select :class="$style['w-full']" v-model="model.id" filterable remote placeholder="接收业务员"
                    :remote-method="fetch" :loading="loading">
                    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
            </el-form-item>
        </el-form>
        <div :class="$style['dialog-footer']">
            <el-button @click="handleClose">取消</el-button>
            <el-button type="primary" @click="onOk">确定</el-button>
        </div>
    </el-dialog>
</template>
<script>
import { fetchSalesmanListByName, postBulkTransferCustomersToSalesman } from "@/api/salemanCenter/index";
export default {
    props: {
        purMerchantIds: {
            type: Array,
            default: () => []
        },
        // 旧销售员id
        salesmanId: {
            type: String,
            default: ''
        },
        // 销售商id
        salesMerchantId: {
            type: String,
            default: ''
        },
        visible: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            model: {
                id: ''
            },
            options: [],
            loading: false,
            rules: {
                id: {
                    required: true,
                    message: '请选择业务员'
                }
            }
        }
    },
    created() {
        this.fetch()
    },
    methods: {
        handleOpen() {
            this.$emit('update:visible', true)
            this.$nextTick(() => {
                this.$refs.form.clearValidate()
            })
        },
        handleClose() {
            this.$emit('update:visible', false)
        },

        async onOk() {
            let valid = await this.$refs.form.validate()
            if (!valid) return;


            const loading = this.$loading({
                lock: true,
                text: '转移中...',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            });
            try {
                let purMerchantIds = this.purMerchantIds.map(item => item.id)
                const params = {
                    purMerchantIds,
                    newSalesmanId: this.model.id,
                    oldSalesmanId: this.salesmanId,
                    salesMerchantId: this.salesMerchantId
                }
                const { code, msg } = await postBulkTransferCustomersToSalesman(params)
                if (code !== 0) {
                    throw new Error({ msaage: msg })
                }
                loading.close();
                this.$message.success('转移成功');
                this.$emit('update:visible', false)
                this.$emit('ok', this.model.id)
                this.model = { id: '' }
            } catch (error) {
                console.log('error', error)
                loading.close();
            }
        },

        /**
         * 获取平台业务员列表
         */
        async fetch(name) {
            try {
                this.loading = true;
                const { data, code } = await fetchSalesmanListByName({ name, salesMerchantId: this.salesMerchantId });
                if (code === 0) {
                    // `${item.name}(${item.mobile})`
                    this.options = data?.map(item => ({ value: item.id, label: item.name }))
                }
            } catch (error) {
                console.error('fetch saleman', error);
            } finally {
                this.loading = false;
            }
        }
    }
}
</script>
<style lang="scss" module>
.w-full {
    width: 100%;
}

.dialog-footer {
    text-align: right;
}
</style>