<template>
  <div class="archivesPageContent">
    <im-search-pad
      :has-expand="false"
      :model="listQuery"
      @reset="resetForm"
      @search="onSearchSubmitFun"
    >
      <im-search-pad-item prop="feedbackType">
        <el-select v-model="listQuery.model.feedbackType" placeholder="请选择反馈类型">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item prop="listTime">
        <el-date-picker
          @change="timeSelect"
          v-model="listTime"
          type="daterange"
          align="right"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          :picker-options="pickerOptions"
        />
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        v-model="listQuery.model.handleStatus"
        :tabs="approvalStatusList"
        @change="chageTabsFun"
      >
        <template slot="button">
          <div></div>
        </template>
      </tabs-layout>
      <div class="table">
        <el-table v-if="list" v-loading="listLoading" :data="list" row-key="id" border fit highlight-current-row style="width: 100%">
          <el-table-column align="center" width="80" :render-header="renderHeader" fixed>
            <template slot-scope="scope">
              {{scope.$index+1}}
            </template>
          </el-table-column>
          <el-table-column v-for="(item, index) in tableTitle" :key="index" :min-width="(item.width?item.width:'350px')" :label="item.label" show-overflow-tooltip align="left">
            <template slot-scope="{row}">
              <span v-if="item.name=='feedbackType'">{{ row[item.name] ? row[item.name].desc : '' }}</span>
              <span v-else>{{ row[item.name] }}</span>
            </template>
          </el-table-column>

          <el-table-column fixed="right" align="center" label="操作" width="120" class="itemAction">
            <template slot-scope="scope">
              <el-row class="table-edit-row">
                <span v-if="listQuery.model.handleStatus == 'N' && checkPermission(['admin', 'admin-message:processing'])" class="table-edit-row-item">
                  <el-button @click="dispose(scope.row)" type="text" >处理</el-button>
                </span>
                <span v-if="checkPermission(['admin', 'admin-message:del'])" class="table-edit-row-item">
                  <el-button @click="del(scope.row)" type="text" >删除</el-button>
                </span>
              </el-row>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="listQuery.current" :limit.sync="listQuery.size" @pagination="getlist" />
      </div>
    </div>
    <el-dialog v-if="showEdit" :title="row.title" :visible.sync="showEdit" width="700px">
      <edit :visible.sync="showEdit" :isReload.sync="submitReload" :row.sync="row"></edit>
    </el-dialog>
    <!-- 处理弹窗 -->
    <el-dialog
      title="需求处理"
      :visible.sync="dealVisible"
      width="30%"
      >
      <el-input type="textarea" :rows="3" placeholder="请输入需求处理备注" v-model="dealText"></el-input>
      <span slot="footer" class="dialog-footer">
        <!-- <el-button @click="dialogVisible = false">取 消</el-button> -->
        <el-button type="primary" @click="handleDealBut">确 定</el-button>
      </span>
</el-dialog>
  </div>
</template>

<script>
import checkPermission from '@/utils/permission'
import tableInfo from "@/views/callCenter/messageComplaints/tableInfo";
import Pagination from "@/components/Pagination";
import { list, dispose, del } from "@/api/callCenter/messageComplaints";
import TabsLayout from '@/components/TabsLayout'
export default {
  data() {
    return {
      listTime: [],
      listQuery: {
        current: 1,
        size: 10,
        model: {
          handleStatus: "N",
        },
        map: {},
      },
      dealVisible:false,
      currentObj:{},
      dealText:'',
      options: [
        {
          label: "商品问题",
          value: "PRODUCT_PROBLEM",
        },
        {
          label: "物流运输",
          value: "LOGISTICS",
        },
        {
          label: "退款/退货",
          value: "REFUND",
        },
        {
          label: "过期",
          value: "COMPLAINT",
        },
        {
          label: "客服",
          value: "CUSTOMER_SERVICE",
        },
        {
          label: "其它",
          value: "OTHER",
        },
      ],
      showEdit: false,
      list: [],
      listLoading: false,
      tableTitle:[],
      total: 0,
      pickerOptions: {
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      tableSelectTitle: [0, 1, 2, 3],
      showSelectTitle: false,
    };
  },
  computed: {
    approvalStatusList() {
      return [
        {
          name: '未处理',
          value: 'N',
          hide: !checkPermission(['admin', 'admin-message:Unhandled'])
        },
        {
          name: '已处理',
          value: 'Y',
          hide: !checkPermission(['admin', 'admin-message:handled'])
        }
      ]
    }
  },
  methods: {
    checkPermission,
    timeSelect(e) {
      this.listQuery.map.createTime_st = e[0];
      this.listQuery.map.createTime_ed = e[1];
    },
    // 获取列表
    async getlist() {
      this.listLoading = true;
      let { data } = await list(this.listQuery);
      this.list = data.records;
      this.total = data.total;
      this.listLoading = false;
    },
    // 处理
    dispose(row) {
      this.dealText = '';
      this.dealVisible = true;
      this.currentObj = row;
    },
    // 确定按钮
    async handleDealBut(){
       let parmas = {
        id: this.currentObj.id,
        remark: this.dealText,
      };
      let data = await dispose(parmas);
      this.list = [];
      this.getlist();
      if(data.code == 0 && data.msg == 'ok'){
        this.dealVisible = false;
        this.$message.success("处理成功！");
      }
      this.dealText = '';
    },
    // 删除
    del(row) {
        this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then( async() => {
      let { data } = await del({ ids: [row.id] });
      if (data) {
        this.getlist();
        this.$message.success("已成功删除该留言信息！");
      }
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });

    },
    onSearchSubmitFun() {
      this.getlist();
    },
    chageTabsFun() {
      this.list = [];
      this.listQuery.current = 1;
      this.getlist();
      this.initTbaleTitle()
    },
    resetForm() {
      this.listQuery = {
        current: 1,
        size: 10,
        model: {
          handleStatus: this.listQuery.model.handleStatus,
        },
        map: {},
      };
      this.listTime = [];
      this.getlist();
    },
    initTbaleTitle() {
      this.tableTitle = tableInfo[this.listQuery.model.handleStatus];
      this.tableSelectTitle = []
    },
    renderHeader(h, { column }) {
      var titles = tableInfo[this.listQuery.model.handleStatus];
      var titlesName = ["显示字段项", "隐藏字段项"];
      return (
        <div style="position:relative">
          <div onClick={this.showHeaer}>
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="640px"
            center
            append-to-body={true}
          >
            <el-transfer
              vModel={this.tableSelectTitle}
              data={titles}
              titles={titlesName}
              onChange={this.setleftTitleFun}
            ></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button onClick={this.closeHeaer}>取消</el-button>
              <el-button type="primary" onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      );
    },
    setleftTitleFun(val) {
      this.tableSelectTitle = val;
    },
    showHeaer: function () {
      this.showSelectTitle = true;
    },
    closeHeaer: function () {
      this.showSelectTitle = false;
      this.tableSelectTitle = [];
    },
    setHeaer: function () {
      var titles = tableInfo[this.listQuery.model.handleStatus];
      var listinfo = titles.filter((element, index, self) => {
        return !this.tableSelectTitle.includes(element.key);
      });
      this.tableTitle = listinfo;
      this.showSelectTitle = !this.showSelectTitle;
    },
  },
  created() {
    this.getlist();
    this.initTbaleTitle()
  },
  components: {
    Pagination,
    TabsLayout
  },
};
</script>


<style lang="scss" scoped>
.archivesPageContent {
  padding: 0;
  .temp_searchBox {
    height: 64px;
    overflow: hidden;
    margin-bottom: 0;
  }
  .form-inline {
    height: 60px;
    overflow: hidden;
  }
  .title {
    border-bottom: 2px solid #ebecee;
    margin-bottom: 16px;
    span {
      margin-bottom: -2px;
      padding: 0 15px;
      height: 40px;
      line-height: 30px;
      display: block;
      background: rgba(255, 255, 255, 0);
      border-bottom: 2px solid rgb(64, 158, 255);
      font-size: 16px;
      font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
        "PingFang SC"-400;
      font-weight: 400;
      color: rgb(64, 158, 255);
    }
  }
  .formItem {
    width: 586px;
  }
  .line {
    color: #dfe6ec;
    margin: 0 6px;
  }
  .typeTabs {
    height: 40px;
    margin-bottom: -2px;
    margin-left: 10px;
  }
}
</style>
