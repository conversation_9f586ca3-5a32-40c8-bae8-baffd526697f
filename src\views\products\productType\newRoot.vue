<template>
  <el-dialog :close-on-click-modal="false" :before-close="clearFun" :show-close="true" :title="(row.id>0?'编辑':'新增')+'分类'" :visible.sync="visible" width="35%">
    <div class="newRootContent">
      <!-- <div style="position:absolute;top:10px; right:15px; background:#fff; height:38px;" >
        <div>
          <el-button @click="clearFun()">取 消</el-button>
          <el-button type="primary" @click="submitFun('ruleForm')"
            >确 定</el-button
          >
        </div>
      </div> -->
      <el-form ref="ruleForm" class="form" :model="query" label-width="146px">
        <input v-model="query.path" type="hidden">
        <input v-model="query.parentId" type="hidden">
        <input v-model="query.parentCategoryCode" type="hidden">
        <el-form-item
          v-if="query.level>0"
          class="formItem"
          prop="pName"
          label="父级分类:"
        >
          <el-input v-model="query.pName" style="width:250px" placeholder="请填写父级分类" :disabled="true" />
        </el-form-item>
        <el-form-item
          class="formItem"
          label="产品分类编码:"
        >
          <el-input
            v-model="query.categoryCode"
            :disabled="true"
            style="width:250px"
            placeholder="分类编码由系统生成"
          />
        </el-form-item>

        <el-form-item
          class="formItem"
          prop="label"
          label="分类名称:"
          :rules="[{ required: true, message: '请填写分类名称',trigger: ['blur','change'] },
                   { max:10, message: '分类编码最多10位',trigger: ['blur','change'] }]"
        >
          <el-input
            v-model="query.label"
            clearable
            style="width:250px"
            placeholder="请填写分类名称"
          />
        </el-form-item>
        <el-form-item
          class="formItem"
          prop="sortValue"
          label="分类排序:"
          :rules="[{  validator: validateNumber,required: true, trigger: 'blur' }]"
        >
          <el-input-number
            v-model="query.sortValue"
            clearable
            style="width:250px"
            placeholder="请填写分类排序"
          />
        </el-form-item>

        <el-form-item
          class="formItem"
          label="前端是否显示:"
          prop="whetherShowFrontend"
          :rules="[{ required: true, message: '请选择前端是否显示' }]"
        >
          <el-radio v-model="query.whetherShowFrontend" label="Y">前端显示</el-radio>
          <el-radio v-model="query.whetherShowFrontend" label="N">隐藏</el-radio>
        </el-form-item>
        <el-form-item
          v-if="query.level!=0"
          class="formItem"
          label="封面:"
          prop="pictIdS"
        >
          <el-upload
            class="avatar-uploader"
            :action="$uploadUrl"
            :data="insertProgram"
            :headers="headersProgram"
            :show-file-list="false"
            :on-success="uploadSuccess"
            :before-upload="beforeUpload"
            accept=".jpg,.png,.bmp,.jpeg"
          >
            <el-image
              v-if="query.pictIdS!=''"
              style="width: 120px; height: 120px"
              :src="query.pictIdS"
              class="avatar"
              fit="contain"
            />
            <i v-else class="el-icon-plus avatar-uploader-icon" />
          </el-upload>
        </el-form-item>

      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="clearFun()">取 消</el-button>
      <el-button
        v-if="checkPermission(['admin','productType:add','productType:edit'])"
        type="primary"
        @click="submitFun('ruleForm')"
      >确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import checkPermission from '@/utils/permission'
import { uploadFile } from '@/api/file'
import { getToken } from '@/utils/auth'
import { addChild, editApi } from '@/api/products/categoryPlatform'
export default {
  props: {
    row: {
      type: Object
    },
    visible: {
      type: Boolean,
      default: false,
      required: true
    },
    isReload: {
      type: Boolean,
      default: false,
      required: true
    },
    actionItem: {
      required: true
    }
  },
  data() {
    return {
      headersProgram: {
        token: getToken(),
        Authorization: 'Basic YWRtaW5fdWk6YWRtaW5fdWlfc2VjcmV0'
      },
      insertProgram: {
        folderId: 0
      },
      query: this.initQuery()
    }
  },
  mounted() {
    console.info(this.row)
    this.query = Object.assign(this.query, this.row)
    if (this.query.whetherShowFrontend == null) {
      this.query.whetherShowFrontend = 'Y'
    } else if (typeof this.query.whetherShowFrontend === 'object') {
      this.query.whetherShowFrontend = this.query.whetherShowFrontend.code
    }

    if (this.query.id > 0) {
      this.query.pName = this.query.pName.substring(0, this.query.pName.lastIndexOf('\/'))
    }
  },
  beforeDestroy() {},
  methods: {
    checkPermission,
    validateNumber(rule, value, cb) {
        // 输入正数，保留小数点后两位
        if (!value) {
          return cb()
        }
        if (!this.isNumber2(value)) {
          return cb(new Error('只能输入正数'))
        }
        cb()
    },
    isNumber2 (value) {
      let v = parseFloat(value);
      if (isNaN(v)) {
        return false;
      }
      value = v;
      if (value <= 0) {
        return false;
      }
      let arr = (value + '').split('.');
      if (arr[1] && arr[1].length > 2) {
        return false;
      }
      return true;
    },
    initQuery: function() {
      return {
        level: 0,
        'id': 0,
        'label': '',
        'parentId': 0,
        'pictIdS': '',
        'sortValue': 0,
        'whetherShowFrontend': 'Y'
      }
    },
    clearFun: function() {
      this.$emit('update:visible', false)
      this.$emit('update:row', {})
    },
    async submitFun(ruleForm) {
      var _this = this
      _this.$refs[ruleForm].validate(async(valid) => {
        if (valid) {
          let data = null
          // if(_this.query.id>0){
          //    data= await addChild(_this.query);
          // }else{
          data = await editApi({
            'id': _this.query.id,
            'label': _this.query.label,
            'parentId': _this.query.parentId,
            'pictIdS': _this.query.pictIdS,
            'sortValue': _this.query.sortValue,
            'whetherShowFrontend': _this.query.whetherShowFrontend
          })
          // }
          if (data.code == 0) {
            _this.$emit('update:actionItem', _this.query)
            _this.$emit('update:visible', false)
            _this.$emit('update:isReload', true)
          }
        } else {
          return false
        }
      })
    },
    uploadSuccess(res, file) {
      this.query.pictIdS = res.data.url
    },
    beforeUpload(file) {
      const fileTypeList = ['image/png', 'image/pjpeg', 'image/jpeg', 'image/bmp']
      const isJPG = fileTypeList.indexOf(file.type) > -1
      const isLt2M = file.size / 1024 / 1024 < 5

      if (!isJPG) {
        this.$message.error('上传图片格式错误!')
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
      }
      return isJPG && isLt2M
    }

  }
}
</script>
<style lang="less" scoped>
.newRootContent {
  margin: -30px -20px;
  border-top: 1px solid #ebecee;
  padding: 30px 20px;
  .avatar-uploader {
    width: 120px;
    height: 120px;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 120px;
    height: 120px;
    line-height: 120px;
    text-align: center;
  }
  .avatar {
    width:120px;
    height: 120px;
    display: block;
  }
}
</style>
