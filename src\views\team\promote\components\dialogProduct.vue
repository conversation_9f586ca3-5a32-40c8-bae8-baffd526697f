<template>
  <div>
    <el-dialog title="选择商品" :visible.sync="dialogProduct" v-if="dialogProduct" width="70%" :before-close="closeDia" :close-on-click-modal="false">
      <!-- 搜索模块 -->
      <div>
        <im-search-pad :has-expand="false" :model="model" @reset="reset" @search="searchLoad">
          <im-search-pad-item prop="saleMerchantName">
            <el-input v-model="model.saleMerchantName" placeholder="请输入商家名称" />
          </im-search-pad-item>
          <im-search-pad-item prop="erpCode">
            <el-input v-model="model.erpCode" placeholder="请输入ERP商品编码" />
          </im-search-pad-item>
          <im-search-pad-item prop="productName">
            <el-input v-model="model.productName" placeholder="请输入商品名称" />
          </im-search-pad-item>
          <im-search-pad-item prop="manufacturer">
            <el-input v-model="model.manufacturer" placeholder="请输入生产厂家" />
          </im-search-pad-item>
        </im-search-pad>
        <div v-loading="loading">
          <table-pager :rowKey="rowKey" :reserveSelection="true" ref="multipleTable" :height="600" :options="tableColumns"
            :remote-method="load" :data.sync="list" :selection="true" @selection-change="handleSelectionChange"
            @selection-all="handleSelectAll" :isNeedButton="false" :pageSize="pageSize">
          </table-pager>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogProduct = false">取 消</el-button>
        <el-button type="primary" @click="submitFrom">确 定{{ selectList.length > 0 ? `(${selectList.length})` : '' }}
        </el-button>
      </span>
    </el-dialog>

  </div>
</template>


<script>
  import {
    bindPopularize
  } from '@/api/organization/index';
  import { list } from '@/api/products/product'
  import {
    mapGetters
  } from 'vuex'
  const TableColumns = [
    {
      label: "商家名称",
      name: "saleMerchantName",
      prop: "saleMerchantName",
      width: "150"
    },
    {
      label: "ERP商品编码",
      name: "erpCode",
      prop: "erpCode",
      width: "130"
    },
    {
      label: "商品名称",
      name: "productName",
      prop: "productName",
      width: "180"
    },
    {
      label: "规格",
      name: "spec",
      prop: "spec",
      width: "80"
    },
    {
      label: "单位",
      name: "unit",
      prop: "unit",
      width: "80"
    },
    {
      label: "生产厂家",
      name: "manufacturer",
      prop: "manufacturer",
      width: "180"
    },
    {
      label: "销售价",
      name: "salePrice",
      prop: "salePrice",
      width: "120"
    },
    {
      label: "成本价",
      name: "stockQuantity",
      prop: "stockQuantity",
      width: "120"
    }
  ];
  const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({
      key: i,
      ...TableColumns[i]
    });
  }
  export default {
    //import引入的组件
    components: {},

    data() {
      return {
        // 获取row的key值
        rowKey: "id",
        dialogProduct: false,
        loading: false,
        page: 1,
        pageSize: 10,
        total: 0,
        list: [],
        selectList: [],
        tableColumns: TableColumnList,
        model: {
          saleMerchantName: '',
          productName: '',
          erpCode: '',
          manufacturer: ''
        },
      }
    },
    //生命周期 - 挂载完成（可以访问DOM元素）
    mounted() {},

    computed: {
      ...mapGetters([
        'organizationInfo'
      ]),
    },

    created() {},

    filters: {},

    //方法集合
    methods: {
      async load(query) {
        this.loading = true;
        // let params = {
        //   current: this.page,
        //   map: {},
        //   model: this.model,
        //   order: 'descending',
        //   size: this.pageSize,
        //   sort: 'id'
        // };
        let listQuery = {
          model:{
            ...this.model,
            whetherOnSale:"Y"
          }
        };
        Object.assign(listQuery, query);
        let result = await list(listQuery);
        this.loading = false;
        if (result.code != 0 && result.msg != 'ok') {
          return
        };
        this.total = result.data.total;
        this.list = result.data.records;
        return result;
      },
      handleSelectionChange(val) {
        console.log('handleSelectionChange', val);
        this.selectList = val;
      },
      handleSelectAll(val) {
        console.log('handleSelectAll', val);
        this.selectList = val;
      },
      openDia() {
        this.dialogProduct = true;
        // this.load();
        this.model = {
          saleMerchantName: '',
          productName: '',
          erpCode: '',
          manufacturer: ''
        };
      },
      closeDia() {
        this.dialogProduct = false;
        this.$refs.multipleTable.clearSelection();
        this.selectList = [];
      },
      
      searchLoad() {
        this.handleRefresh({
          page: 1,
          pageSize: this.pageSize
        })
      },
      reset() {
        this.model = {
          saleMerchantName: '',
          productName: '',
          erpCode: '',
          manufacturer: ''
        };
        this.handleRefresh({
          page: 1,
          pageSize: this.pageSize
        })
      },
      handleRefresh(pageParams) {
        this.$refs['multipleTable'].doRefresh(pageParams)
      },
      submitFrom() {
        let productIds = [];
        if (this.selectList.length < 1) {
          this.$message.warning('请先选择商品');
        }
        this.selectList.forEach(element => {
          productIds.push(element.id);
        })
        let params = {
          organizationId: this.organizationInfo.id,
          productIds
        };
        bindPopularize(params).then(res => {
          if (res.code == 0 && res.msg == 'ok') {
            this.$message.success('绑定商品成功');
            this.$refs.multipleTable.clearSelection();
            this.selectList = [];
            this.$emit('updataChange');
            this.closeDia();
          }
        })
      },
    },

  }

</script>


<style lang='scss' scoped>

</style>
