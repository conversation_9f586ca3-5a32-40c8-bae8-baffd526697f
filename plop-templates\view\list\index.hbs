{{#if template}}
<template>
  <div class="{{name}}-index">
    <!--搜索Form-->
    <im-search-pad
      :is-expand.sync="isExpand"
      :model="model"
      @reset="reload"
      @search="searchLoad"
    >
      <im-search-pad-item prop="prop">
        <el-input v-model="model.prop" placeholder="请输入prop" />
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <!--Tabs布局-->
      <tabs-layout
        ref="tabs-layout"
        :tabs="tabs"
        v-model="model.prop"
        @change="handleChangeTab"
      >
        <!--tabs右上角相关按钮-->
        <template slot="button">
          <el-button size="small" @click="reload">刷新</el-button>
        </template>
      </tabs-layout>
      <!--分页table-->
      <table-pager ref="pager-table" :options="tableColumns" :remote-method="load" :data.sync="tableData">
        <!--插入自定义column-->
        <el-table-column label="column-prop-name" slot="column-prop">
          <slot slot-scope="{row}"></slot>
        </el-table-column>
        <!--操作栏-->
        <div slot-scope="props">
          <el-row class="table-edit-row">
            <span class="table-edit-row-item">
              <el-button type="text">操作相关按钮</el-button>
            </span>
          </el-row>
        </div>
      </table-pager>
    </div>
  </div>
</template>
{{/if}}

{{#if script}}
<script>
const TableColumns = [
  { label: "label", name: "prop", prop: 'prop', slot: true }
]
const TableColumnList = [];
for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({ key: i, ...TableColumns[i] });
}
import { listApi } from '@/api/xxx' // TODO 替换成对应用的列表api

export default {
  name: '{{ properCase name }}Index',
  components: {
  },
  props: {
  },
  data() {
    return {
      isExpand:false,
      search: '',
      tableData: [],
      pageSize: 10,
      tableColumns: TableColumnList,
      model: {
        prop: ''
      }
    }
  },
  computed: {
    tabs () {
      return [
        { name: 'Tab', value: 'TAB_VALUE' }
      ]
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    async load(params) {
      let listQuery = {
        model: {
          prop: this.model.prop
        }
      }
      Object.assign(listQuery, params)
      // TODO 替换成对应用的列表api
      return await listApi(listQuery)
    },
    reload() {
      this.$refs['tabs-layout'].reset()
      this.model = {
        prop: ''
      }
      this.handleRefresh({
        page: 1,
        pageSize: this.pageSize
      })
    },
    handleChangeTab (tab) {
      this.model.prop = tab.value
      this.handleRefresh({
        page: 1,
        pageSize: this.pageSize
      })
    },
    searchLoad() {
      this.handleRefresh({
        page: 1,
        pageSize: this.pageSize
      })
    },
    handleRefresh(pageParams) {
      this.$refs['pager-table'].doRefresh(pageParams)
    }
  }
}
</script>
{{/if}}

{{#if style}}
<style lang="scss" scoped>
  .{{name}}-index {
  }
</style>
{{/if}}
