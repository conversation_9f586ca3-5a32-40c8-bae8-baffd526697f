<!-- 地区树 -->
<template>
  <el-dialog
    v-loading="loading"
    title="选择区域"
    :visible.sync="visible"
    width="30%"
    :before-close="handleClose">
    <el-tree
      ref="areaTree"
      :data="treeList"
      show-checkbox
      @check="handleCheck"
      node-key="id">
    </el-tree>
    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import request from '@/utils/request'
  export default {
    props: {
      title: {
        type: String,
        default: '选择区域'
      },
    },
    data () {
      return {
        loading: false,
        visible: false,
        treeList: [], // 地区树数据
        currentTree: '', //当前选中的树
      }
    },
    mounted () {
      this.load()
    },
    methods: {
      show () {
        this.visible = true;
      },
      async load () {
        this.loading = true
        const { data } = await request.get(`/authority/area/anno/tree`)
        this.loading = false
        this.treeList = data || []
      },
      handleClose(done) {
        done()
      },
      handleCheck (node, tree) {
        if (this.currentTree === '') {
          this.currentTree = node.id.slice(0, 2)
        } else {
          if (node.id.slice(0,2) !== this.currentTree) {
            this.currentTree = node.id.slice(0, 2)
            this.$refs.areaTree.setCheckedKeys([])
            this.$refs.areaTree.setCheckedKeys([node.id])
          } else {
            this.$refs.areaTree.setCurrentKey(node.id)
          }
        }
      },
      async handleConfirm () {
        this.loading = true
        // 根据地区树选择返回解析结果
        let checkedKeys = this.$refs['areaTree'].getCheckedKeys();
        let checkedKeysTrue = this.$refs['areaTree'].getCheckedKeys(true);
        let halfCheckedKeys = this.$refs['areaTree'].getHalfCheckedKeys();
        request.post(`/authority/area/anno/analysisByCheck`, {
          checkedKeys,
          checkedKeysTrue,
          halfCheckedKeys,
        }).then(res => {
          this.$emit('on-confirm', res.data);
          this.visible = false
        }).finally(() => {
          this.loading = false;
        });
      }
    }
  }
</script>
