
import requestAxios from '@/utils/requestAxios'

// 获取列表数据
export function list(data) {
  return requestAxios({
    url: "/api/finance/admin/financeCashOut/page",
    method: 'post',
    data
  })
}

// 数据统计
export function statistics(data) {
  return requestAxios({
    url: "/api/finance/admin/financeCashOut/countStatus",
    method: 'post',
    data
  })
}
//审核
export function updateCashRemarks(data) {
  return requestAxios({
    url: "/api/finance/admin/financeCashOut/updateCashRemarks",
    method: 'post',
    params:data
  })
}
