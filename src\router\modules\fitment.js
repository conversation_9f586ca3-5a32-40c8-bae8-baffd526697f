import Layout from '@/layout'

const fitmentCentric = {
    path: '/fitment',
    name: 'fitment',
    redirect: 'fitment/wechat/index',
    meta: {
        title: '商城装修',
        icon: 'component'
    },
    alwaysShow: true,
    component: Layout,
    children: [
        {
            path: '/fitment/wechat/index',
            name: 'wechatfitment',
            meta: { title: '小程序装修' },
            component: () => import('@/views/fitment/wechat/index')
        },
        // {
        //     path: '/fitment/wechatStore/index',
        //     name: 'wechatStorefitment',
        //     meta: { title: '小程序商家首页装修' },
        //     component: () => import('@/views/fitment/wechatStore/index')
        // }
    ]
}

export default fitmentCentric