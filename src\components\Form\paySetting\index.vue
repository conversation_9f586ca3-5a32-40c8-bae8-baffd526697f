<template>
  <div class="tempPaySettingForm">
    <el-form ref="form" :model="form" label-width="100px">
      <div>
        <div class="title">渠道信息</div>
        <el-form-item class="formItem" label="支付方式:">
          <el-input :disabled="true" v-model="form.payName"></el-input>
        </el-form-item>
        <el-form-item class="formItem" label="描述:" required>
          <el-input v-model="form.detail" type="textarea"></el-input>
        </el-form-item>
        <el-form-item class="formItem" label="状态:">
          <el-switch
            v-model="form.start"
            active-text="启动"
            inactive-text="停用"
          ></el-switch>
        </el-form-item>
      </div>
      <div>
        <div class="title">公众号信息</div>
        <el-form-item class="formItem" label="APPID:">
          <el-input v-model="form.payName"></el-input>
        </el-form-item>
        <el-form-item class="formItem" label="AppSecret:">
          <el-input v-model="form.payName"></el-input>
        </el-form-item>
      </div>
      <div>
        <div class="title">商户参数</div>
        <el-form-item class="formItem" label="商户号:">
          <el-input v-model="form.merchantNumber"></el-input>
        </el-form-item>
        <el-form-item class="formItem" label="商户秘钥:">
          <el-input v-model="form.merchantKey"></el-input>
        </el-form-item>
        <el-button type="primary" @click="onSubmit">立即创建</el-button>
        <el-button>取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default {
  data() {
    return {
      form: {
        payName: "银联支付",
        detail: "银联支付",
        start: true,
        APPID: "",
        AppSecret: "",
        merchantNumber: "",
        merchantKey: ""
      }
    };
  },
  props: {
    cardId: {
      required: true,
      type: Number,
      default: 0
    }
  },
  methods: {
    onSubmit: function() {}
  },
  
  mounted() {},
  beforeDestroy() {}
};
</script>
<style lang="scss" scoped>
@import "@/styles/element-variables.scss";
.tempPaySettingForm {
  .title {
    color: #333333;
    font-family: "Arial-BoldMT", "Arial Bold", "Arial";
    font-weight: 700;
    font-style: normal;
    font-size: 15px;
    text-indent: 4px;
    margin-bottom: 20px;
    border-left: 3px solid $--color-primary;
  }
  .formItem{margin-bottom: 16px;}
}
</style>
