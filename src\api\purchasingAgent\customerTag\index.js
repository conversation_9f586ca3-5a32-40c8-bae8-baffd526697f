import request from '@/utils/request'

// 客户标签库分页列表查询
export function pageCustTag(data){
    return request({
        url: '/merchant/admin/customerTag/page',
        method: 'post',
        data
    })
}

// 客户标签库详情
export function detailCustTag(id) {
    return request({
        url: `/merchant/admin/customerTag/${id}`,
        method: 'get',
    })
}

// 客户标签删除
export function delCustTag(id){
    return request({
        url: `/merchant/admin/customerTag?ids[]=${id}`,
        method: 'delete'
    })
}

// 客户标签新增
export function addCustTag(data){
    return request({
        url:'/merchant/admin/customerTag',
        method: 'post',
        data
    })
}

// 客户标签修改
export function editCustTag(data){
    return request({
        url:'/merchant/admin/customerTag',
        method: 'put',
        data
    })
}