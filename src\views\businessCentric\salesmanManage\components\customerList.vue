<template>
  <div v-loading="loading">
    <tabs-layout ref="tabs-layout" :tabs="tabs" v-model="tabCode" @change="handleChangeTab"></tabs-layout>
    <!--搜索Form-->
    <im-search-pad :has-expand="false" :is-expand.sync="isExpand" :model="model" @reset="reload" @search="searchLoad">
      <im-search-pad-item prop="erpCode">
        <el-input v-model="model.erpCode" @keyup.enter.native="searchLoad" placeholder="请输入ERP客户编码" />
      </im-search-pad-item>
      <im-search-pad-item prop="name">
        <el-input v-model="model.name" @keyup.enter.native="searchLoad" placeholder="请输入客户名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="ceoName">
        <el-input v-model="model.ceoName" @keyup.enter.native="searchLoad" placeholder="请输入负责人" />
      </im-search-pad-item>
      <im-search-pad-item prop="ceoMobile">
        <el-input v-model="model.ceoMobile" @keyup.enter.native="searchLoad" placeholder="请输入负责人联系电话" />
      </im-search-pad-item>
      <template slot="botton" v-if="tabCode === 'ALREADY'">
        <el-button @click="onBatchTransfer" :disabled="!multipleSelection.length">批量转移</el-button>
      </template>
    </im-search-pad>
    <div class="tab_bg">
      <el-table ref="multipleTable" border :data="tableData" tooltip-effect="dark" style="width: 100%"
        @selection-change="handleSelectionChange" max-height="400">
        <el-table-column type="selection" width="55" fixed align="center"></el-table-column>
        <el-table-column prop="erpCode" label="ERP客户编码" width="150"></el-table-column>
        <el-table-column prop="name" label="客户名称" show-overflow-tooltip width="200"></el-table-column>
        <el-table-column prop="socialCreditCode" show-overflow-tooltip label="社会统一信用代码" width="150"></el-table-column>
        <el-table-column prop="merchantTypeName" label="企业类型" width="150"></el-table-column>
        <el-table-column prop="ceoName" label="负责人" width="120"></el-table-column>
        <el-table-column prop="ceoMobile" label="负责人电话" width="130"></el-table-column>
        <el-table-column prop="region" label="所在地区" width="180">
          <template slot-scope="{row}">
            <div>{{ row.provinceName }} - {{ row.cityName }} - {{ row.countyName }}</div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template slot-scope="{row}">
            <el-button v-if="tabCode == 'ALREADY'" type="text" @click="handleUnbind(row.id)">解绑</el-button>
            <el-button v-else type="text" @click="handleBind(row.id)">绑定</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="page-row">
        <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size.sync="limit"
          layout="total, sizes, prev, pager, next, jumper" :total="totalCount">
        </el-pagination>
      </div>
    </div>
    <div class="bottom_btn">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="submit">{{ `${tabCode === 'ALREADY' ? '解绑' : '绑定'}` }}</el-button>
    </div>

    <select-takeover-salesman :pur-merchant-ids="multipleSelection" :sales-merchant-id="saleMerchantId" :salesman-id="salesmanId" @ok="handleSelectTakeover"
      :visible.sync="visible"></select-takeover-salesman>
  </div> 
</template>


<script>

import SelectTakeoverSalesman from './BusinessComponents/SelectTakeoverSalesman.vue';

import {
  bondedPurMerchants,
  unboundPurMerchants,
  bindingPurMerchants,
  unbindPurMerchants,
  purMerchantsNumber,
  postBulkTransferCustomersToSalesman
} from '@/api/salemanCenter/index'
export default {
  //import引入的组件
  components: {},
  props: {
    saleMerchantId: {
      type: String,
      default: ''
    },
    salesmanId: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      // 接手业务员选择弹窗
      visible: false,
      loading: false,

      isExpand: false,
      model: {
        name: '',
        erpCode: '',
        ceoName: '',
        ceoMobile: ''
      },
      tabCode: 'ALREADY',
      page: 1,
      limit: 10,
      totalCount: 0,
      tableData: [],
      multipleSelection: [],
      tabNum: {
        bindNumber: 0,
        unboundNumber: 0
      }
    }
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.load();
    this.tabNumFn();
  },

  computed: {
    tabs() {
      return [{
        name: `已绑定客户(${this.tabNum.bindNumber})`,
        value: 'ALREADY',
      },
      {
        name: `未绑定客户(${this.tabNum.unboundNumber})`,
        value: 'NOT',
      }
      ]
    }
  },

  created() { },

  filters: {},

  //方法集合
  methods: {
    /**
     * 批量转移点击事件
     */
    onBatchTransfer() {
      this.visible = true
    },

    /**
     * 确认转移用户到业务员
     */
    handleSelectTakeover(newSalesmanId) {
      this.reload();
      this.tabNumFn();
      this.$emit('closeCustomer');
    },
    load() {
      let params = {
        current: this.page,
        map: {},
        model: {
          ...this.model,
          salesmanId: this.salesmanId,
          saleMerchantId: this.saleMerchantId
        },
        order: 'descending',
        size: this.limit,
        sort: 'id'
      };
      this.loading = true;
      if (this.tabCode == 'ALREADY') {
        // 已绑定客户
        bondedPurMerchants(params).then(res => {
          if (res.code == 0 && res.msg == 'ok') {
            console.log('已绑定客户', res);
            this.tableData = res.data.records || [];
            this.totalCount = res.data.total;
          }
        }).finally(() => {
          this.loading = false;
        })
      } else {
        unboundPurMerchants(params).then(res => {
          if (res.code == 0 && res.msg == 'ok') {
            console.log('未绑定客户', res);
            this.tableData = res.data.records || [];
            this.totalCount = res.data.total;
          }
        }).finally(() => {
          this.loading = false;
        })
      }
    },
    tabNumFn() {
      let params = {
        current: this.page,
        map: {},
        model: {
          ...this.model,
          salesmanId: this.salesmanId,
          saleMerchantId: this.saleMerchantId
        },
        order: 'descending',
        size: this.limit,
        sort: 'id'
      };
      purMerchantsNumber(params).then(res => {
        if (res.code == 0 && res.msg == 'ok') {
          this.tabNum = res.data;
        }
      })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleSizeChange(val) {
      this.limit = val;
      this.load();
      this.tabNumFn();
    },
    handleCurrentChange(val) {
      this.page = val;
      this.load();
      this.tabNumFn();
    },
    handleCancel() {
      this.$emit('closeCustomer');
    },
    submit() {
      console.log('this.multipleSelection', this.multipleSelection);
      if (this.multipleSelection.length == 0) {
        this.$emit('closeCustomer');
        return;
      }
      let list = this.multipleSelection.map(item => {
        return item.id;
      });
      if (this.tabCode == 'ALREADY') {
        // 批量解绑
        this.$confirm("您确定批量解绑这些客户吗?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: 'warning',
        }).then(res => {
          this.unbindCustomer(list)
        })
      } else {
        // 批量绑定
        this.$confirm("您确定批量绑定这些客户吗?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: 'warning',
        }).then(res => {
          this.bindCustomer(list)
        })
      }
    },
    // 绑定客户
    handleBind(id) {
      this.$confirm("您确定绑定此客户吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: 'warning',
      }).then(res => {
        this.bindCustomer([id]);
      })
    },
    // 绑定客户调用接口
    bindCustomer(list) {
      let params = {
        purMerchantIds: list,
        saleMerchantId: this.saleMerchantId,
        salesmanId: this.salesmanId
      };
      bindingPurMerchants(params).then(res => {
        if (res.code == 0 && res.msg == 'ok') {
          this.$message.success('绑定客户成功');
          this.reload();
          if (list.length > 1) {
            this.$emit('closeCustomer');
          }
        }
      })
    },
    // 解绑
    handleUnbind(id) {
      this.$confirm("您确定解绑此客户吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: 'warning',
      }).then(res => {
        this.unbindCustomer([id]);
      })
    },
    // 解绑客户调用接口
    unbindCustomer(list) {
      let params = {
        purMerchantIds: list,
        saleMerchantId: this.saleMerchantId,
        salesmanId: this.salesmanId
      };
      unbindPurMerchants(params).then(res => {
        if (res.code == 0 && res.msg == 'ok') {
          this.$message.success('解绑客户成功');
          this.reload();
          this.tabNumFn();
          if (list.length > 1) {
            this.$emit('closeCustomer');
          }
        }
      })
    },

    //   刷新
    reload() {
      this.model = {
        name: '',
        erpCode: '',
        ceoName: '',
        ceoMobile: ''
      }
      this.handleRefresh()

    },
    searchLoad() {
      this.handleRefresh()
      // this.tabNumFn();
    },
    handleRefresh(pageParams) {
      this.page = 1;
      this.load();
      this.tabNumFn();
    },
    // tab切换
    handleChangeTab(tab) {
      this.tabCode = tab.value
      this.handleRefresh()
    },
  },
  components: {
    SelectTakeoverSalesman
  }
}

</script>


<style lang='scss' scoped>
.page-row {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #505465;
  font-size: 13px;
  margin-top: 16px;
}

.bottom_btn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
