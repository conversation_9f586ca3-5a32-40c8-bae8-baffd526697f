<template>
  <div class="detail-wrapper">
    <p class="detail-title">
      客户档案详情
    </p>
    <div class="detail-items">
      <detail-item item-name="首营状态">
        <p v-if="data.firstCampStatus">首营状态：{{data.firstCampStatus.desc}}</p>
      </detail-item>
      <detail-item item-name="基础信息">
        <ul>
          <li>客户编码：{{data.code}}</li>
          <li>客户名称：{{data.name}}</li>
          <li>统一社会信用代码：{{data.socialCreditCode}}</li>
          <li>法人代表：{{data.legalPerson}}</li>
          <li>负责任人：{{data.ceoName}}</li>
          <li>负责人手机：{{data.ceoMobile}}</li>
          <li>质量负责人：{{data.qualityPersonInCharge}}</li>
          <li>所在区域：{{data.region}}</li>
          <li>注册地址：{{data.registerAddress}}</li>
          <li>注册资金：{{data.registerCapital}}</li>
          <li>客户标识码：{{data.identifyCode}}</li>
          <li>客户分组：{{data.merchantGroup}}</li>
        </ul>
      </detail-item>
      <detail-item item-name="基础信息">
        <ul>
          <li v-for="item in data.businessCategoryDetailList">{{item.categoryCode}}：{{item.parentName}}</li>
        </ul>
      </detail-item>
      <detail-item item-name="客商资质">
        <p>企业类型：{{data.merchantType}}</p>
        <el-table
          :data="merchantLicense"
          row-key="id"
          border
          fit
          highlight-current-row
          style="width: 100%;margin-bottom: 20px;"
        >

          <el-table-column
            v-for="(item, index) in tableTitle"
            :key="index"
            :width="item.width"
            :label="item.label"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              <span v-if="item.isImg === false">{{ row[item.name] }}</span>
              <span v-if="item.isImg === true"><img :src="row[item.name]" class="productImg"> </span>
            </template>
          </el-table-column>
          <el-table-column width="100" label="操作">
            <template slot-scope="{ row }">
              <el-button type="text" v-on:click="goDetail(row)">预览</el-button>
              <el-button type="text" v-on:click="download(row)">下载</el-button>
            </template>
          </el-table-column>
        </el-table>
      </detail-item>
      <detail-item item-name="收货地址">
        <el-table
        :data="data.deliveryAddressDetailDTOList"
        row-key="id"
        border
        fit
        highlight-current-row
        style="width: 100%;margin-bottom: 20px;"
      >

        <el-table-column
          v-for="(item, index) in tableAddressTitle"
          :key="index"
          :width="item.width"
          :label="item.label"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            <span>{{ row[item.name] }}</span>


          </template>
        </el-table-column>
      </el-table>
      </detail-item>
      <detail-item item-name="发票信息" style="border:0">
        <ul>
          <li>发票类型：{{data.invoiceInfo ? data.invoiceInfo.invoiceType.desc : '无'}}</li>
          <li>发票抬头：{{data.invoiceInfo ?  data.invoiceInfo.name : '无'}}</li>
          <li>税号：{{data.invoiceInfo ? data.invoiceInfo.taxNumber : '无'}}</li>
          <li>注册电话：{{data.invoiceInfo ? data.invoiceInfo.registerMobile: '无'}}</li>
          <li>银行账号：{{data.invoiceInfo ? data.invoiceInfo.bankNumber:'无'}}</li>
          <li>开户银行：{{data.invoiceInfo ? data.invoiceInfo.depositBank:'无'}}</li>
        </ul>
      </detail-item>
    </div>
    <el-dialog
      title="客户资质"
      :visible.sync="deliveryVisible"
      width="75%">
      <div style="width:100%;height: 500px;text-align: center">
        <img :src="filePath" style="height: 100%;"/>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import { getPurMerchantLicenseDetail,getListByLicenseBaseType } from '@/api/group'
  const TableColumns = [
    { label: "证件类型", name: "name",isImg: false },
    { label: "证件号", name: "licenseNumber",isImg: false },
    { label: "过期时间", name: "licenseEndTime",isImg: false },
    { label: "证件图片", name: "filePath",isImg: true }
  ];
  const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({ key: i, ...TableColumns[i] });
  }
  const TableAddressColumns = [
    { label: "收货人姓名", name: "name" },
    { label: "联系手机", name: "mobilPhone" },
    { label: "联系电话", name: "fixedPhone" },
    { label: "收货区域", name: "region" },
    { label: "详细地址", name: "detailedAddress" }
  ];
  const TableAddressColumnList = [];
  for (let i = 0; i < TableAddressColumns.length; i++) {
    TableAddressColumnList.push({ key: i, ...TableAddressColumns[i] });
  }

  import DetailItem from './detail-item'
  export default {
    components: {
      DetailItem
    },
    data() {
      return {
        tableTitle: TableColumnList,
        tableAddressTitle: TableAddressColumnList,
        purMerchantId: '',
        data: {},
        type: 'BUYER',
        merchantLicenses: [],
        merchantLicense: [],
        deliveryVisible: false,
        filePath: ''
      }
    },
    mounted() {
      this.getDetail()
    },
    methods: {
      goDetail(row) {
        this.deliveryVisible = true
        this.filePath = row.filePath
      },
      async getDetail() {
        let t
        const {data} = await getPurMerchantLicenseDetail(this.$route.params.id)
        this.data = data
        this.merchantLicenses = data.merchantLicenses
        this.getType()
      },
      async getType() {
        const {data} = await getListByLicenseBaseType(this.type)
        data.map((item,i) => {
          this.merchantLicenses.map((info,j) => {
            if(item.id === info.licenseBaseId) {
              this.merchantLicense.push({
                name: item.name,
                licenseNumber: info.licenseNumber,
                licenseEndTime: info.licenseEndTime,
                filePath: info.filePath
              })
            }
          })
        })
      },
      download(row) {
        const eleLink = document.createElement('a')
        eleLink.download = row.name
        eleLink.style.display = 'none'
        eleLink.href = row.filePath
        // 触发点击
        document.body.appendChild(eleLink)
        eleLink.click()
        // 然后移除
        document.body.removeChild(eleLink)
      }
    }
  }
</script>
