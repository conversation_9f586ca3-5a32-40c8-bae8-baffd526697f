<template>
  <el-dialog
    title="申请拒收"
    :visible.sync="deliveryVisible"
    width="75%"
    @close="handleClose('ruleForm')">
    <ul class="table-top"><li>订单编号：{{rejection.orderNo}}</li><li v-if="rejection.purMerchant">客户名称：{{rejection.purMerchant.name}}</li><li>订单总金额：<span v-text="rejection.totalMoney"></span></li><li>下单时间：{{rejection.orderCreateTime}}</li></ul>
    <el-alert
      :closable="false"
      type="warning"
      style="margin: 15px 0;">
      <p><span class="watch-icon">!</span>拒收功能仅作为售后维权的补充功能，请勿过度依赖和使用，请与客户确认是否已经拒收。</p>
    </el-alert>
    <el-table border :data="tableData" :span-method="spanMethod" :default-expand-all="true" class="expand-table">
      <el-table-column type="expand" align="left">
        <template>
          <el-table :data="tableData" :show-header="false">
            <el-table-column type="index"></el-table-column>
            <el-table-column prop="productImg" width="80">
              <template slot-scope="scope">
                <img :src="scope.row.productImg | imgFilter" class="productImg">
                <!-- <img :src="productImg" class="productImg" v-if="scope.row.productImg === ''"> -->
              </template>
            </el-table-column>
            <el-table-column prop="productCode" width="152"></el-table-column>
            <el-table-column prop="productName"></el-table-column>
            <el-table-column prop="spec" width="80"></el-table-column>
            <el-table-column prop="saleMerchantName"></el-table-column>
            <el-table-column prop="unitMoney" width="80"></el-table-column>
            <el-table-column prop="totalNum" width="80"></el-table-column>
            <el-table-column prop="subtotal" width="90"></el-table-column>
          </el-table>
        </template>
      </el-table-column>
      <el-table-column label="商品主图" width="80">
        <template v-if="rejection.orderDeliveryInfoDetailVo">
          <span class="scope-span">物流公司：{{rejection.orderDeliveryInfoDetailVo.logisName}}</span>
          <span class="scope-span">运单号：{{rejection.orderDeliveryInfoDetailVo.shippingNo}}</span>
          <span class="scope-span">状态：{{rejection.orderDeliveryInfoDetailVo.deliveryStatus.desc}}</span>
        </template>
      </el-table-column>
      <el-table-column label="商品编码" width="152"></el-table-column>
      <el-table-column label="商品名称"></el-table-column>
      <el-table-column label="规格" width="80"></el-table-column>
      <el-table-column label="生产厂家"></el-table-column>
      <el-table-column label="单价" width="80"></el-table-column>
      <el-table-column label="购买数量" width="80"></el-table-column>
      <el-table-column label="小计" width="90"></el-table-column>
    </el-table>
    <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm" style="margin-top: 20px;">
      <el-form-item label="拒收原因：" prop="rejectionReason">
        <el-input v-model="ruleForm.rejectionReason" type="textarea" style="width: 600px;" placeholder="请输入拒收原因" rows="4"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose('ruleForm')">取 消</el-button>
      <el-button type="primary" @click="onsubmit('ruleForm')" v-if="checkPermission(['admin','orderDetail:reject'])">申请拒收</el-button>
      </span>
  </el-dialog>
</template>

<script>
  import {listRejectionWindowVo,saveApplyReject} from '@/api/trade'
  import checkPermission from "@/utils/permission";
  import productImg from "@/assets/product.png";
  export default {
    name: "rejection-dialog",
    props: ['dialogData','deliveryVisible'],
    data() {
      return {
        productImg: productImg,
        visible: true,
        way: '1',
        rejection: '',
        tableData:[],
        ruleForm: {
          rejectionReason: ''
        },
        rules: {
          rejectionReason: [{required: true, message: '请输入拒收原因', trigger: 'blur'}]
        }
      }
    },
    mounted() {
      this.getDevelivery()
    },
    methods: {
      checkPermission,
      spanMethod({ row, column, rowIndex, columnIndex }) {
        if(columnIndex === 1) {
          return [1, 10]
        }
      },
      async getDevelivery() {
        const {data} = await listRejectionWindowVo(this.dialogData)
        this.rejection = data
        this.tableData = data.orderDeliveryInfoDetailVo.orderDeliveryList
      },
      handleClose(formName) {
        this.$refs[formName].resetFields();
        this.$emit('componentResult','close')
      },
      onsubmit(formName){
        this.$refs[formName].validate((valid) => {
          if (valid) {
            saveApplyReject({deliveryInfoIdId:this.dialogData,rejectionReason:this.ruleForm.rejectionReason}).then(res=>{
              this.$message.success('申请拒收成功!')
              this.$emit('componentResult','close')
            })
          } else {
            return false;
          }
        });
      },
    }
  }
</script>

<style lang="scss">

</style>
