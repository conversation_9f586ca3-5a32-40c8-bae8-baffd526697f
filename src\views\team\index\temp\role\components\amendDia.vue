<!--
 * @: 
-->
<template>
  <div>
    <el-dialog title="编辑角色" width="30%" :visible.sync="dialogFormVisible" v-if="dialogFormVisible" :close-on-click-modal="false">
      <el-form :model="form">
        <el-form-item label="角色等级" label-width="100px">
          <div>{{ form.grade }}级</div>
        </el-form-item>
        <el-form-item label="活动名称" label-width="100px">
          <el-input :maxlength="10" placeholder="角色名称，最多不超过10个字" v-model="form.name" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDia">取 消</el-button>
        <el-button type="primary" @click="submitRoleForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>


<script>
  export default {
    //import引入的组件
    components: {},

    data() {
      return {
        dialogFormVisible: false,
        form: {
          name: '',
          grade: 1,
        },
        currentItem: {},

      }
    },
    //生命周期 - 挂载完成（可以访问DOM元素）
    mounted() {},

    computed: {},

    created() {},

    filters: {},

    //方法集合
    methods: {
      openDia(item) {
        this.dialogFormVisible = true;
        this.currentItem = item;
        this.form.name = item.name;
        this.form.grade = item.grade;
      },
      closeDia() {
        this.dialogFormVisible = false;
        this.currentItem = {};
        this.form = {
          name: '',
          grade: 1,
        };
      },
      submitRoleForm() {
        let result = {
          ...this.currentItem,
          ...this.form
        }
        this.$emit('submitRoleEdit', result);
        setTimeout(()=>{
            this.closeDia();
        },500)
      },
    },

  }

</script>


<style lang='scss' scoped>

</style>
