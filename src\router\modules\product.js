
import Layout from '@/layout'

const productsRouter = {
  path: '/productCenterCopy',
  name: 'productCenterCopy',
  redirect: '/productCenterCopy/archives/list',
  meta: {
    title: '产品中心',
    icon: 'list'
  },
  component: Layout,
  children: [
    {
      path: 'archives/list',
      name: 'archivesList',
      meta: { title: '产品档案' },
      component: () => import('@/views/products/archives/list')
    },
    {
      path: 'archives/edit',
      name: 'archivesEdit',
      meta: { title: '产品档案编辑' },
      hidden: true,
      component: () => import('@/views/products/archives/newPageEdit')
    },
    {
      path: 'import/list',
      name: 'importList',
      meta: { title: '商品导入' },
      component: () => import('@/views/products/importList/index')
    },
    {
      path: 'productType/list',
      name: 'productTypeList',
      meta: { title: '产品分类' },
      component: () => import('@/views/products/productType/list')
    },
    // {
    //   path: 'productArchives/edit',
    //   name: 'archivesEdit',
    //   meta: { title: '档案编辑' },
    //   hidden:true,
    //   component: () => import('@/views/products/archives/edit')
    // },
    {
      path: 'brand/list',
      name: 'brandList',
      meta: { title: '品牌管理' },
      component: () => import('@/views/products/brand/list'),
    },
    {
      path: 'brand/edit',
      name: 'brandEdit',
      hidden: true,
      meta: { title: '品牌编辑' },
      component: () => import('@/views/products/brand/newPageEdit'),
    },
    // {
    //   path: 'brandManagement/edit',
    //   name: 'brandEdit',
    //   meta: { title: '品牌编辑' },
    //   hidden:true,
    //   component: () => import('@/views/products/brand/edit'),
    // },
    {
      path: 'product/detail',
      name: 'productsDetail',
      meta: { title: '商品详情' },
      component: () => import('@/views/products/product/detail')
    }
  ]
}

export default productsRouter
