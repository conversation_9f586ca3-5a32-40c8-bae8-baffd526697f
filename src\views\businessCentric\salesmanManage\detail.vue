<template>
  <div v-if="listLoading" class="archivesEditContent">
    <!-- 顶部按钮部分 -->
    <div class="top_title flex_between_center">
      <span>{{
        $route.query && $route.query.type == "edit"
          ? "编辑"
          : $route.query && $route.query.type == "see"
            ? "查看"
            : "新增"
      }}业务员档案</span>
      <div v-if="$route.query && $route.query.type !== 'see'">
        <el-popover v-model="rejectFlag" placement="bottom-end" title="取消提醒" width="300" trigger="click">
          <el-button slot="reference">取消</el-button>
          确定取消编辑?取消后编辑内容将不被保存!
          <div style="text-align: right; margin: 0; padding-top: 14px">
            <el-button size="mini" @click="rejectFlag = false">取消</el-button>
            <el-button type="primary" size="mini" @click="back">确定</el-button>
          </div>
        </el-popover>
        <el-button type="primary" @click="submit('editForm')">{{ $route.query && $route.query.type == 'edit'?'保存':'生成档案' }}</el-button>
      </div>
      <div v-else>
        <!-- <el-button type="primary">通过</el-button>
        <el-button>驳回</el-button> -->
        <el-button slot="reference" @click="back">返回</el-button>
      </div>
    </div>
    <!-- 顶部按钮部分结束 -->

    <!-- 表单部分 -->
    <el-form ref="editForm" :inline="true" label-width="140px" :model="formQuery" :rules="rules">
      <!-- 账户信息 -->
      <div class="item">
        <div class="title"><span>账户信息</span></div>
        <div>
          <el-form-item
            class="formItem"
            prop="mobile"
            label="注册手机号:"
            :rules="[
              { required: true, message: '请填写注册手机号', trigger: 'blur' },
            ]"
          >
            <el-input v-model.trim="formQuery.mobile" clearable style="width: 200px" placeholder="请填写注册手机号" />
          </el-form-item>
          <el-form-item class="formItem" prop="mobile" label="登录账号:">
            <el-input
              v-model.trim="formQuery.mobile"
              clearable
              style="width: 200px"
              :disabled="true"
              placeholder="请填写登录账号"
            />
          </el-form-item>
          <el-form-item class="formItem" prop="pwd" label="登录密码:">
            <el-input
              v-model.trim="formQuery.pwd"
              clearable
              style="width: 200px"
              :disabled="true"
              placeholder="初始密码手机号后6位"
            />
          </el-form-item>
        </div>
      </div>

      <!-- 账户信息结束 -->

      <!-- 基础信息 -->
      <div class="item">
        <div class="title"><span>基础信息</span></div>
        <el-form-item class="formItem" prop="code" label="业务员编码:">
          <el-input v-model="code" :disabled="true" clearable style="width: 200px" :value="'系统根据规则自动生成'" placeholder="请填写客户编码" />
        </el-form-item>
        <el-form-item
          class="formItem"
          prop="name"
          label="真实姓名:"
          :rules="[
            { required: true, message: '请填写真实姓名', trigger: 'blur' },
          ]"
        >
          <el-input v-model.trim="formQuery.name" clearable style="width: 200px" placeholder="请填写真实姓名" />
        </el-form-item>
        <el-form-item
          class="formItem"
          prop="regionId"
          label="所在区域:"
          :rules="[
            { required: true, message: '请选择所在区域', trigger: 'blur' },
          ]"
        >
          <el-cascader
            ref="city"
            v-model="formQuery.regionId"
            style="width: 200px"
            placeholder="请选择所在区域"
            :props="{ value: 'id', label: 'label' }"
            :options="areasTree"
            clearable
            @change="cityChange"
          />
        </el-form-item>
        <el-form-item
          class="formItem"
          prop="sex"
          label="性别:"
          :rules="[
            { required: true, message: '请选择性别', trigger: 'change' },
          ]"
        >
          <el-select v-model="formQuery.sex" placeholder="请选择性别">
            <el-option label="男" value="M" />
            <el-option label="女" value="W" />
            <el-option label="未知" value="N" />
          </el-select>
        </el-form-item>
        <el-form-item
          class="formItem"
          prop="inOfficeCompanyId"
          label="任职公司:"
        >
          <el-select clearable filterable v-model="formQuery.inOfficeCompanyId" placeholder="请选择任职公司">
            <el-option v-for="item in saleMerchantList" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
      </div>
      <!-- 基础信息结束 -->

      <!-- 证件信息 -->
      <div class="item" style="padding-bottom: 30px;">
        <div class="title"><span>证件信息</span></div>
        <div class="tips">支持JPG、JPEG、PNG、BMP格式，大小不超过2M</div>
        <el-table :data="formQuery.credentials" style="width: 100%" border>
          <el-table-column prop="type" label="证件类型" />
          <el-table-column prop="idNumber" label="证件号">
            <template slot-scope="{ row }">
              <el-input v-if="row.isEdit" v-model.trim="row.idNumber" placeholder="请输入证件号" />
              <span v-else>{{ row.idNumber }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="time" label="过期时间" width="340">
            <template slot-scope="{ row }" style="display:flex">
              <el-date-picker
                v-if="row.isEdit"
                v-model="row.licenseEndTime"
                :disabled="row.isForever"
                type="datetime"
                style="width: 220px; margin-right: 10px"
                placeholder="选择日期"
                value-format="yyyy-MM-dd"
                :picker-options="timeProp"
              />
              <span v-if="!row.isEdit && !row.isForever">{{
                row.licenseEndTime
              }}</span>
              <el-checkbox
                v-if="row.isEdit || row.isForever"
                v-model="row.isForever"
                :disabled="!row.isEdit"
                @change="handleChangForever(row)"
              >长期</el-checkbox>
            </template>
          </el-table-column>
          <el-table-column prop="frontImageUrl" width="180" label="身份证人像面">
            <template slot-scope="{ row }">
              <el-upload
                v-if="row.isEdit"
                ref="uploadlisence"
                :file-list="row.filePathList"
                :action="uploadParams.frontAction"
                :data="uploadParams.data"
                :headers="uploadParams.headers"
                list-type="picture-card"
                :show-file-list="false"
                :on-remove="handleRemove"
                :on-success="uploadSuccess"
                :before-upload="beforeUpload"
                accept=".jpg,.png,.bmp,.jpeg"
              >
                <img v-if="row.frontImageUrl && row.frontImageUrl.length > 0" :src="row.frontImageUrl" class="avatar">
                <i v-else class="el-icon-plus avatar-uploader-icon" />
              </el-upload>
              <span v-else>
                <el-image
                  v-if="row.frontImageUrl && row.frontImageUrl.length != 0"
                  style="width: 50px; height: 50px"
                  :src="row.frontImageUrl"
                  :preview-src-list="[row.frontImageUrl]"
                />
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="backImageUrl" width="180" label="身份证国徽面">
            <template slot-scope="{ row }">
              <el-upload
                v-if="row.isEdit"
                ref="uploadlisence"
                :action="uploadParams.backAction"
                :data="uploadParams.data"
                :headers="uploadParams.headers"
                list-type="picture-card"
                :on-remove="handleRemoveOpp"
                :show-file-list="false"
                :on-success="uploadSuccessOpp"
                :before-upload="beforeUpload"
                accept=".jpg,.png,.bmp,.jpeg"
              >
                <img v-if="row.backImageUrl && row.backImageUrl.length>0" :src="row.backImageUrl" class="avatar">
                <i class="el-icon-plus avatar-uploader-icon" />
              </el-upload>
              <span v-else>
                <el-image
                  v-if="row.backImageUrl && row.backImageUrl.length != 0"
                  style="width:50px; height:50px"
                  :src="row.backImageUrl"
                  :preview-src-list="[row.backImageUrl]"
                />
              </span>
            </template>
          </el-table-column>
          <el-table-column v-if="$route.query.type != 'see'" label="操作" width="150">
            <template slot-scope="scope">
              <el-button v-if="!scope.row.isEdit && checkPermission(['admin', 'admin-salesman:edit'])" type="text" @click="handleEdit">编辑</el-button>
              <el-button v-else type="text" @click="hanldeSure">确定</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 证件信息结束 -->

      <!-- 银行卡信息 -->
      <div class="item" v-if="$route.query.type === 'add'">
        <div class="title"><span>银行卡信息</span></div>
        <el-form-item class="formItem" prop="bankAccount" label="银行账户:">
          <el-input
            v-model.trim="formQuery.bankCardSaveReq.bankAccount"
            :disabled="true"
            clearable
            style="width: 200px"
            placeholder="由业务员填写"
          />
        </el-form-item>
        <el-form-item class="formItem" prop="bankName" label="开户银行:">
          <el-input
            v-model.trim="formQuery.bankCardSaveReq.bankName"
            :disabled="true"
            clearable
            style="width: 200px"
            placeholder="由业务员填写"
          />
        </el-form-item>
        <el-form-item class="formItem" prop="bankNumber" label="开户银行:">
          <el-input
            v-model.trim="formQuery.bankCardSaveReq.bankNumber"
            :disabled="true"
            clearable
            style="width: 200px"
            placeholder="由业务员填写"
          />
        </el-form-item>
      </div>
      <!-- 银行卡信息结束 -->
      <!-- 其他信息结束 -->
      <!-- 推广协议开始 -->
      <div class="item" v-if="$route.query.type === 'add' || ($route.query.type === 'see' && formQuery.sendProtocol === 'SEND')">
        <div class="title"><span>推广协议</span></div>
        <el-table :data="formQuery.signAgree" style="width: 100%" border class="tableAgreement">
          <el-table-column prop="contractName" label="协议名称" />
          <el-table-column prop="signingName" label="甲方" />
          <el-table-column prop="customerName" label="乙方" />
          <el-table-column prop="signDate" label="合同开始时间" />

          <el-table-column prop="contractStatus" label="签署状态">
            <template slot-scope="scope">
              {{ dealStatus(scope.row.contractStatus) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150">
            <template slot-scope="scope">
              <el-button type="text" @click="watchDetail(scope.row.contractUrl)">查看</el-button>
              <el-button type="text" @click="downLoad(scope.row.downUrl)">下载</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 推广协议结束 -->
    </el-form>
    <!-- 表单部分结束 -->
  </div>
</template>

<script>
import {
  areas
} from '@/api/businessCenter/businessList'
import {
  getToken
} from '@/utils/auth'
// import {
//   getLocalUser
// } from "@/utils/local-user";
import {
  getsalesman,
  getSigningContract,
  addSalesman,
  editSalesman,
  getSaleMerchantList
} from '@/api/salemanCenter/index' // TODO 替换成对应用的列表api
import checkPermission from '@/utils/permission'
export default {
  name: 'salesmanManageDetail',
  // import引入的组件
  components: {},

  filters: {},

  data() {
    var mobileValidator = (rule, value, callback) => {
      const reg = /^1\d{10}$/
      if (!value) {
        return callback(new Error('请输入注册手机号'))
      } else if (!reg.test(value)) {
        return callback(new Error('请输入正确的手机号'))
      } else {
        callback()
      }
    }
    return {
      listLoading: true,
      rejectFlag: false,
      saleMerchantList: [],
      code: '', // 业务员编码
      formQuery: {
        mobile: '',
        pwd: '',
        name: '',
        regionId: [],
        sex: '',
        credentials: [{
          type: '身份证',
          idNumber: '',
          licenseEndTime: '',
          frontImageUrl: '', // 正面照
          backImageUrl: '', // 反面照
          isForever: false,
          isEdit: false
        }], // 证件
        signAgree: [],
        bankCardSaveReq: {
          bankAccount: '',
          bankName: '',
          bankNumber: ''
        },
        role: 'EMPLOYEE',
        serviceType: 'N',
        whetherOnJob: 'Y',
        inOfficeCompanyId: ''
      },
      rules: {
        mobile: [{
          validator: mobileValidator,
          trigger: 'blur',
          required: true
        }]
      },
      uploadParams: {
        frontAction: '/api/crm/admin/salesman/upload/front',
        backAction: '/api/crm/admin/salesman/upload/back',
        headers: {
          token: getToken(),
          Authorization: 'Basic YWRtaW5fdWk6YWRtaW5fdWlfc2VjcmV0'
        },
        data: {
          pur: 0,
          sale: 0,
          tenant: 0,
          userid: '',
          folderId: 0
        }
      },
      timeProp: {
        disabledDate(r) {
          return r.getTime() < Date.now() - 24 * 60 * 60 * 1000
        }
      },
      areasTree: [],
      regionId: [] // 所在区域
    }
  },

  computed: {},
  // 生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    if (this.$route.query.type === 'see' || this.$route.query.type === 'edit') {
      this.getDetail()
    }
    this.setSaleMerchantList()
  },

  created() {
    this.getareas()
  },

  // 方法集合
  methods: {
    setSaleMerchantList() {
      getSaleMerchantList().then(res => {
        if (res.code !== 0) return
        this.saleMerchantList = res.data
      })
    },
    checkPermission,
    dealStatus(type) {
      let text = ''
      switch (type) {
        case 'DRAFT':
          text = '草稿'
          break
        case 'RECALLED':
          text = '已撤回'
          break
        case 'SIGNING':
          text = '签署中'
          break
        case 'REJECTED':
          text = '已退回'
          break
        case 'COMPLETE':
          text = '已完成'
          break
        case 'EXPIRED':
          text = '已过期'
          break
        case 'FILLING':
          text = '拟定中'
          break
        case 'INVALIDING':
          text = '作废中'
          break
        case 'INVALIDED':
          text = '已作废'
          break
      }
      return text
    },
    watchDetail(url) {
      window.open(url)
    },
    downLoad(url) {
      window.location.href = url
    },
    async  getDetail() {
      const res = await getsalesman(this.$route.query.id)
      const resSign = await getSigningContract({ id: this.$route.query.id })
      if (res.code === 0 && res.msg === 'ok' && resSign.code === 0 && resSign.msg === 'ok') {
        let signData = {}
        signData = resSign.data.contractList ? resSign.data.contractList : {}
        signData.customerName = resSign.data.customerName
        signData.phone = resSign.data.phone
        signData.signingName = resSign.data.signingName
        res.data.signAgree = []
        res.data.signAgree.push(signData)
        this.dealData(res.data || {})
      }
    },
    dealData(detail) {
      this.formQuery = {
        credentials: [{
          type: '身份证',
          idNumber: detail.idNumber,
          licenseEndTime: (detail.forever && detail.forever.code == 'Y') ? '' : detail.expiryDate,
          frontImageUrl: detail.frontImageUrl, // 正面照
          backImageUrl: detail.backImageUrl, // 反面照
          isForever: !!((detail.forever && detail.forever.code == 'Y')),
          isEdit: false
        }],
        signAgree: detail.signAgree,
        mobile: detail.mobile,
        pwd: '',
        name: detail.name,
        bankCardSaveReq: detail.bankCardRes ? { ...detail.bankCardRes } : {},
        regionId: [detail.provinceId, detail.cityId, detail.districtId],
        sex: detail.sex ? detail.sex.code : 'N',
        role: detail.role ? detail.role.code : '',
        id: detail.id,
        serviceType: detail.serviceType ? detail.serviceType.code : '',
        whetherOnJob: detail.whetherOnJob ? detail.whetherOnJob.code : '',
        sendProtocol: detail.sendProtocol ? detail.sendProtocol.code : '',
        inOfficeCompanyId: detail.inOfficeCompanyId
      }
      this.code = detail.code
    },
    back() {
      this.$router.go(-1)
    },
    async getareas() {
      const {
        data
      } = await areas()
      data.forEach(item => {
        if (item.id != '0') {
          this.areasTree.push(item)
        }
      })
    },
    // 所在区域改变事件
    cityChange(e) {
      this.formQuery.provinceId = e[0]
      this.formQuery.cityId = e[1]
      this.formQuery.countyId = e[2]
      this.regionId = e
    },
    handleRemove(file, fileList) {
      this.formQuery.credentials[0].frontImageUrl = ''
    },
    uploadSuccess(res, file, fileList) {
      console.log('file', res, file.response)
      if (res.data == null) {
        this.$message.warning(res.msg)
      } else {
        this.formQuery.credentials[0].frontImageUrl = res.data.frontImageUrl
        this.formQuery.credentials[0].idNumber = res.data.idNumber
        this.formQuery.name = res.data.name
        this.formQuery.sex = res.data.sex ? res.data.sex.code : 'N'
      }
    },
    handleRemoveOpp(file, fileList) {
      this.formQuery.credentials[0].backImageUrl = ''
    },
    uploadSuccessOpp(res, file, fileList) {
      if (res.data == null) {
        this.$message.warning(res.msg)
      } else {
        this.formQuery.credentials[0].backImageUrl = res.data.backImageUrl
        if (res.data.forever.code == 'N') {
          this.formQuery.credentials[0].licenseEndTime = ''
          this.formQuery.credentials[0].isForever = false
        } else {
          this.formQuery.credentials[0].licenseEndTime = res.data.expiryDate
          this.formQuery.credentials[0].isForever = true
        }
      }
    },
    beforeUpload(file) {
      const fileTypeList = [
        'image/png',
        'image/pjpeg',
        'image/jpeg',
        'image/bmp'
      ]
      const isJPG = fileTypeList.indexOf(file.type) > -1
      const isLt2M = file.size / 1024 / 1024 < 5

      if (!isJPG) {
        this.$message.error('上传图片格式错误!')
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
      }
      return isJPG && isLt2M
    },
    //  编辑
    handleEdit() {
      this.formQuery.credentials[0].isEdit = true
    },
    handleChangForever(e) {
      console.log('----------->', e)
    },
    //  确定
    hanldeSure() {
      this.formQuery.credentials[0].isEdit = false
    },
    submit() {
      this.$refs.editForm.validate(async(valid) => {
        // console.log('------->',valid);
        if (valid) {
          // 验证成功
          const params = {
            ...this.formQuery,
            backImageUrl: this.formQuery.credentials[0].backImageUrl,
            frontImageUrl: this.formQuery.credentials[0].frontImageUrl,
            expiryDate: this.formQuery.credentials[0].licenseEndTime,
            forever: this.formQuery.credentials[0].isForever ? 'Y' : 'N',
            idNumber: this.formQuery.credentials[0].idNumber,
            inOfficeCompanyId: this.formQuery.inOfficeCompanyId || null
          }
          console.log('------->', params)
          if (this.$route.query.type == 'edit') {
            params.id = this.formQuery.id
            editSalesman(params).then(res => {
              if (res.code == 0 && res.msg == 'ok') {
                this.$message.success('修改成功')
                this.$router.push({
                  path: '/businessCentric/salesmanManage/index'
                })
                // this.$route.push({
                //   path:'/businessCentric/salesmanManage/index'
                // });
              }
            })
          } else {
            addSalesman(params).then(res => {
              if (res.code == 0 && res.msg == 'ok') {
                this.$message.success('新增成功')
                this.$router.push({
                  path: '/businessCentric/salesmanManage/index'
                })
                // this.back();
              }
            })
          }
        }
      })
    }
  }
}

</script>

<style lang='scss' scoped>
  .archivesEditContent {
    border-top: 1px solid #ebecee;
    padding: 0px 20px;
    background-color: #fff;

    .item {
      width: 100%;
      margin-bottom: 30px;
      border-bottom: 1px solid #eeeeee;

      &:last-child {
        margin-bottom: 0;
      }

      .title {
        padding: 0 0 15px;

        span {
          font-size: 16px;
          padding-left: 10px;
          border-left: 4px solid rgba(64, 158, 255, 1);
        }
      }
    }

    .top_title {
      height: 56px;
      line-height: 56px;
      font-family: "PingFangSC-Regular", "PingFang SC", sans-serif;
      font-size: 18px;
      text-align: left;
      border-bottom: 1px solid #eeeeee;
      margin-bottom: 20px;

      .el-button {
        margin-left: 10px;
      }
    }
  }

  .tips {
    color: #aaa;
    margin-bottom: 20px;
  }
  .tableAgreement{
    margin-bottom: 30px;
  }

  ::v-deep .el-upload {
    width: 40px;
    height: 40px;
    position: relative;
  }

  ::v-deep .el-upload>i {
    font-size: 16px;
    position: absolute;
    left: 50%;
    top: 50%;
    -webkit-transform: translateX(-50%) translateY(-50%);
    transform: translateX(-50%) translateY(-50%);
  }

  ::v-deep .el-upload-list .el-upload-list__item {
    width: 40px;
    height: 40px;
  }

  ::v-deep .hide .el-upload--picture-card {
    display: none;
  }

  ::v-deep .el-icon-circle-close {
    color: #fff;
  }
::v-deep .avatar-uploader .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }
   ::v-deep .avatar-uploader .el-upload:hover {
      border-color: #409eff;
    }
    ::v-deep .avatar-uploader-icon {
      font-size: 24px;
      color: #8c939d;
      width: 40px;
      height: 40px;
      line-height: 40px;
      text-align: center;
    }
    ::v-deep .avatar {
      width: 50px;
      height: 50px;
      display: block;
    }
</style>
