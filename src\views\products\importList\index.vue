<template>
    <div class="systemPageContent">
      <div class="tab_bg">
        <tabs-layout
          ref="tabs-layout"
          :tabs="[{ name: '产品导入', value: ''}]"
        >
          <template slot="button">
            <div>
              <el-button type="primary" icon="el-icon-plus" @click="showImportFun" v-if="checkPermission(['admin','admin-platformProduct:import'])">新建导入</el-button>
              <el-button icon="el-icon-refresh" @click="onSearchSubmitFun">刷新</el-button>
            </div>
          </template>
        </tabs-layout>
        <div class="table">
          <table-pager
            ref="pager-table"
            :data.sync="list"
            :options="tableTitles"
            :remoteMethod="getList"
            :operationWidth="180"
            @change-page="multipleSelection = []"
          >
            <template slot="uploadStatus" slot-scope="{ row }">
              <span v-if="row['uploadStatus'].code == 'SUCCESS'" class="el-tag el-tag--success">成功</span>
              <span v-if="row['uploadStatus'].code == 'FAILURE'" class="el-tag el-tag--danger">失败</span>
              <span v-if="row['uploadStatus'].code == 'PROCESSING'" class="el-tag ">处理中</span>
            </template>
            <div slot-scope="scope">
              <el-row class="table-edit-row">
                  <span class="table-edit-row-item">
                    <el-button v-if="checkPermission(['admin','admin-platformProduct:logDownload'])&&scope.row['uploadStatus'].code != 'PROCESSING'" type="text" @click="downloadFun(scope.row)">下载失败结果</el-button>
                  </span>
                  <span class="table-edit-row-item" style="margin-left: 5px;">
                    <el-button @click="deleteFun(scope.row)"  v-if="checkPermission(['admin','admin-platformProduct:logDel'])&&scope.row['uploadStatus'].code != 'PROCESSING'" type="text">删除</el-button>
                  </span>
              </el-row>
            </div>
          </table-pager>
        </div>
      </div>
      <el-dialog
        title="导入产品"
        :visible.sync="showImport"
        width="428px"
        :append-to-body="true"
        :before-close="closeDialogFun">
          <div class="uploadContent">
              <el-upload
                ref="upload"
                class="avatar-uploader"
                drag
                :action="$excelImportUrl"
                :headers="headersProgram"
                :on-change="beforeUpload"
                :file-list="fileList"
                :on-success="uploadSuccess"
                :show-file-list="true"
                :auto-upload="false"
                :multiple="false"
                accept=".xlsx"
                >
                <!-- :limit="1" -->
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">将文件拖拽至此区域或选择文件上传</div>
                <div class="el-upload__tip" slot="tip">当前仅支持xlsx格式文件，文件大小1M以内且在5000条 数据以内，若超出限制，请分批导入
                  <el-link  type="primary" href="https://eyaolink-dev-bucket.oss-cn-shenzhen.aliyuncs.com/0/2021/02/7bc6f664-f923-49e6-a40d-5ed5583a538b.xlsx" target="_blank">下载模板</el-link> </div>
            </el-upload>

        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="showImport = false">取 消</el-button>
          <el-button type="primary" @click="submitUpload">确 定</el-button>
        </span>
      </el-dialog>
  </div>
</template>
<script>
import { uploadFile } from "@/api/file";
import { getToken } from "@/utils/auth";
import checkPermission from "@/utils/permission";
import { importProductList, importProductFile,importProductDelete,exportProductLog } from "@/api/products/product";
import Pagination from "@/components/Pagination";
import TabsLayout from '@/components/TabsLayout'
const TableColumns = [
  { label: "导入时间", name: "createTime", prop: 'createTime', width: "170px" },
  { label: "导入状态", name: "uploadStatus", prop: 'uploadStatus', width: "100px", columnSlot: true },
  { label: "操作人", name: "createUserName", prop: 'createUserName', width: "100px" }
]
const TableColumnList = [];
for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({ key: i, ...TableColumns[i] });
}
export default {
  components: {
    Pagination,
    TabsLayout
  },
  data() {
    return {
      headersProgram: {
        token: getToken(),
        Authorization: "Basic YWRtaW5fdWk6YWRtaW5fdWlfc2VjcmV0"
      },
      // insertProgram: {
      //   folderId: 0
      // },
      fileList:[],
      tableTitles: TableColumnList,
      showEdit: false,
      showImport: false,
      showItemTable: false,
      row: {},
      submitReload: false,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        size: 10,
        model: {
          productType: 'PLATFORM_PRODUCT',
          uploadStatus: ''
        },
        current: 1
      }
    };
  },
  watch: {
    submitReload: function(newVal, oldVal) {
      if (newVal) {
        this.submitReload = false
        this.$refs['pager-table'].doRefresh()
        this.$refs['pager-table'].doRefresh()
      }
    }
  },
  methods: {
    checkPermission,
    closeDialogFun(){
        this.showImport=false;
    },
    onSearchSubmitFun(){
      this.listQuery.size= 10;
      this.$refs['pager-table'].doRefresh()
    },
    async getList(params) {
      Object.assign(this.listQuery, params)
      return await importProductList(this.listQuery)
    },
    showImportFun() {
      this.showImport = true;
    },
    editFun(row) {
      this.row = row;
      this.showEdit = true;
    },
    initQuery(){
      this.listQuery={
        size: 10,
        model:{
          productType: 'PLATFORM_PRODUCT',
          uploadStatus: ''
        },
        current: 1
      }
    },
    async actionDeleteFun(row) {
      let data = await importProductDelete(row.id);
      if (data.code == 0) {
        this.$refs['pager-table'].doRefresh()
      }
    },
    deleteFun(row){
      var _this=this;
      this.$confirm('此操作将永久删除该信息, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        _this.actionDeleteFun(row)
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    beforeUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < 5;
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!');
      }
      var uploadFiles = this.$refs.upload.uploadFiles;
      if(uploadFiles.length > 1){
        uploadFiles.splice(0,1);
      }
      return  isLt2M;
    },
    uploadSuccess(res, file) {
      if(res.code === 0){
        this.fileList=[];
        this.showImport = false;
        this.initQuery();
        this.$refs['pager-table'].doRefresh()
      }else{
        this.$message.error('上传失败!');
      }
    },
    submitUpload() {
      this.$refs.upload.submit();
    },
    downloadFun:async function(row){
      let data = await exportProductLog({
          "current": 1,
          "map": {},
          "model": {
            "productUploadId": row.id
          },
          "order": "descending",
          "size": 10,
          "sort": "id"
        })
        const blob = new Blob([data.data], { //取响应回来的数据
          // type: "application/vnd.ms-excel;",  xls
           type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;",
        });
        const href = window.URL.createObjectURL(blob); // 创建下载的链接
        const downloadElement = document.createElement("a");
        downloadElement.href =href;
        downloadElement.download = decodeURI(data.headers["filename"]);
        document.body.appendChild(downloadElement);
        downloadElement.click(); // 点击下载
        document.body.removeChild(downloadElement); // 下载完成移除元素
        window.URL.revokeObjectURL(href);
    }
  },
  mounted() {
  },
  beforeDestroy() {}
};
</script>
<style lang="scss" scoped>
@import "@/styles/element-variables.scss";
</style>
<style lang="less" scoped>
// /deep/ .uploadContent .avatar-uploader .el-upload-dragger{
//     width:385px;
//     height:180px;
// }
// /deep/ .uploadContent .avatar-uploader .el-upload__tip{
//   width: 380px;
//   margin-top: 10px;
//   line-height: 20px;
// }


/deep/ .avatar-uploader .el-upload{width: 100%; height: auto;}
/deep/ .uploadContent .avatar-uploader .el-upload-dragger{
    width:100%;
    height:180px;
}
/deep/ .uploadContent .avatar-uploader .el-upload__tip{
  width: 100%;
  margin-top: 10px;
  line-height: 20px;
}

</style>
