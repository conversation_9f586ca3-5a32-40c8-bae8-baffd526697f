<template>
  <div>
    <footer class="footer edit-posi">
      <div v-if="preview" class="edit-pop" @click="activeEdit('footer')">点击编辑页脚</div>
      <div class="service-intro">
        <div v-if="preview" class="service-type">
          <div class="service-type-info">
            <ul>
              <li>新手指南</li>
              <li>免费注册</li>
              <li>资质认证</li>
              <li>网站操作流程</li>
              <li>小程序操作流程</li>
            </ul>
          </div>
          <div class="service-type-info">
            <ul>
              <li>支付与配送</li>
              <li>在线支付</li>
              <li>货道付款</li>
              <li>邮局汇款</li>
              <li>公司转账</li>
            </ul>
          </div>
          <div class="service-type-info">
            <ul>
              <li>售后帮助</li>
              <li>退货政策</li>
              <li>退货流程</li>
              <li>退款说明</li>
              <li>发票制度</li>
            </ul>
          </div>
          <div class="service-type-info">
            <ul>
              <li>消费者保障</li>
              <li>如实描述</li>
              <li>急速发货</li>
              <li>假一赔十</li>
              <li>7天退货</li>
            </ul>
          </div>
        </div>
        <div v-else class="service-type">
          <div v-for="(item, index) in dataList" :key="index" class="service-type-info">
            <ul>
              <li>{{ item.moduleName }}</li>
              <li v-for="val in item.linkNameList" :key="val.id" @click="bindUrl(val.linkUrl)">{{ val.linkName }}</li>
            </ul>
          </div>
        </div>
        <div v-if="preview" class="service">
          <div class="service-phone">
            <p>028-06248048</p>
            <span>工作时间: 周一至周五 9:00 - 18:00</span>
            <div class="service-button">
              <div class="service-img" />
              联系客服
              <div class="service-pop">
                <div class="service-content">
                  <div class="pop-title">
                    <img src="../../../../assets/img/index/icon-customer-service-block.png">联系客服
                  </div>
                  <p>QQ客服：<span>654822198</span></p>
                  <p>电话客服：028-06248048</p>
                  <div class="service-qq">QQ交谈</div>
                  <div class="line" />
                  <div class="pop-title">
                    <img src="../../../../assets/img/index/icon_time.png">联系客服
                  </div>
                  <p>周一至周五 9:00 - 18:00</p>
                </div>
              </div>
            </div>
          </div>
          <div class="service-code">
            <div class="service-code-item">
              <img alt src="../../../../assets/img/index/applets-code.png">
            </div>
            <p>扫一扫小程序</p>
          </div>
        </div>
        <div v-else class="service">
          <div class="service-phone">
            <p>{{ page.pageDataAdVoMap.PC_SERVICE_CENTER[0].linkUrl }}</p>
            <span>工作时间: 周一至周五 9:00 - 18:00</span>
            <div class="service-button">
              <div class="service-img" />
              联系客服
            </div>
          </div>
          <div class="service-code">
            <div class="service-code-item">
              <img alt :src="page.pageDataAdVoMap.PC_SERVICE_CENTER[0].picUrl">
            </div>
            <p>扫一扫小程序</p>
          </div>
        </div>
      </div>
      <div v-if="preview" class="footer-last">
        <p>Copyright @ 2020 基药云版权所有 All Right Reserved</p>
        <p>备案号 粤ICP备20056016</p>
      </div>
      <div v-else class="footer-last">
        <div v-html="page.content" />
      </div>
    </footer>
  </div>
</template>

<script>

import {listByParentId} from "@/views/fitment/pcmall/components/index";

export default {
  name: 'Footer',
  props: {
    preview: {
      type: Boolean,
      default: true
    },
    page: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      dataList: []
    }
  },
  mounted() {
    listByParentId().then(res => {
      this.dataList = res.data.map(val => {
        val.linkNameList && val.linkNameList.length > 0 ? val.linkNameList.map(item => {
          for (let key in item.linkNameList) {
            item.linkName = key
            item.linkUrl = item.linkNameList[key]
          }
          return item
        }) : ''
        return val
      })
      console.log(this.dataList)
    })
  },
  methods: {
    bindUrl(path) {
      if (path.substr(0, 1) === '/') {
        this.$router.push({ path })
      } else {
        window.open(path, '_blank')
      }
    },
    activeEdit(type) {
      this.$emit('active-edit', type)
    }
  }
}
</script>

<style lang="less" scoped>
  .footer {
    margin: 0 auto;
    background-color: #2A3D5F;
    min-width: 1200px;

    .icon-row {
      width: 1200px;
      margin: 25px auto;
      padding-top: 8px;
      height: 64px;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: space-between;

      .footer-icon {
        margin: 0 17px;
        display: flex;
        width: calc(20% - 34px);

        .footer-icon-text {
          color: #0971EB;
          font-size: 22px;
          display: flex;
          flex-direction: column;
          flex-wrap: wrap;
          justify-content: center;
          text-align: left;
          padding-left: 7px;
          flex: 1;

          .footer-icon-name {
            height: 1em;
            overflow: hidden;
          }

          .footer-icon-little {
            font-size: 14px;
            margin-top: 4px;
            color: #999;
          }
        }

        .footer-icon-child {
          margin-top: 2px;
          width: 55px;
          height: 55px;
          text-indent: -999px;
        }
      }
    }

    .service-intro {
      width: 1200px;
      margin: 0 auto;
      height: 245px;
      display: flex;
      position: relative;

      .service-type {
        margin: 60px 0;
        display: flex;
        width: 740px;

        .service-type-info {
          text-align: left;
          width: 184px;

          li {
            cursor: pointer;
            margin-top: 10px;

            font-size: 12px;
            color: #dcdde0;

            &:hover {
              color: #3A6BED;
            }
          }

          li:first-child {
            font-size: 16px;
            font-weight: 500;
            color: #ffffff;
            margin-top: 0;
            margin-bottom: 20px;

            &:hover {
              color: #ffffff;
            }
          }
        }
      }
      .service{
        display: flex;
        .service-phone{
          text-align: left;
          margin-top: 60px;
          p{
            font-size: 24px;
            font-weight: 600;
            color: #ffffff;
          }
          span{
            font-size: 12px;
            color: #ffffff;
            padding-top: 16px;
            display: inline-block;
          }
          .service-button{
            width: 90px;
            height: 34px;
            opacity: 1;
            border: 1px solid #ffffff;
            display: flex;
            align-items: center;
            font-size: 12px;
            color: #ffffff;
            justify-content: center;
            margin-top: 15px;
            cursor: pointer;
            position: relative;
            .service-img{
              width: 12px;
              height: 12px;
              margin-right: 4px;
              background: url("~@/assets/img/index/icon-customer-service.png");
              background-size: 100% 100%;
            }
            .service-pop{
              position: absolute;
              bottom: 34px;
              left: 0;
              width: 188px;
              text-align: left;
              padding-bottom: 4px;
              box-shadow: 0px 4px 14px 0px rgba(2,12,38,0.08);
              z-index: 100;
              line-height: initial;
              display: none;
              .service-content{
                background-color: #fff;
                padding: 20px;
              }
              .pop-title{
                display: flex;
                align-items: center;
                font-size: 14px;
                color: #1e2439;
                margin-bottom: 13px;
                img{
                  width: 16px;
                  height: 16px;
                  margin-right: 8px;
                }
              }
              p{
                font-size: 12px;
                color: #505465;
                margin-bottom: 8px;
                span{
                  color: #0056E5;
                }
              }
              .service-qq{
                text-align: center;
                padding: 6px 0;
                width: 67px;
                background: #0056e5;
                border-radius: 12px;
                font-size: 12px;
                font-weight: 500;
                color: #ffffff;
                cursor: pointer;
              }
              .line{
                width: 142px;
                height: 1px;
                opacity: 1;
                background: #ebecee;
                margin: 16px 0;
              }
            }
            &:hover {
              background-color: #fff;
              border: 0;
              color: #2A3D5F;
              .service-img{
                background: url("~@/assets/img/index/icon-customer-service-block.png");
                background-size: 100% 100%;
              }
              .service-pop{
                display: block;
              }
              .el-icon--right{
                transform: rotate(180deg);
                transition: all .3s;
              }
            }
          }
        }

        .service-code {
          position: absolute;
          right: 0;
          top: 58px;
          text-align: center;
          .service-code-item {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 110px;
            height: 110px;
            background-color: #fff;
            img{
              width: 100px;
              height: 100px;
            }
          }
          p{
            font-size: 12px;
            color: #ffffff;
            margin-top: 6px;
          }
        }
      }

    }

    .footer-last {
      border: 1px solid rgba(255,255,255,0.1);
      padding-top: 31px;
      padding-bottom: 39px;
      color: #fff;
      font-size: 12px;
      text-align: center;
      p{
        margin-top: 10px;
      }
    }
  }
</style>
