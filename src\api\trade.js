import request from '@/utils/requestAxios'
import requestExport from '@/utils/requestExport'
//订单数量
export function orderList(query) {
  return request({
    url: '/api/order/admin/orderInfo/page',
    method: 'post',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//订单统计数量
export function orderStatistics(data) {
  return request({
    url: `/api/order/admin/orderInfo/statistics`,
    method: 'post',
    data
  })
}
//退货统计数量
export function salesReturnStatistics() {
  return request({
    url: `/api/order/admin/salesReturnInfo/statistics`,
    method: 'get'
  })
}
//拒收统计数量
export function rejectionStatistics() {
  return request({
    url: `/api/order/admin/rejection/statistics`,
    method: 'get'
  })
}
//退款统计数量
export function refundStatistics() {
  return request({
    url: `/api/order/admin/salesRefundInfo/statistics`,
    method: 'get'
  })
}
export function orderDetail(id) {
  return request({
    url: `/api/order/admin/orderInfo/getOrderInfoDetail/${id}`,
    method: 'get'
  })
}
//发货弹框
export function listProductDelivery(orderId) {
  return request({
    url: `/api/order/admin/orderInfo/listProductDelivery/${orderId}`,
    method: 'get'
  })
}
//确认发货
export function addDelivery(query) {
  return request({
    url: '/api/order/admin/orderInfo/addDelivery',
    method: 'post',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//审核
export function auditOrderInfo(query) {
  return request({
    url: '/api/order/admin/orderInfo/auditOrderInfo',
    method: 'post',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//取消订单
export function addOrderCancel(query) {
  return request({
    url: '/api/order/admin/orderInfo/addOrderCancel',
    method: 'post',
    params: query
  })
}
//申请拒收
export function saveApplyReject(query) {
  return request({
    url: `/api/order/admin/orderInfo/saveApplyReject`,
    method: 'post',
    params: query
  })
}
//修改订单备注
export function updateRemark(query) {
  return request({
    url: '/api/order/admin/orderInfo/updateRemark',
    method: 'post',
    params: query
  })
}
//发货单类表
export function orderDeliveryList(query) {
  return request({
    url: '/api/order/admin/orderDelivery/page',
    method: 'post',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//拒收弹出框
export function listRejectionWindowVo(deliveryInfoIdId) {
  return request({
    url: `/api/order/admin/orderInfo/listRejectionWindowVo/${deliveryInfoIdId}`,
    method: 'get'
  })
}
//退款弹出框
export function listRefundWindowVo(orderId) {
  return request({
    url: `/api/order/admin/orderInfo/listRefundWindowVo/${orderId}`,
    method: 'get'
  })
}
//调价
export function listModifyPriceVo(orderId) {
  return request({
    url: `/api/order/admin/orderInfo/listModifyPriceVo/${orderId}`,
    method: 'get'
  })
}
//拒收列表
export function rejectionList(query) {
  return request({
    url: '/api/order/admin/rejection/page',
    method: 'post',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//拒收详情
export function rejectionDetail(id) {
  return request({
    url: `/api/order/admin/rejection/getRejectionInfoDetail/${id}`,
    method: 'get'
  })
}
//拒收确定收货
export function rejectionComfirm(id) {
  return request({
    url: '/api/order/admin/rejection/confirmReceipt',
    method: 'post',
    params: {
      id: id
    }
  })
}
//审核成功
export function refuseRejection(id,refuseReason) {
  return request({
    url: '/api/order/admin/rejection/refuseRejection',
    method: 'post',
    params: {
      id: id,
      refuseReason: refuseReason
    }
  })
}
//审核拒绝
export function agreeRejection(query) {
  return request({
    url: '/api/order/admin/rejection/agreeRejection',
    method: 'post',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}


//退货
export function salesReturnInfoList(query) {
  return request({
    url: '/api/order/admin/salesReturnInfo/page',
    method: 'post',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
export function returnDetail(id) {
  return request({
    url: `/api/order/admin/salesReturnInfo/getSalesReturnInfo/${id}`,
    method: 'get'
  })
}
//审核拒绝
export function refuseSalesReturn(id,refuseReason) {
  return request({
    url: '/api/order/admin/salesReturnInfo/refuseSalesReturn',
    method: 'post',
    params: {
      id: id,
      refuseReason: refuseReason
    }
  })
}
//审核成功
export function agreeSalesReturn(query) {
  return request({
    url: '/api/order/admin/salesReturnInfo/agreeSalesReturn',
    method: 'post',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//退款
export function salesRefundInfoList(query) {
  return request({
    url: '/api/order/admin/salesRefundInfo/page',
    method: 'post',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
export function refundDetail(id) {
  return request({
    url: `/api/order/admin/salesRefundInfo/getSalesRefundDetail/${id}`,
    method: 'get'
  })
}
//审核拒绝
export function refuseSalesRefund(id,refuseReason) {
  return request({
    url: '/api/order/admin/salesRefundInfo/refuseSalesRefund',
    method: 'post',
    params: {
      id: id,
      refuseReason: refuseReason
    }
  })
}
//审核成功
export function agreeSalesRefund(query) {
  return request({
    url: '/api/order/admin/salesRefundInfo/agreeSalesRefund',
    method: 'post',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//确认收货
export function saleComfirm(id) {
  return request({
    url: '/api/order/admin/salesReturnInfo/confirmReceipt',
    method: 'post',
    params: {
      id: id
    }
  })
}
//退款流水
export function refundFlowList(query) {
  return request({
    url: '/api/order/admin/refundFlow/page',
    method: 'post',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//代客下单，获取订单价格信息

export function OrderConfirm(query) {
  return request({
    url: '/api/cart/admin/cart/OrderConfirm',
    method: 'post',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//保存下单

export function saveOrder(query) {
  return request({
    url: '/api/cart/admin/cart/saveOrder',
    method: 'post',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}

//物流
export function getExpressInfo(logisNo) {
  return request({
    url: `/api/general/admin/expressInfo/get`,
    method: 'get',
    params: {
      logisNo:logisNo
    }
  })
}
//快递种类
export function getExpressType(id) {
  return request({
    url: `/authority/parameter/${id}`,
    method: 'get'
  })
}
//获取订单统计价格
export function statisticsMoney(query) {
  return request({
    url: '/api/order/admin/orderInfo/statisticsMoney',
    method: 'post',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}

// 获取订单列表 获取客户名称
export function purMerchantName(params) {
  return request({
    url: `/merchant/admin/purMerchant/feign/findByPurMerchantLikeName`,
    method: 'post',
    params: params,
    data: {
      "current": 1,
      "map": {},
      "model": {},
      "order": "descending",
      "size": 10,
      "sort": "id"
    }
  })
}

/**
 * 导出订单列表
*/
export function exportOrderListReq(data) {
  return requestExport({
    method: 'post',
    url: '/api/order/admin/orderInfo/platform/export/order/info',
    data
  })
}

/**
 * 导出订单明细
*/
export function exportOrderDetailsReq(data) {
  return requestExport({
    method: 'post',
    url: '/api/order/admin/orderInfo/platform/export/orderItem/info',
    data
  })
}

// 获取订单明细导出数量
export function fetchOrderDetailExportCount(data) {
  return request({
    url: '/api/order/admin/orderInfo/detailExportCount',
    method: 'post',
    data
  })
}

// 根据关键词获取商家列表
export function getMerchantList2Keyword(commerceModel, keyword) {
  return new Promise((resolve, reject) => {
    request({
      url: `/api/merchant/admin/saleMerchant/getSaleMerchantByName?commerceModel=${commerceModel}&name=${keyword}`,
      method: 'get'
    }).then((res) => {
      let resList = []
      res.data.forEach((item) => {
        resList.push({value: item})
      })
      resolve(resList)
    }).catch((e) => {
      reject(e)
    })
  })
}