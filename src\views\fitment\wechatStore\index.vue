<template>
    <div v-if="queryBackgroundInfo!=null" class="merchant_index" @click.stop="checkPermission(['admin','fitment-wechat:edit'])&&showDrawerFun()" 
    v-bind:style="{background:'url('+queryBackgroundInfo.image+')  no-repeat;'}">
        
        <Search></Search>
        <StoreHeader  v-if="pagePlateId!=''" :pagePlateId="pagePlateId"></StoreHeader>
        <product-types-tabs v-if="pagePlateId!=''" :pagePlateId="pagePlateId" :floorsCompomentObject="floorsCompomentObject"></product-types-tabs>

        <el-drawer
            :destroy-on-close="true"
            :size="'450px'"
            append-to-body
            :wrapperClosable="false"
            :visible.sync="drawer"
            :with-header="false">
            <div class="flex_between_center top" >
                <div>设置背景</div> 
                <div>
                    <el-button @click="drawer=false" >取 消</el-button>
                    <el-button type="primary"  @click="submitFun()" >提交</el-button>
                </div>
            </div>
            <div class="tipBox">
                <p class="title">添加背景图片</p>
            </div>
            <el-form  label-width="75px" class="form">
                <el-form-item class="setItemBox" :label-width="'0'" label=" "   >
                        <i class="el-icon-error closeI" @click="removeItem()"></i>
                        <div class="flex_between_start">
                            <div class="uploadImgBox" >
                                <el-upload
                                    class="avatar-uploader"
                                    :action="$uploadUrl"
                                        :data="insertProgram"
                                        :headers="headersProgram"
                                        :on-success="uploadSuccess"
                                        :before-upload="beforeUpload"
                                        :show-file-list="false"
                                        multiple
                                    >
                                    <el-image v-if="backgroundInfo.image!=''" style="width: 80px; height: 80px" :src="backgroundInfo.image" :fit="'cover'"></el-image>
                                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                                </el-upload>
                            </div>
                        </div>
                    </el-form-item>
            </el-form>
        </el-drawer>
    </div>
</template>
<script>
import checkPermission from '@/utils/permission' 
import {
  pageComponentList,
  deleteByPageComponentId,
  pageADList,
  pageADAdd,
  pageADEdit
} from "@/api/fitment";
import { query } from "@/api/setting/data/dictionaryItem";
import { uploadFile } from "@/api/file";
import { getToken } from "@/utils/auth";
import { pageInfo }  from "@/api/fitment";
import Search from '../commons/wechatStore/Search'
import StoreHeader from '../commons/wechatStore/Header'
import ProductTypesTabs from '../commons/wechatStore/ProductTypesTabs'
export default {
data() {
return {
    pagePlateId:"",
    navTapCompomentObj:null,
    floorsCompomentObject:null,
    selectList:[],
    drawer:false,
    backgroundInfo:{},
    queryBackgroundInfo:null,
    headersProgram: {
        token: getToken(),
        Authorization: "Basic YWRtaW5fdWk6YWRtaW5fdWlfc2VjcmV0"
    },
    insertProgram: {
        folderId: 0
    },
};
},
components:{
      Search,
      StoreHeader,
      ProductTypesTabs
},
props: {},
methods: {
    checkPermission,
    showDrawerFun(){
        this.drawer=true
        this.getSelectList();
        this.backgroundInfo=Object.assign({},this.queryBackgroundInfo)
    },
    async submitFun(){
        await deleteByPageComponentId(this.pageComponentId)
        this.queryBackgroundInfo={...{},...this.backgroundInfo}
        var data = await pageADAdd(this.queryBackgroundInfo)
        if (data.code == 0) {
            this.drawer=false
            this.initFun();
        } else {
            this.$message.error("提交胶囊图片失败！");
        }
    },
    removeItem(){
       this.backgroundInfo={
            id: "",
            showStatus:"Y",
            pagePlateId:this.pagePlateId,
            pageComponentId:this.pageComponentId,
            dataType:"CUSTOM",
            image: "",
            sortValue:this.backgroundInfo.length
        }
    }, 
    
    //  上传功能
    async getSelectList() {
      this.list = [];
      this.isLoading = true;
      let { data } = await query(
        {
          dictionaryId:"40083396470731372",
          // code:"WXCHARTLOCATION"
        }
      );
      this.selectList = data;
    },

    uploadIndexItem:function(index){
      this.uploadIndex=index
    },
    beforeUpload(file) {
      let fileTypeList=["image/png", "image/pjpeg", "image/jpeg", "image/bmp"]
      const isJPG = fileTypeList.indexOf(file.type) >-1;
      const isLt2M = file.size / 1024 / 1024 < 5;

      if (!isJPG) {
        this.$message.error('上传图片格式错误!');
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!');
      }
      return isJPG && isLt2M;
    },
    uploadSuccess(res, file) {
      this.backgroundInfo.image=res.data.url;
    },
    async pageInfoFun(){
      let {data} = await pageInfo("WeChatHome")
      if(data!=null){
        this.pagePlateId=data.id
        data.pageComponentList.forEach(item=>{
          if(item.componentCode=="floors"){
            this.floorsCompomentObject=item
          }
        })
        this.initFun()
      }
    },
    async initFun() {
      var { data } = await pageComponentList({
        current: 1,
        map: {},
        model: {
          componentCode: "banner"
        },
        order: "descending",
        size: 10,
        sort: "id"
      });
      this.pageComponentId = data.records[0].id;
      let adList = await pageADList({
        current: 1,
        map: {},
        model: {
          pageComponentId: this.pageComponentId
        },
        order: "descending",
        size: 10,
        sort: "id"
      });
      if(adList.data.records!=null&&adList.data.records.length>0){
          this.queryBackgroundInfo = adList.data.records[0];
          this.queryBackgroundInfo.showStatus = adList.data.records[0].showStatus.code;
      }else{

          console.info({
            id: "",
            showStatus:"Y",
            pagePlateId:this.pagePlateId,
            pageComponentId:this.pageComponentId,
            dataType:"CUSTOM",
            image: "",
            linkUrl: ""
        })
         this.queryBackgroundInfo={
            id: "",
            showStatus:"Y",
            pagePlateId:this.pagePlateId,
            pageComponentId:this.pageComponentId,
            dataType:"CUSTOM",
            image: "",
            linkUrl: ""
        }
         
      }
    },
  },
  mounted() {
    this.pageInfoFun()
  },
  beforeDestroy() {}
};
</script>
<style lang="less" scoped>
.merchant_index{
     border: 1px dashed red;
    width:375px;
    background-size:contain;
}



.top{
    border-bottom: 1px solid #efefef;
    height:60px;
    padding: 0 15px;
}
.tipBox{
    margin-top: 16px;
    padding: 0 15px;
    width: 100%;
    p.title{
        font-size: 14px;
        height: 19px;
        font-size: 14px;
        font-weight: 400;
        margin:0;
    }
    p.tip{
        font-size: 14px;
        margin:0;
        height: 40px;
        font-family:  -400;
        font-weight: 400;
        color: #aaaaaa;
        line-height: 20px;

    }
}
.form{
     margin:0 auto;
     width:100%;
     padding:10px;
     position: relative;
    .formItem{margin-bottom: 5px;}
    .closeI{  
        opacity: 0;
        position: absolute;
        right: -18px;
        top: -25px;
        font-size: 20px;
        cursor: pointer;
    }
    .fromBox{padding-top:10px; max-height: calc(100vh - 320px); overflow-y: auto;}
    .setItemBox {border: 1px solid #efefef; padding:18px 10px; width:420px;}
    .setItemBox:hover .closeI{  
    opacity: 1;
}
}


.addbtn{
    cursor: pointer;
    margin:0 auto;
    text-align: center;
    margin-top: 16px;
    width:325px;
    height: 40px;
    line-height: 40px;
    background: #ffffff;
    border: 1px solid #409eff;
    font-size: 14px;
    font-weight: 400;
    color: #409eff;
}



.avatar-uploader{height:80px; width:80px;border: 1px solid #efefef; }
.avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 80px;
    height: 80px;
    line-height: 80px;
    text-align: center;
  }
  .avatar {
    width: 80px;
    height: 80px;
    display: block;
  }
  .inputItem{margin-bottom:12px;}
</style>