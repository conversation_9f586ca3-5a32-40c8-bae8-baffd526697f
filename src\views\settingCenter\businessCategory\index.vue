<template>
  <div class="businessScopePageContent">
    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        :tabs="[ { name: '经营类目' } ]"
        @change="handleChangeTab"
      >
        <template slot="button">
          <el-button   @click="onSearchSubmitFun">刷新</el-button>
          <!-- <el-button  type="primary" @click="editFun({id:0,whetherShowFrontend:{code: 'Y', desc:'是'}})">+新增经营类目</el-button> -->
        </template>
      </tabs-layout>
      <div class="table">
        <el-table
          ref="tableDom"
          v-loading="listLoading"
          :data="list"
          row-key="id"
          border
          fit
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column
            align="center"
            width="65"
            show-overflow-tooltip
            :render-header="renderHeader"
            fixed
          >
            <template slot-scope="scope">
              <span>{{scope.$index+1 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="categoryCode"  width="150" show-overflow-tooltip label="经营类目编码" ></el-table-column>
          <el-table-column prop="parentName"  width="250" show-overflow-tooltip label="经营类目类型" ></el-table-column>
          <el-table-column prop="name"  show-overflow-tooltip label="经营类目名称" ></el-table-column>
          <el-table-column fixed="right" align="center" label="操作" width="100" class="itemAction"  v-if="checkPermission(['admin','admin-setting-businessCategory:edit'])" >
            <template slot-scope="{row}">
              <el-row class="table-edit-row">
                <span class="table-edit-row-item">
                  <el-button @click="editFun(row)" type="text" >编辑</el-button>
                </span>
              </el-row>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="listQuery.current" :limit.sync="listQuery.size" @pagination="getList" />
      </div>
    </div>
    <!-- 设置 编辑 -->
    <edit v-if="showEdit" :visible.sync="showEdit" :isReload.sync="submitReload"   :row.sync="row"></edit>
    <!-- 设置 编辑 -->
  </div>
</template>
<script>
import checkPermission from "@/utils/permission";
import { getAllParentCodeOrChildren,list,deleteApi,query } from '@/api/setting/businessCategory'
import edit from '@/views/settingCenter/businessCategory/edit'
import Pagination from '@/components/Pagination'
import TabsLayout from '@/components/TabsLayout'

export default {
  data() {
    return {
      loadNodeMap:new Map(),
      showEdit:false,
      row:{},
      submitReload:false,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        model:{},
        current: 1,
        size: 10,
        order: "ascending",
        sort:"categoryCode,parentCategoryCode"
      },
    };
  },
  watch:  {
    submitReload:function(newVal,oldVal){
      if(newVal){
        this.submitReload=false;
        if(this.row.parentCategoryCode!=''&&this.row.parentCategoryCode!='0'&&this.row.parentCategoryCode!=null){
           this.reloadTreeNode(this.row)
        }else{
            this.onSearchSubmitFun()
        }
      }
    }
  },
  components: {
    Pagination,
    edit,
    TabsLayout
  },
  methods: {
    checkPermission,
    editFun:function(row){
        this.row=row;
        this.showEdit=true
    },
    renderHeader (h,{column}) {
      return (
        <div style="position:relative">
          <div>
            <i class="el-icon-menu" />
          </div>
        </div>
      )
    },
     setHeaer:function(){
      this.showSelectTitle=!this.showSelectTitle
    },
    onSearchSubmitFun: function() {
      this.list = [];
      this.getList();
    },
    editRootFun:function(row){
        this.row=row;
        this.newRoot=true
    },
    deleteRootFun(row){
      var _this=this;
      this.$confirm('此操作将永久删除该信息, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        _this.actionDeleteFun(row)
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    async actionDeleteFun(row) {
       let data = await deleteApi(row.id);
      if(data.code==0&&row.parentCategoryCode!=''&&row.parentCategoryCode!='0'&&row.parentCategoryCode!=null){
       this.reloadTreeNode(row)
      }else{
        this.onSearchSubmitFun()
      }
    },
    async getList() {
      this.listLoading = true
      const { data } = await query()
      // data.forEach(element => {
      //   element['hasChildren']=true
      // });
      this.list = data
      this.listLoading = false
    },
    async loadChildren(tree, treeNode, resolve) {
        this.loadNodeMap.set(tree.categoryCode, {tree, treeNode, resolve})
        const { data } = await getAllParentCodeOrChildren(tree.id)
        data.forEach(element => {
          element['hasChildren']=true
        });
        resolve(data)
    },
    async reloadTreeNode(row){
        const {tree, resolve} = this.loadNodeMap.get(row.parentCategoryCode);
        this.$set(this.$refs.tableDom.store.states.lazyTreeNodeMap, row.parentCategoryCode, []);
        const { data } = await getAllParentCodeOrChildren(row.parentId)
        data.forEach(element => {
          element['hasChildren']=true
        });
        resolve(data)
    }
  },
  mounted() {
      this.getList()
  },
  beforeDestroy() {}
};
</script>
<style lang="scss" scoped>
@import "@/styles/element-variables.scss";

.businessScopePageContent {
  padding: 0;
  // padding: 15px;
    .table{padding:12px;}
   .title{

        border-bottom:2px solid #EBECEE;
     margin-bottom: 16px;
      span{
        margin-bottom: -2px;
        padding:0 15px;
        height: 40px;
        line-height: 30px;
        display:block;
        background: rgba(255,255,255,0);
        border-bottom:2px solid rgb(64, 158, 255);
        font-size: 16px;
        font-family: 'PingFangSC-Regular', 'PingFang SC', 'PingFangSC-Regular', 'PingFang SC'-400;
        font-weight: 400;
        color:rgb(64, 158, 255);
      }
  }

  .formItem{width:586px;}
  .line{color:#dfe6ec; margin:0 6px;}
}
</style>
