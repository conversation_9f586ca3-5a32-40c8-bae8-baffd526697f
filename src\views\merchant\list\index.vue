<template>
  <div>
    <div class="search-wrapper">
      <search-pad @search="onSubmit" @reset="load">
        <el-form-item>
          <el-input placeholder="请输入客户编码" v-model="listQuery.model.code" />
        </el-form-item>
        <el-form-item>
          <el-input placeholder="请输入客户名称" v-model="listQuery.model.name" />
        </el-form-item>
        <el-form-item>
          <el-cascader
            placeholder="请选择所在区域"
            v-model="listQuery.model.address"
            :options="options"
            :props="{ checkStrictly: true,expandTrigger: 'hover',
                      value: 'id',
                      label: 'label',
                      children: 'children'}"
            @change="parentChangeAction"
            clearable
            style="width: 240px;"></el-cascader>
        </el-form-item>
        <el-form-item>
          <el-input placeholder="请输入企业负责人" v-model="listQuery.model.ceoName" />
        </el-form-item>
        <el-form-item>
          <el-select placeholder="请选择客户分组" v-model="listQuery.model.merchantGroup">
            <el-option v-for="item in list" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
      </search-pad>
    </div>
    <div class="tab_bg">
      <el-tabs v-model="activeName">
        <el-tab-pane label="客户档案" name="first"></el-tab-pane>
      </el-tabs>
      <table-pager ref="todoTable" :options="tableTitle" :data.sync="tableData" :remote-method="load" :selection="true" @selection-change="handleSelectionChange">
        <div slot-scope="props">
          <el-link @click="$router.push({ name: 'clientDetail', params: { id: props.row.purMerchantId } })">查看</el-link>
        </div>

      </table-pager>
    </div>
  </div>
</template>

<script>
import { merchantPurSaleRelList,findByUserIdSale,listMerchantGroupBySaleMerchantId,trees } from "@/api/group";
import Sortable from "sortablejs";
const TableColumns = [
  { label: "客户状态", prop: "publishStatus.desc",width: '80' },
  { label: "客户编码", prop: "code" },
  { label: "客户名称", prop: "name" },
  { label: "社会统一信用代码", prop: "socialCreditCode",width: '180' },
  { label: "法人代表", prop: "legalPerson" },
  { label: "企业类型", prop: "merchantType" },
  { label: "客户分组", prop: "merchantGroup" },
  { label: "联系人", prop: "ceoName" },
  { label: "联系电话", prop: "ceoMobile" },
  { label: "所在地区", prop: "region" },
  { label: "注册地址", prop: "registerAddress" },
  { label: "首营状态", prop: "firstCampStatus.desc" },
  { label: "创建时间", prop: "createTime" },
];
const TableColumnList = [];
for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({ key: i, ...TableColumns[i] });
}

export { TableColumnList };

export default {
  name: "DragTable",
  filters: {
    statusFilter(status) {
      const statusMap = {
        published: "success",
        draft: "info",
        deleted: "danger",
      };
      return statusMap[status];
    },
  },
  data() {
    return {
      showSelectTitle: false,
      tableTitle: TableColumnList,
      tableData: [],
      tableVal: [],
      list: [],
      total: 0,
      page: 0,
      listLoading: false,
      listQuery: {
        model: {
          saleMerchantId: '',
          ceoName: '',
          provinceId: '',
          cityId: '',
          countyId: '',
          merchantGroupId: '',
          merchantTypeId: '',
          purMerchantCode: ''
        },
        order: 'descending',
        sort: 'id',
      },
      form: {
        ceoName: '',
        name: '',
        code: '',
        address: '',
        merchantGroup: '',
        purMerchantCode: ''
      },
      sortable: null,
      oldList: [],
      newList: [],
      activeName: 'first',
      options: []
    };
  },
  created() {
/*    this.getSaleId()*/
    this.getArea()
  },
  methods: {
    onSubmit() {
      this.handleRefresh({
        page: 1,
        pageSize: 10
      })

    },
    handleSelectionChange() {

    },
    //客户分组
    async getList() {
      const { data } = await listMerchantGroupBySaleMerchantId(this.listQuery.model.saleMerchantId)
      this.list = data
    },
    //地区
    async getArea() {
      const { data } = await trees()
      this.options = data
    },
    parentChangeAction(val) {
      this.listQuery.model = {
            provinceId: val[0],
            cityId: val[1],
            countyId: val[2]
      }
    },
    // 获取经销商id
    async getSaleId() {
      let userId = localStorage.getItem('userId')
      const { data } = await findByUserIdSale(userId)
      this.saleMerchantId = data.id
      this.listQuery.model.saleMerchantId=data.id
      this.getList()
    },
    async load(params) {
      this.listLoading = true
      Object.assign(this.listQuery, params)
      return await merchantPurSaleRelList(this.listQuery)
    },
    handleRefresh(pageParams) {
      this.$refs.todoTable.doRefresh(pageParams)
    },
    handleSelectionChange(val) {
      this.ids = val.map(function(item,index) {
        return item.id;
      })

    },
    renderHeader(h, { column }) {
      // h即为cerateElement的简写，具体可看vue官方文档
      return (
        <div style="position:relative">
          <div onClick={this.setHeaer}>
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="640px"
            center
          >
            <el-transfer
              vModel={this.tableVal}
              data={this.tableTitle}
            ></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button type="primary" onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      );
    },

    setHeaer: function () {
      this.showSelectTitle = !this.showSelectTitle;
    },

    pagination(val) {
      this.listQuery.current = val.page
      this.listQuery.size = val.limit
      this.getList()
    },
    setSort() {
      const el = this.$refs.dragTable.$el.querySelectorAll(
        ".el-table__body-wrapper > table > tbody"
      )[0];
      this.sortable = Sortable.create(el, {
        ghostClass: "sortable-ghost", // Class name for the drop placeholder,
        setData: function (dataTransfer) {
          // to avoid Firefox bug
          // Detail see : https://github.com/RubaXa/Sortable/issues/1012
          dataTransfer.setData("Text", "");
        },
        onEnd: (evt) => {
          const targetRow = this.list.splice(evt.oldIndex, 1)[0];
          this.list.splice(evt.newIndex, 0, targetRow);

          // for show the changes, you can delete in you code
          const tempIndex = this.newList.splice(evt.oldIndex, 1)[0];
          this.newList.splice(evt.newIndex, 0, tempIndex);
        },
      });
    },
  },
};
</script>

<style>
.sortable-ghost {
  opacity: 0.8;
  color: #fff !important;
  background: #42b983 !important;
}
</style>

<style scoped>
</style>
