import requestAxios from '@/utils/requestAxios'
import request from '@/utils/request'

export function list(data) {
    return requestAxios({
        url: '/api/authority/parameter/anno/getPage',
        method: 'post',
        data
    })
}

export function parameterEdit(data,type){
    return requestAxios({
        url: type == 'add' ? '/api/authority/parameter/save' : '/api/authority/parameter/update',
        method: type == 'add' ? 'post' : 'put',
        data
    })
}

export function editApi(data) {
    return requestAxios({
        url: '/api/authority/parameter',
        method: data.id == 0 ? 'post' : 'put',
        data
    })
}

export function deleteApi(id) {
    return requestAxios({
        url: '/api/authority/parameter?ids[]=' + id,
        method: 'delete'
    })
}