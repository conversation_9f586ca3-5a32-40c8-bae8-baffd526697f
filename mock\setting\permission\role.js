const Mock = require('mockjs')

const List = []
const count = 100

const baseContent = '<p>I am testing data, I am testing data.</p><p><img src="https://wpimg.wallstcn.com/4c69009c-0fd4-4153-b112-6cb53d1cf943"></p>'
const image_uri = 'https://wpimg.wallstcn.com/e4558086-631c-425c-9430-56ffb46e70b3'

for (let i = 0; i < count; i++) {
    List.push(Mock.mock({
        code: '@string("lower", 5, 10)',
        describe: '@ctitle(5, 50)',
        dsType: 'ALL',
        endCreateTime: '@datetime',
        id: '@increment',
        name: '@cname',
        orgList: [],
        readonly:'@boolean',
        startCreateTime	: '@datetime',
        'status': '@boolean'
    }))
}

module.exports = [
    {
        url: '/api/authority/role/page',
        type: 'post',
        response: config => {
            const { model, current = 1, size = 10, sort } = config.body
            let mockList = List.filter(item => {
                // if (model.status == "") return true
                // else if (model.status.toLowerCase() !== item.status.toLowerCase()) return false
                // else return true
                return true
            })
            if (sort === '-id') {
                mockList = mockList.reverse()
            }

            const pageList = mockList.filter((item, index) => index < size * current && index >= size * (current - 1))

            return {
                code: 20000,
                data: {
                    total: mockList.length,
                    records: pageList
                }
            }
        }
    },
    {
        url: '/api/authority/role',
        type: 'delete',
        response: config => {
            return {
                code: 20000,
                data: ""
            }
        }
    },
    {
        url: '/api/authority/role',
        type: 'all',
        response: config => {
            const { id } = config.body
            for (const article of List) {
                if (article.id === +id) {
                    return {
                        code: 20000,
                        data: article
                    }
                }
            }
        }
    }
]
 