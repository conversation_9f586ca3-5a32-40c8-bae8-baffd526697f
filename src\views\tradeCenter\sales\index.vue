<template>
  <div>
    <div class="search-wrapper">
      <search-pad @search="searchLoad" @reset="reload">
        <el-form-item>
          <el-input placeholder="请输入订单编号" v-model="model.orderNo"/>
        </el-form-item>
        <el-form-item>
          <el-input placeholder="请输入客户名称" v-model="model.purMerchantName"/>
        </el-form-item>
        <el-form-item>
          <el-input placeholder="请输入下单人" v-model="model.userName"/>
        </el-form-item>
        <el-form-item>
          <el-input placeholder="请输入下单人手机号" v-model="model.mobile"/>
        </el-form-item>
        <el-form-item>
          <el-date-picker
            type="daterange"
            range-separator="至"
            v-model="model.during"
            value-format="yyyy-MM-dd"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>
       <!-- <el-form-item>
          <el-select v-model="model.orderChannel" placeholder="下单渠道" clearable @change="getType">
            <el-option value="PC" label="PC"/>
            <el-option value="APP" label="APP"/>
            <el-option value="WECHAT" label="小程序"/>
          </el-select>
        </el-form-item>-->
      </search-pad>
    </div>
    <div class="tab_bg">
      <div class="varietiesBan-list-container">
        <div class="varietiesBan-list-tabs-wrapper">
          <div class="varietiesBan-list-tabs">
            <div class="tab" :class="{'active': currentTab == index}" v-for="(tab, index) in tabs"
                 :key="index"
                 v-if="checkPermission(['admin',tab.permission])"
                 @click="handleChangeTab(index,tab.value)">
              {{ tab.name }}（{{tab.count}}）
            </div>
          </div>
          <div class="operations">
            <!-- <el-button  v-if="checkPermission(['admin','order:export'])">导出单据</el-button> -->
            <el-button  @click="reload">刷新</el-button>
            <el-button type="primary"  @click="$router.push({path: '/tradeCenter/index'})" v-if="checkPermission(['admin','order:buy'])">+ 代客下单</el-button>
          </div>
        </div>
      </div>
      <table-pager ref="todoTable" :options="tableTitle" :remote-method="load" :data.sync="tableData">
        <template slot="orderChannel">
          <el-table-column label="下单渠道" :formatter="channelFormatter" width="150" show-overflow-tooltip></el-table-column>
        </template>
        <template slot="orderStatus.desc">
          <el-table-column label="订单状态" :formatter="statusFormatter"></el-table-column>
        </template>
        <div slot-scope="props" v-if="checkPermission(['admin','order:detail'])">
          <el-button type="text" @click="$router.push({path: '/sale/detail',query:{id: props.row.id}})">查看详情</el-button>
        </div>

      </table-pager>
    </div>
  </div>
</template>

<script>
  const TableColumns = [
    { label: "订单编号", name: "orderNo",prop: "orderNo",width: '190'},
    { label: "客户名称", name: "purMerchantName", prop:'purMerchantName',width: '220' },
    { label: "订单商品数", name: "totalNum",prop: 'totalNum',width: '95' },
    { label: "订单总金额", name: "totalMoney", prop:'totalMoney',width: '95'  },
    { label: "实付总金额", name: "totalRealMoney", prop:'totalRealMoney',width: '95'  },
    { label: "订单状态", name: "orderStatus.desc",prop: 'orderStatus.desc',slot: true},
    { label: "支付状态", name: "payStatus.desc",prop: 'payStatus.desc'},
    { label: "下单渠道", name: "orderChannel",prop: 'orderChannel',slot: true },
    { label: "收货人", name: "receiverName",prop:'receiverName' },
    { label: "收货人手机", name: "receiverPhone",prop:'receiverPhone',width: '115'  },
    { label: "下单人", name: "orderUserName",prop: 'orderUserName',width: '100'},
    { label: "下单人手机", name: "orderUserPhone",prop: 'orderUserPhone',width: '115'},
    { label: "下单时间", name: "orderCreateTime",prop: 'orderCreateTime',width: '160'},
    { label: "订单备注", name: "remark",prop: 'remark'},
  ];
  const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({ key: i, ...TableColumns[i] });
  }
  import {orderList,orderStatistics } from '@/api/trade'
  import checkPermission from '@/utils/permission';
  export default {
    data () {
      return {
        loading: '',
        search: '',
        controlType: '',
        currentTab: 0,
        tabs: [
          { name: '全部', value: 'ALL',count: 0,permission: 'order-all:view',countName: 'all' },
          { name: '待审核', value: 'PENDING',count:0,permission: 'order-pending:view',countName: 'pending' },
          { name: '待付款', value: 'WAIT_PAY',count: 0,permission: 'order-waitpay:view',countName: 'waitPay' },
          { name: '待发货', value: 'WAIT_DELIVERY',count: 0,permission: 'order-waitDelivery:view',countName: 'waitDelivery' },
          { name: '发货中', value: 'PART_DELIVERY',count: 0,permission: 'order-partDelivery:view',countName: 'partDelivery' },
          { name: '已发货', value: 'HAD_DELIVERY',count: 0,permission: 'order-hadDelivery:view',countName: 'hadDelivery' },
          { name: '已完成', value: 'SUCCESS',count: 0,permission: 'order-success:view',countName: 'finish' },
          { name: '已取消', value: 'CANCEL',count: 0,permission: 'order-cancel:view',countName:'cancel' },
        ],
        tableData: [],
        page: 1,
        pageSize: 10,
        totalPage: 0,
        total: 0,
        tableTitle: TableColumnList,
        model: {
          orderNo: '',
          purMerchantName: '',
          userName: '',
          mobile: '',
          during: '',
          orderStatus: ''
        },
        products: [],
        ids: []
      }
    },
    mounted() {
      this.getCount()
    },
    methods: {
      checkPermission,
      //tab数量
      async getCount() {
        const {data} = await orderStatistics()
        this.tabs.forEach(item=> {
          item.count = data[item.countName]
        })
      },
      channelFormatter(row) {
        if(row.orderChannel === 'wechat') {
          return '小程序'
        } else {
          return row.orderChannel
        }
      },
      statusFormatter(row) {
        if(row.orderStatus.code === 'WAIT_PROCESS') {
            return row.orderAuditStatus.desc
        } else {
            return row.orderStatus.desc
        }
      },
      async load(params) {
        let listQuery = {
          model: {
            purMerchantName: this.model.purMerchantName === ''?null:this.model.purMerchantName,
            orderNo: this.model.orderNo,
            userName: this.model.userName === '' ? null : this.model.userName,
            mobile: this.model.mobile === '' ? null : this.model.mobile,
            startTime: this.model.during[0],
            endTime: this.model.during[1],
            orderChannel: this.model.orderChannel,
            orderStatus: this.model.orderStatus
          }
        }
        Object.assign(listQuery, params)
        this.loading = true
        return await orderList(listQuery)
      },
      reload() {
        this.currentTab = 0
        this.model = {
          purMerchantName: '',
          orderNo: '',
          salesmanName: '',
          salesmanMobile: '',
          during: ''
        }
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      handleChangeTab (index,value) {
        this.currentTab = index
        if(value === 'ALL') {
          this.model.orderStatus = ''
        } else {
          this.model.orderStatus = value
        }
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      searchLoad() {
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      getType(val) {
        this.model.orderChannel = val
      },
      handleRefresh(pageParams) {
        this.$refs.todoTable.doRefresh(pageParams)
      }
    }
  }
</script>

<style lang="scss" scoped>
</style>
