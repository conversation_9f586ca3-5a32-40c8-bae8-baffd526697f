<template>
  <div class="temp_ProductTableButton">
    <el-button style="width:120px;padding:0 -10px; text-indent: -10px;" @click="showProductTableDialogFun()">选择商品分类</el-button>
    <el-dialog v-if="showProductTypeTableDialog==true" append-to-body title="商品列表" :visible.sync="showProductTypeTableDialog" :before-close="closeDialogFun" :close-on-click-modal="false" width="80%" :show-close="false">
      <div style="position:absolute;top:10px; right:15px; background:#fff; height:38px;">
        <div>
          <el-button @click="clearFun()">取 消</el-button>
        </div>
      </div>
      <div class="showProductTable">
        <el-table
          ref="tableDom"
          v-loading="listLoading"
          :data="list"
          row-key="id"
          border
          fit
          :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
          lazy
          :load="load"
          max-height="650"
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column type="" prop="categoryCode" align="center" label="分类编码" width="180" />
          <el-table-column prop="label" label="分类名称" show-overflow-tooltip />
          <el-table-column align="center" prop="pictIdS" label="图片" width="100">
            <template slot-scope="{row}">
              <el-popover v-if="row.pictIdS!=''" placement="right" trigger="hover">
                <el-image
                  style="width: 200px; height: 200px"
                  fit="contain"
                  :src="row.pictIdS | imgFilter"
                />
                <el-image
                  slot="reference"
                  style="width: 30px; height: 30px"
                  fit="cover"
                  :src="row.pictIdS | imgFilter"
                />
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column prop="sortValue" label="排序" sortable width="100" />
          <el-table-column align="center" prop="whetherShowFrontend" label="前端显示" width="120">
            <template slot-scope="{row}">
              <span v-if="row['whetherShowFrontend']==null" />
              <span v-else-if="row['whetherShowFrontend'].code == 'Y'" class="el-tag el-tag--success">{{ row['whetherShowFrontend'].desc }}</span>
              <span v-else class="el-tag el-tag--danger">{{ row['whetherShowFrontend'].desc }}</span>
            </template>
          </el-table-column>
          <el-table-column align="right" label="操作" width="100" class="itemAction">
            <template slot-scope="scope">
              <el-button type="text" @click="selectProductTypeItemFun(scope.row)">选中</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { list, getChildren, deleteApi } from '@/api/products/categoryPlatform'
export default {
  props: {
    selectItems: {
      type: String,
      default: '',
      required: true
    }
  },
  data() {
    return {
      loadNodeMap: new Map(),
      showProductTypeTableDialog: false,
      listQuery: {
        model: {
          whetherOnSale: 'Y',
          approvalStatus: 'ACCEPTED'
        },
        current: 1,
        size: 10
      },
      selectRowItems: [],
      list: [],
      total: 0,
      listLoading: true
    }
  },
  computed: {
    selectRowVal: {
      get() {
        this.selectItems
      },
      set(val) {
        this.$emit('update:selectItems', val)
        this.$emit('confirm')
      }
    }
  },
  mounted() {
  },
  beforeDestroy() {},
  methods: {
    clearFun() {
      this.listQuery = {
        model: {
          whetherOnSale: 'Y',
          approvalStatus: 'ACCEPTED'
        },
        current: 1,
        size: 10
      },
      this.list = [],
      this.total = 0,
      this.listLoading = true,
      this.showProductTypeTableDialog = false
    },
    closeDialogFun() {
      this.showProductTypeTableDialog = false
    },
    showProductTableDialogFun() {
      this.showProductTypeTableDialog = true
      this.getList()
    },
    async getList() {
      this.listLoading = true
      this.listQuery.model = { ...this.listQuery.model, commerceModel: "SAAS_PLATFORM" }
      const { data } = await list(this.listQuery)
      data.records.forEach(element => {
        element['pName'] = element.label
        element['path'] = element.categoryCode
        element['hasChildren'] = true
        element['level'] = 0
      })
      this.list = data.records
      this.total = data.total
      this.listLoading = false
    },
    async load(tree, treeNode, resolve) {
      this.loadNodeMap.set(tree.id, { tree, treeNode, resolve })
      const formData = new FormData()
      formData.append('id', tree.id)
      const { data } = await getChildren(formData)
      data.forEach(element => {
        element['pName'] = tree.pName + '/' + element.label
        element['path'] = tree.categoryCode + '/' + element.categoryCode
        element['hasChildren'] = (tree.level + 1) < 3
        element['level'] = tree.level + 1
      })
      resolve(data)
    },
    selectProductTypeItemFun(item) {
      this.selectRowVal = item.categoryCode
      this.$emit('confirm')
      this.clearFun()
    }
  }
}
</script>
<style lang="less" scoped>
.temp_ProductTableButton{width:100%;

}
.showProductTable{
    margin: -30px -20px;
    border-top: 1px solid #ebecee;
    padding: 10px 20px;
}
.temp_searchBox{height:45px;overflow: hidden; margin-bottom: 0;  padding:0; border-left:none;}

</style>
