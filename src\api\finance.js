import requestAxios from '@/utils/requestAxios'
import qs from 'qs'
//商家业务账单
export function financeBillOrder(query) {
  return requestAxios({
    url: `/api/finance/admin/financeBillOrder/page`,
    method: 'post',
    data: query
  })
}
//一键结算
export function merchantSettlement(merchantsId) {
  return requestAxios({
    url: `/api/finance/admin/financeBillOrder/settlement/${merchantsId}`,
    method: 'get'
  })
}
//批量结算
export function batchSettlement(data) {
  return requestAxios({
    url: `/api/finance/admin/financeBillOrder/batchSettlement`,
    method: 'post',
    params: data
  })
}
//商家业务账单详情
export function financeBillOrderDetail(query) {
  return requestAxios({
    url: `/api/finance/admin/financeBillOrderDetail/page`,
    method: 'post',
    data: query
  })
}
//详情初始化
export function fboCount(query) {
  return requestAxios({
    url: `/api/finance/admin/financeBillOrderDetail/countSta`,
    method: 'post',
    data: query
  })
}
//详情tab状态统计
export function fboDetailCountStatus(query) {
  return requestAxios({
    url: `/api/finance/admin/financeBillOrderDetail/countStatus`,
    method: 'post',
    data: query
  })
}
//商家详情里面批量结算
export function fboDetailBatchSettlement(data) {
  return requestAxios({
    url: `/api/finance/admin/financeBillOrderDetail/batchSettlement`,
    method: 'post',
    params: data
  })
}

//业务员账单
export function financeBillProduct(query) {
  return requestAxios({
    url: `/api/finance/admin/financeBillProduct/page`,
    method: 'post',
    data: query
  })
}
//批量结算
export function productBatchSettlement(data) {
  return requestAxios({
    url: `/api/finance/admin/financeBillProduct/batchSettlement`,
    method: 'post',
    headers: {
      'Content-type': 'application/x-www-form-urlencoded'
    },
    data:qs.stringify(data)
  })
}
//一键结算
export function saleSettlement(salesmanId) {
  return requestAxios({
    url: `/api/finance/admin/financeBillProduct/settlement/${salesmanId}`,
    method: 'get'
  })
}
//业务员详情列表
export function financeBillProductDetail(query) {
  return requestAxios({
    url: `/api/finance/admin/financeBillProductDetail/page`,
    method: 'post',
    data: query
  })
}
//业务员详情tab状态统计
export function fbpDetailCountStatus(query) {
  return requestAxios({
    url: `/api/finance/admin/financeBillProductDetail/countStatus`,
    method: 'post',
    data: query
  })
}
//账单初始化
export function fbpCount(query) {
  return requestAxios({
    url: `/api/finance/admin/financeBillProductDetail/countSta`,
    method: 'post',
    data: query
  })
}

//详情批量结算
export function fbpDetailBatchSettlement(data) {
  return requestAxios({
    url: `/api/finance/admin/financeBillProductDetail/batchSettlement`,
    method: 'post',
    params: data
  })
}
//商家结算单管理
export function financeSettlementOrder(query) {
  return requestAxios({
    url: `/api/finance/admin/financeSettlementOrder/page`,
    method: 'post',
    data: query
  })
}
//商家结算单tab状态统计
export function fsoDetailCountStatus(query) {
  return requestAxios({
    url: `/api/finance/admin/financeSettlementOrder/countStatus`,
    method: 'post',
    data: query
  })
}

//业务员结算单
export function financeSettlementProduct(query) {
  return requestAxios({
    url: `/api/finance/admin/financeSettlementProduct/page`,
    method: 'post',
    data: query
  })
}
//业务员结算单tab状态统计
export function fspDetailCountStatus(query) {
  return requestAxios({
    url: `/api/finance/admin/financeSettlementProduct/countStatus`,
    method: 'post',
    data: query
  })
}
//金额统计
export function countSettlement(data) {
  return requestAxios({
    url: `/api/finance/admin/financeBillOrderDetail/countSettlement`,
    method: 'post',
    params: data
  })
}
//业务员金额统计
export function salesmanCountSettlement(data) {

  return requestAxios({
    url: `/api/finance/admin/financeBillProductDetail/countSettlement`,
    method: 'post',
    params: data
  })
}
