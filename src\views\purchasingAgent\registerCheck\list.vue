<template>
  <!-- 修改成功！ -->
  <div class="archivesPageContent">
    <im-search-pad
      :has-expand="false"
      :model="listQuery"
      @reset="resetForm"
      @search="getlist"
    >
      <im-search-pad-item prop="purMerchantName">
        <el-input v-model="listQuery.model.purMerchantName" placeholder="请输入客户名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="merchantTypeId">
        <el-select v-model="listQuery.model.merchantTypeId" placeholder="请选择企业类型">
          <el-option v-for="item in listmerchantType" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item prop="cityValue">
        <el-cascader placeholder="请选择所在区域"  v-model="cityValue" :props="props" @change="cityChange " clearable />
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        v-model="listQuery.model.approvalStatus.code"
        :tabs="approvalStatusList"
        @change="chageTabsFun"
      >
        <template slot="button">
          <div>
            <el-button v-if="listQuery.model.approvalStatus.code== 'PENDING' && checkPermission(['admin', 'registercheck:batchRejection'])" :disabled="multipleSelectionIds.length== 0" @click="updatePurMerchantApplyRejected">批量驳回</el-button>
            <el-button v-if="listQuery.model.approvalStatus.code== 'PENDING' && checkPermission(['admin', 'registercheck:batchAudit'])" :disabled="multipleSelectionIds.length== 0" @click="updatePurMerchantApplyAccepted">批量通过</el-button>
            <el-button v-if="listQuery.model.approvalStatus.code== 'REJECTED' && checkPermission(['admin', 'registercheck:batchPending'])" :disabled="multipleSelectionIds.length== 0" @click="updatePurMerchantApplyPending">批量再审</el-button>
            <!-- <el-button @click="outExcel" :disabled="multipleSelectionIds.length== 0">导出档案</el-button> -->
            <el-button @click="refresh">刷新</el-button>
          </div>
        </template>
      </tabs-layout>
      <div class="table">
        <el-table v-if="list" @selection-change="selectTableItemFun" v-loading="listLoading" :data="list" row-key="id" border fit highlight-current-row style="width: 100%">
          <el-table-column align="center" width="65" :render-header="renderHeader" fixed>
            <template slot-scope="scope">
              <span>{{ scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column type="selection" width="55" align="center" fixed></el-table-column>
          <el-table-column v-for="(item, index) in tableTitle" :key="index" :min-width="(item.width?item.width:'350px')" :label="item.label" show-overflow-tooltip align="left">
            <template slot-scope="{row}">
              <el-button v-if="item.name=='publishStatus'&&row[item.name].code=='Y'" type="text" style="color:#409EFF">已启用</el-button>
              <el-button v-else-if="item.name=='publishStatus'&&row[item.name].code=='N'" type="text" style="color:#FF3C54">已冻结</el-button>
              <span v-else>{{ row[item.name] }}</span>
            </template>
          </el-table-column>

          <el-table-column fixed="right" align="center" label="操作" width="150" class="itemAction">
            <template slot-scope="scope">
              <el-row class="table-edit-row">
                <span v-if="checkPermission(['admin', 'registercheck:detail'])" class="table-edit-row-item">
                  <el-button @click="detailFun(scope.row)" type="text" >查看详情</el-button>
                </span>
              </el-row>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-if="total>0" :pageSizes="[2, 10, 20, 50]" :total="total" :page.sync="listQuery.current" :limit.sync="listQuery.size" @pagination="getlist" />
      </div>
    </div>

  </div>
</template>

<script>
import { setContextData, getContextData } from "@/utils/auth";
import checkPermission from '@/utils/permission'
import Pagination from "@/components/Pagination";
import {
  list,
  updatePurMerchantApplyPending,
  updatePurMerchantApplyAccepted,
  getPurApplyMerhcantCount,
  updatePurMerchantApplyRejected,
} from "@/api/purchasingAgent/registercheck";
import { merchantType } from "@/api/purchasingAgent/archivesList";
import tableInfo from "@/views/purchasingAgent/registerCheck/tableInfo";
import { downloadFile } from "@/utils/commons";
import { areas } from "@/api/enterprise";
import TabsLayout from '@/components/TabsLayout'

export default {
  components: {
    Pagination,
    TabsLayout
  },
  data() {
    return {
      arealist: [],
      props: {
        lazy: true,
        checkStrictly: true,
        async lazyLoad(node, resolve) {
          const { level } = node;
          let id = node.data ? node.data.id : "";
          let res = await areas({ parentId: id });
          let list = res.data;
          list.forEach((item) => {
            item.value = item.id;
            item.leaf = level >= 2;
          });
          resolve(list);
        },
      },
      cityValue: [],
      list: [],
      total: 0,
      listLoading: true,
      multipleSelectionIds: [],
      multipleSelection: [],
      listmerchantType: [],
      tableTitle: [],
      submitReload: false,
      pageCount: {
        pendingCount: 0,
        acceptedCount: 0,
        rejectedCount: 0,
        expireCount: 0,
      },
      listQuery: {
        page: 1,
        size: 10,
        model: {
          approvalStatus: { code: "PENDING" },
        },
      },
      showEditPage: false,
      row: {},
      tableSelectTitle: [0, 1, 2, 3],
      showSelectTitle: false,
    };
  },
  computed: {
    approvalStatusList() {
      return [
        {
          name: '待审核' + ((+this.pageCount.pendingCount) > 0 ? '(' + this.pageCount.pendingCount + ')' : ''),
          value: 'PENDING',
          hide: !checkPermission(['admin','registercheck-pending:view'])
        },
        {
          name: '已审核' + ((+this.pageCount.acceptedCount) > 0 ? '(' + this.pageCount.acceptedCount + ')' : ''),
          value: 'ACCEPTED',
          hide: !checkPermission(['admin','registercheck-accepted:view'])
        },
        {
          name: '已驳回' + ((+this.pageCount.rejectedCount) > 0 ? '(' + this.pageCount.rejectedCount + ')' : ''),
          value: 'REJECTED',
          hide: !checkPermission(['admin','registercheck-rejected:view'])
        }
      ]
    }
  },
  methods: {
    checkPermission,
    refresh() {
      this.listQuery ={
        ...this.listQuery,
        current: 1,
        size: 10
      }
      this.cityValue = []
      this.initTbaleTitle()
      this.getlist()
    },
    cityChange(e) {
      this.listQuery.model.provinceId = e[0];
      this.listQuery.model.cityId = e[1];
      this.listQuery.model.countyId = e[2];
    },
    checkSelect() {
      if (this.multipleSelectionIds.length < 1) {
        this.$alert("请至少选择一个采购商");
        return true;
      }
    },
    selectTableItemFun(val) {
      let arr = [];
      val.forEach((item) => {
        arr.push(item.purMerchantApplyId);
      });
      this.multipleSelectionIds = arr;
      this.multipleSelection = val;
    },
    async getPurApplyMerhcantCount() {
      let { data } = await getPurApplyMerhcantCount();
      this.pageCount = data;
    },
    setSucss(data, type, msg) {
      if (!data) {
        return false;
      }
      // this.listQuery.model.approvalStatus.code = type;
      this.$message.success(msg);
      this.getlist();
    },
    // 批量待审
    async updatePurMerchantApplyPending() {
      this.$confirm("此操作将批量再审商家，是否继续？","提示",{
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: 'warning',
      }).then(async ()=>{
        if (this.checkSelect()) return 0;
        let { data } = await updatePurMerchantApplyPending({
          'ids[]': this.multipleSelectionIds.toString(),
        });
        this.setSucss(data, "PENDING", "批量待审成功！");
      })
    },
    // 批量审核
    async updatePurMerchantApplyAccepted() {
      this.$confirm("此操作将审核通过采购商申请，是否继续？","提示",{
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: 'warning',
      }).then(async ()=>{
        if (this.checkSelect()) return 0;
        let { data } = await updatePurMerchantApplyAccepted({
          'ids': this.multipleSelectionIds.toString(),
        });
        this.setSucss(data, "ACCEPTED", "批量审核成功！");
      })
    },
    // 批量驳回
    async updatePurMerchantApplyRejected() {
      this.$confirm("此操作将审核驳回采购商申请，是否继续？","提示",{
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: 'warning',
      }).then(async ()=>{
        if (this.checkSelect()) return 0;
        let { data } = await updatePurMerchantApplyRejected({
          'ids[]': this.multipleSelectionIds.toString(),
        });
        this.setSucss(data, "REJECTED", "批量驳回成功！");
      })
    },
    // 获取商家类型
    async getmerchantType() {
      let { data } = await merchantType();
      this.listmerchantType = data;
    },
    async getlist() {
      this.list = []
      this.getPurApplyMerhcantCount();
      this.listLoading = true;
      const { data } = await list(this.listQuery);
      this.list = data.records;
      this.total = data.total;
      this.listLoading = false;
    },

    chageTabsFun: function () {
      this.list = [];
      this.listQuery.current =1
      this.initTbaleTitle();
      this.getlist();
    },
    resetForm() {
      this.cityValue = []
      this.listQuery = {
        page: 1,
        size: 10,
        model: {
          approvalStatus: { code: this.listQuery.model.approvalStatus.code },
        },
      };
      this.getlist();
    },
    async detailFun(item) {
      setContextData('registerCheck_list', this.listQuery)
      this.$router.push({
        path: "/purchasingAgent/registerCheck/detail",
        query: {
          id: item.purMerchantApplyId,
          tabType: this.listQuery.model.approvalStatus.code,
        },
      });
    },
    //  导出档案
    async outExcel() {
      if (this.multipleSelection.length > 0) {
        const tHeader = ["id"];
        const filterVal = ["purMerchantApplyId"];
        this.tableTitle.forEach(function (item) {
          tHeader.push(item.label);
          filterVal.push(item.name);
        });
        let exportData = this.formatJson(this.multipleSelection, filterVal);
        downloadFile({
          tHeader: tHeader,
          fileName: "采购商注册审批列表",
          exportData: exportData,
        });
      } else {
        this.$message.error("请在注册审批列表中勾选需要导出的采购商");
      }
    },
    formatJson(dataList, filterVal) {
      return dataList.map((v) =>
        filterVal.map((j) => {
          if (j === "publishStatus") {
             return v[j].code =="Y" ? '已启用' : '已冻结';
          } else {
            return v[j];
          }
        })
      );
    },
    initTbaleTitle() {
      this.tableTitle = tableInfo[this.listQuery.model.approvalStatus.code];
      this.tableSelectTitle = [];
    },
    renderHeader(h, { column }) {
      var titles = tableInfo[this.listQuery.model.approvalStatus.code];
      var titlesName = ["显示字段项", "隐藏字段项"];
      return (
        <div style="position:relative">
          <div onClick={this.showHeaer}>
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="640px"
            center
            append-to-body={true}
          >
            <el-transfer
              vModel={this.tableSelectTitle}
              data={titles}
              titles={titlesName}
              onChange={this.setleftTitleFun}
            ></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button onClick={this.closeHeaer}>取消</el-button>
              <el-button type="primary" onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      );
    },
    setleftTitleFun(val) {
      this.tableSelectTitle = val;
    },
    showHeaer: function () {
      this.showSelectTitle = true;
    },
    closeHeaer: function () {
      this.showSelectTitle = false;
      this.tableSelectTitle = [];
    },
    setHeaer: function () {
      var titles = tableInfo[this.listQuery.model.approvalStatus.code];
      var listinfo = titles.filter((element, index, self) => {
        return !this.tableSelectTitle.includes(element.key);
      });
      this.tableTitle = listinfo;
      this.showSelectTitle = !this.showSelectTitle;
    },
  },
  created() {
    this.getmerchantType();
  },
  mounted() {
    this.initTbaleTitle();
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (from.path == "/purchasingAgent/registerCheck/detail") {
        if (getContextData("registerCheck_list") != "") {
          vm.listQuery = getContextData("registerCheck_list");
        }
      }
      vm.initTbaleTitle();
      vm.getlist();
    });
  },
  watch: {
    submitReload(newVal, oldVal) {
      if (newVal) {
        this.submitReload = false;
        this.listQuery.model.approvalStatus.code = this.listQuery.model.approvalStatus.code;
        this.getlist();
      }
    },
  }
};
</script>

<style lang="scss" scoped>
.archivesPageContent {
  padding: 0;
  .temp_searchBox {
    height: 64px;
    overflow: hidden;
    margin-bottom: 0;
  }
  .form-inline {
    height: 60px;
    overflow: hidden;
  }
  .title {
    border-bottom: 2px solid #ebecee;
    margin-bottom: 16px;
    span {
      margin-bottom: -2px;
      padding: 0 15px;
      height: 40px;
      line-height: 30px;
      display: block;
      background: rgba(255, 255, 255, 0);
      border-bottom: 2px solid rgb(64, 158, 255);
      font-size: 16px;
      font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
        "PingFang SC"-400;
      font-weight: 400;
      color: rgb(64, 158, 255);
    }
  }
  .formItem {
    width: 586px;
  }
  .line {
    color: #dfe6ec;
    margin: 0 6px;
  }
  .typeTabs {
    height: 40px;
    margin-bottom: -2px;
    margin-left: 30px;
  }
}
</style>
