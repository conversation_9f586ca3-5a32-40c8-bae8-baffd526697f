<template>
  <div>
    <div class="search-wrapper">
      <search-pad @search="searchLoad" @reset="reload">
        <el-form-item>
          <el-input placeholder="请输入业务单号" v-model="model.businessNo"/>
        </el-form-item>
        <el-form-item>
          <el-input placeholder="请输入申请人" v-model="model.applicantUser"/>
        </el-form-item>
        <el-form-item>
          <el-date-picker
            type="daterange"
            range-separator="至"
            v-model="during"
            value-format="yyyy-MM-dd"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>
      </search-pad>
    </div>
    <div class="tab_bg">
      <div class="varietiesBan-list-container">
        <div class="varietiesBan-list-tabs-wrapper">
          <div class="varietiesBan-list-tabs">
            <div class="tab" :class="{'active': currentTab == index}" v-for="(tab, index) in tabs"
                 :key="index"
                 v-if="checkPermission(['admin',tab.permission])"
                 @click="handleChangeTab(index,tab.value)">
              {{ tab.name }}（{{tab.count}}）
            </div>
          </div>
          <div class="operations">
            <!-- <el-button  v-if="checkPermission(['admin','withdraw:export'])">导出</el-button> -->
            <el-button  @click="reload">刷新</el-button>
          </div>
        </div>
      </div>

      <table-pager ref="bussinessTabel" :options="tableTitle" :remote-method="load" :data.sync="tableData" :pageSize="pageSize" :selection="isWait" :isNeedButton="isWait"  @selection-change="handleSelectionChange">
        <template slot="amount">
          <el-table-column label="金额（元）" width="100">
            <slot slot-scope="{row}">
              {{row.amount|getDecimals}}
            </slot>
          </el-table-column>
        </template>
        <template slot="paymentId">
          <el-table-column label="收款单号" width="160">
            <slot slot-scope="{row}">
              <span class="text-primary">{{row.paymentId}}</span>
            </slot>
          </el-table-column>
        </template>
        <template slot="reviewUser" v-if="model.businessStatus==='RETECT'">
          <el-table-column label="取消人" width="160" prop="reviewUser" />
        </template>
        <template slot="reviewTime" v-if="model.businessStatus==='RETECT'">
          <el-table-column label="取消时间" width="160" prop="reviewTime" />
        </template>
        <template slot="remarks" v-if="model.businessStatus==='RETECT'">
          <el-table-column label="取消原因" width="160" prop="remarks" />
        </template>

        <div slot-scope="props">
          <el-button type="text" v-if="checkPermission(['admin','withdraw:verify'])" @click="handleVerify(props.row.id)">审核</el-button>
        </div>
      </table-pager>
    </div>
    <el-dialog title="取消充值单原因" :visible.sync="verifyVisible" width="500px">
      <el-form :model="vForm">
        <el-form-item>
          <el-input placeholder="请输入备注信息" v-model="vForm.remarks" type="textarea" rows="4"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="verifyVisible = false">取 消</el-button>
        <el-button @click="submit(3)">驳 回</el-button>
        <el-button type="primary" @click="submit(1)">审核通过</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  const TableColumns = [
    { label: "业务单号", name: "reviewUser",prop: "type.desc",width: "150"},
    { label: "业务类型", name: "id", prop:"id",width: "170" },
    { label: "业务单状态", name: "orderNumber", prop:"orderNumber",width: "180" },
    { label: "申请人", name: "settlementOrderId", prop:"settlementOrderId" },
    { label: "金额（元）", name: "amount", prop:"amount",slot: true  },
    { label: "制单人", name: "createUserName",prop:'createUserName',width: "100" },
    { label: "制单时间", name: "createTime",prop:'createTime',width: "170" },
    { label: "收款单号", name: "paymentId", prop:"paymentId",slot: true  },
    { label: "取消人", name: "reviewUser", prop:"reviewUser",slot: true  },
    { label: "取消时间", name: "reviewTime", prop:"reviewTime",slot: true },
    { label: "取消原因", name: "remarks", prop:"remarks",slot: true  },
  ];
  const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({ key: i, ...TableColumns[i] });
  }
  import checkPermission from "../../../utils/permission";
  import { statistics,list,updateCashRemarks } from '@/api/finance/withdraw/index.js'
  export default {
    data () {
      return {
        loading: '',
        currentTab: 0,
        tabs: [
          { name: '充值中', value: 'WAIT',count: 0,permission: 'rechange-wait:view' },
          { name: '已完成', value: 'FINISH',count: 0,permission: 'rechange-finish:view'  },
          { name: '已取消', value: 'REJECT',count: 0,permission: 'rechange-reject:view'  }
        ],
        tableData: [],
        page: 1,
        pageSize: 10,
        totalPage: 0,
        total: 0,
        tableTitle: TableColumnList,
        ids: [],
        during: '',
        model: {
          businessNo: '',
          businessStatus: 'WAIT',
          applicantUser: '',
        },
        verifyVisible: false,
        isWait: true,//操作列
        vForm: {
          remarks: '',
          id: ''
        }
      }
    },
    mounted() {
      this.getCount()
    },
    methods: {
      checkPermission,
      async getCount() {
        const query = {
          merchantsId: this.$route.params.merchantsId
        }
        const {data} = await statistics(query)
        this.tabs.forEach(item=>{
          item.count = data[item.value.toLowerCase()]
        })
      },
      handleSelectionChange(val) {

      },
      handleVerify(id) {
        this.vForm.id = id
      },
      async submit(status) {
        const param = {
          ...this.vForm,
          status: status //1,//状态 1 -> 通过 3 -> 驳回
        }
        const {data} = await updateCashRemarks(param)
        this.$message.success('该提现单审核操作完成！')
        this.verifyVisible = false
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      async load(params) {
        const listQuery = {
          model: {
            ...this.model,
            startTime: this.during[0],
            endTime: this.during[1]
          }
        }
        Object.assign(listQuery, params)
        this.loading = true
        const {data} = await list(listQuery)
        this.todoName = data.records[0].merchantName
        this.loading = false
        return { data }
      },
      searchLoad() {
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      reload() {
        this.model={}
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      handleChangeTab(index,value) {
        this.currentTab = index
        this.model.businessStatus = value
        if(value !== 'WAIT'){
          this.isWait = false
        } else {
          this.isWait = true
        }
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      handleRefresh(pageParams) {
        this.$refs.bussinessTabel.doRefresh(pageParams)
      }
    }
  }
</script>

<style lang="less" scoped>
  .tab_bg{
    border-top: solid 15px #f2f3f4 ;
    height:auto;
  }
</style>
