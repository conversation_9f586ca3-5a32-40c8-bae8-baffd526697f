<template>
  <div>
    <div class="search-wrapper">
      <search-pad @search="searchLoad" @reset="reload">
        <el-form-item>
          <el-input placeholder="请输入拒收单号" v-model="model.rejectionNo"/>
        </el-form-item>
        <el-form-item>
          <el-input placeholder="请输入客户编码" v-model="model.code"/>
        </el-form-item>
        <el-form-item>
          <el-input placeholder="请输入客户名称" v-model="model.purMerchantName"/>
        </el-form-item>
        <el-form-item>
          <el-date-picker
            type="daterange"
            range-separator="至"
            v-model="model.during"
            value-format="yyyy-MM-dd"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>

      </search-pad>
    </div>
    <div class="tab_bg">
      <div class="varietiesBan-list-container">
        <div class="varietiesBan-list-tabs-wrapper">
          <div class="varietiesBan-list-tabs">
            <div class="tab" :class="{'active': currentTab == index}" v-for="(tab, index) in tabs"
                 :key="index"
                 v-if="checkPermission(['admin',tab.permission])"
                 @click="handleChangeTab(index,tab.value)">
              {{ tab.name }}（{{tab.count}}）
            </div>
          </div>
          <div class="operations">
            <!-- <el-button  v-if="checkPermission(['admin','rejection:export'])">导出单据</el-button> -->
            <el-button  @click="reload">刷新</el-button>
          </div>
        </div>
      </div>
      <table-pager ref="todoTable" :options="tableTitle" :remote-method="load" :data.sync="tableData">
        <div slot-scope="props" style="width: 65px;">
          <el-button type="text" @click="$router.push({name: '/tradeCenter/rejection/detail',query:{id: props.row.id}})" v-if="checkPermission(['admin','rejection:detail'])">查看详情</el-button>
        </div>

      </table-pager>
    </div>
  </div>
</template>

<script>
  const TableColumns = [
    { label: "拒收单号", name: "rejectionNo",prop: "rejectionNo",width:'150'},
    { label: "拒收时间", name: "createTime", prop:'createTime',width: '160'},
    { label: "物流公司", name: "logisName",prop: 'logisName',width: '95' },
    { label: "物流单号", name: "logisticsNo", prop:'logisticsNo',width: '150'  },
    { label: "关联单号", name: "orderNo",prop: 'orderNo',width:'165'},
    { label: "客户编码", name: "purMerchantCode",prop: 'purMerchantCode' },
    { label: "客户名称", name: "purMerchantName",prop:'purMerchantName' },
    { label: "申请人", name: "applicant",prop:'applicant',  },
    { label: "拒收状态", name: "rejectionStatus.desc",prop: 'rejectionStatus.desc'},
    { label: "拒收原因", name: "rejection",prop: 'rejection',width: '105'},
  ];
  const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({ key: i, ...TableColumns[i] });
  }
  import {rejectionList,rejectionStatistics } from '@/api/trade'
  import checkPermission from '@/utils/permission';
  export default {
    data () {
      return {
        loading: '',
        search: '',
        controlType: '',
        currentTab: 0,
        tabs: [
          { name: '全部', value: 'ALL',count: 0,countName: 'all',permission: 'rejection-all:view' },
          { name: '待审核', value: 'PENDING',count: 0,countName: 'pending',permission: 'rejection-pending:view' },
          { name: '待入库', value: 'WAREHOUSING',count: 0,countName: 'stock',permission: 'rejection-stock:view' },
          { name: '已入库', value: 'ACCEPTED',count: 0,countName: 'finish',permission: 'rejection-finish:view' }
        ],
        tableData: [],
        page: 1,
        pageSize: 10,
        totalPage: 0,
        total: 0,
        tableTitle: TableColumnList,
        model: {
          deliveryStatus: '',
          purMerchantName: '',
          rejectionNo: '',
          code: '',
          during: ''
        },
        products: [],
        ids: []
      }
    },
    mounted() {
      this.getCount()
    },
    methods: {
      checkPermission,
      //tab数量
      async getCount() {
        const {data} = await rejectionStatistics()
        this.tabs.forEach(item=> {
          item.count = data[item.countName]
        })
      },
      searchLoad() {
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      async load(params) {
        let listQuery = {
          model: {
            purMerchantName: this.model.purMerchantName,
            rejectionNo: this.model.rejectionNo,
            code: this.model.code,
            deliveryStatus: this.model.deliveryStatus,
            startTime: this.model.during[0],
            endTime: this.model.during[1],
          }
        }
        Object.assign(listQuery, params)
        this.loading = true
        return await rejectionList(listQuery)
      },
      handleChangeTab (index,value) {
        this.currentTab = index
        if(value === 'ALL') {
          this.model.deliveryStatus = ''
        } else {
          this.model.deliveryStatus = value
        }
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      reload() {
        this.currentTab = 0
        this.model = {
          deliveryStatus: '',
            purMerchantName: '',
            rejectionNo: '',
            code: '',
            during: ''
        }
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      handleRefresh(pageParams) {
        this.$refs.todoTable.doRefresh(pageParams)
      },
      handleSelectionChange(val) {
        this.ids = val.map(function(item,index) {
          return item.id;
        })

      }
    }
  }
</script>

<style lang="scss" scoped>
</style>
