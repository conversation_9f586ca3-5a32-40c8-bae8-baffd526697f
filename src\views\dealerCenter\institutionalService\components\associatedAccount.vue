<template>
  <el-dialog title="关联账号" :visible.sync="visible" top="25px" :close-on-click-modal="false" width="900px">
    <div style="margin-bottom: 15px; display: flex">
      <el-input
        style="margin-right: 10px; width: 400px"
        v-model="keyword"
        placeholder="请输入姓名/登录账号/注册手机号"
      />
      <el-button type="primary" @click="search">搜索</el-button>
      <el-button @click="reset">重置</el-button>
    </div>
    <div class="table" v-loading="loading">
      <el-table
        ref="table"
        :data="currentAccountList"
        style="width: 100%"
        row-key="accountId"
        @selection-change="handleSelectionChange"
      >
        <el-table-column :reserve-selection="true" align="center" type="selection" width="55" fixed />
        <el-table-column prop="status" label="账号状态" width="180">
          <template slot-scope="{ row }">
            <span>{{ row.status.desc }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="姓名" />
        <el-table-column prop="account" label="登录账号" />
        <el-table-column prop="phone" label="注册手机号" />
        <el-table-column prop="registerTime" label="最新修改时间" />
        <el-table-column fixed="right" align="center" label="操作" class="itemAction" width="130">
          <template slot-scope="{ row }">
            <el-row class="table-edit-row">
              <span class="table-edit-row-item">
                <el-link type="primary" style="margin-right: 10px" @click="showPassword(row)">查看密码</el-link>
              </span>
            </el-row>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <ShowPasswordDialog ref="showPasswordDialogRef" />
    <span slot="footer" class="dialog-footer">
      <span>已勾选：{{ selectedAccountList.length }}</span>
      <el-pagination
        :background="true"
        :current-page.sync="listQuery.current"
        :page-size.sync="listQuery.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @current-change="handleCurrentChange"
      />
      <span>
        <el-button @click="hide">取消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="confirm">确定</el-button>
      </span>
    </span>
  </el-dialog>
</template>

<script>
import { getAccountList, saveAccountList } from '@/api/dealerManagement'
import accountTable from '../accountTable'
import ShowPasswordDialog from './showPasswordDialog.vue'

export default {
  name: 'associatedAccount',
  components: {
    ShowPasswordDialog
  },
  data() {
    return {
      visible: false,
      loading: false,
      keyword: '',
      saveLoading: false,
      accountList: [],
      searchAccountList: [],
      currentAccountList: [],
      tableTitle: accountTable,
      listQuery: {
        current: 1,
        size: 10
      },
      orgInfoId: '',
      selectedAccountList: []
    }
  },
  computed: {
    total() {
      if (Array.isArray(this.searchAccountList) && this.searchAccountList.length > 0) return this.searchAccountList.length
      if (Array.isArray(this.accountList)) return this.accountList.length
      return 0
    }
  },
  methods: {
    fieldIsValid(val) {
      return ![null, undefined].includes(val)
    },
    search() {
      this.listQuery.current = 1
      this.searchAccountList = this.accountList.filter((item) => {
        const { name, account, phone } = item
        const { keyword } = this
        return (
          (this.fieldIsValid(name) && name.includes(keyword)) ||
          (this.fieldIsValid(account) && account.includes(keyword)) ||
          (this.fieldIsValid(phone) && phone.includes(keyword))
        )
      })
      this.$nextTick(() => {
        if (this.searchAccountList.length === 0) this.currentAccountList = []
        else this.handleCurrentChange(1)
      })
    },
    reset() {
      this.searchAccountList = []
      this.keyword = ''
      this.handleCurrentChange(1)
    },
    handleSelectionChange(val) {
      this.selectedAccountList = val.map(item => { item.selected = true; item.orgInfoId = this.orgInfoId; return item })
    },
    handleCurrentChange(val) {
      // 如果不是搜索,就按原本的分页,否则就是搜索的分页
      let arr = this.searchAccountList.length > 0 ? this.searchAccountList : this.accountList
      this.currentAccountList = arr.slice((val - 1) * 10, 10 * val)
    },
    showPassword(row) {
      this.$refs.showPasswordDialogRef.show(row)
    },
    show(orgId, orgInfoId) {
      this.loading = true
      this.visible = true
      this.orgInfoId  = orgInfoId
      this.accountList = []
      getAccountList(orgId)
        .then((res) => {
          if (res.code !== 0) return
          this.accountList = res.data || []
          const total = this.accountList.length
          if (total > 0) {
            this.currentAccountList = this.accountList.slice(0, 10)
            this.setSelectedAccountList(res.data)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    hide() {
      this.visible = false
    },
    setSelectedAccountList(data) {
      this.selectedAccountList = []
      this.$refs.table.clearSelection();
      this.selectedAccountList = data.filter((item) => {
        if (item.selected === true) {
          this.$refs.table.toggleRowSelection(item, true)
          return item
        }
      })
    },
    getSelectedAccountList() {
      const { orgInfoId } = this
      return {
        orgInfoId,
        saveDTO: this.selectedAccountList
      }
    },
    confirm() {
      this.saveLoading = true
      saveAccountList(this.getSelectedAccountList())
        .then((res) => {
          if (res.code !== 0) return
          this.$message.success('操作成功')
          this.hide()
          this.$emit('setSuccess')
        })
        .finally(() => {
          this.saveLoading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .pagination-container {
    margin-top: 0;
  }
}
</style>
