<template>
  <div class="taskAddOrEdit">
    <div class="taskAddOrEdit_Header">
      <span class="title"
        >{{ isDisable ? "查看" : id ? "编辑" : "新增" }}任务</span
      >
      <div>
        <el-popover
          v-if="!isDisable"
          v-model="rejectFlag"
          placement="bottom-end"
          title="取消提醒"
          width="300"
          trigger="click"
        >
          <el-button slot="reference">取消</el-button>
          确定取消编辑?取消后编辑内容将不被保存!
          <div style="text-align: right; margin: 0; padding-top: 14px">
            <el-button size="mini" @click="rejectFlag = false">取消</el-button>
            <el-button type="primary" size="mini" @click="toBack"
              >确定</el-button
            >
          </div>
        </el-popover>
        <el-button
          v-if="!isDisable"
          type="primary"
          :loading="saveLoading"
          @click="toSave"
          >保存</el-button
        >
        <el-button v-if="isDisable" @click="toBack">返回</el-button>
      </div>
    </div>
    <div class="taskAddOrEdit_Content">
      <el-form
        ref="taskForm"
        :rules="taskRules"
        :model="taskInfo"
        label-width="100px"
        label-position="right"
        :show-message="false"
      >
        <p class="tit">基础信息</p>
        <div class="form_box">
          <div>
            <el-form-item label="任务类型:" prop="taskType">
              <el-select
                v-model="taskInfo.taskType"
                style="width: 300px"
                placeholder="任务类型"
                :disabled="!!id"
                @change="changeTaskType()"
              >
                <el-option
                  v-for="item in typeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="关联商家:" prop="saleMerchantIdsList">
              <el-select
                :disabled="isDisable"
                multiple
                collapse-tags
                v-model="taskInfo.saleMerchantIdsList"
                style="width: 300px"
                placeholder="关联商家"
              >
                <el-option
                  v-for="item in merchantOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="核算频率:" prop="frequency">
              <el-select
                :disabled="isDisable || taskInfo.taskType === 3"
                v-model="taskInfo.frequency"
                style="width: 300px"
                placeholder="核算频率"
              >
                <el-option
                  v-for="item in frequencyOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </div>
          <div>
            <el-form-item
              v-if="taskInfo.taskType === 3"
              label="任务周期:"
              prop="time"
            >
              <el-date-picker
                :key="1"
                v-model="taskInfo.time"
                type="year"
                value-format="yyyy"
                placeholder="选择年"
                :disabled="!!id"
                @change="handleYearRange"
                style="width: 300px"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item
              v-if="taskInfo.taskType !== 3"
              label="任务周期:"
              prop="time"
            >
              <el-date-picker
                :key="2"
                :disabled="isDisable"
                v-model="taskInfo.time"
                type="monthrange"
                range-separator="至"
                start-placeholder="开始月份"
                end-placeholder="结束月份"
                value-format="yyyy-MM"
                :clearable="!id"
                @change="handleMonthRange"
                style="width: 300px"
              >
              </el-date-picker>
            </el-form-item>
          </div>
          <div>
            <span class="tips"
              >核算频率指任务的更新&统计周期，每日：按天更新任务、核算完成率，每月：按自然月下发任务、核算完成率。</span
            >
          </div>
        </div>
        <p class="tit">
          任务规则
          <el-button
            v-if="!isDisable && taskInfo.taskType === 4"
            type="primary"
            class="button_icon4"
            title="新增阶段"
            size="mini"
            icon="el-icon-plus"
            circle
            plain
            @click="toAdd()"
          ></el-button>
        </p>
        <div class="form_box_type">
          <!-- 拜访任务,拓客任务 -->
          <el-form-item
            v-if="[0, 2].includes(taskInfo.taskType)"
            label="代表目标:"
            prop="goal"
            ><el-input-number
              :disabled="isDisable"
              :controls="false"
              v-model="taskInfo.goal"
              placeholder="请输入"
              style="width: 300px"
            ></el-input-number
            ><span>家次/人</span>
          </el-form-item>
          <!-- 协访任务 -->
          <div v-if="taskInfo.taskType === 1">
            <el-form-item label="一级主管目标:" label-width="115px" prop="goalFirst"
              ><el-input-number
                :disabled="isDisable"
                :controls="false"
                v-model="taskInfo.taskVisitCoachedGoal.goalFirst"
                placeholder="请输入"
                style="width: 285px"
              ></el-input-number
              ><span>家次/人</span>
            </el-form-item>
            <el-form-item label="二级主管目标:" label-width="115px" prop="goalSecond"
              ><el-input-number
                :disabled="isDisable"
                :controls="false"
                v-model="taskInfo.taskVisitCoachedGoal.goalSecond"
                placeholder="请输入"
                style="width: 285px"
              ></el-input-number
              ><span>家次/人</span>
            </el-form-item>
            <el-form-item label="三级主管目标:" label-width="115px" prop="goalThird"
              ><el-input-number
                :disabled="isDisable"
                :controls="false"
                v-model="taskInfo.taskVisitCoachedGoal.goalThird"
                placeholder="请输入"
                style="width: 285px"
              ></el-input-number
              ><span>家次/人</span>
            </el-form-item>
            <el-form-item label="四级主管目标:" label-width="115px" prop="goalFourth"
              ><el-input-number
                :disabled="isDisable"
                :controls="false"
                v-model="taskInfo.taskVisitCoachedGoal.goalFourth"
                placeholder="请输入"
                style="width: 285px"
              ></el-input-number
              ><span>家次/人</span>
            </el-form-item>
          </div>
          <!-- 业绩任务 -->
          <span class="tips" v-if="taskInfo.taskType === 3"
            >业绩目标由系统自动从EDP获取，保存后即可获取最新的指标数据。</span
          >
          <!-- 客户数发展目标 -->
          <div v-if="taskInfo.taskType === 4">
            <div
              class="kh_item"
              v-for="(item, index) in taskInfo.khList"
              :key="index"
            >
              <el-form-item
                label="阶段名称:"
                label-width="180px"
                :prop="`khList[${index}].orderCustomerName`"
                :rules="[{ required: true, message: '', trigger: 'blur' }]"
                ><el-input
                  :disabled="isDisable"
                  v-model="item.orderCustomerName"
                  placeholder="请输入"
                  style="width: 220px"
                  maxlength="10"
                  show-word-limit
                ></el-input>
                <el-button
                  type="danger"
                  class="button_icon4"
                  title="删除阶段"
                  size="mini"
                  icon="el-icon-minus"
                  circle
                  plain
                  v-if="!isDisable && taskInfo.khList.length > 1"
                  @click="toDelete(index)"
                ></el-button>
              </el-form-item>
              <el-form-item
                label="下单客户总家数:"
                label-width="180px"
                :prop="`khList[${index}].orderCustomerNum`"
                :rules="[{ required: true, message: '', trigger: 'blur' }]"
                ><el-input-number
                  :disabled="isDisable"
                  :controls="false"
                  v-model="item.orderCustomerNum"
                  placeholder="请输入"
                  style="width: 220px"
                  :min="0"
                  :precision="0"
                ></el-input-number
                ><span>家/人</span>
              </el-form-item>
              <el-form-item
                label="下单客户-重点客户家数:"
                label-width="180px"
                :prop="`khList[${index}].orderKeyCustomerNum`"
                :rules="[{ required: true, message: '', trigger: 'blur' }]"
                ><el-input-number
                  :disabled="isDisable"
                  :controls="false"
                  v-model="item.orderKeyCustomerNum"
                  placeholder="请输入"
                  style="width: 220px"
                  :min="0"
                  :precision="0"
                ></el-input-number
                ><span>家/人</span>
              </el-form-item>
            </div>
          </div>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script>
import {
  salesmanTaskDetail,
  salesmanTaskUpdate,
  salesmanTaskAdd,
  getMerchantList,
} from "@/api/taskManagement.js";
import dayjs from "dayjs";
export default {
  name: "taskAddOrEdit",
  components: {},
  props: {},
  data() {
    return {
      id: "",
      isDisable: false, // 是否禁止编辑
      rejectFlag: false,
      saveLoading: false,
      typeOptions: [
        {
          label: "拜访任务",
          value: 0,
        },
        {
          label: "协访任务",
          value: 1,
        },
        {
          label: "拓客任务",
          value: 2,
        },
        {
          label: "业绩任务",
          value: 3,
        },
        {
          label: "客户数发展目标",
          value: 4,
        },
      ],
      frequencyOptions: [
        {
          label: "每日",
          value: 0,
        },
        {
          label: "每月",
          value: 1,
        },
      ],
      merchantOptions: [],
      taskInfo: {
        taskType: 0,
        time: "",
        frequency:'',
        saleMerchantIdsList: [],
        goal: undefined,
        khList: [],
        taskVisitCoachedGoal:{
          goalFirst: undefined,
          goalSecond: undefined,
          goalThird: undefined,
          goalFourth: undefined,
        }
      },
      initialMonth: [],
      taskRules: {
        taskType: [
          { required: true, message: "请选择任务类型", trigger: "blur" },
        ],
        goal: [{ required: true, message: "", trigger: "blur" }],
        time: [{ required: true, message: "请选择任务周期", trigger: "blur" }],
        saleMerchantIdsList: [
          { required: true, message: "请选择关联商家", trigger: "blur" },
        ],
        frequency: [{ required: true, message: "", trigger: "blur" }],
      },
    };
  },
  computed: {},
  watch: {},
  created() {
    this.id = this.$route.query.id ?? null;
    this.id ? this.getData() : this.reset();
    this.isDisable = this.$route.query.isDisable ?? false;
    // this.getSaleId();
    this.getMerchant();
  },
  mounted() {},
  methods: {
    // 获取企业
    getMerchant() {
      getMerchantList({ saleMerchantIds: null })
        .then((res) => {
          if (res.code === 0) {
            this.merchantOptions = res.data || [];
          }
        })
        .catch(() => {
          this.merchantOptions = [];
        });
    },
    // 查询数据
    getData() {
      salesmanTaskDetail(this.id).then((res) => {
        if (res?.data) {
          if (res.data.taskType === 3) {
            this.initialMonth = dayjs(res.data.startYearMonth).format("YYYY");
          } else {
            this.initialMonth =
              res.data.startYearMonth && res.data.endYearMonth
                ? [res.data.startYearMonth, res.data.endYearMonth]
                : [];
          }

          this.taskInfo = {
            ...res.data,
            saleMerchantIdsList: res.data?.saleMerchantIds,
            time: this.initialMonth,
            khList: res.data?.customerDevelopDetail ?? [{}], // 业绩任务
            goal: res.data.taskVist?.goal ?? undefined, // 拜访任务
            taskVisitCoachedGoal: res.data?.salesmanTaskVisitCoachedSaveDTO ?? {}, // 协访任务
          };
          if (res.data.taskType === 2) {
            // 拓客任务
            this.taskInfo.goal = res.data.taskApply?.goal ?? undefined;
          }
        }
      });
    },
    // 重置
    reset() {
      this.taskInfo = {
        taskType: 0,
        time: "",
        khList: [{}],
        saleMerchantIdsList: [],
        goal: undefined,
        frequency:'',
        taskVisitCoachedGoal:{},
      };
      this.fileList = [];
      this.initialMonth = [];
    },
    changeTaskType(){
      this.taskInfo.time = '';
      if(this.taskInfo.taskType === 3){
        this.taskInfo.frequency = 1;
      }
    },
    // 月份校验规则
    handleMonthRange(value) {
      if (!value) {
        this.taskInfo.time = value;
        return;
      }
      let start = value[0];
      let end = value[1];
      // 编辑状态不可更改开始时间，结束时间在开始时间之前
      if (this.id && this.initialMonth[0] !== value[0]) {
        start = this.initialMonth[0];
        end = dayjs(end).isBefore(this.initialMonth[0], "month")
          ? this.initialMonth[0]
          : end;
        this.$message.error("编辑状态不可更改开始时间!");
      }
      // 新增状态开始时间不能在当前时间之前
      if (!this.id && dayjs(start).isBefore(dayjs(), "month")) {
        start = dayjs().format("YYYY-MM");
        this.$message.error("不能选择过去月份!");
      }
      // 结束时间在当前时间之后
      if (dayjs(end).isBefore(dayjs(), "month")) {
        end = dayjs().format("YYYY-MM");
      }
      this.$nextTick(() => {
        this.taskInfo.time = [start, end];
      });
    },
    // 年份校验规则
    handleYearRange(value) {
      let currentYear = new Date().getFullYear();
      let selectedYear = value ? new Date(value).getFullYear() : null;
      if (selectedYear && selectedYear < currentYear) {
        this.$message.error("不能选择过去年份!");
        this.taskInfo.time = currentYear.toString();
      }
    },
    // 返回任务管理页面
    toBack() {
      this.$store.dispatch("tagsView/delView", this.$route);
      this.$router.push("/businessCentric/taskManagement");
    },
    // 请求参数整合
    getRequestData() {
      let temp = {
        startYearMonth: this.taskInfo.time?.[0] ?? "",
        endYearMonth: this.taskInfo.time?.[1] ?? "",
        taskType: this.taskInfo.taskType ?? "",
        id: this.id ?? "",
        frequency: this.taskInfo.frequency ?? "",
        saleMerchantIdsList: this.taskInfo.saleMerchantIdsList ?? [],
      };
      // 拜访任务
      if (this.taskInfo.taskType === 0) {
        temp.taskVist = {
          goal: this.taskInfo.goal,
        };
      }
      // 协访任务
      if(this.taskInfo.taskType === 1){
        temp.salesmanTaskVisitCoachedSaveDTO = this.taskInfo.taskVisitCoachedGoal;
      }
      if (this.taskInfo.taskType === 2) {
        temp.taskApply = {
          goal: this.taskInfo.goal,
        };
      }
      // 业绩任务
      if (this.taskInfo.taskType === 3) {
        temp.startYearMonth = this.taskInfo.time
          ? dayjs(this.taskInfo.time).format("YYYY-01")
          : "";
        temp.endYearMonth = this.taskInfo.time
          ? dayjs(this.taskInfo.time).format("YYYY-12")
          : "";
      }
      // 客户数发展目标
      if (this.taskInfo.taskType === 4) {
        temp.customerDevelopDetails = this.taskInfo.khList ?? [];
      }
      return temp;
    },
    // 更新
    onUpdate() {
      this.saveLoading = true;
      let temp = this.getRequestData();
      salesmanTaskUpdate(temp)
        .then((res) => {
          this.saveLoading = false;
          if (res.code === 0 && res.data) {
            this.$message.success("编辑成功!");
            this.toBack();
          }
        })
        .catch((err) => {
          this.saveLoading = false;
          console.log(err);
        });
    },
    // 新增
    onSave() {
      this.saveLoading = true;
      let temp = this.getRequestData();
      salesmanTaskAdd(temp)
        .then((res) => {
          this.saveLoading = false;
          if (res.code === 0 && res.data) {
            this.$message.success("新增成功!");
            this.toBack();
          }
        })
        .catch((err) => {
          this.saveLoading = false;
          console.log(err);
        });
    },
    // 保存前置校验
    async toSave() {
      try {
        let f = await this.$refs.taskForm.validate();
        if (f) {
          this.id ? this.onUpdate() : this.onSave();
        }
      } catch (err) {
        console.log(err);
      }
    },
    // 新增阶段
    toAdd() {
      if (this.taskInfo.khList.length < 5) {
        this.taskInfo.khList.push({});
        let box = document.querySelector(".form_box_type");
        if (box) {
          this.$nextTick(() => {
            box.scrollTop = box.scrollHeight;
          });
        }
      } else {
        this.$message.error("最多设置5个阶段！");
      }
    },
    // 删除阶段
    toDelete(index) {
      this.taskInfo.khList.splice(index, 1);
    },
  },
};
</script>

<style scoped lang="scss">
.taskAddOrEdit {
  background: #fff;
  height: calc(100vh - 86px - 32px);
  display: flex;
  flex-direction: column;
  padding: 0 20px 20px 20px;
  .taskAddOrEdit_Header {
    height: 56px;
    min-height: 56px;
    border-bottom: 1px solid #eeeeee;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    .title {
      font-size: 18px;
    }
    .el-button {
      margin-left: 10px;
    }
  }
  .taskAddOrEdit_Content {
    flex: auto;
    color: #505465;
    overflow: scroll;
    .el-form {
      height: 100%;
      display: flex;
      flex-direction: column;
      .form_box_type {
        flex: auto;
        height: 0;
        overflow: scroll;
      }
    }
    .tit {
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 20px;
    }
    .button_icon4 {
      margin-left: 8px;
      padding: 4px;
    }
    .tips {
      font-size: 12px;
      color: #ff2d47;
      margin-top: 8px;
    }
    .form_box {
      margin-bottom: 24px;
      > div {
        display: flex;
        .el-form-item {
          margin-bottom: 10px;
        }
        > .el-form-item + .el-form-item {
          margin-left: 28px;
        }
      }
      .tips {
        margin-left: 100px;
      }
    }
    .form_box_type {
      .el-form-item {
        margin-bottom: 8px;
      }
      .el-input-number + span {
        margin-left: 8px;
      }
      .upload-demo {
        width: 400px;
      }
      .kh_item {
        border-left: 4px solid #eeeeee;
      }
      .kh_item + .kh_item {
        margin-top: 30px;
      }
      .tips {
        margin: 8px;
      }
    }
    ::v-deep .el-form .el-input__inner {
      text-align: left;
    }
  }
}
</style>
