<template>
  <el-dialog :visible.sync="dialogVisible" width="550px" top="50px" :close-on-click-modal="false">
    <div slot="title">
      <el-tabs v-model="paySource">
        <el-tab-pane v-for="item in payPlatformList" :label="item.label" :name="item.name" :key="item.name" />
      </el-tabs>
      <div class="form_wrapper">
        <PaySettingForm ref="paySettingFormRef" :paySource="paySource" @cancel="dialogVisible=false" @confirm="confirm"/>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import PaySettingForm from './paySettingForm.vue'

export default {
  name: 'paySettingDialog',
  components: {
    PaySettingForm
  },
  data() {
    return {
      // 支付来源
      paySource: 'PC',
      dialogVisible: false,
      payPlatformList: [
        {
          label: 'PC端',
          name: 'PC'
        },
        {
          label: '小程序端',
          name: 'WECHAT'
        }
      ]
    }
  },
  methods: {
    close() {
      this.dialogVisible = false
    },
    show(saleMerchantId) {
      this.$nextTick(()=>{
        this.paySource = 'PC'
        this.dialogVisible = true
        this.$refs.paySettingFormRef.init(3, saleMerchantId)
      })
    },
    confirm() {
      this.close()
      this.$emit('confirm')
    }
  }
}
</script>

<style lang="less" scoped>
/deep/ .el-dialog {
  .el-dialog__body {
    display: none;
  }
  .el-dialog__header {
    padding: 10px 0 0;
  }
}
/deep/ .el-tabs__nav {
  margin: 0 15px;
  .el-tabs__active-bar {
    padding: 0 20px;
  }
}
</style>
