import requestAxios from '@/utils/requestAxios'

// 企业库列表
export  function list(data) {
  return requestAxios({
    url: '/api/merchant/admin/enterprise/page',
    method: 'post',
    data
  })
}

export function areas(data) {
  return requestAxios({
    url: '/api/authority/area/linkage',
    method: 'get',
    params: data
  })
}

// 查询详情
export function deleteEnterprise(id) {
  return requestAxios({
    url: `/api/merchant/admin/enterprise/${id}`,
    method: 'get',
  })
}

// 新增企业
export function addEnterprise(data) {
  return requestAxios({
    url: '/api/merchant/admin/enterprise',
    method: 'post',
    data
  })
}

// 修改企业
export function editEnterprise(data) {
  return requestAxios({
    url:'/api/merchant/admin/enterprise',
    method: 'put',
    data
  })
}

// 企业变更审核分页查询
export function explainPage(data) {
  return requestAxios({
    url:'/api/merchant/admin/purMerchant/explain/page',
    method: 'post',
    data
  })
}

// 企业变更审核
export function explainAudit(data) {
  return requestAxios({
    url:'/api/merchant/admin/purMerchant/explain/audit',
    method: 'post',
    data
  })
}
