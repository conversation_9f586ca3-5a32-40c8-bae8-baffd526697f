import Layout from '@/layout'
const massCenter = {
  path: '/massCenter',
  name: 'massCenter',
  redirect: 'massCenter/merchantsQualification',
  meta: {
    title: '质量中心',
    icon: 'component'
  },
  component: Layout,
  children: [
    {
      path: 'merchantsQualification',
      name: 'merchantsQualification',
      meta: { title: '客商资质' },
      component: () => import('@/views/massCenter/merchantsQualification/list')
    },
    {
      path: 'merchantsQualification/detail',
      name: 'merchantsQualificationdetail',
      hidden: true,
      meta: { title: '客商资质详情', activeMenu: '/massCenter/merchantsQualification',noCache: true },
      component: () => import('@/views/massCenter/merchantsQualification/detail')
    },
    {
      path: 'productQualification',
      name: 'productQualification',
      meta: { title: '商品资质' },
      component: () => import('@/views/massCenter/productQualification/list')
    },
    {
      path: 'productQualification/detail',
      name: 'productQualificationdetail',
      hidden: true,
      meta: {
        title: '商品资质详情',
        activeMenu: '/massCenter/productQualification',
        noCache: true
      },
      component: () => import('@/views/massCenter/productQualification/detail')
    }
  ]
}

export default massCenter