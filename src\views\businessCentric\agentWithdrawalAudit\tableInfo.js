export default {
  REPEAL: [
    {
      key: 0,
      label: '业务员编码',
      name: "salesmanCode",
      width: '180px',
      disabled: true
    },
    {
      key: 1,
      label: '业务员姓名',
      name: "realName",
      width: '100px',
      disabled: true
    },
    {
      key: 2,
      label: '业务员手机号',
      name: "contactNumber",
      width: '200px',
      disabled: true
    },
    // {
    //   key: 3,
    //   label: '撤销人',
    //   name: "createTime",
    //   width: '240px'
    // },
    {
      key: 4,
      label: '撤销原因',
      name: "applyReason",
      width: '240px'
    },
    {
      key: 12,
      label: '所属商家',
      name: "merchantName",
      width: '150px'
    },
    {
      key: 5,
      label: '商品主图',
      name: "pictIdS",
      width: '80px'
    },
    {
      key: 6,
      label: '商品编码',
      name: "productCode",
      width: '170px'
    },
    {
      key: 7,
      label: '商品名称',
      name: "productName",
      width: '200px'
    },
    {
      key: 8,
      label: '规格',
      name: "spec",
      width: '200px'
    },
    {
      key: 9,
      label: '生产厂家',
      name: "manufacturer",
      width: '200px'
    },
    {
      key: 10,
      label: '代理区域',
      name: "area",
      width: '200px'
    },
    {
      key: 11,
      label: '推广费（元）',
      name: "promotionExpenses",
      width: '120px'
    }
  ],
  PENDING: [
    {
      key: 0,
      label: '业务员编码',
      name: "salesmanCode",
      width: '180px',
      disabled: true
    },
    {
      key: 1,
      label: '业务员姓名',
      name: "realName",
      width: '100px',
      disabled: true
    },
    {
      key: 2,
      label: '业务员手机号',
      name: "contactNumber",
      width: '200px',
      disabled: true
    },
    {
      key: 3,
      label: '撤销原因',
      name: "applyReason",
      width: '240px'
    },
    {
      key: 11,
      label: '所属商家',
      name: "merchantName",
      width: '150px'
    },
    {
      key: 4,
      label: '商品主图',
      name: "pictIdS",
      width: '80px'
    },
    {
      key: 5,
      label: '商品编码',
      name: "productCode",
      width: '170px'
    },
    {
      key: 6,
      label: '商品名称',
      name: "productName",
      width: '200px'
    },
    {
      key: 7,
      label: '规格',
      name: "spec",
      width: '200px'
    },
    {
      key: 8,
      label: '生产厂家',
      name: "manufacturer",
      width: '200px'
    },
    {
      key: 9,
      label: '代理区域',
      name: "area",
      width: '200px'
    },
    {
      key: 10,
      label: '推广费（元）',
      name: "promotionExpenses",
      width: '120px'
    }
  ],
  REJECTED: [
    {
      key: 0,
      label: '业务员编码',
      name: "salesmanCode",
      width: '180px',
      disabled: true
    },
    {
      key: 1,
      label: '业务员姓名',
      name: "realName",
      width: '100px',
      disabled: true
    },
    {
      key: 2,
      label: '业务员手机号',
      name: "contactNumber",
      width: '200px',
      disabled: true
    },
    {
      key: 3,
      label: '撤销原因',
      name: "applyReason",
      width: '240px'
    },
    {
      key: 3,
      label: '所属商家',
      name: "merchantName",
      width: '150px'
    },
    {
      key: 4,
      label: '商品主图',
      name: "pictIdS",
      width: '80px'
    },
    {
      key: 5,
      label: '商品编码',
      name: "productCode",
      width: '170px'
    },
    {
      key: 6,
      label: '商品名称',
      name: "productName",
      width: '200px'
    },
    {
      key: 7,
      label: '规格',
      name: "spec",
      width: '200px'
    },
    {
      key: 8,
      label: '生产厂家',
      name: "manufacturer",
      width: '200px'
    },
    {
      key: 9,
      label: '代理区域',
      name: "area",
      width: '200px'
    },
    {
      key: 10,
      label: '推广费（元）',
      name: "promotionExpenses",
      width: '120px'
    },
    {
      key: 11,
      label: '驳回理由',
      name: "rejectionReason",
      width: '120px'
    }
  ]
}