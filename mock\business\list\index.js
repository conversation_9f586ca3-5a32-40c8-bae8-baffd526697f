const Mock = require('mockjs')

const count = 100

const baseContent = '<p>I am testing data, I am testing data.</p><p><img src="https://wpimg.wallstcn.com/4c69009c-0fd4-4153-b112-6cb53d1cf943"></p>'
const image_uri = 'https://wpimg.wallstcn.com/e4558086-631c-425c-9430-56ffb46e70b3'

let businessList = []
for (let i = 0; i < count; i++) {
    businessList.push(Mock.mock({
        id: '@increment',
        approvalStatus: Mock.Random.pick([{
            code: "PENDING",
            desc: '待审批'
        },
        {
            code: "ACCEPTED",
            desc: '已审批'
        },
        {
            code: "REJECTED",
            desc: '已驳回'
        }]),
        code: 'SJBM' + Mock.Random.string('number', 5),
        publishStatus: Mock.Random.pick([{code: 'N',desc: '否'}, {code: 'Y', desc: '是'}]),
        identifyCode: '@string("lower", 5, 10)',
        siteName: Mock.Random.csentence(5),
        name: Mock.Random.csentence(5),
        socialCreditCode: '@string("lower", 17)',
        legalPerson: Mock.Random.cname(2),
        ceoName: Mock.Random.pick([1,3,4]),
        ceoMobile: Mock.Random.pick([***********,***********,***********]),
        region: Mock.Random.city(true),
        registerAddress: "@county(true)",
        updateUser: Mock.Random.cname(2),
        updateTime: Mock.Random.date('yyyy-MM-dd-HH-mm'),
        approvalUser: Mock.Random.cname(3),
        approvalDate: Mock.Random.date('yyyy-MM-dd'),
        managementEndDate: Mock.Random.date('yyyy-MM-dd')
    }))
}
module.exports = [
    {
        url: '/api/merchant/admin/saleMerchant/page',
        type: 'post',
        response: config => {
            const { importance, type, title, page = 1, size = 20, sort, model } = config.body
            let mockList = businessList.filter(item => {
                if(model.approvalStatus.code == item.approvalStatus.code) {
                    return item
                }
                // console.log(item.publishStatus)
                if(model.publishStatus.code == item.publishStatus.code) {
                    console.log(444)
                    return item 
                }
            })

            if (sort === '-id') {
                mockList = mockList.reverse()
            }

            const pageList = mockList.filter((item, index) => index < size * page && index >= size * (page - 1))

            return {
                code: 20000,
                data: {
                    total: mockList.length,
                    items: pageList
                }
            }
        }
    },
    {
        url: '/api/merchant/admin/saleMerchant/pages',
        type: 'post',
        response: _ => {
            return {
                code: 20000,
                data: 'success'
            }
        }
    }
]
