const { Random } = require('mockjs')
const Mock = require('mockjs')

const List = []
const count = 100

const baseContent = '<p>I am testing data, I am testing data.</p><p><img src="https://wpimg.wallstcn.com/4c69009c-0fd4-4153-b112-6cb53d1cf943"></p>'
const image_uri = 'https://wpimg.wallstcn.com/e4558086-631c-425c-9430-56ffb46e70b3'

for (let i = 0; i < count; i++) {
    List.push(Mock.mock({
        id: '@increment',
        approvalStatus: Mock.Random.pick([{
            code: "PENDING",
            desc: '待审批'
        },
        {
            code: "ACCEPTED",
            desc: '已审批'
        },
        {
            code: "REJECTED",
            desc: '已驳回'
        }]),
        approvalTime: Mock.Random.date('yyyyMMdd'),
        approvalUser: Mock.Random.cname(3),
        ceoMobile: Mock.Random.pick(['133', '135', '189', '158']) + Mock.mock(/\d{8}/),
        ceoName: Mock.Random.cname(2),
        code: '@string("lower", 5, 10)',
        identifyCode: '@string("lower", 5, 10)',
        legalPerson: Mock.Random.cname(2),
        merchantTypeId: '2',
        name: Mock.Random.csentence(5),
        publishStatus: Mock.Random.pick(["Y", "N"]),
        regionId: Mock.Random.city(true),
        registerAddress: "@county(true)",
        registerCapital: '@increment',
        socialCreditCode: '@string("lower", 5, 10)',
        userId: '@increment',
        loginTime: Mock.Random.date('yyyy-MM-dd'),
        code: 'CGSYLYS' + Mock.Random.string('number', 5)
    }))
}
module.exports = [
    {
        url: '/vue-element-admin/purMerchant/page',
        type: 'post',
        response: config => {
            const { importance, type, title, page = 1, limit = 20, sort } = config.query
            let model = config.query.model || config.body.model
            model = typeof(model)=='string'? JSON.parse(model):model
            console.log(model)
            let mockList = List.filter(item => {
                if( model.approvalStatus.code && model.approvalStatus.code == item.approvalStatus.code ) return item
            })

            if (sort === '-id') {
                mockList = mockList.reverse()
            }

            const pageList = mockList.filter((item, index) => index < limit * page && index >= limit * (page - 1))

            return {
                code: 20000,
                data: {
                    total: mockList.length,
                    items: pageList
                }
            }
        }
    }
    // {
    //     url: '/vue-element-admin/purMerchant/page',
    //     type: 'get',
    //     response: _ => {
    //         return {
    //             code: 20000,
    //             data: 'success'
    //         }
    //     }
    // }
]

