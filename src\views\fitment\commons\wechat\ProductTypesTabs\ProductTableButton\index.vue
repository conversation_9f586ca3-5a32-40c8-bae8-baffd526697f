<template>
  <div class="temp_ProductTableButton">
    <el-button style="width:100px;padding:0 -10px; text-indent: -10px;" @click="showProductTableDialogFun()">选择商品</el-button>
    <el-dialog v-if="showProductTableDialog==true" append-to-body title="商品列表" :visible.sync="showProductTableDialog" :before-close="closeDialogFun" :close-on-click-modal="false" width="80%" :show-close="false">
      <div style="position:absolute;top:10px; right:15px; background:#fff; height:38px;">
        <div>
          <el-button @click="clearFun()">取 消</el-button>
          <el-button type="primary" @click="submitFun()">确 定</el-button>
        </div>
      </div>
      <div class="showProductTable">
        <div class="temp_searchBox">
          <el-form ref="searchForm" :inline="true" :model="listQuery" class="form-inline">
            <el-form-item label="">
              <el-input v-model="listQuery.model.productName"  placeholder="请输入商品名称" style="width:250px" />
            </el-form-item>
            <el-form-item>
              <el-button  type="primary" @click="getList()">搜索</el-button>
              <el-button  @click="resetForm('searchForm')">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="table">
          <el-table
            v-if="list"
            ref="table"
            v-loading="listLoading"
            :data="list"
            row-key="id"
            border
            fit
            highlight-current-row
            @select="onSelect"
            style="width: 100%"
            @select-all="onAllSelect"
          >
            <el-table-column
              align="center"
              width="50"
              label="序号"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                <span>{{ scope.$index+1 }}</span>
              </template>
            </el-table-column>
            <el-table-column
              :reserve-selection="true"
              align="center"
              type="selection"
              width="55"
            />

            <el-table-column
              v-for="(item, index) in tableTitle"
              :key="index"
              :width="item.width"
              :min-width="(item.width?item.width:'350px')"
              :label="item.label"
              show-overflow-tooltip
              align="center"
            >
              <template slot-scope="{row}">
                <el-popover v-if="item.name=='pictIdS'" placement="right" trigger="hover">
                  <el-image
                    style="width: 200px; height: 200px"
                    fit="contain"
                    :src="row[item.name]|imgFilter"
                  />
                  <el-image
                    slot="reference"
                    style="width: 30px; height: 30px"
                    fit="cover"
                    :src="row[item.name]|imgFilter"
                  />
                </el-popover>
                <span v-else-if="item.name=='approvalStatus'">{{ row[item.name].desc }}</span>
                <span v-else-if="item.name=='publishStatus'&&row[item.name].code=='PUT_ON_SALE'">{{ row[item.name].desc }}</span>
                <el-button v-else-if="item.name=='publishStatus'&&row[item.name].code=='PULL_OFF_SHELVES'" type="text">{{ row[item.name].desc }}</el-button>

                <span v-else-if="item.name=='stockQuantityStatus'">{{ row[item.name].desc }}</span>
                <el-button v-else-if="item.name=='salePrice'" type="text" style="color:#FF6E1B">{{ row[item.name] }}</el-button>
                <el-button v-else-if="item.name=='costPrice'" type="text" style="color:#2DAC0C">{{ row[item.name] }}</el-button>
                <el-button v-else-if="item.name=='grossProfit'" type="text" style="color:#2DAC0C">{{ row[item.name] }}</el-button>
                <span v-else>{{ row[item.name] }}</span>
              </template>
            </el-table-column>
          </el-table>
          <pagination v-show="total>0" :total="total" :page.sync="listQuery.current" :limit.sync="listQuery.size" @pagination="getList" />
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import { list } from '@/api/products/product'
export default {
  // filters: {
  //   imgFilter: function(value) {
  //     if (value != null && value != '') {
  //       return value.split(',')[0]
  //     }
  //   }
  // },
  components: {
    Pagination
  },
  props: {
    selectItems: {
      type: Array,
      default: [],
      required: true
    },
  },
  data() {
    return {
      tableTitle: [
        {
          key: 1,
          label: '库存状态',
          name: 'stockQuantityStatus',
          width: '110px',
          disabled: true
        },
        {
          key: 2,
          label: '商品主图',
          name: 'pictIdS',
          width: '110px',
          disabled: true
        },
        {
          key: 3,
          label: '商品编码',
          name: 'productCode',
          width: '150px',
          disabled: true
        },
        {
          key: 4,
          label: '商品名称',
          name: 'productName',
          width: '200px'
        },
        {
          key: 5,
          label: '规格',
          name: 'spec',
          width: '150px'
        },
        {
          key: 6,
          label: '批准文号',
          name: 'approvalNumber',
          width: '150px'
        },
        {
          key: 7,
          label: '销售价',
          name: 'salePrice',
          width: '150px'
        },
        // {
        //   key: 8,
        //   label: '成本价',
        //   name: 'costPrice',
        //   width: '150px'
        // },
        // {
        //   key: 9,
        //   label: '毛利率',
        //   name: 'grossProfitRate',
        //   width: '150px'
        // },
        // {
        //   key: 10,
        //   label: '毛利',
        //   name: 'grossProfit',
        //   width: '150px'
        // },
        // {
        //   key: 11,
        //   label: '医保价',
        //   name: 'medicarePrice',
        //   width: '150px'
        // },
        // {
        //   key: 12,
        //   label: '零售价',
        //   name: 'retailPrice',
        //   width: '150px'
        // },
        {
          key: 13,
          label: '剂型',
          name: 'agentiaType',
          width: '120px'
        },
        {
          key: 14,
          label: '生产厂家',
          name: 'manufacturer'
        },
        {
          key: 15,
          label: '商品条形码',
          name: 'barCode',
          width: '180px'
        },
        {
          key: 16,
          label: '所属商家',
          name: 'saleMerchantName',
          width: '150px'
        },
        // {
        //   key: 17,
        //   label: '审批人',
        //   name: 'approvalUserName',
        //   width: '200px'
        // },
        // {
        //   key: 18,
        //   label: '审批原因',
        //   name: 'rejectReason',
        //   width: '180px'
        // },
        // {
        //   key: 19,
        //   label: '审批时间',
        //   name: 'approvalTime'
        // }

      ],
      showProductTableDialog: false,
      listQuery: {
        model: {
          whetherOnSale: 'Y',
          approvalStatus: 'ACCEPTED',
          productName:''
        },
        current: 1,
        size: 10
      },
      selectRowItems: [],
      list: [],
      total: 0,
      listLoading: true
    }
  },
  computed: {
    selectRowVal: {
      get() {
        this.selectItems
      },
      set(val) {
        this.$emit('update:selectItems', val)
      }
    }
  },
  mounted() {
  },
  beforeDestroy() {},
  methods: {
    clearFun() {
      this.listQuery = {
        model: {
          whetherOnSale: 'Y',
          approvalStatus: 'ACCEPTED',
          productName:''
        },
        current: 1,
        size: 10
      },
      this.list = [],
      this.total = 0,
      this.listLoading = true,
      this.showProductTableDialog = false
    },
    showProductTableDialogFun() {
      this.listQuery = {
        model: {
          whetherOnSale: 'Y'
        },
        current: 1,
        size: 10
      },
      this.list = [],
      this.total = 0,
      this.listLoading = true,
      this.showProductTableDialog = true
      this.getList()
    },
    async getList() {
      this.listLoading = true
      this.listQuery.model = { ...this.listQuery.model, commerceModel: "SAAS_PLATFORM" }
      const { data } = await list(this.listQuery)
      this.list = data.records
      this.total = data.total
      this.listLoading = false
      this.displayTable()
    },
    resetForm(){
      this.listQuery = {
        model: {
          whetherOnSale: 'Y',
          approvalStatus: 'ACCEPTED',
          productName:''
        },
        current: 1,
        size: 10
      };
      this.getList();
    },
    closeDialogFun() {
      this.showProductTableDialog = false
    },
    onAllSelect(selection) {
      this.onSelect(selection)
    },
    onSelect: function(val, row) {
      if (val.length < 9999) {
        this.selectRowItems = []
        val.forEach(item => {
          this.selectRowItems.push({
            dataType: 'PRODUCT',
            showStatus: 'Y',
            dataParam: item.id
          })
        })
      } else {
        this.$refs.table.toggleRowSelection(row, false)
      }
    },
    displayTable() {
      const vm = this
      vm.selectItems.forEach(item => {
        vm.list.forEach(rowItem => {
          if (item.dataParam === rowItem.id) {
            vm.$refs.table.toggleRowSelection(rowItem, true)
          }
        })
      })
    },
    submitFun() {
      this.selectRowVal = [...[], ...this.selectRowItems]
      this.clearFun()
    }
  }
}
</script>
<style lang="less" scoped>
.temp_ProductTableButton{width:100%;

}
.showProductTable{
    margin: -30px -20px;
    border-top: 1px solid #ebecee;
    padding: 10px 20px;
}
.temp_searchBox{height:45px;overflow: hidden; margin-bottom: 0;  padding:0; border-left:none;}

</style>
