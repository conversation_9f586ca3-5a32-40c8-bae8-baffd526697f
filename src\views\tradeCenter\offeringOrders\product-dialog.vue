<template>
  <el-dialog
    title="选择商品"
    :visible.sync="visible"
    width="80%"
    :before-close="handleClose">
    <div class="search-wrapper">
      <search-pad @search="load" @reset="reset">
        <el-form-item>
          <el-input placeholder="请输入商品编码" v-model="search.productCode"></el-input>
        </el-form-item>
        <el-form-item>
          <el-input placeholder="请输入商品名称" v-model="search.productName"></el-input>
        </el-form-item>
        <el-form-item>
          <el-input placeholder="请输入生产厂家" v-model="search.manufacturer"></el-input>
        </el-form-item>
        <el-form-item>
          <el-select v-model="search.stockCondition" placeholder="库存" style="width: 110px">
            <el-option label="全部" value="ALL"></el-option>
            <el-option label="有货" value="WITH"></el-option>
            <el-option label="缺货" value="WITHOUT"></el-option>
          </el-select>
        </el-form-item>
        <!--<el-form-item>
          <el-select v-model="stockCondition" placeholder="库存" clearable @change="getType">
            <el-option :value="1" label="有货"/>
            <el-option :value="2" label="缺货"/>
          </el-select>
        </el-form-item>-->
      </search-pad>
    </div>

    <el-table :data="productList" ref="productTable">
      <el-table-column type="selection" width="55" :selectable="selectable"></el-table-column>
      <el-table-column label="序号" type="index" width="50"/>
      <el-table-column label="产品主图" width="80">
        <template slot-scope="scope">
          <img :src="scope.row.pictIdS | imgFilter" width="50px">
          <!-- <img :src="pictImg" width="50px" v-if="scope.row.pictIdS == null || scope.row.pictIdS == ''"> -->
        </template>
      </el-table-column>
      <el-table-column label="商品编码" prop="productCode" width="170"/>
      <el-table-column label="商品名称" prop="productName"/>
      <el-table-column label="规格" prop="spec"/>
      <el-table-column label="单位" prop="unit" width="60"/>
      <el-table-column label="生产厂家" prop="manufacturer"/>
      <el-table-column label="销售价" prop="salePrice"/>
      <el-table-column label="库存" prop="stockQuantity" width="80"/>
    </el-table>
    <el-pagination
      @size-change="load"
      @current-change="load"
      :current-page.sync="page"
      :page-size.sync="pageSize"
      layout="->, prev, pager, next, sizes, jumper"
      :total="total"/>
    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import request from '@/utils/request'
  import load from 'jszip/lib/load'
  import SearchPad from '@/components/searchPad'
  import {productList} from '@/api/product.js'
  import productImg from '../../../assets/product.png'
  export default {
    components: {
      SearchPad
    },
    data () {
      return {
        pictImg: productImg,
        loading: false,
        visible: false,
        stock: '',
        productList: [],
        radio: 0,
        page: 1,
        pageSize: 10,
        total: 0,
        keywordSelect: 1,
        keyword: '',
        products: [],
        model: {
          stockCondition: ''
        },
        search: {
          stockCondition: 'ALL',
          productCode: '',
          productName: '',
          manufacturer: ''
        }
      }
    },
    mounted () {
      this.load()
    },
    methods: {
      selectable(row,index) {
        if(row.stockQuantity == 0) {
          return false
        } else {
          return true
        }
      },
      async querySearch(queryString, cb) {
        let params = {
          model: {
            "productCode": this.search.productCode,
            "productName": this.search.productName,
            "stockCondition": this.search.stockCondition,
            "whetherOnSale": 'Y'
          },
          order: "descending",
          sort: "id"
        }
        const {data} = await productList(params)
        this.products = data.records
        for(let item in data.records){
          this.products.push({'value': item.approvalUserName})
        }
        // 调用 callback 返回建议列表的数据
        cb(this.products)
      },
      reset() {
        this.search = {}
        this.search.stockCondition = "ALL"
        this.load()
      },
      async load () {
        this.loading = true
        const { data } = await request.post('product/admin/product/page', {
          current: this.page,
          map: {},
          model: {
            "productCode": this.search.productCode,
            "productName": this.search.productName,
            "stockCondition": this.search.stockCondition,
            "whetherOnSale": 'Y'
          },
          order: 'descending',
          size: this.pageSize,
          sort: 'id'
        })
        if(data) {
          this.productList = data.records
          this.page = data.current
          this.total = data.total
        } else {
          this.productList = []
          this.page = 0
          this.total = 0
        }
        this.loading = false
      },
      handleClose(done) {
        done()
      },
      handleConfirm () {
        this.$emit('getData', this.$refs.productTable.selection)
        this.$refs.productTable.clearSelection()
        this.visible = false
      },
      splitString (val) {
        if(val) {
          return val.split(',')[0]
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  .search-wrapper{
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    .el-input{
      width: 200px;
      margin-right: 10px;
    }
  }
  .el-table{
    margin-bottom: 20px;
    ::v-deep{
      .el-radio{
        .el-radio__label{
          display: none;
        }
      }
    }
  }
</style>
