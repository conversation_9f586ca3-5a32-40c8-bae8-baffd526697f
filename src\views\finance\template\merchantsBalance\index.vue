<template>
  <div class="archivesPageContent">
    <div class="temp_searchBox">
      <el-form
        :inline="true"
        ref="searchForm"
        :model="listQuery"
        class="form-inline"
      >
        <el-form-item label="" prop="name">
          <el-input
            v-model="listQuery.model.shopname"
            placeholder="请输入店铺名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="" prop="factory">
          <el-input
            v-model="listQuery.model.name"
            placeholder="请输入商家名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="">
          <el-cascader
            placeholder="请选择所在区域"

            v-model="cityValue"
            :props="props"
            @change="cityChange"
            clearable
          >
          </el-cascader>
        </el-form-item>
        <el-form-item>
          <el-form-item label="">
            <el-select
              v-model="listQuery.model.publishStatus"
              placeholder="请选择商家状态"
            >
              <!-- <el-option label="全部" value=""></el-option> -->
              <el-option label="已启用" value="Y"></el-option>
              <el-option label="已冻结" value="N"></el-option>
            </el-select>
          </el-form-item>
          <el-button type="primary" @click="onSearchSubmitFun">搜索</el-button>
          <el-button @click="resetForm('searchForm')">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="title flex_between_center">
      <tabs-layout
        ref="tabs-layout"
        :tabs="[ { name: '商家余额' } ]"
        @change="handleChangeTab"
      >
        <template slot="button">
          <el-button :disabled="multipleSelectionId.length == 0">批量审核</el-button>
        </template>
      </tabs-layout>
    </div>

    <div class="table">
      <el-table
        ref="table"
        v-if="list"
        @selection-change="selectTableItemFun"
        v-loading="listLoading"
        :data="list"
        row-key="id"
        border
        fit
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column
          align="center"
          width="65"
          :render-header="renderHeader"
          fixed
        >
          <template slot-scope="scope">
            <span>{{ scope.$index + 1 }} </span>
          </template>
        </el-table-column>
        <el-table-column type="selection" width="55" fixed></el-table-column>
        <el-table-column
          v-for="(item, index) in tableTitle"
          :key="index"
          :min-width="item.width ? item.width : '350px'"
          :label="item.label"
          show-overflow-tooltip
          align="left"
        >
          <template slot-scope="{ row }">
            <span>{{ row[item.name] }}</span>
          </template>
        </el-table-column>

        <el-table-column
          fixed="right"
          align="center"
          label="操作"
          width="150"
          class="itemAction"
        >
          <template slot-scope="scope">
            <el-button type="text" >查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-if="total > 0"
        :pageSizes="[10, 20, 50, 100]"
        :total="total"
        :page.sync="listQuery.current"
        :limit.sync="listQuery.size"
        @pagination="getlist"
      />
    </div>
    <!-- 设置 编辑 -->
    <!-- <el-dialog v-if="showEditPage" :title="(row.id>0?'编辑':'新增')+'销售商档案'" :visible.sync="showEditPage" width="80%" :show-close="false">
      <edit :visible.sync="showEditPage" :isReload.sync="submitReload" :tabType.sync="listQuery.model.approvalStatus.code" :row.sync="row"></edit>
    </el-dialog> -->
    <!-- 设置 编辑 -->
  </div>
</template>

<script>
import Pagination from "@/components/Pagination";
import { areas } from "@/api/enterprise";
import tableInfo from "@/views/finance/merchantsBalance/tableInfo";
import TabsLayout from '@/components/TabsLayout'
export default {
  name: "merchantsBalance",
  data() {
    return {
      listLoading: false,
      list: [1,2,3],
      tabType: "list",
      listQuery: {
        current: 1,
        size: 10,
        model: {},
      },
      total: 100,
      cityValue: [],
      tableTitle: [],
      tableSelectTitle: [0, 1, 2, 3],
      multipleSelection: [],
      multipleSelectionId: [],
      showSelectTitle: false,
      props: {
        lazy: true,
        async lazyLoad(node, resolve) {
          const { level } = node;
          let id = node.data ? node.data.id : "";
          let res = await areas({ parentId: id });
          let list = res.data;
          list.forEach((item) => {
            item.value = item.id;
            item.leaf = level >= 2;
          });
          resolve(list);
        },
      },
    };
  },
  methods: {
    cityChange(e) {
      this.listQuery.model.provinceId = e[0];
      this.listQuery.model.cityId = e[1];
      this.listQuery.model.countyId = e[2];
    },
    selectTableItemFun: function (val) {
      // let arr = [];
      // val.forEach((item) => {
      //   arr.push(item.id);
      // });
      // this.multipleSelection = val;
      // this.multipleSelectionId = arr;
    },
    onSearchSubmitFun() {},
    getlist() {},

    initTbaleTitle() {
      this.tableSelectTitle = [];
      this.tableTitle = tableInfo[this.tabType];
    },
    renderHeader(h, { column }) {
      var titles = tableInfo[this.tabType];
      var titlesName = ["显示字段项", "隐藏字段项"];
      return (
        <div style="position:relative">
          <div onClick={this.showHeaer}>
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="640px"
            center
            append-to-body={true}
          >
            <el-transfer
              vModel={this.tableSelectTitle}
              data={titles}
              titles={titlesName}
              onChange={this.setleftTitleFun}
            ></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button onClick={this.closeHeaer}>取消</el-button>
              <el-button type="primary" onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      );
    },
    setleftTitleFun(val) {
      this.tableSelectTitle = val;
    },
    showHeaer: function () {
      this.showSelectTitle = true;
    },
    closeHeaer: function () {
      this.showSelectTitle = false;
      this.tableSelectTitle = [];
    },
    setHeaer: function () {
      var titles = tableInfo[this.tabType];
      var listinfo = titles.filter((element, index, self) => {
        return !this.tableSelectTitle.includes(element.key);
      });
      this.tableTitle = listinfo;
      this.showSelectTitle = !this.showSelectTitle;
    },
  },
  created() {
    this.initTbaleTitle();
  },
  components: {
    Pagination,
    TabsLayout
  }
};
</script>


<style lang="scss" scoped>
.archivesPageContent {
  padding: 0;
  .temp_searchBox {
    height: 64px;
    overflow: hidden;
    margin-bottom: 0;
  }
  .form-inline {
    height: 60px;
    overflow: hidden;
  }
  .title {
    border-top: 16px solid #f2f3f4;
    border-bottom: 2px solid #ebecee;
    margin-bottom: 35px;
    padding: 0 12px;
    padding-top: 10px;
    span {
      margin-bottom: -2px;
      padding: 0 15px;
      height: 40px;
      line-height: 30px;
      display: block;
      background: rgba(255, 255, 255, 0);
      border-bottom: 2px solid rgb(64, 158, 255);
      font-size: 16px;
      font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
        "PingFang SC"-400;
      font-weight: 400;
      color: rgb(64, 158, 255);
    }
  }

  .table {
    padding: 0 12px;
  }
  .formItem {
    width: 586px;
  }
  .line {
    color: #dfe6ec;
    margin: 0 6px;
  }
  .typeTabs {
    height: 40px;
    margin-bottom: -2px;
    margin-left: 30px;
  }
}
</style>
