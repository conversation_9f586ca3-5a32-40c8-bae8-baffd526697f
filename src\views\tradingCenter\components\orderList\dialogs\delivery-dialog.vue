<template>
    <el-dialog
      title="商品发货"
      :visible.sync="deliveryVisible"
      width="76%"
      @close="handleClose">
      <p>选择商品：待发货{{waitPayNum}}   已发货{{partDeliveryNum}}</p>
      <ul class="table-top"><li>订单编号：{{delivery.orderNo}}</li><li v-if="delivery.purMerchant">客户名称：{{delivery.purMerchant.name}}</li>
        <li>下单时间：{{delivery.orderCreateTime}}</li></ul>
      <el-table border :data="tableData" @select-all="selectAll" @select='onTableSelect' ref="productTable">
        <el-table-column type="index" />
        <el-table-column type="selection" width="55"/>
        <el-table-column prop="productImg" label="商品主图" width="80">
          <template slot-scope="scope">
            <img :src="scope.row.productImg | imgFilter" class="productImg">
            <!-- <img :src="productImg" class="productImg" v-if="scope.row.productImg === ''"> -->
          </template>
        </el-table-column>
        <el-table-column label="商品编码" prop="productCode" width="152"/>
        <el-table-column label="商品名称" prop="productName"/>
        <el-table-column label="规格" prop="spec" width="80"/>
        <el-table-column label="生产厂家" prop="saleMerchantName"/>
        <el-table-column label="购买数量" prop="buyNum" width="78"/>
        <el-table-column label="已发数量" prop="endDeliveryNum" width="78"/>
        <el-table-column label="发货数量" prop="deliveryNum" width="122">
          <template slot-scope="{row}">
            <el-input-number v-if="row.show===true" controls-position="right" :min="1" :max="row.maxNum" v-model="row.num" style="width:110px;"></el-input-number>
            <span v-if="row.show===false">{{row.deliveryNum}}</span>
          </template>
        </el-table-column>
      </el-table>
      <div v-if="delivery.orderAddressInfo">
      <p>收件人：{{delivery.orderAddressInfo.receiverName}}{{delivery.orderAddressInfo.phone}}</p>
      <p>收货地址：{{delivery.orderAddressInfo.fullAddress}}</p>
      </div>
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" class="demo-ruleForm">
        <el-form-item label="发货方式：">
          <el-radio-group v-model="ruleForm.shippingReqWay" @change="changeWay">
            <el-radio label="EXPRESS">自己联系物流</el-radio>
            <el-radio label="NONE">无需物流</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="ruleForm.shippingReqWay === 'EXPRESS'" label="物流公司：" prop="logisName" :rules="[{required:ruleForm.shippingReqWay === 'EXPRESS' ? true : false,trigger: 'blur',message:'物流公司不能为空'}]">
          <el-select placeholder="请输入物流公司" v-model="ruleForm.logisName" style="width: 50%;">
            <el-option v-for="item in logisData" :label="item" :value="item"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="ruleForm.shippingReqWay === 'EXPRESS'" label="物流单号：" prop="shippingNo" :rules="[{required:ruleForm.shippingReqWay === 'EXPRESS' ? true : false,trigger: 'blur',message:'物流单号不能为空'}]">
          <el-input placeholder="请输入物流单号" v-model="ruleForm.shippingNo" style="width: 50%;"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="onsubmit('ruleForm')" v-if="checkPermission(['admin','orderDetail:delivery'])">发 货</el-button>
      </span>
    </el-dialog>
</template>

<script>
import {listProductDelivery,addDelivery,getExpressType} from '@/api/trade'
import checkPermission from "@/utils/permission";
import productImg from "@/assets/product.png";
export default {
  name: "delivery-dialog",
  props: ['dialogData','deliveryVisible'],
  data() {
    return {
      productImg: productImg,
      visible: false,
      delivery: '',
      ruleForm: {
        shippingReqWay: 'EXPRESS',
        logisName: '',
        shippingNo: '',
        deliveryReqStatus: 'HAD_DELIVERY'
      },
      rules: {},
      tableData: [],
      partDeliveryNum: 0,
      waitPayNum: 0,
      logisData: []
    }
  },
  mounted() {
    this.getExpress()
    this.getDevelivery()
  },
  methods: {
    checkPermission,
    async getDevelivery() {
     const {data} = await listProductDelivery(this.dialogData)
      this.delivery = data
      this.tableData = data.productList
      this.tableData.forEach(item => {
        item.show = false
        this.$set(item,'num',0)    //必须要这样赋值
        item.maxNum = item.deliveryNum
        this.partDeliveryNum += item.endDeliveryNum
        this.waitPayNum += item.deliveryNum
      })
    },
    async getExpress() {
      const data = await getExpressType(15)
      this.logisData = data.data.value.split(',')
    },
    changeWay(val) {
      if (val === 'EXPRESS') {
        this.ruleForm.deliveryReqStatus = 'HAD_DELIVERY'
      } else {
        this.ruleForm = {
          deliveryReqStatus: 'HAD_SIGN',
          logisName: '',
          shippingNo: '',
          shippingReqWay: 'NONE'
        }
      }
    },
    selectAll(rows) {
      if(rows.length === this.tableData.length) {
        this.tableData.forEach(item => {
          item.show = true
          item.deliverylNum = item.deliveryNum
          item.num = item.deliveryNum
        })
      } else {
        this.tableData.forEach(item => {
          item.show = false
        })
      }
    },
    onTableSelect(rows, row) {
      let selected = rows.length && rows.indexOf(row) !== -1
      const t = this.tableData.map(itm=>itm.orderItemId).indexOf(row.orderItemId)
      if(selected===true) {
        this.tableData[t].deliverylNum = row.deliveryNum
        this.tableData[t].num = row.deliveryNum
        this.tableData[t].show = true
      } else {
        this.tableData[t].show = false
      }
    },
    handleSelectionChange(rows,row){
     if (rows.length > 0) {
       if(rows.length === this.tableData.length) {
         this.tableData.forEach(item => {
           item.show = true
           item.deliverylNum = rows[0].deliveryNum
         })
       } else {
         let t = 0
         t = this.tableData.map(itm => itm.productId).indexOf(rows[rows.length - 1].productId)
         this.tableData[t].show = true
         this.tableData[t].deliverylNum = rows[t].deliveryNum
       }
      } else {
        this.tableData.forEach(item => {
          item.show = false
        })
      }
    },
    handleClose() {
      this.ruleForm = {
        shippingReqWay: 'EXPRESS',
        logisName: '',
        shippingNo: '',
        deliveryReqStatus: 'HAD_DELIVERY'
      }
      this.$refs['ruleForm'].resetFields()
      this.$refs.productTable.clearSelection()
      this.$emit('componentResult','close')
    },
    handleChange(id,val) {
      console.log(this.tableData)
      console.log(id,val)
      this.tableData.map(v=>{

      })

    },
    onsubmit(formName) {
      const selections = this.$refs.productTable.selection
      if (selections.length !== 0) {
      const deliveryItemList = selections.map((item, indx) => {
        if(!item.deliverylNum) {
          this.$message.warning('请填写发货数量')
          return false
        } else {
          return {
            deliverylNum: item.num,
            orderItemId: item.orderItemId
          }
        }

      })
      this.$refs[formName].validate((valid) => {
        if (valid && deliveryItemList[0] !== false) {
          const params = {
            ...this.ruleForm,
            deliveryReqStatus: this.ruleForm.deliveryReqStatus,
            orderId: this.dialogData,
            deliveryItemList: deliveryItemList,
            fullAddress: this.delivery.orderAddressInfo.fullAddress,
            receiverName: this.delivery.orderAddressInfo.receiverName,
            receiverPhone: this.delivery.orderAddressInfo.receiverPhone
          }
          addDelivery(params).then(res => {
            this.$message.success('所选商品已发货')
            this.$emit('componentResult', 'close')
          })
        } else {
          return false
        }
      })
      } else {
        this.$message.warning('请选择商品进行操作！')
      }
    }

  },
  watch: {
  }
}
</script>

<style lang="scss">
  .table-top {
    background: #f7f7f8;
    border: 1px solid #dfe6ec;
    margin-bottom: 0;
    padding: 15px 10px;
    border-bottom: 0;
    li {
      display: inline-block;
      list-style: none;
      margin-right: 20px;
    }
  }
</style>
