<template>
    <div class="items "   @click="checkPermission(['admin','fitment-wechat:edit'])&&showDrawerFun()">
        <div class="flex_between_start searchItem">
            <span >{{query.placeholderText}}</span>
            <button>搜索</button>
        </div>
        <el-drawer
            :destroy-on-close="true"
            :size="'345px'"
            append-to-body
            :wrapperClosable="false"
            :visible.sync="drawer"
            :with-header="false">
            <div class="flex_between_center top" >
                <div>预置关键词</div> 
                <div>
                    <el-button @click="drawer=false" >取 消</el-button>
                    <el-button type="primary"  @click="submitFun()" >提交</el-button>
                </div>
            </div>
            <el-form ref="ruleForm" :model="query" label-width="60px" class="form">
                <el-form-item label="关键词">
                    <el-radio-group v-model="type" @change="changeRadio">
                        <el-radio label="default">使用默认</el-radio>
                        <el-radio label="setting">自定义</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label=" " :label-width="'40px'" prop="placeholderText" >
                    <el-input :disabled="type!='setting'" v-model="setText" placeholder="请设置提示文字"></el-input>
                </el-form-item>
            </el-form>
        </el-drawer>
    </div>
    
</template>
<script>
import checkPermission from '@/utils/permission'
import { 
    pageComponentList,
    pageADList,
    pageADAdd,
    pageADEdit 
}  from "@/api/fitment";
export default {
data() {
return {
    componentCode:"search_placeholdertext",
    drawer:false,
    type:"default",
    defaultText:"输入商品名称/厂家/商家",
    setText:"输入商品名称/厂家/商家",
    query:{
        pageComponentId:1,
        id:11,
        placeholderText:"输入商品名称/厂家/商家",
    }
    
};
},
props: {},
methods: {
    checkPermission,
    showDrawerFun(){
        this.drawer=true;
        this.initFun()
    },
    changeRadio(val){
        if(val=="default"){
            this.setText=this.defaultText+""
        }else{
             this.setText=""
        }
    },
    async submitFun() {
      // 编辑->提交
      let _this = this;
      if(this.setText!=""){
          _this.query.placeholderText=this.setText
          _this.$message.success("成功");
          this.drawer=false
      }else{
          _this.$message.error("修改失败");
      }
      
    },
    async initFun(){
       var componentList = await  pageComponentList({
            "current": 1,
            "map": {},
            "model": {
                "componentCode": "search_placeholdertext"
            },
            "order": "descending",
            "size": 10,
            "sort": "id"
        })
    }
},
mounted() {
    
},
beforeDestroy() {}
};
</script>
<style lang="less" scoped>
.items{
    border:1px  dashed  red;
    margin-bottom: 12px;
    cursor: pointer;
} 
.searchItem{
    margin: 0 auto;
    width: 345px ;
    height: 30px ;
    border-radius: 125px ;
    background: #f2f8ff;
    border: 1px solid #409eff;
    position: relative;
}
.searchItem span{
    display:block;
    outline:none;
    width: 343px ;
    height: 28px ;
    line-height: 28px ;
    text-indent: 10px;
    border: none;
    border-radius: 125px 0 0 125px;
    font-size: 12px ;
    font-weight: 400 ;
    text-align: left ;
    background: transparent;
}
.searchItem button{
    width: 48px;
    height: 28px;
    background: #409eff;
    border: 1px solid #409eff;
    border-radius: 125px;
    font-size: 12px;
    outline:none;
    color: #ffffff;
}


.top{
    border-bottom: 1px solid #efefef;
    height:60px;
    padding:0 15px;
   
}
.form{
     padding:0 15px;
     margin-top: 16px;
     width:100%;

}
</style>