import { deepClone } from "@/utils";

const initTableItem = [
  {   key: '0.0',
    label: '商家名称',
    name: "saleMerchantName",
    width:'240px',
    disabled: true
  },
  {   key: 0,
    label: '客户状态',
    name: "publishStatus",
    width:'80px',
    disabled: true
  },
  {   key: 2,
    label: '客户名称',
    name: "name",
    width:'240px',
    disabled: true
  },
  {   key: 4,
    label: '企业类型',
    name: "merchantType",
    width:'136px',
    disabled: true
  },
  {   key: 6,
    label: '负责人',
    name: "ceoName",
    width:'110px'
  },
  {   key: 8,
    label: '所在地区/注册地址',
    name: "region",
    width:'168px'
  },
  {   key: 9,
    label: '企业纸质',
    name: "merchantLicenseFileList",
    width:'160px'
  },
  {   key: '9.1',
    label: '客户来源',
    name: "customerSource",
    width:'144px'
  },
  {   key: '9.2',
    label: '业务员',
    name: "salesmanName",
    width:'144px'
  },
  {   key: 10,
    label: '操作时间',
    name: "createTime",
    width:'170px'
  },
]

export default {
    ACCEPTED: deepClone(initTableItem),
    PENDING: deepClone(initTableItem),
    REJECTED: deepClone(initTableItem)
}
