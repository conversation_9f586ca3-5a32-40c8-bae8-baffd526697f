<template>
    <div class="items"  @click="checkPermission(['admin','fitment-wechat:edit'])&&showDrawerFun()">
        <div class="hotItems" >
            <span>热搜：</span>
            <el-tag class="hotItemInfo" type="info" v-for="(item, index) in queryHotItems" :key="index">{{item.name}}</el-tag>    
        </div>
        <el-drawer
            :destroy-on-close="true"
            :size="'345px'"
            append-to-body
            :wrapperClosable="false"
            :visible.sync="drawer"
            :with-header="false">
            <div class="flex_between_center top" >
                <div>搜索热词</div> 
                <div>
                    <el-button @click="drawer=false" >取 消</el-button>
                    <el-button type="primary"  @click="submitFun()" >提交</el-button>
                </div>
            </div>
            <div class="tipBox">
                <p class="title">搜索热词</p>
                <p class="tip">最多添加20个热词，默认展示前10个热词，其它的热词在搜索页中显示，鼠标拖拽可调热词顺序</p>
            </div>
            <el-form  label-width="75px" class="fromBox form">
                <el-form-item label="热词名称" class="hotItem"  v-for="(item,index) in hotItems" :key="index"   v-dragging="{ item: item, list: hotItems, group: 'item' }" >
                    <i class="el-icon-error" @click="removeItem(index)"></i>
                    <el-input v-model="item.name" placeholder="请设置提示文字"></el-input>
                </el-form-item>
            </el-form>
            <div class="addbtn" @click="addItemBtnFun">
                + 添加热词
            </div>
        </el-drawer>
    </div>
</template>
<script>
import checkPermission from '@/utils/permission' 
import { 
    pageComponentList,
    deleteByPageComponentId,
    pageADList,
    pageADAdd,
    pageADListAdd,
    pageADEdit 
}  from "@/api/fitment";
export default {
data() {
    return {
        drawer:false,
        parentId:null,
        queryHotItems:null,
        hotItems:null
    };
},
props: {
    pagePlateId:{
        type:String,
        required:true
    }
},
methods: {
    checkPermission,
    showDrawerFun(){
        this.drawer=true
        this.hotItems=[...[],...this.queryHotItems]
    },
    addItemBtnFun(){
        this.hotItems.push({
          id: "",
          pagePlateId:this.pagePlateId,
          showStatus:"Y",
          pageComponentId:this.pageComponentId,
          name: "",
          sortValue:this.hotItems.length
        })
    },
    removeItem(index){
        this.hotItems.splice(index,1)
    },
    async submitFun(){
        await deleteByPageComponentId(this.pageComponentId)
        console.info(this.hotItems)
        this.hotItems.forEach((item,index)=>{
            item.sortValue=index;
        })
        this.queryHotItems=[...[],...this.hotItems]
        this.drawer=false
        var data = await pageADListAdd(this.queryHotItems)
        if (data.code == 0) {
            this.initFun();
        } else {
            this.$message.error("提交失败！");
        }
    },
    async initFun(){
        var {data} = await  pageComponentList({
            "current": 1,
            "map": {},
            "model": {
                "componentCode": "hot_search"
            },
            "order": "descending",
            "size": 10,
            "sort": "id"
        })
        this.pageComponentId=data.records[0].id
        let adList = await  pageADList({
            "current": 1,
            "map": {},
            "model": {
                pageComponentId:data.records[0].id
            },
            "order": "descending",
            "size": 10,
            "sort": "id"
        })
        adList.data.records.forEach(item=>{
            item.showStatus=item.showStatus.code
        })
        this.queryHotItems=adList.data.records
    }
},
mounted() {
    // 请求热搜接口
    this.initFun()
},
beforeDestroy() {}
};
</script>
<style lang="less" scoped>
.items{
    border:1px  dashed  red;
    margin-bottom: 12px;
    cursor: pointer;
    .hotItemInfo{margin-right: 10px;}
} 
.hotItems{ margin: 0 auto;margin-top: 7px; width: 345px ;}
.hotItems span{
    line-height: 24px;
    height: 24px;
    font-size: 12px;
    font-weight: 400;
    text-align: center;
    border-radius: 20px;
}


.fromBox {
  max-height: calc(100vh - 260px);
  overflow-y: auto;
  padding: 20px 15px;
  width: 100%;
}
.top{
    border-bottom: 1px solid #efefef;
    height:60px;
    padding:0 25px;
   
}
.form{
     margin:0 auto;
     margin-top: 16px;
     width:305px;
    .hotItem{
        border: 1px solid #efefef;
        padding:15px 5px 15px 0 ;
        position: relative;
        i{  
            opacity: 0;
            position: absolute;
            right: -15px;
            top: -20px;
            font-size: 20px;
        }
        
    }
    :hover i{  
        opacity: 1;
    }
}
.addbtn{
    cursor: pointer;
    margin:0 auto;
    text-align: center;
    margin-top: 16px;
    width:305px;
    height: 40px;
    line-height: 40px;
    background: #ffffff;
    border: 1px solid #409eff;
    font-size: 14px;
    font-weight: 400;
    color: #409eff;
}
.tipBox{
    margin:0 auto;
    margin-top: 16px;
    width:305px;
    p.title{
        font-size: 14px;
        height: 19px;
        font-size: 14px;
        font-weight: 400;
    }
    p.tip{
        font-size: 14px;
        
        height: 40px;
        font-family:  -400;
        font-weight: 400;
        color: #aaaaaa;
        line-height: 20px;

    }
}
</style>