<template>
  <div class="detail-item">
    <module-title :title="itemName" />
  <div><slot></slot></div>
  </div>
</template>

<script>
import ModuleTitle from '@/components/PageModuleTitle'
export default {
  name: "detail-item",
  components: {
    ModuleTitle
  },
  props: {
    itemName: {
      type: String,
      default: '卡片名称'
    },
    tip: {
      type: String,
      default: ''
    }
  }
}
</script>

<style scoped>

</style>
