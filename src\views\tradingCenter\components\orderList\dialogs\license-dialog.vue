<template>
    <el-dialog
      title="客户资质"
      :visible.sync="deliveryVisible"
      width="75%"
      @close="handleClose">
      <el-carousel :interval="4000" type="card" height="500px">
        <el-carousel-item v-for="item in dialogData" :key="item.filePath">
          <h3 class="medium"><img :src="item.filePath" style="height: 100%;"/></h3>
        </el-carousel-item>
      </el-carousel>
    </el-dialog>
</template>

<script>
export default {
  name: "license-dialog",
  props: ['dialogData','deliveryVisible'],
  data() {
    return {
    }
  },
  methods: {
    handleClose() {
      this.$emit('componentResult','close')
    },
  }
}
</script>

<style scoped>

</style>
