<template>
  <div class="container" v-if="currentRow && currentRow.id">
    <div class="tob_btn">
      <div class="top_name">{{ currentRow.name }} {{ total > 0 ? `(${total})`: '' }}</div>
      <div>
        <el-input style="width:200px;margin-right:20px" v-model="model.keyword" @blur="reload" @keyup.enter.native="reload" placeholder="搜索姓名/手机号"></el-input>
        <el-button v-if="checkPermission(['admin', 'admin-team-role:modify'])" size="small" type="primary" plain @click="batchChange">批量变更</el-button>
        <el-button v-if="checkPermission(['admin', 'admin-team-role:del'])" size="small" type="primary" plain @click="batchRemove">批量移除</el-button>
        <el-button v-if="checkPermission(['admin', 'admin-team-role:add'])" size="small" type="primary" @click="addMember">+ 新增成员</el-button>
      </div>
    </div>
    <!-- 表格 -->
      <table-pager :rowKey="rowKey" :reserveSelection="true" ref="multipleTable" :height="600" :options="tableColumns"
        :remote-method="initData" :data.sync="listData" :selection="true" @selection-change="handleSelectionChange"
        @selection-all="handleSelectAll" :isNeedButton="false" :pageSize="pageSize">
        <el-table-column label="所在区域"  width="300" slot="countyName">
          <slot slot-scope="scope">
            <div>{{ scope.row.provinceName }} - {{ scope.row.cityName }} - {{ scope.row.countyName }}</div>
          </slot>
        </el-table-column>
      </table-pager>
      <addMemberDia ref="addMemberDiaRef" @handleReload="handleReload"></addMemberDia>
      <roleChange ref="roleChangeRef" @handleReload="handleReload"></roleChange>
  </div>
</template>
<script>
  import addMemberDia from '@/views/team/index/temp/role/components/addMemberDia'
  import { salesManList,deleteSalesMan } from '@/api/organization/role';
  import roleChange from "@/views/team/index/temp/role/components/roleChange"
  import checkPermission from '@/utils/permission';
  const TableColumns = [
    {
      label: "业务员编码",
      name: "code",
      prop: "code",
      width: "200"
    },
    {
      label: "姓名",
      name: "salesmanName",
      prop: "salesmanName",
    },
    {
      label: "手机号码",
      name: "mobile",
      prop: "mobile",
      width: "200"
    },
    {
      label: "所在区域",
      name: "countyName",
      prop: "countyName",
      width: "300",
      slot:true
    },
    {
      label: "所属部门",
      name: "departmentName",
      prop: "departmentName",
      width: "200"
    }
  ];
  const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({
      key: i,
      ...TableColumns[i]
    });
  }
  export default {
    data() {
      return {
        rowKey: "id",
        query:{
            current:1,
            map:{},
            order: 'descending',
            size: 10,
            sort: 'id'
        },
        pageSize:10,
        tableColumns: TableColumnList,
        model: {
          keyword: ''
        },
        total:0,
        listData: [],
        currentRow: null,
        selectList:[]
      }
    },
    components: {
      // 组件
      addMemberDia,
      roleChange
    },
    computed: {
      // 计算属性computed : 
      // 1. 支持缓存，只有依赖数据发生改变，才会重新进行计算
      // 2. 不支持异步，当computed内有异步操作时无效，无法监听数据的变化
      // 3. computed 属性值会默认走缓存，计算属性是基于它们的响应式依赖进行缓存的，也就是基于data中声明过或者父组件传递的props中的数据通过计算得到的值
      // 4. 如果一个属性是由其他属性计算而来的，这个属性依赖其他属性，是一个多对一或者一对一，一般用computed
      // 5.如果computed属性属性值是函数，那么默认会走get方法；函数的返回值就是属性的属性值；在computed中的，属性都有一个get和一个set方法，当数据变化时，调用set方法。
    },
    filters: {

    },
    watch: {
      // 监听属性watch：
      // 1. 不支持缓存，数据变，直接会触发相应的操作；
      // 2. watch支持异步；
      // 3. 监听的函数接收两个参数，第一个参数是最新的值；第二个参数是输入之前的值；
      // 4. 当一个属性发生变化时，需要执行对应的操作；一对多；
      // 5. 监听数据必须是data中声明过或者父组件传递过来的props中的数据，当数据变化时，触发其他操作，函数有两个参数，
      // 　　immediate：组件加载立即触发回调函数执行，
      // 　　deep: 深度监听，为了发现对象内部值的变化，复杂类型的数据时使用，例如数组中的对象内容的改变，注意监听数组的变动不需要这么做。注意：deep无法监听到数组的变动和对象的新增，参考vue数组变异,只有以响应式的方式触发才会被监听到。
      // 　　deepdemo：
      //      obj:{
      //          handler(){
      //              console.log('obj 变了')
      //          },
      //          deep:true
      //      }

    },
    methods: {
      checkPermission,
        async initData(query){
            let listQuery = {
                model:{
                    ...this.model,
                    orgRoleId:this.currentRow.id
                }
            };
            console.log('query',query,listQuery);
            Object.assign(listQuery,query);
            let result = await salesManList(listQuery);
            if (result.code != 0 && result.msg != 'ok') {
                return
            };
            this.listData = result.data.records;
            this.total = result.data.total;
            return result;
        },
      //方法集合
      // 批量变更
      batchChange() {
        if(this.selectList.length < 1) {
          this.$message.warning('请先选择需要变更成员');
          return
        }
        let query = {
          ...this.currentRow,
          list:this.selectList
        }
        this.$refs.roleChangeRef.openDia(query);
      },
      // 批量移除
      batchRemove() {
        console.log('-------->',this.selectList);
        if(this.selectList.length < 1) {
          this.$message.warning('请先选择需要移除的成员');
          return
        }
        let salesManIds = [];
        this.selectList.forEach(element=>{
          salesManIds.push(element.id);
        });
        let params = {
          orgRoleId:this.currentRow.id,
          ids:salesManIds
        };
        this.$confirm(`您确定从${this.currentRow.name}里移除成员吗？`,"移除成员",{
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: 'warning',
        }).then(()=>{
          console.log('------------');
          deleteSalesMan(params).then(res=>{
            if(res.code == 0 && res.msg == 'ok') {
              this.$message.success('批量移除这些成员成功');
              this.$refs.multipleTable.clearSelection();
              this.reload();
            }
          })
        })
      },
      // 新增成员
      addMember() {
        console.log('---------->');
        this.$refs.addMemberDiaRef.handleOpen(this.currentRow);
      },
      switchover(row) {
        // console.log('row---->',row);
        this.currentRow =null;
        setTimeout(() => {
          this.currentRow = row;
        }, 10);
      },
      reload() {
        this.handleRefresh({
          page: 1,
          pageSize: this.pageSize
        })
      },
      handleRefresh(pageParams) {
        this.$refs['multipleTable'].doRefresh(pageParams)
      },
      handleSelectionChange(val) {
        console.log('val---->', val);
        this.selectList = val;
      },
      handleSelectAll(val) {
        console.log('val---->', val);
        this.selectList = val;
      },
      handleReload() {
        this.$refs.multipleTable.clearSelection();
        this.reload();
      }
    },
    mounted() {
      // 方法调用
    },
    beforeDestroy() {}
  }

</script>

<style lang="less" scoped>
  .container {
    background-color: #fff;
    padding: 20px;

    .tob_btn {
        margin-bottom: 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .top_name {
        font-size: 18px;
      }
    }
  }

</style>
