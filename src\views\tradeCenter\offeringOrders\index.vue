<template>
  <div v-if="isDefaultShow===true" class="detail-wrapper">
    <p class="detail-title">
      代客下单
    </p>
    <div style="padding-left: 20px;">
      <el-input
      v-model="client.name"
      placeholder="请选择客户"
      style="width: 300px;"
    >
      <el-button slot="append" icon="el-icon-more" @click="openClient"></el-button></el-input>
    </div>
    <div class="detail-items">
      <detail-item itemName="客户信息">
        <ul>
          <li>客户编码：{{client.code}}</li>
          <li>客户名称：{{client.name}}</li>
          <li>客户类型：{{client.merchantType}}</li>
          <li>客户资质：<el-link class="link-dialog" @click="openLisence">查看客户资质</el-link></li>
          <li>联系人：{{client.ceoName}}</li>
          <li>联系电话：{{client.ceoMobile}}</li>
          <li>所在区域：{{client.region}}</li>
        </ul>
      </detail-item>
      <detail-item itemName="收货地址" v-if="client.deliveryAddressList">
        <el-table
          ref="singleTable"
          :data="client.deliveryAddressList"
          highlight-current-row
          @current-change="handleCurrentChange"
          :show-header="false"
          style="width: 100%">
          <el-table-column label="选择" width="30">
            <template slot-scope="scope">
              <el-radio v-model="deliveryAddressId" :label="scope.row.id" @change="radioChange"></el-radio>
            </template>
          </el-table-column>
          <el-table-column
            property="name"
            label="姓名"
            width="80px">
          </el-table-column>
          <el-table-column
            property="mobilPhone"
            label="电话"
          width="110px">
          </el-table-column>
          <el-table-column
            property="region"
            label="地址">
            <template slot-scope="{row}">
              <span>{{row.region}}{{row.detailedAddress}}</span>
            </template>
          </el-table-column>
        </el-table>
        <!--<el-radio-group class="address-group" v-model="deliveryAddressId">
          <el-radio v-for="item in client.deliveryAddressList" :label="item.id">{{item.name}}<span style="display: inline-block;margin: 0 15px;">{{item.mobilPhone}}</span>{{item.region}}{{item.detailedAddress}}</el-radio>
        </el-radio-group>-->
      </detail-item>
      <detail-item itemName="发票信息" v-if="client.invoiceInfo">
          <p>发票类型：{{client.invoiceInfo.invoiceType.desc ? client.invoiceInfo.invoiceType.desc : '无'}}</p>
          <p>发票抬头：{{client.invoiceInfo.name ?  client.invoiceInfo.name : '无'}}</p>
          <p>税号：{{client.invoiceInfo.taxNumber ? client.invoiceInfo.taxNumber : '无'}}</p>
          <p>单位地址：{{client.invoiceInfo.registerAddress ? client.invoiceInfo.registerAddress : '无'}}</p>
          <p>注册电话：{{client.invoiceInfo.registerMobile ? client.invoiceInfo.registerMobile: '无'}}</p>
          <p>银行账号：{{client.invoiceInfo.bankNumber ? client.invoiceInfo.bankNumber:'无'}}</p>
          <p>开户银行：{{client.invoiceInfo.depositBank ? client.invoiceInfo.depositBank:'无'}}</p>
      </detail-item>
      <detail-item itemName="商品信息">
        <div>
          <el-input
          v-model="state"
          placeholder="请选择商品"
          style="width: 300px;"

        >
          <el-button slot="append" icon="el-icon-more" @click="openProductDialog"></el-button></el-input>
        </div>
        <p style="background: #f7f7f8;margin-bottom: 0;padding: 10px;border: 1px solid #dfe6ec;border-bottom: 0;">客户名称：{{client.name}}</p>
        <el-table
          border
        :data="selectedData"
        style="width: 100%">
          <el-table-column type="index" />
          <el-table-column label="产品主图" width="80">
            <template slot-scope="scope">
              <img  :src="scope.row.pictIdS | imgFilter" width="50px">
              <!-- <img :src="pictImg" width="50px" v-if="scope.row.pictIdS == null || scope.row.pictIdS == ''"> -->
            </template>
          </el-table-column>
          <el-table-column label="商品编码" prop="productCode"/>
          <el-table-column label="商品名称" prop="productName"/>
          <el-table-column label="规格" prop="spec"/>
          <el-table-column label="生产厂家" prop="manufacturer"/>
          <el-table-column label="单价" prop="salePrice"/>
          <el-table-column label="购买数量" width="135px;" prop="quantity">
            <template slot-scope="scope">
              <el-input-number  v-model="scope.row.quantity"  controls-position="right" @change="getOrderConfirm(scope.row.id,$event)" @blur="getItemTotal(scope.row)" :min="1" :max="Number(scope.row.stockQuantity)" style="width:110px;"></el-input-number>
            </template>
          </el-table-column>
          <el-table-column label="小计" prop="itemTotal">
            <slot slot-scope="{row}">
              {{row.itemTotal|getDecimals}}
            </slot>
          </el-table-column>
          <el-table-column label="操作" width="55px">
            <template slot-scope="scope">
              <slot><el-button type="text" class="text-primary" @click="handleDel(scope.$index)">删除</el-button></slot>
            </template>
          </el-table-column>
        </el-table>
        <div class="remarks">
          <span>订单备注：</span>
          <el-input type="textarea" v-model="remark" rows="3" placeholder="请输入订单备注" style="width: 35%;"></el-input>
          <div class="fr" v-if="orderData!==''">
            <p><span class="text-red">{{orderData.itemAmount}}</span>种<span class="text-red">{{orderData.productTotalQuantity}}</span>件，商品总价：<span class="price">￥{{orderData.productTotalAmount}}</span></p>
            <p>运费：<span class="price">￥{{orderData.freightAmount===null ? 0 : orderData.freightAmount}}</span></p>
            <p>优惠：<span class="price">￥{{orderData.discountAmount===null ? 0 : orderData.discountAmount}}</span></p>
            <p style="font-size: 16px;font-weight: bolder">总金额：<span class="text-red price">￥{{orderData.originalTotalPrice}}</span></p>
          </div>
        </div>
      </detail-item>
      <div class="fr" style="margin-top: 20px;"><el-button type="primary" @click="orderSubmit" :disabled="disabled" v-if="checkPermission(['admin','offering:submit'])">提交订单</el-button></div>
    </div>
    <product-dialog ref="productDialog" @getData="getProductData"/>
    <client-dialog :visible="showAdd" @changeShow="changeAddUser" @getClient="getClient"/>
    <license-dialog :licenseVisible="showLicense" @changeShow="changeLicense" :dialogData="merchantLicenses"/>
  </div>
  <div v-else-if="isDefaultShow===false">
    <div class="detail-items">
      <div class="order-header">
        <el-button type="success" circle icon="el-icon-check" class="fl"></el-button>
        <h3>订单提交成功，请及时联系客户完成付款<br><small style="font-weight: 500;">订单提交成功，等待客户付款</small></h3>
        <p class="fr">应付总金额：<span class="text-red">￥{{orders.totalMoney}}</span><br><el-button type="text" @click="$router.push({path: '/sale/detail',query:{id:orders.id}})">查看详情</el-button></p>
      </div>
      <ul class="order-list" v-if="orders.orderAddressInfo">
        <li>订单编号：{{orders.orderNo}}</li>
        <li>下单时间：{{orders.updateTime}}</li>
        <li v-if="orders.purMerchant">客户名称：{{orders.purMerchant.name}}</li>
        <li>收货人：{{orders.orderAddressInfo.receiverName}}</li>
        <li>联系手机：{{orders.orderAddressInfo.receiverPhone}}</li>
        <li>收货地址：{{orders.orderAddressInfo.fullAddress}}</li>
        <li>下单人：{{orders.orderUserName}}</li>
        <li>下单人手机：{{orders.orderUserPhone}}</li>
      </ul>
    </div>
    <div style="width:100%;height: 20px;background-color: #F2F3F4;">
    </div>
    <p class="detail-title" style="margin-top: 0;">
      商品信息
    </p>
    <el-table :data="orders.orderItemList" style="width: 100%" :show-header="false" stripex>
        <el-table-column type="index" width="40" align="right"/>
        <el-table-column>
          <template slot-scope="{row}">
            <p class="product-slot">{{row.productName}}
            <span>规格：{{row.spec}}</span>
            <span>厂家：{{row.saleMerchantName}}</span>
            <span>销售价：{{row.unitMoney}}</span>
            <span>数量：{{row.totalNum}}</span>
            </p>
          </template>
        </el-table-column>
        <el-table-column prop="subtotal" width="120">
          <template slot-scope="scope">
         ￥{{scope.row.subtotal}}
          </template>
        </el-table-column>

      </el-table>
  </div>
</template>

<script>
  import checkPermission from "../../../utils/permission";
  import detailItem from '@/views/merchant/list/detail-item'
  import { getPurMerchantLicenseDetail,merchantPurSaleRelList } from '@/api/group'
  import {OrderConfirm,saveOrder,orderDetail} from '@/api/trade'
  import productDialog from './product-dialog'
  import clientDialog from './client-dialog'
  import licenseDialog from './license-dialog'
  import productImg from '../../../assets/product.png'
  export default {
  name: "index",
  components: {
    detailItem,
    productDialog,
    clientDialog,
    licenseDialog
  },
  data() {
    return {
      client: {},
      namedArgumentList: '',
      tableclient: '',
      showLicense: false,
      showAdd: false,
      state: '',
      selectedData: [],
      deliveryAddressId: '',
      remark: '',
      merchantLicenses: [],//客户资质,
      purMerchantId: '',
      orderData: '',
      items:[],
      disabled: true,
      orders: '',
      isDefaultShow: true,
      hash: [],
      pictImg: productImg
    }
  },
  methods: {
    checkPermission,
    radioChange(val) {
      this.deliveryAddressId = val
    },
    handleCurrentChange(val) {
      this.deliveryAddressId = val.id
    },
    handleDel(index) {
      this.selectedData.splice(index,1)
    },
    getItemTotal(row) {
      if (!row.quantity) {
        this.$message.warning('购买数量不能为空')
        row.quantity = 1
      }
      row.itemTotal = row.quantity * row.salePrice

    },
    changeAddUser(data) {
      if (data === 'false') {
        this.showAdd = false
      } else {
        this.showAdd = true
      }
    },
    changeLicense(data) {
      if (data === 'false') {
        this.showLicense = false
      } else {
        this.showLicense = true
      }
    },
    openProductDialog () {
      this.$refs.productDialog.visible = true
    },
    getProductData(val) {
      this.disabled = false
      val.forEach(item => {
        /*if (item.stockQuantity < 10) {
          this.$set(item,'quantity',item.stockQuantity)    //必须要这样赋值
        } else {
          this.$set(item,'quantity',10)    //必须要这样赋值
        }*/
        this.$set(item,'quantity',1)    //必须要这样赋值
        item.itemTotal = item.salePrice * item.quantity
        this.hash.push(item)
      })
      this.selectedData = [...new Set(this.hash)]
      this.getOrderConfirm()
    },
    //获取订单价格
    async getOrderConfirm(id,val) {
        this.items = this.selectedData.map(item => {
          if(!item.quantity) {
            item.quantity = 1
          }
          if (item.id === id) {
            if(!val) {
              val = 1
            }
            item.itemTotal = val * item.salePrice
          }
          return {
            productId: item.id,
            quantity: item.quantity,
            skuId: item.skuIds[0]
          }
        })
        const params = {
          deliveryAddressId: this.deliveryAddressId,
          items: this.items,
          purMerchantId: this.purMerchantId,
          remark: this.remark
        }
        const {data} = await OrderConfirm(params)
        this.orderData = data

    },
    async orderSubmit() {
      const params = {
        deliveryAddressId: this.deliveryAddressId,
        items: this.items,
        purMerchantId: this.purMerchantId,
        remark: this.remark
      }
      const {data} = await saveOrder(params)
      this.$message.success('提交订单成功！')
      this.getOrder(data.id)
      this.isDefaultShow = false
    },
    //获取订单信息
    async getOrder(id) {
      const {data} = await orderDetail(id)
      this.orders = data
    },
    openLisence() {
      this.showLicense = true
    },
    openClient() {
      this.showAdd = true
    },
    //获取客户信息
    async getClient(val) {
      this.purMerchantId = val.purMerchantId
      const { data } = await getPurMerchantLicenseDetail(this.purMerchantId)
      this.client = data
      this.deliveryAddressId = this.client.deliveryAddressList[0].id
      this.$nextTick(()=>{
        this.$refs.singleTable.setCurrentRow(this.client.deliveryAddressList[0])
      })

      this.merchantLicenses = this.client.merchantLicenses
    },
    splitString(val) {
      console.log(val)
      if(val) {
        return val.split(',')[0]
      }
    }
  },
  mounted() {
    merchantPurSaleRelList({model:{}}).then(({data})=>{
      this.client = data.records[0]
      this.getClient(this.client)
    })
  }
}
</script>

<style lang="scss">
  .detail-wrapper {
    .el-input-group__append {
      background: #fff;
      border: none;
      .el-button {
        margin-left: -70px;
      }
    }
  }
  .address-group {
    .el-radio {
      display: block;
      margin-bottom: 10px;
    }
    margin-bottom: 20px;
  }
  .remarks {
    padding: 20px;
    border-right: 1px solid #ddd;
    border-left: 1px solid #ddd;
    span {
      vertical-align: top;
    }
  }
  .product-slot {
    span {
      padding-left: 10px;
      color: #999;
    }
  }
 .singleTable{
   ::v-deep{
     .el-radio{
       .el-radio__label{
         display: none;
       }
     }
   }

 }
 .order-list {
   padding-left: 0;
   li {display:inline-block;width: 25%;list-style: none;line-height: 32px;}
 }
  .order-header {
    padding: 30px 0;
    border-bottom: 1px solid #dfe6ec;
    .fl {
      margin-right: 10px;
      font-size: 18px;
    }
    .fr {
      text-align: right;
    }
    h3 {
      display: initial;
    }
    p {
      margin-top: -15px;
    }
  }
  .text-red {
    color: #FF3C54;
  }
  .price {
    display: inline-block;
    width: 30%;
  }
  .remarks {
    font-size: 14px;
    padding-bottom: 60px;

    .fr {
      width: 40%;
      text-align: right;
    }
    p {
      margin: 5px;
    }
  }

</style>
