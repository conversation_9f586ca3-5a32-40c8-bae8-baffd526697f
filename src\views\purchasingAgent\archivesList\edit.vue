<template>
  <div class="archivesEditContent">
    <el-dialog append-to-body title="" :visible.sync="isShowBigPic" width="30%">
      <img width="100%" :src="dialogImageUrl" alt="" srcset="" />
    </el-dialog>

    <el-form :inline="true" label-width="140px" :model="query" ref="editForm" :rules="rules">
      <div class="item">
        <div class="title"><span>基础信息</span></div>
        <div>
          <el-form-item class="formItem" prop="code" label="客户编码:">
            <el-input :disabled="true" clearable style="width: 200px" :value="'系统根据规则自动生成'" placeholder="请填写客户编码"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="name" label="客户名称:" :rules="[
              { required: true, message: '请填写客户名称！', trigger: 'blur' },
              {min:3,message: '请至少输入3个字', trigger: 'blur'}
            ]">
            <el-input :disabled="isEdit" clearable style="width: 200px" v-model="query.name" placeholder="请填写客户名称"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="identifyCode" label="客户识别码:" :rules="[
              {
                required: true,
                min:4,
                max:4,
                message: '请输入4位客户识别码，如： ZKYY',
                trigger: 'blur',
              },
            ]">
            <el-input :disabled="isEdit" clearable style="width: 200px" v-model="query.identifyCode" placeholder="请输入4位客户识别码，如： ZKYY"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="socialCreditCode" label="社会统一信用代码:" :rules="[
              {
                required: true,
                message: '请输入社会统一信用代码',
                trigger: 'blur',
              },
            ]">
            <el-input :disabled="isEdit" clearable style="width: 200px" v-model="query.socialCreditCode" placeholder="请输入社会统一信用代码"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="legalPerson" label="法定代表人:" :rules="[
              {
                required: false,
                message: '请填写法定代表人姓名',
                trigger: 'blur',
              },
            ]">
            <el-input clearable :disabled="isEdit" style="width: 200px" v-model="query.legalPerson" placeholder="请填写法定代表人姓名"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="ceoName" label="负责人:" :rules="[
              { required: true, message: '请填写负责人姓名', trigger: 'blur' },
            ]">
            <el-input :disabled="isEdit" clearable style="width: 200px" v-model="query.ceoName" placeholder="请填写负责人姓名"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="ceoMobile" label="负责人手机:">
            <el-input :disabled="isEdit" clearable style="width: 200px" v-model="query.ceoMobile" placeholder="请填写负责人手机"></el-input>
          </el-form-item>

          <el-form-item class="formItem" prop="qualityPersonInCharge" label="质量负责人:" :rules="[
              {
                required: true,
                message: '请输入质量负责人姓名',
                trigger: 'blur',
              },
            ]">
            <el-input :disabled="isEdit" clearable style="width: 200px" v-model="query.qualityPersonInCharge" placeholder="请输入质量负责人姓名"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="regionId" label="所在区域:" :rules="[
              { required: true, message: '请选择所在区域', trigger: 'blur' },
            ]">
            <el-cascader :disabled="isEdit" ref="city" v-model="query.regionId" style="width: 200px" placeholder="请选择所在区域"  :props="{ value: 'id', label: 'label'}" @change="cityChange"
              :options="areasTree" clearable>
            </el-cascader>
          </el-form-item>
          <el-form-item class="formItem" prop="registerAddress" label="注册地址:" :rules="[
              { required: true, message: '请填写注册地址', trigger: 'blur' },
            ]">
            <el-input :disabled="isEdit" clearable style="width: 200px" v-model="query.registerAddress" placeholder="请填写注册地址"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="registerCapital" label="注册资金:" :rules="[
              {
                required: false,
                message: '请填写注册资金',
                trigger: 'blur',
              },
            ]">
            <el-input :disabled="isEdit" clearable style="width: 200px" v-model="query.registerCapital" placeholder="请填写注册资金"></el-input>
          </el-form-item>
        </div>
      </div>

      <div class="item" v-if="!row.id">
        <div class="title"><span>账户信息</span></div>
        <div>
          <el-form-item class="formItem" prop="loginAccount" label="登录账号:" :rules="[
              { required: true, message: '请填写登录账号', trigger: 'blur' },
            ]">
            <el-input :disabled="isEdit" clearable style="width: 240px" v-model="query.loginAccount" placeholder="请填写登录账号"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="password" label="登录密码:">
            <el-input :disabled="isEdit" style="width: 240px" placeholder="请输入密码" v-model="query.password" show-password></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="confirmPassword" label="确认密码:">
            <el-input :disabled="isEdit" style="width: 240px" placeholder="请输入密码" v-model="query.confirmPassword" show-password></el-input>
          </el-form-item>
        </div>
      </div>

      <div class="item">
        <div class="title"><span>经营范围</span></div>
        <el-row :gutter="12" v-for="(list,key) of businessScope" :key="key" style="padding-bottom: 20px">
          <el-col :span="2">
            <div style="text-align: right;">{{key}}</div>
          </el-col>
          <el-col :span="22">
            <el-checkbox-group v-model="checkList" :disabled="isEdit" @change="checkListFun">
              <el-checkbox v-for="item in list" :key="item.id" :label="item.id">
                <span>{{item.childrenName}}</span>
              </el-checkbox>
            </el-checkbox-group>
          </el-col>
        </el-row>
      </div>
      <div class="item">
        <div class="title"><span>客户资质</span></div>
        <div>
          <template>
            <el-form-item class="formItem" prop="merchantTypeId" label="企业类型:" :rules="[
                {
                  required: true,
                  message: '请选择企业类型',
                  trigger: 'blur',
                },
              ]">
              <el-select :disabled="isEdit" v-model="query.merchantTypeId" placeholder="请选择企业类型">
                <el-option v-for="item in listmerchantType" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <div class="graytext">
              上传材料为复印件加盖企业原印公章且均在有效期内，支持JPG、JPEG、PNG、BMP格式，大小不超过2M
            </div>
            <template>
              <el-table :data="lisenceTableDate" style="width: 100%" border>
                <el-table-column prop="label" label="证件类型"></el-table-column>
                <el-table-column prop="licenseNumber" label="证件号">
                  <template slot-scope="{ row }">
                    <el-input v-if="row.isEdit" placeholder="请输入证件号" v-model="row.licenseNumber"></el-input>
                    <span v-else>{{ row.licenseNumber }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="licenseEndTime" label="过期时间">
                  <template slot-scope="{ row }">
                    <el-date-picker v-if="row.isEdit" v-model="row.licenseEndTime" type="datetime" style="width: 240px" placeholder="选择日期" value-format="yyyy-MM-dd HH:mm:ss"></el-date-picker>
                    <span v-else>{{ row.licenseEndTime }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="filePath" label="附件">
                  <template slot-scope="{ row }">
                    <el-button v-if="!row.isEdit&&!row.filePath" type="text" >查看示例图片</el-button>
                    <el-upload v-if="row.isEdit" :class="{hide: !row.isEdit}" ref="uploadlisence" :limit="3" :file-list="row.filePathList" :action="$uploadUrl" :data="insertProgram"
                      :headers="headersProgram" list-type="picture-card" :on-remove="handleRemove" :on-success="uploadSuccess" :before-upload="beforeUpload" accept=".jpg,.png,.bmp,.jpeg">
                      <i class="el-icon-plus"></i>
                    </el-upload>

                    <span v-else>
                      <img v-for="file in row.filePathList" :key="file.url" class="el-upload-list__item-thumbnail" :src="file.url" alt="" style="contain:cover;width:40px;height:40px;margin-right:5px">
                    </span>
                  </template>
                </el-table-column>
                <el-table-column label="操作">
                  <template slot-scope="{ row }">
                    <el-button type="text"  v-show="!row.isEdit" @click="editLisenceFun(row)"> 编 辑 </el-button>
                    <el-button type="text"  v-show="row.isEdit" @click="cancelLisenceEdit(row)" style="color: rgb(127, 127, 127);"> 取 消 </el-button>
                    <el-button type="text"  v-show="row.isEdit" @click="confirmLisenceEdit(row)"> 确 定 </el-button>
                    <el-button type="text"  v-show="!row.isEdit">预览</el-button>
                    <el-button type="text"  v-show="!row.isEdit">下载</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </template>
          </template>
        </div>
      </div>

      <div class="item">
        <div class="title"><span>发货地址</span></div>
        <template>
          <el-table :data="addrtableDate" style="width: 100%" border>
            <el-table-column prop="name" label="收货人姓名" width="120">
              <template slot-scope="{ row }">
                <el-input v-if="row.isEdit" placeholder="请输入收货人" v-model="row.name"></el-input>
                <span v-else>{{row.name}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="mobilPhone" label="联系手机" width="170">
              <template slot-scope="{ row }">
                <el-input v-if="row.isEdit" placeholder="请输入联系手机" v-model="row.mobilPhone"></el-input>
                <span v-else>{{row.mobilPhone}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="fixedPhone" label="联系电话" width="170">
              <template slot-scope="{ row }">
                <el-input v-if="row.isEdit" placeholder="请输入联系电话" v-model="row.fixedPhone"></el-input>
                <span v-else>{{row.fixedPhone}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="region" label="收获区域" width="320">
              <template slot-scope="{ row }">
                <el-cascader :disabled="!row.isEdit" ref="addr" v-model="row.address" style="width: 240px" placeholder="请选择所在区域"  :props="{ value: 'id', label: 'label'}"
                  :options="areasTree" clearable>
                </el-cascader>
              </template>
            </el-table-column>
            <el-table-column prop="detailedAddress" label="详细地址" width="220">
              <template slot-scope="{ row }">
                <el-input v-if="row.isEdit" placeholder="请输入详细地址" v-model="row.detailedAddress"></el-input>
                <span v-else>{{row.detailedAddress}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="region" align="center" width="250" label="是否启用">
              <template slot-scope="{row}">
                <span v-if="row.isEdit">
                  <el-radio v-model="row.isOpen"  label="Y">启用</el-radio>
                  <el-radio v-model="row.isOpen"  label="N">禁用</el-radio>
                </span>
                <span v-else> {{row.isOpen == 'Y'? '启用' : '禁用'}} </span>
              </template>
            </el-table-column>
            <el-table-column fixed="right" align="center" label="操作" width="130" class="itemAction">
              <template slot-scope="scope">
                <el-button type="text" @click="editAddrItem(scope.row, scope.$index)" v-show="!scope.row.isEdit" >编辑</el-button>
                <el-button type="text"  v-show="scope.row.isEdit" @click="canceladdrEdit(scope.row, scope.$index)" style="color: rgb(127, 127, 127);"> 取 消 </el-button>
                <el-button type="text" @click="confirmaddrEdit(scope.row, scope.$index)" v-show="scope.row.isEdit" >确定</el-button>
                <el-button type="text" v-show="!scope.row.isEdit" @click="delAddr(scope.row, scope.$index)" >删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div style="text-align: center;line-height:38px; border:1px solid #dfe6ec;border-top:0;">
            <el-button @click="address" type="text"  slot="reference">+添加发货地址</el-button>
          </div>
        </template>
      </div>

      <div class="item">
        <div class="title"><span>发票信息</span></div>
        <template>
          <div>
            <el-form-item class="formItem" prop="fpinvoiceType" label="发票类型:" :rules="[{ required: true, message: '请选择发票类型', trigger: 'blur' },]">
              <el-radio-group v-model="query.fpinvoiceType">
                <el-radio :disabled="isEdit" v-model="query.fpinvoiceType" label="VATINVOICE">增值税普通发票</el-radio>
                <el-radio :disabled="isEdit" v-model="query.fpinvoiceType" label="SPECIALINVOICE">增值税专用发票</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
          <div>
            <el-form-item class="formItem" prop="fpname" label="发票抬头:" :rules="[{ required: true, message: '请填写发票抬头', trigger: 'blur' },]">
              <el-input :disabled="isEdit" clearable style="width: 400px" v-model="query.fpname" placeholder="请填写发票抬头"></el-input>
            </el-form-item>
          </div>
          <div>
            <el-form-item class="formItem" prop="fptaxNumber" label="税号:" :rules="[{ required: true, message: '请填写税号', trigger: 'blur' },]">
              <el-input :disabled="isEdit" clearable style="width: 400px" v-model="query.fptaxNumber" placeholder="请填写税号"></el-input>
            </el-form-item>
          </div>

          <div v-if="query.fpinvoiceType == 'SPECIALINVOICE'">
            <el-form-item class="formItem" prop="fpregisterMobile" label="注册电话:" :rules="[{ required: true, message: '请填写注册电话', trigger: 'blur' },]">
              <el-input :disabled="isEdit" clearable style="width: 400px" v-model="query.fpregisterMobile" placeholder="请填写注册电话"></el-input>
            </el-form-item>
          </div>

          <div v-if="query.fpinvoiceType == 'SPECIALINVOICE'">
            <el-form-item class="formItem" prop="fpbankNumber" label="银行账号:" :rules="[{ required: true, message: '请填写银行账号', trigger: 'blur' },]">
              <el-input :disabled="isEdit" clearable style="width: 400px" v-model="query.fpbankNumber" placeholder="请填写银行账号"></el-input>
            </el-form-item>
          </div>

          <div v-if="query.fpinvoiceType == 'SPECIALINVOICE'">
            <el-form-item class="formItem" prop="fpdepositBank" label="开户银行:" :rules="[{ required: true, message: '请填写开户银行', trigger: 'blur' },]">
              <el-input :disabled="isEdit" clearable style="width: 400px" v-model="query.fpdepositBank" placeholder="请填写开户银行"></el-input>
            </el-form-item>
          </div>
        </template>
      </div>
    </el-form>

    <div label-width="100px" style="
        position: absolute;
        top: 2vh;
        right: 15px;
        background: #fff;
        height: 38px;
      ">
      <template v-if="!query.id">
        <el-popover v-model="rejectFlag" placement="bottom-end" title="取消提醒" width="300" trigger="click">
          <el-button slot="reference">取消</el-button>
          确定取消编辑?取消后编辑内容将不被保存!
          <div style="text-align: right; margin: 0;padding-top:14px">
            <el-button size="mini" @click="rejectFlag = false">取消</el-button>
            <el-button type="primary" size="mini" @click="cancel(false)">确定</el-button>
          </div>
        </el-popover>
        <el-button @click="edit('editForm')" type="primary">提交</el-button>
      </template>
      <template v-else>
        <template v-if="tabType == 'PENDING'">
          <el-popover v-model="rejectFlag" placement="bottom-end" title="驳回理由" width="300" trigger="click">
            <el-button slot="reference">驳回</el-button>
            <el-form ref="rejectform" :model="rejectText">
              <el-form-item prop="text" :rules="[{required: true, message: '请填写驳回理由',trigger: 'blur'},{required: true,min:5, message: '请至少填写5个字！',trigger: 'blur'}]">
                <el-input type="textarea" :rows="3" placeholder="请输入驳回理由" v-model="rejectText.text">
                </el-input>
              </el-form-item>
            </el-form>
            <div style="text-align: right; margin: 0;padding-top:14px">
              <el-button size="mini" @click="rejectFlag = false">取消</el-button>
              <el-button type="primary" size="mini" @click="rejected">确定</el-button>
            </div>
          </el-popover>
          <el-button type="primary" @click="accepted">通 过</el-button>
        </template>
        <template v-else-if="tabType == 'REJECTED'">
          <el-popover v-model="rejectFlag" placement="bottom-end" title="驳回理由" width="300" trigger="click">
            <el-button slot="reference">驳回理由</el-button>
            {{query.rejectReason}}
            <div style="text-align: right; margin: 0;padding-top:14px">
              <el-button type="primary" size="mini" @click="rejectFlag = false">知道了</el-button>
            </div>
          </el-popover>
        </template>
        <template v-else>
          <el-button v-if="query.publishStatus.code == 'Y'" @click="frozen">冻结</el-button>
          <el-button @click="enable" v-else v-show="isEdit">启用</el-button>
          <el-button @click="isEdit = !isEdit" v-show="isEdit">编辑</el-button>
          <el-button @click="cancel(true)" v-show="!isEdit">取消</el-button>
          <el-button @click="edit('editForm')" v-show="!isEdit" type="primary">提交</el-button>
        </template>
      </template>
    </div>
  </div>
</template>
<script>
import { getToken } from "@/utils/auth";
import { checkNumPot2 } from "@/utils/rules";
import rule from "@/utils/rules";
import {
  add,
  listByLicenseBaseType,
  getitems,
  enable,
  accepted,
  rejected,
  frozen,
  edititem,
} from "@/api/purchasingAgent/archivesList";
import {
  findSaleScope,
  addrList,
  adaddr,
  deladdr,
  editaddr,
  areas,
  addLicense,
  editLicense,
} from "@/api/businessCenter/businessList";
import Tinymce from "@/components/Tinymce";
export default {
  data() {
    let that = this;
    var checkNumPot3 = (rule, value, callback) => {
      const reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
      if (!value) {
        return callback(new Error("请填写数字"));
      } else if (!reg.test(value)) {
        return callback(new Error("请填写数字,最多2位小数"));
      } else if (value <= 0 || value > 100) {
        return callback(new Error("请填写0-100以内的数"));
      } else {
        callback();
      }
    };
    var validatePass2 = (rule, value, callback) => {
      if (value !== this.query.password) {
        callback(new Error("两次输入密码不一致!"));
      }
      callback();
    };
    return {
      rules: {
        password: [
          { required: true, message: "密码不能为空", trigger: "blur" },
          {
            required: true,
            min: 6,
            max: 20,
            message: "长度在 6 到 20 个字符",
            trigger: "blur",
          },
        ],
        confirmPassword: [
          { required: true, message: "确认密码不能为空", trigger: "blur" },
          {
            required: true,
            min: 6,
            max: 20,
            message: "长度在 6 到 20 个字符",
            trigger: "blur",
          },
          { validator: validatePass2, trigger: "blur" },
        ],
        registerCapital: [
          { validator: checkNumPot2, trigger: "blur", required: true },
        ],
        ceoMobile: rule.phone,
        orderAmountRate: [
          { validator: checkNumPot3, trigger: "blur", required: true },
        ],

        // rule
      },
      addrRules: {
        mobilPhone: rule.phone,
      },
      arealist: [],
      props: {},
      isEdit: false,
      checkList: [],
      businessScope: {},
      adAddr: {},
      editBusiness: false,
      tableDate: [],
      businessTableLabel: [
        {
          name: "label",
          label: "证件类型",
          width: "300px",
        },
        {
          name: "licenseNumber",
          label: "证件号",
          width: "300px",
        },
        {
          name: "licenseEndTime",
          label: "过期时间",
          width: "300px",
        },
        {
          name: "filePath",
          label: "附件",
          width: "300px",
        },
        {
          name: "operation",
          label: "操作",
        },
      ],
      editCertificates: false,
      adAddressflag: false,
      dialogImageUrl: "",
      isShowBigPic: false,
      certificatesForm: {
        id: 1,
        certificatesType: "",
        outTime: "",
        annex: "",
      },
      regionId: "",
      addrtableDate: [],
      query: {
        name: "",
        type: "",
        uploadPics: [],
        approvalStatus: { code: "PENDING" },

      },
      rejectText: {},
      rejectFlag: false,
      tablist: [],
      lisenceClickItem: {},
      lisenceFlag: false,
      adAddrsss: {},
      insertProgram: {
        folderId: 0,
      },
      headersProgram: {
        token: getToken(),
        Authorization: "Basic YWRtaW5fdWk6YWRtaW5fdWlfc2VjcmV0",
      },
      adAddrSelect: {},
      lisenceTableDate: [],
      lisenceEdit: [],
      areasTree: [],
    };
  },
  props: {
    row: {
      type: Object,
    },
    listmerchantType: {
      type: Array,
    },
    visible: {
      type: Boolean,
      default: false,
      required: true,
    },
    isReload: {
      type: Boolean,
      default: false,
      required: true,
    },
    tabType: {
      type: String,
      default: false,
      required: true,
    },
  },
  components: {
    Tinymce,
  },
  methods: {
    address() {
      let obj = {
        cityId: "",
        countyId: "",
        detailedAddress: "",
        fixedPhone: "",
        isOpen: "Y",
        merchantId: this.row.id || "",
        isEdit: true,
        mobilPhone: "",
        name: "",
        provinceId: "",
        address: [],
      };
      this.addrtableDate.push(obj);
    },
    // 删除地址
    async delAddr(row, index) {
      if (row.id) {
        let { data } = await deladdr({ ids: [row.id] });
        if (data) {
          this.$message.success("已删除该地址！");
          this.addrtableDate.forEach((item, index) => {
            console.log(item);
            if (item.id == row.id) {
              this.addrtableDate.splice(index, 1);
            }
          });
        }
      } else {
        this.addrtableDate.splice(index, 1);
      }
      this.oldAddrtableDate = this.addrtableDate.slice(0);
    },
    // 获取地址
    async getaddrList() {
      if (this.row.id) {
        let { data } = await addrList(this.row.id);
        data.forEach((item) => {
          item.isOpen = item.isOpen.code;
          item.isEdit = false;
          item.address = [item.provinceId, item.cityId, item.countyId];
        });
        this.addrtableDate = data;
        this.oldAddrtableDate = JSON.parse(JSON.stringify(data));
      }
    },
    canceladdrEdit(row, index) {
      if (this.oldAddrtableDate.length == 0) {
        this.addrtableDate = [];
        return;
      }
      this.addrtableDate = JSON.parse(JSON.stringify(this.oldAddrtableDate));
    },
    async confirmaddrEdit(row, index) {
      if (row.address.length == 0 || row.name == "" || row.mobilPhone == "") {
        this.addrtableDate = JSON.parse(JSON.stringify(this.oldAddrtableDate));
        return;
      }
      row.provinceId = row.address[0];
      row.cityId = row.address[1];
      row.countyId = row.address[2];
      row.isEdit = false;
      if (this.row.id) {
        if (row.id) {
          let { data } = await editaddr(row);
          if (data.id) {
            this.$message.success("修改地址成功");
            row = data;
          }
        } else {
          let { data } = await adaddr(row);
          if (data.id) {
            this.$message.success("添加地址成功");
            data.address = [data.provinceId, data.cityId, data.countyId];
            this.$set(this.addrtableDate, index, data);
          }
        }
      }
      this.oldAddrtableDate = JSON.parse(JSON.stringify(this.addrtableDate));
    },
    editAddrItem(row, index) {
      row.isEdit = true;
    },
    async getareas() {
      let { data } = await areas();
      this.areasTree = data;
    },
    editLisenceFun(row) {
      row.isEdit = true;
      this.editLisenceItem = JSON.parse(JSON.stringify(row));
    },
    cancelLisenceEdit(row) {
      this.editLisenceItem.isEdit = false;
      this.lisenceTableDate.forEach((item, index) => {
        if (item.licenseBaseId == row.licenseBaseId) {
          this.$set(this.lisenceTableDate, index, this.editLisenceItem);
        }
      });
    },
    async confirmLisenceEdit(row) {
      row.isEdit = false;
      row.filePathList.forEach((item) => {
        item.url = item.response.data.url;
      });
      row.merchantId = this.row.id;
      if (row.merchantId) {
        if (row.id && row.merchantId) {
          let { data } = await editLicense(row);
          this.$message.success("修改资质成功");
        } else {
          let { data } = await addLicense(row);
          this.$message.success("添加资质成功");
        }
      }
    },
    getsrc(str) {
      if (!str) {
        return [];
      } else {
        let arr = str.split(",");
        let list = [];
        arr.forEach((item) => {
          let obj = {
            response: {
              data: {
                url: "",
              },
            },
          };
          obj.response.data.url = item;
          obj.url = item;
          list.push(obj);
        });
        return list;
      }
    },
    uploadSuccess(res, file, fileList) {
      this.lisenceTableDate.forEach((item, index) => {
        if (item.licenseBaseId == this.editLisenceItem.licenseBaseId) {
          item.filePath = this.getFilePath(fileList);
          item.filePathList = fileList;
        }
      });
    },
    handleRemove(file, fileList) {
      this.lisenceTableDate.forEach((item, index) => {
        if (item.licenseBaseId == this.editLisenceItem.licenseBaseId) {
          item.filePath = this.getFilePath(fileList);
          item.filePathList = fileList;
        }
      });
    },
    getFilePath(fileList) {
      let str = "";
      fileList.forEach((item) => {
        let url = item.response.data.url;
        str += item.response.data.url + ",";
      });
      return str.substr(0, str.length - 1);
    },
    beforeUpload(file) {
      const isJPG = file.type === "image/jpeg";
      const isLt2M = file.size / 1024 / 1024 < 5;

      if (!isJPG) {
        this.$message.error("上传头像图片只能是 JPG 格式!");
      }
      if (!isLt2M) {
        this.$message.error("上传头像图片大小不能超过 2MB!");
      }
      return isJPG && isLt2M;
    },
    resetSucss(type, msg, tabType) {
      if (type) {
        this.$message.success(msg);
        if (tabType) {
          this.$emit("update:tabType", tabType);
        }
        this.$emit("update:visible", false);
        this.$emit("update:isReload", true);
      }
    },
    cancel(e) {
      if (!e) {
        this.$emit("update:visible", false);
        this.$emit("update:isReload", false);
      }
      this.isEdit = !this.isEdit;
      this.adAddrsss = {};
    },
    cityChange(e) {
      this.query.provinceId = e[0];
      this.query.cityId = e[1];
      this.query.countyId = e[2];
      this.regionId = e;
    },
    // 通过审核
    async accepted() {
      let { data } = await accepted(this.query.id);
      this.resetSucss(data, "已通过该采购商！", "ACCEPTED");
    },
    // 驳回审核
    async rejected() {
      this.$refs.rejectform.validate(async (valid) => {
        if (valid) {
          let { data } = await rejected({
            id: this.query.id,
            rejectReason: this.rejectText.text,
          });
          this.resetSucss(data, "已驳回该采购商！", "REJECTED");
        } else {
          return false;
        }
      });

    },
    // 冻结
    async frozen() {
      let { data } = await frozen(this.query.id);
      this.row.publishStatus.code = "N";
      this.$emit("update:row", this.row);
      this.cancel();
      this.$message.success("已冻结该销售商！");
    },
    // 启用
    async enable() {
      let { data } = await enable(this.query.id);
      this.row.publishStatus.code = "Y";
      this.$emit("update:row", this.row);
      this.cancel();
      this.$message.success("已启用该销售商！");
    },
    checkListFun(e) {
      this.query.businessCategoryId = e;
    },
    // 获取经营范围
    async getjyfw() {
      let { data } = await findSaleScope();
      let obj = {};
      data.forEach((item) => {
        if (obj[item.parentName + ":"]) {
          obj[item.parentName + ":"].push(item);
        } else {
          obj[item.parentName + ":"] = [];
          obj[item.parentName + ":"].push(item);
        }
      });
      this.businessScope = obj;
    },
    // 获取详情
    async getitem() {
      this.listLoading = true;
      if (!this.row.id) {
        let tableDate = (await listByLicenseBaseType()).data;
        tableDate.forEach((item) => {
          let obj = {
            licenseBaseId: item.id,
            licenseEndTime: "",
            filePath: "",
            licenseNumber: "",
            label: item.name,
            isEdit: false,
            filePathList: this.getsrc(item.filePath),
          };
          this.lisenceTableDate.push(obj);
        });
        this.listLoading = false;
        this.query.deliveryAddressSaveDTOList = [];
        return;
      }
      let { data } = await getitems(this.row.id);
      if (!data.deliveryAddressDetailDTOList) {
        data.deliveryAddressSaveDTOList = [];
      }
      this.query = data;
      this.query.regionId = [data.provinceId, data.cityId, data.countyId];

      this.$set(this.query, "fpinvoiceType", data.invoiceInfo.invoiceType.code);
      this.$set(this.query, "fpname", data.invoiceInfo.name);
      this.$set(this.query, "fpdepositBank", data.invoiceInfo.depositBank);
      this.$set(this.query, "fptaxNumber", data.invoiceInfo.taxNumber);
      this.$set(
        this.query,
        "fpregisterMobile",
        data.invoiceInfo.registerMobile
      );
      this.$set(this.query, "fpbankNumber", data.invoiceInfo.bankNumber);

      delete this.query.invoiceInfo;
      if (data.businessCategoryDetailList) {
        data.businessCategoryDetailList.forEach((item) => {
          this.checkList.push(item.id);
        });
      }

      this.listmerchantType.forEach((item) => {
        if (item.name == data.merchantType) {
          this.$set(this.query, "merchantTypeId", item.id);
        }
      });
      let tableDate = (await listByLicenseBaseType()).data;
      tableDate.forEach((item) => {
        let obj = {
          licenseBaseId: item.id,
          licenseEndTime: "",
          filePath: "",
          isForever: "",
          licenseNumber: "",
          label: item.name,
          isEdit: false,
        };
        data.merchantLicenses.find((ids) => {
          if (item.id == ids.licenseBaseId) {
            obj.licenseEndTime = ids.licenseEndTime;
            obj.filePath = ids.filePath;
            obj.filePathList = this.getsrc(ids.filePath);
            obj.licenseNumber = ids.licenseNumber;
            obj.label = item.name;
            obj.merchantId = ids.merchantId;
          }
        });
        this.lisenceTableDate.push(obj);
      });
    },
    clearFun: function () {
      this.$emit("update:visible", false);
      this.$emit("update:row", {});
    },
    edit(content) {
      this.$refs[content].validate(async (valid) => {
        if (valid) {
          if (!this.row.id) {
            this.query.merchantLicenses = [];
            this.lisenceTableDate.forEach((item) => {
              if (item.filePath != "") {
                this.query.merchantLicenses.push(item);
              }
            });
            if (!this.query.merchantLicenses.length && !this.addrtableDate) {
              this.$message.error("请填写采购商资质");
              return 0;
            }
            this.query.deliveryAddressSaveDTOList = this.addrtableDate;
            if (this.query.fpinvoiceType == "VATINVOICE") {
              this.query.invoiceInfoSaveDTO = {
                invoiceType: this.query.fpinvoiceType,
                name: this.query.fpname,
                taxNumber: this.query.fptaxNumber,
              };
            } else {
              this.query.invoiceInfoSaveDTO = {
                invoiceType: this.query.fpinvoiceType,
                name: this.query.fpname,
                taxNumber: this.query.fptaxNumber,
                registerMobile: this.query.fpregisterMobile,
                bankNumber: this.query.fpbankNumber,
                depositBank: this.query.fpdepositBank,
              };
            }
            let arr = [];
            this.addrtableDate.forEach((item) => {
              this.query.deliveryAddressSaveDTOList.push({
                cityId: item.address[1],
                provinceId: item.address[0],
                countyId: item.address[2],
                fixedPhone: item.fixedPhone,
                mobilPhone: item.mobilPhone,
                name: item.name,
                detailedAddress: item.detailedAddress,
              });
            });
            this.query.merchantLicenseSaveDTOListDTOList = arr;

            let { data } = await add(this.query);
            if (data.id) {
              this.$message.success("创建采购商成功");
              this.query = {};
            }
          } else {
            this.query.merchantLicenses = [];
            this.lisenceTableDate.forEach((item) => {
              if (item.filePath != "") {
                this.query.merchantLicenses.push(item);
              }
            });

            if (this.query.fpinvoiceType == "VATINVOICE") {
              this.query.invoiceInfoSaveDTO = {
                invoiceType: this.query.fpinvoiceType,
                name: this.query.fpname,
                taxNumber: this.query.fptaxNumber,
              };
            } else {
              this.query.invoiceInfoSaveDTO = {
                invoiceType: this.query.fpinvoiceType,
                name: this.query.fpname,
                taxNumber: this.query.fptaxNumber,
                registerMobile: this.query.fpregisterMobile,
                bankNumber: this.query.fpbankNumber,
                depositBank: this.query.fpdepositBank,
              };
            }
            let row = JSON.parse(JSON.stringify(this.query));
            row.businessCategoryId = this.checkList;
            delete row.deliveryAddressDetailDTOList;
            delete row.businessCategoryDetailList;
            delete row.region;
            let { data } = await edititem(row);
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
      this.isEdit = false;
    },

    showBigPic(file) {
      this.isShowBigPic = true;
      this.dialogImageUrl = file.url;
    },
    editCertificatesFun(file) {
      this.editCertificates = true;
    },
    submitFun: function (archivesForm) {
      this.$refs[archivesForm].validate((valid) => {
        if (valid) {
          this.$emit("update:visible", false);
          console.info("提交修改");
          this.$emit("update:isReload", true);
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
  },
  created() {
    this.query = this.row;
    this.getaddrList();
    this.getareas();
    this.getitem();
    this.getjyfw();
  },
  mounted() {
    if (this.query.id) {
      this.isEdit = true;
    }
  },
  beforeDestroy() {},
};
</script>
<style lang="less" scoped>
.archivesEditContent {
  margin: -30px -20px;
  border-top: 1px solid #ebecee;
  padding: 30px 20px;
  background-color: #fff;
  .item {
    width: 100%;
    margin-bottom: 30px;
    border-bottom: 1px solid #eeeeee;
    .title {
      padding: 0 0 15px;
      span {
        font-size: 16px;
        padding-left: 10px;
        border-left: 4px solid rgba(64, 158, 255, 1);
      }
    }
  }

  .uploadPic {
    padding-bottom: 100%;
    margin-bottom: -100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    > div {
      min-width: 100%;
      height: 25px;
    }
  }
  .productPicContent .text p {
    font-family: "PingFangSC-Regular", "PingFang SC", sans-serif,
      "PingFangSC-Regular", "PingFang SC", sans-serif-400;
    font-weight: 400;
    color: #aaaaaa;
    line-height: 20px;
    font-size: 13px;
    margin: 0;
  }
  .detailMsg {
    font-family: "PingFangSC-Regular", "PingFang SC", sans-serif,
      "PingFangSC-Regular", "PingFang SC", sans-serif-400;
    font-weight: 400;
    color: #aaaaaa;
    line-height: 20px;
    padding-bottom: 20px;

    font-size: 13px;
  }
  /deep/ .el-input.is-disabled .el-input__inner {
    color: #04060c;
    background-color: #eaf7e7;
  }
  /deep/ .is-checked .is-disabled .el-checkbox__inner {
    color: #2fa338;
    background-color: #1b9e38;
  }
  /deep/ .is-checked .el-checkbox__label {
    color: #04060c;
  }
  /deep/ .is-disabled .is-checked .el-radio__inner {
    background-color: #1b9e38;
  }
  /deep/ .is-disabled.is-checked.el-radio > .el-radio__label {
    color: #04060c;
  }
  /deep/ .el-col {
    line-height: 40px;
  }
}
// /deep/ .el-input.is-disabled .el-input__inner {
//   color: #04060c;
//   background-color: #eaf7e7;
// }
// /deep/ .is-checked .is-disabled .el-checkbox__inner {
//   color: #2fa338;
//   background-color: #1b9e38;
// }
// /deep/ .is-checked .el-checkbox__label {
//   color: #04060c;
// }
// /deep/ .is-disabled .is-checked .el-radio__inner {
//   background-color: #1b9e38;
// }
// /deep/ .is-disabled.is-checked.el-radio > .el-radio__label {
//   color: #04060c;
// }

// /deep/.el-checkbox__input.is-disabled + span.el-checkbox__label{

// }
/deep/ .el-upload {
  width: 40px;
  height: 40px;
  position: relative;
}
/deep/ .el-upload > i {
  font-size: 16px;
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translateX(-50%) translateY(-50%);
  transform: translateX(-50%) translateY(-50%);
}
/deep/ .el-upload-list .el-upload-list__item {
  width: 40px;
  height: 40px;
}
/deep/ .hide .el-upload--picture-card {
  display: none;
}
</style>
