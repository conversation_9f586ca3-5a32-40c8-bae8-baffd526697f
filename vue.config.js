'use strict'
const path = require('path')
const CompressionPlugin = require('compression-webpack-plugin')
const defaultSettings = require('./src/settings.js')

const isProduction = process.env.NODE_ENV === 'production'

function resolve(dir) {
  return path.join(__dirname, dir)
}


const cdn = {
  css: isProduction ? [
    './cdn/<EMAIL>',
    './cdn/font_materialdesignicons.min.css'
    // 'https://unpkg.com/element-ui@2.13.2/lib/theme-chalk/index.css',
    // 'https://cdn.jsdelivr.net/npm/@mdi/font@latest/css/materialdesignicons.min.css'
  ] : [],
  js: isProduction ? [
    // 'https://cdn.jsdelivr.net/npm/vue@2.6.11/dist/vue.js',
    // 'https://unpkg.com/vue-router@3.5.3/dist/vue-router.js',
    // 'https://unpkg.com/vuex@3.1.2/dist/vuex.js',
    // 'https://unpkg.com/axios@0.19.0/dist/axios.min.js',
    // 'https://unpkg.com/element-ui@2.13.2/lib/index.js',
    // 'https://cdn.jsdelivr.net/npm/lodash@4.17.15/lodash.min.js'
    './cdn/vue.js',
    './cdn/vue-router.js',
    './cdn/vuex.js',
    './cdn/axios.min.js',
    './cdn/<EMAIL>',
    './cdn/lodash.min.js'
  ] : []
}

const name = defaultSettings.title || '' // page title

// If your port is set to 80,
// use administrator privileges to execute the command line.
// For example, Mac: sudo npm run
// You can change the port by the following method:
// port = 9527 npm run dev OR npm run dev --port = 9527
const port = process.env.port || process.env.npm_config_port || 9527 // dev port

// All configuration item explanations can be find in https://cli.vuejs.org/config/
module.exports = {
  /**
   * You will need to set publicPath if you plan to deploy your site under a sub path,
   * for example GitHub Pages. If you plan to deploy your site to https://foo.github.io/bar/,
   * then publicPath should be set to "/bar/".
   * In most cases please use '/' !!!
   * Detail: https://cli.vuejs.org/config/#publicpath
   */
  publicPath: './',
  outputDir: 'dist',
  assetsDir: 'static',
  lintOnSave: false,
  productionSourceMap: false,

  devServer: {
    port: port,
    open: true,
    overlay: {
      warnings: false,
      errors: true
    },
    // before: require('./mock/mock-server.js'),
    proxy: {
      // [process.env.VUE_APP_BASE_API]: {
      //   // target: 'https://test.eyao.link',
      //   target: 'https://testplatform.guoyaoplat.com/',
      //   changeOrigin: true,
      //   pathRewrite: {
      //     ['^' + process.env.VUE_APP_BASE_API]: process.env.VUE_APP_BASE_API
      //   }
      // }
      [process.env.VUE_APP_BASE_API]: {
        // target: "https://test.eyao.link",
        target: "https://testplatform.guoyaoplat.com/",
        // target: "http://192.168.3.163:9004", // 邱金金
        // target: "http://192.168.3.165:9004", // 霍雨浩
        // target: "http://192.168.3.143:9004", // 吕
        // target: "http://192.168.1.17:9004", // 黄华
        changeOrigin: true,
        pathRewrite: {
          ["^" + process.env.VUE_APP_BASE_API]: process.env.VUE_APP_BASE_API,
        },
      },
    }
  },
  configureWebpack: {
    // provide the app's title in webpack's name field, so that
    // it can be accessed in index.html to inject the correct title.
    name: name,
    resolve: {
      alias: {
        '@': resolve('src')
      }
    },
    //忽略将这些加入打包
    externals: isProduction ? {
      vue: 'Vue',
      'vue-router': 'VueRouter',
      vuex: 'Vuex',
      'element-ui': 'ELEMENT',
      axios: 'axios',
      lodash: '_'
    } : {},
    plugins: isProduction ? [
      // gzip 压缩
      // new CompressionPlugin({
      //   test: /\.js$|\.html$|\.css/, //匹配文件名
      //   threshold: 10240, //对超过10k的数据进行压缩
      //   deleteOriginalAssets: true //是否删除原文件
      // })
    ] : []
  },
  chainWebpack(config) {

    if (process.env.NODE_ENV === 'production') {
      // 压缩代码
      config.optimization.minimize(true)
      //关闭预加载关闭prefetch
      //vuecli 3默认开启prefetch(预先加载模块)，提前获取用户未来可能会访问的内容在首屏会把这十几个路由文件都下载下来
      config.plugins.delete('prefetch')
      config.plugins.delete('preload')
    } else {
      config.plugin('preload').tap(() => [
        {
          rel: 'preload',
          // to ignore runtime.js
          // https://github.com/vuejs/vue-cli/blob/dev/packages/@vue/cli-service/lib/config/app.js#L171
          fileBlacklist: [/\.map$/, /hot-update\.js$/, /runtime\..*\.js$/],
          include: 'initial'
        }
      ])
      // when there are many pages, it will cause too many meaningless requests
      config.plugins.delete('prefetch')
    }


    // 生产环境注入cdn
    config.plugin('html')
      .tap(args => {
        args[0].cdn = cdn
        return args
      })


    // set svg-sprite-loader
    config.module
      .rule('svg')
      .exclude.add(resolve('src/icons'))
      .end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()

    config
      .when(process.env.NODE_ENV !== 'development',
        config => {
          config
            .plugin('ScriptExtHtmlWebpackPlugin')
            .after('html')
            .use('script-ext-html-webpack-plugin', [{
              // `runtime` must same as runtimeChunk name. default is `runtime`
              inline: /runtime\..*\.js$/
            }])
            .end()
          config
            .optimization.splitChunks({
              chunks: 'all',
              minSize: 30000,
              cacheGroups: {
                libs: {
                  name: 'chunk-libs',
                  test: /[\\/]node_modules[\\/]/,
                  priority: 10,
                  chunks: 'initial' // only package third parties that are initially dependent
                },
                elementUI: {
                  name: 'chunk-elementUI', // split elementUI into a single package
                  priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
                  test: /[\\/]node_modules[\\/]_?element-ui(.*)/ // in order to adapt to cnpm
                },
                commons: {
                  name: 'chunk-commons',
                  test: resolve('src/components'), // can customize your rules
                  minChunks: 3, //  minimum common number
                  priority: 5,
                  reuseExistingChunk: true
                },
                views: {
                  name: 'chunk-views',
                  test: resolve('src/views'), // can customize your rules
                  minChunks: 2, //  minimum common number
                  priority: 6,
                  reuseExistingChunk: true
                }
              }
            })
          // https:// webpack.js.org/configuration/optimization/#optimizationruntimechunk
          config.optimization.runtimeChunk('single')
        }
      )
  }
}
