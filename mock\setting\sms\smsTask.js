const Mock = require('mockjs')

const List = []
const count = 100

const baseContent = '<p>I am testing data, I am testing data.</p><p><img src="https://wpimg.wallstcn.com/4c69009c-0fd4-4153-b112-6cb53d1cf943"></p>'
const image_uri = 'https://wpimg.wallstcn.com/e4558086-631c-425c-9430-56ffb46e70b3'

for (let i = 0; i < count; i++) {
    List.push(Mock.mock({
        id: '@increment',
        bizId: '@title(20, 20)',
        code: '@string("lower", 5, 10)',
        createDate: '@datetime',
        createMonth: '@datetime',
        createTime: '@datetime',
        createUser: '@increment',
        createUserName: '@cname',
        createWeek: '@title(1, 52)',
        ext: '@string("lower", 5, 10)',
        fee: '@increment',
        message: '@ctitle(20, 50)',
        receiver: /^1\d{10}$/,
        'sendStatus|1': ['WAITING', 'SUCCESS', 'FAIL'],
        'status|1': ['WAITING', 'SUCCESS', 'FAIL'],
        taskId: '@increment',
        updateTime: '@datetime',
        updateUser: '@increment' 
    }))
}

module.exports = [
    {
        url: '/api/msgs/smsTask/page',
        type: 'post',
        response: config => {
            const { model, current = 1, size = 10, sort } = config.body
            let mockList = List.filter(item => {
                if (model.status == "") return true 
                else if (model.status.toLowerCase() !== item.status.toLowerCase()) return false
                else return true
            })
            if (sort === '-id') {
                mockList = mockList.reverse()
            }

            const pageList = mockList.filter((item, index) => index < size * current && index >= size * (current - 1))

            return {
                code: 20000,
                data: {
                    total: mockList.length,
                    records: pageList
                }
            }
        }
    },
    {
        url: '/api/msgs/smsTask/\.*',
        type: 'get',
        response: config => {
            console.info(config.query)
            const { id } = config.query
            for (const article of List) {
                if (article.id === +id) {
                    return {
                        code: 20000,
                        data: article
                    }
                }
            }
        }
    },
    {
        url: '/api/msgs/smsTask/',
        type: 'get',
        response: _ => {
            return {
                code: 20000,
                data: 'success'
            }
        }
    }
]

