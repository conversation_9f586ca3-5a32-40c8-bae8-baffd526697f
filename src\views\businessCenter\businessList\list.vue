<template>
  <div class="archivesPageContent">
    <im-search-pad
      :is-expand.sync="isExpand"
      :model="listQuery"
      @reset="resetForm('searchForm')"
      @search="onSearchSubmitFun"
    >
      <im-search-pad-item prop="shopName">
        <el-input v-model="listQuery.model.shopName" placeholder="请输入店铺名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="name">
        <el-input v-model="listQuery.model.name" placeholder="请输入商家名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="ceoMobile">
        <el-input v-model="listQuery.model.ceoMobile" placeholder="请输入负责人手机" />
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="cityValue">
        <el-cascader placeholder="请选择所在区域" v-model="cityValue" :props="props" @change="cityChange" clearable>
        </el-cascader>
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="publishStatus">
        <el-select v-model="listQuery.model.publishStatus" placeholder="请选择商家状态">
          <el-option label="已启用" value="Y"></el-option>
          <el-option label="已冻结" value="N"></el-option>
        </el-select>
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout
        v-model="listQuery.model.approvalStatus.code"
        :tabs="approvalList"
        @change="chageTabsFun">
        <template slot="button">
          <div>
            <el-button v-if="checkPermission(['admin', 'admin-merchant:accept']) && listQuery.model.approvalStatus.code== 'PENDING'" :disabled="multipleSelectionId.length== 0" @click="updateSaleMerchantAccepted">批量通过</el-button>
            <el-button v-if="checkPermission(['admin', 'admin-merchant:reject']) && listQuery.model.approvalStatus.code== 'PENDING'" :disabled="multipleSelectionId.length== 0" @click="updateSaleMerchantRejected">批量驳回</el-button>
            <el-button v-if="checkPermission(['admin', 'admin-merchant:release'])" :disabled="multipleSelectionId.length== 0"
                       @click="updateSalePublishStatus('Y')">批量启用</el-button>
            <el-button v-if="checkPermission(['admin', 'admin-merchant:freeze'])" :disabled="multipleSelectionId.length== 0" @click="updateSalePublishStatus('N')">批量冻结</el-button>
            <!-- <el-button :disabled="multipleSelection.length == 0" @click="outExcel">导出档案</el-button> -->
            <el-button @click="refresh">刷新</el-button>
            <el-button v-if="checkPermission(['admin', 'admin-merchant:add'])" type="primary" @click="newFun">+新增销售商</el-button>
          </div>
        </template>
      </tabs-layout>
      <div class="table">
        <el-table ref="table" v-if="list" @select="onSelect" @select-all="onAllSelect" @selection-change="selectTableItemFun" v-loading="listLoading" :data="list" row-key="id" border fit
                  highlight-current-row style="width: 100%">
          <el-table-column align="center" width="65" fixed :render-header="renderHeader">
            <template slot-scope="scope">
              <span>{{scope.$index + 1}} </span>
            </template>
          </el-table-column>
          <el-table-column type="selection" width="55" align="center" fixed></el-table-column>
          <el-table-column v-for="(item, index) in tableTitle" :key="index" :min-width="(item.width?item.width:'350px')" :label="item.label" show-overflow-tooltip align="left">
            <template slot-scope="{row}">
            <span v-if="item.name=='publishStatus'" :style="row[item.name].code=='N'?'color:#ff0066':''">
              {{row[item.name].code=='Y'? '已启用' : '已冻结'}}
            </span>
            <span v-else-if="item.name=='commerceModel'">
              <span v-if="row['commerceModel'].code === 'SAAS_PLATFORM'">平台商家</span>
              <span v-else-if="row['commerceModel'].code === 'SAAS'">SAAS商家</span>
              <span v-else>推广商家</span>
            </span>
              <!-- <el-button v-if="item.name=='publishStatus'&&row[item.name].code=='Y'" type="text" style="color:#409EFF">已启用</el-button>
              <el-button v-else-if="item.name=='publishStatus'&&row[item.name].code=='N'" type="text" style="color:#FF3C54">已冻结</el-button> -->
              <span v-else>{{ row[item.name] }}</span>
            </template>
          </el-table-column>

          <el-table-column fixed="right" align="center" label="操作" width="100" class="itemAction">
            <template slot-scope="{row}">
              <el-row class="table-edit-row">
                <div v-if="checkPermission(['admin', 'admin-merchant:detail'])">
                  <el-button @click="detailFun(row)" type="text" >查看详情</el-button>
                </div>
                <div v-if="checkPermission(['admin', 'admin-merchant:paymentSetting'])">
                  <el-button @click="showPaySettingDialog(row.id)" type="text" >支付设置</el-button>
                </div>
              </el-row>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-if="total>0" :pageSizes="[2, 10, 20, 50]" :total="total" :page.sync="listQuery.current" :limit.sync="listQuery.size" @pagination="getlist" />
      </div>
      <PaySettingDialog ref="paySettingDialogRef" />
    </div>

  </div>
</template>

<script>
import checkPermission from "@/utils/permission";
import { downloadFile } from "@/utils/commons";
import { setContextData, getContextData } from "@/utils/auth";
import Pagination from "@/components/Pagination";
import {
  outlist,
  updateSalePublishStatus,
  updateSaleMerchantAccepted,
  updateSaleMerchantRejected,
  getSaleMerhcantCount,
} from "@/api/businessCenter/businessList";
import tableInfo from "@/views/businessCenter/businessList/tableInfo";
import edit from "@/views/businessCenter/businessList/edit";
import { areas } from "@/api/enterprise";
import TabsLayout from '@/components/TabsLayout'
import PaySettingDialog from "@/views/businessCenter/businessList/paySettingDialog";
export default {
  components: {
    Pagination,
    edit,
    TabsLayout,
    PaySettingDialog
  },
  data() {
    return {
      isExpand: false,
      props: {
        lazy: true,
        checkStrictly: true,
        async lazyLoad(node, resolve) {
          const { level } = node;
          let id = node.data ? node.data.id : "";
          let res = await areas({ parentId: id });
          let list = res.data;
          list.forEach((item) => {
            item.value = item.id;
            item.leaf = level >= 2;
          });
          resolve(list);
        },
      },
      list: [],
      listCount: {
        pendingCount: 0,
        acceptedCount: 0,
        rejectedCount: 0,
        expireCount: 0,
      },
      total: 0,
      multipleSelection: [],
      multipleSelectionId: [],
      listLoading: false,
      tableTitle: [],
      listQuery: {
        current: 1,
        size: 10,
        model: {
          commerceModel: "SAAS",
          approvalStatus: { code: "PENDING" },
        },
      },
      showEditPage: false,
      row: {},
      submitReload: "",
      cityValue: [],
      tableSelectTitle: [1],
      showSelectTitle: false,
    };
  },
  watch: {
    submitReload: function (newVal, oldVal) {
      if (newVal) {
        this.getlist();
        this.submitReload = false;
      }
    },
  },
  computed: {
    approvalList() {
      return [
        {
          name: '待审核('+this.listCount.pendingCount+')',
          value: 'PENDING',
          hide: !checkPermission(['admin', 'admin-merchant:penddingView'])
        },
        {
          name: '已通过('+this.listCount.acceptedCount+')',
          value: 'ACCEPTED',
          hide: !checkPermission(['admin', 'admin-merchant:hadAuditView'])
        },
        {
          name: '已驳回('+this.listCount.rejectedCount+')',
          value: 'REJECTED',
          hide: !checkPermission(['admin', 'admin-merchant:rejectedView'])
        },
        {
          name: '已过期('+this.listCount.expireCount+')',
          value: 'stale',
          hide: !checkPermission(['admin', 'admin-merchant:expiredView'])
        }
      ]
    }
  },
  methods: {
    checkPermission,
    refresh() {
      this.listQuery = {
        ...this.listQuery,
        current: 1,
        size: 10
      }
      this.cityValue = []
      this.initTbaleTitle()
      this.getlist()
    },
    updataResolve(data) {
      if (data.code === 0) {
        this.getlist();
      }
    },
    checkSelect() {
      if (this.multipleSelection.length < 1) {
        this.$alert("请至少选择一个商家");
        return true;
      }
    },
    cityChange(e) {
      this.listQuery.model.provinceId = e[0];
      this.listQuery.model.cityId = e[1];
      this.listQuery.model.countyId = e[2];
    },
    //  导出档案
    async outExcel() {
      if (this.multipleSelection.length > 0) {
        const tHeader = ["id"];
        const filterVal = ["id"];
        this.tableTitle.forEach(function (item) {
          tHeader.push(item.label);
          filterVal.push(item.name);
        });
        let exportData = this.formatJson(this.multipleSelection, filterVal);
        downloadFile({
          tHeader: tHeader,
          fileName: "商家列表",
          exportData: exportData,
        });
      } else {
        this.$message.error("请在商家列表中勾选需要商家");
      }
    },
    formatJson(dataList, filterVal) {
      return dataList.map((v) =>
        filterVal.map((j) => {
           if (j === "publishStatus") {
            return v[j].code == 'Y' ? '已启用' : '已冻结';
          } else {
            return v[j];
          }
        })
      );
    },
    // 获取状态数量
    async getSaleMerhcantCount() {
      let params = {
        ...this.listQuery.model
      };
      delete params.approvalStatus;
      console.log('params---->',params);
      let { data } = await getSaleMerhcantCount(params);
      console.log('------>',data);
      this.listCount = data;
    },

    // 批量驳回
    async updateSaleMerchantRejected() {
      this.$confirm("此操作将批量驳回商家，是否继续？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: 'warning',
      }).then(async ()=>{
        if (this.checkSelect()) return 0;
        let data = await updateSaleMerchantRejected({
          ids: this.multipleSelectionId,
        });
        this.updataResolve(data);
      })
    },
    // 批量审核
    async updateSaleMerchantAccepted() {
      this.$confirm("此操作将审核通过商家，是否继续？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: 'warning',
      }).then(async ()=>{
        if (this.checkSelect()) return 0;
        let data = await updateSaleMerchantAccepted({
          ids: this.multipleSelectionId,
        });
        this.updataResolve(data);
      })
    },
    // 批量启用商家
    async updateSalePublishStatus(param) {
      this.$confirm("此操作将批量操作商家，是否继续？","提示",{
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: 'warning',
      }).then(async ()=>{
        if (this.checkSelect()) return 0;
        let data = await updateSalePublishStatus({
          ids: this.multipleSelectionId,
          publishStatus: param,
        });
        this.updataResolve(data);
      });
    },
    async getlist() {
      this.listLoading = true;
      this.getSaleMerhcantCount();
      const { data } = await outlist(this.listQuery);
      this.list = data.records;
      this.total = data.total;
      this.listLoading = false;
    },
    newFun: function () {
      setContextData("businessArchives_list", this.listQuery);
      this.$router.push({
        path: "/businessCenter/businessArchives/list/edit",
      });
    },
    chageTabsFun: function () {
      setContextData("businessArchives_list", this.listQuery);
      this.list = [];
      this.$refs.table.clearSelection();
      this.listQuery.current = 1;
      this.initTbaleTitle();
      this.$nextTick(() => {
        this.getlist()
      })
    },
    onSearchSubmitFun() {
      this.listQuery.current = 1;
      this.getlist();
    },
    resetForm(search) {
      this.listQuery = {
        current: 1,
        size: 10,
        model: {
          commerceModel: "SAAS",
          approvalStatus: this.listQuery.model.approvalStatus,
        },
      };
      this.cityValue = [];
      this.getlist();
    },
    selectTableItemFun: function (val) {
      let arr = [];
      val.forEach((item) => {
        arr.push(item.id);
      });
      this.multipleSelection = val;
      this.multipleSelectionId = arr;
    },
    onAllSelect(selection) {
      this.onSelect(selection);
    },
    onSelect: function (val) {
      this.multipleSelection = val;
    },
    initTbaleTitle() {
      let arr = []
      tableInfo[this.listQuery.model.approvalStatus.code].forEach(item => {
        if(item.name != "id"){
          arr.push(item)
        }
      })
      this.tableTitle = arr
    },
    detailFun(item) {
      setContextData("businessArchives_list", this.listQuery);
      this.$router.push({
        path: "/businessCenter/businessArchives/detail",
        query: {
          tabType: this.listQuery.model.approvalStatus.code,
          id: item.id,
        },
      });
    },
    renderHeader(h, { column }) {
      var titles = tableInfo[this.listQuery.model.approvalStatus.code];
      var titlesName = ["显示字段项", "隐藏字段项"];
      return (
        <div style="position:relative">
          <div onClick={this.showHeaer}>
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="640px"
            center
            append-to-body={true}
          >
            <el-transfer
              vModel={this.tableSelectTitle}
              data={titles}
              titles={titlesName}
              onChange={this.setleftTitleFun}
            ></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button onClick={this.closeHeaer}>取消</el-button>
              <el-button type="primary" onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      );
    },
    setleftTitleFun(val) {
      this.tableSelectTitle = val;
    },
    showHeaer: function () {
      this.showSelectTitle = true;
    },
    closeHeaer: function () {
      this.showSelectTitle = false;
      this.tableSelectTitle = [];
    },
    setHeaer: function () {
      var titles = tableInfo[this.listQuery.model.approvalStatus.code];
      var listinfo = titles.filter((element, index, self) => {
        return !this.tableSelectTitle.includes(element.key);
      });
      this.tableTitle = listinfo;
      this.showSelectTitle = !this.showSelectTitle;
    },
    // 显示支付设置dialog
    showPaySettingDialog(id) {
      this.$refs.paySettingDialogRef.show(id)
    }
  },
  created() {},
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (from.path == "/businessCenter/businessArchives/detail" || from.path == "/businessCenter/businessArchives/list/edit") {
        if (getContextData("businessArchives_list") != "") {
          vm.listQuery = getContextData("businessArchives_list");
        }
      }
      vm.initTbaleTitle();
      vm.getSaleMerhcantCount();
      vm.listQuery={
        current: 1,
        size: 10,
        model: {
          approvalStatus: { code: "PENDING" },
        },
      },
      vm.getlist();
    });
  },
  mounted() {}
};
</script>

<style lang="scss" scoped>
.archivesPageContent {
  padding: 0;
  .temp_searchBox {
    height: 64px;
    overflow: hidden;
    margin-bottom: 0;
  }
  .form-inline {
    height: 60px;
    overflow: hidden;
  }
  .title {
    border-bottom: 2px solid #ebecee;
    margin-bottom: 16px;
    span {
      margin-bottom: -2px;
      padding: 0 15px;
      height: 40px;
      line-height: 30px;
      display: block;
      background: rgba(255, 255, 255, 0);
      border-bottom: 2px solid rgb(64, 158, 255);
      font-size: 16px;
      font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
        "PingFang SC"-400;
      font-weight: 400;
      color: rgb(64, 158, 255);
    }
  }

  .formItem {
    width: 586px;
  }
  .line {
    color: #dfe6ec;
    margin: 0 6px;
  }
  .typeTabs {
    height: 40px;
    margin-bottom: -2px;
    margin-left: 30px;
  }
}
</style>
