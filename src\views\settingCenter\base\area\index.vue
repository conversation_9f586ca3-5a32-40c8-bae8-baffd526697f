<template>
  <div class="menuPageContent">
    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        :tabs="[ { name: '地区管理' } ]"
      >
        <template slot="button">
          <el-button  @click="areaRefreshCacheFun()">刷新</el-button>
          <el-button v-if="checkPermission(['admin','admin-setting-area:add'])"  type="primary" @click="newFun">+新增地区</el-button>
        </template>
      </tabs-layout>
      <div class="table">
        <el-table
          ref="tableDom"
          v-loading="listLoading"
          :data="list"
          row-key="id"
          border
          fit
          :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
          lazy
          :load="load"
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column
            prop="label"
            label="地区名称"
          />
          <el-table-column
            label="行政区级"
            align="center"
            width="150"
          >
            <template slot-scope="{row}">
              {{ row.level.key|areaLevelFilter }}
            </template>
          </el-table-column>
          <el-table-column
            prop="sortValue"
            align="center"
            label="排序"
            width="100"
          />
          <el-table-column fixed="right" align="center" label="操作" width="200" class="itemAction">
            <template slot-scope="scope">
              <el-row class="table-edit-row">
                <span v-if="checkPermission(['admin','admin-setting-area:childrenAdd'])" class="table-edit-row-item">
                  <el-button type="text"  @click="newChildrenFun(scope.row.id)">新增子分类</el-button>
                </span>
                <span v-if="checkPermission(['admin','admin-setting-area:edit'])" class="table-edit-row-item">
                  <el-button type="text"  @click="editChildrenFun(scope.row.id)">编辑</el-button>
                </span>
                <span v-if="checkPermission(['admin','admin-setting-area:del'])" class="table-edit-row-item">
                  <el-button type="text"  @click="deleteRootFun(scope.row)">删除</el-button>
                </span>
              </el-row>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
import { setContextData, getContextData } from '@/utils/auth'
import checkPermission from '@/utils/permission'
import { list, areaRefreshCache, deleteArea } from '@/api/setting/area'
import TabsLayout from '@/components/TabsLayout'
export default {
  components: {
    TabsLayout
  },
  data() {
    return {
      loadNodeMap: new Map(),
      newRoot: false,
      showEdit: false,
      listLoading: false,
      parentId: 0,
      listQuery: {
        typeName: '',
        page: 1,
        limit: 10
      },
      list: [],
      actionItem: null,
      total: 0
    }
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      if (from.path == '/settingCenter/base/area/edit') {
        vm.listQuery = getContextData('base_area_list')
      }
      vm.getList()
    })
  },
  mounted() {
    this.getList()
  },
  beforeDestroy() {},
  methods: {
    checkPermission,
    async areaRefreshCacheFun() {
      await areaRefreshCache()
      await this.onSearchSubmitFun()
    },
    newFun() {
      setContextData('base_area_list', this.listQuery)
      this.$router.push({ path: '/settingCenter/base/area/edit' })
    },
    newChildrenFun(parentId) {
      setContextData('base_area_list', this.listQuery)
      this.$router.push({ path: '/settingCenter/base/area/edit',
        query: {
          parentId: parentId
        }})
    },
    editChildrenFun(id) {
      setContextData('base_area_list', this.listQuery)
      this.$router.push({ path: '/settingCenter/base/area/edit',
        query: {
          id: id
        }})
    },
    deleteRootFun(row) {
      var _this = this
      this.$confirm('此操作将永久删除该信息, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        _this.actionDeleteFun(row)
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    async actionDeleteFun(row) {
      const data = await deleteArea(row.id)
      if (data.code == 0 && row.parentId != '0') {
        this.reloadTreeNode(row.parentId)
      } else {
        this.onSearchSubmitFun()
      }
    },
    async updateEditRootFun(row) {
      if (row.parentId != '0') {
        await this.reloadTreeNode(row.parentId)
        this.actionItem = null
      } else {
        this.onSearchSubmitFun()
      }
    },
    async reloadTreeNode(parentId) {
      const { tree, resolve } = this.loadNodeMap.get(parentId)
      this.$set(this.$refs.tableDom.store.states.lazyTreeNodeMap, parentId, [])
      const { data } = await list(parentId)
      data.forEach(element => {
        element['hasChildren'] = true
      })
      resolve(data)
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    onSearchSubmitFun: function() {
      this.list = []
      this.getList()
    },
    async getList() {
      const { data } = await list(this.parentId)
      data.forEach(element => {
        element['hasChildren'] = true
      })
      this.list = data
    },
    async load(tree, treeNode, resolve) {
      this.loadNodeMap.set(tree.id, { tree, treeNode, resolve })
      const { data } = await list(tree.id)
      data.forEach(element => {
        element['hasChildren'] = true
      })
      resolve(data)
    }
  }
}
</script>
<style lang="less" scoped>

.menuPageContent {
  // padding: 15px;
  padding: 0px;
   .title{
      //  border-bottom:2px solid #EBECEE;

        border-bottom:2px solid #EBECEE;
     margin-bottom: 16px;
      span{
        margin-bottom: -2px;
        padding:0 15px;
        height: 40px;
        line-height: 30px;
        display:block;
        background: rgba(255,255,255,0);
        border-bottom:2px solid rgb(64, 158, 255);
        font-size: 16px;
        font-family: 'PingFangSC-Regular', 'PingFang SC', 'PingFangSC-Regular', 'PingFang SC'-400;
        font-weight: 400;
        color:rgb(64, 158, 255);
      }
  }
  .formItem{width:586px;}
  .line{color:#dfe6ec; margin:0 6px;}
}
</style>
