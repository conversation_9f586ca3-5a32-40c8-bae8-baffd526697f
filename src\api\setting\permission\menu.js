import requestAxios from '@/utils/requestAxios'

export function list() {
    return requestAxios({
        url: '/api/authority/menu/tree',
        method: 'get'
    })
}


export function tree(params) {
    return requestAxios({
        url: `/api/authority/menu/tree/client?clientId=${params.clientId}&commerceModel=${params.commerceModel}`,
        method: 'get'
    })
}


export function getApi(id) {
    return requestAxios({
        url: '/api/authority/menu/' + id,
        method: "get"
    })
}

export function editApi(data) {
    return requestAxios({
        url: '/api/authority/menu',
        method: data.id == 0 ? 'post' : 'put',
        data
    })
}

export function deleteApi(id) {
    return requestAxios({
        url: '/api/authority/menu?ids[]=' + id,
        // headers: { 'content-type': 'application/x-www-form-urlencoded' },
        method: 'delete'
    })
}