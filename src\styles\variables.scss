// copy from element
/* theme color */
$--color-primary: #0056E5;
$--color-success: #2DAC0C;
$--color-warning:#FF6E1B;
$--color-danger: #FF2D47;
$--color-info: #909399;

/*Font Color */
$--color-text-primary:#2A3045;
$--color-text-regular:#505465;
$--color-text-secondary:#828591;
$--color-text-placeholder:#CDCED3;


/*Input */
$--input-width: 240px !default;
/// height||Other|4
$--input-height: 32px !default;
$--input-border-radius:2px;
$--button-font-weight: 400;

// $--color-text-regular: #1f2d3d;

// Border Color
$--border-color-base: #DCDDE0;
$--border-color-light: #DCDDE0;
$--border-color-lighter: #DCDDE0;
$--border-color-extra-light: #DCDDE0;

$--table-border: 1px solid #dfe6ec;
$--table-header-background-color:#f7f7f8;
$--table-row-hover-background-color:#f2f6fd;
$--table-font-color:#505465;

$--button-border-radius:2px;

$--message-info-font-color:#0056E5;
$--message-padding:8px 12px 8px 12px;
$--message-min-width:320px;

//$tableBorder:1px solid #dfe6ec;  默认
$tableBorder:1px solid #D9D9D9;

$tableTextColor: #595959;


// base color
$blue:#324157;
$light-blue:#3A71A8;
$red:#C03639;
$pink: #E65D6E;
$green: #30B08F;
$tiffany: #4AB7BD;
$yellow:#FEC171;
$panGreen: #30B08F;

// sidebar
$menuText:#bfcbd9;
$menuActiveText:#409EFF;
$subMenuActiveText:#f4f4f5; // https://github.com/ElemeFE/element/issues/12951

$menuBg:#304156;
$menuHover:#263445;

$subMenuBg:#1f2d3d;
$subMenuHover:#001528;

$sideBarWidth: 235px;



$subMenuChildrenHover:#409EFF;
$subMenuChildrenSelectText:#409EFF;
$subMenuChildrenText:#001528;
$subMenuChildrenBG:#fff;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;

  subMenuChildrenHover: $subMenuChildrenHover;
  subMenuChildrenSelectText: $subMenuChildrenSelectText;
  subMenuChildrenText: $subMenuChildrenText;
  subMenuChildrenBG: $subMenuChildrenBG;
}
