import axios from 'axios'
import { MessageBox, Message } from 'element-ui'
import store from '@/store'
import { getToken } from '@/utils/auth'

// create an axios instance
const service = axios.create({
  headers: {
    'Content-Type': 'application/json;charse=UTF-8'
  },
  baseURL: 'http://192.168.0.113:20500',
  timeout: 15000 
})

service.interceptors.request.use(
  config => {
    if (config.headers['Content-Type']==undefined) {
      config.headers['Content-Type'] = 'application/json;charse=UTF-8'
    }
    config.headers['Authorization'] = 'Basic YWRtaW5fdWk6YWRtaW5fdWlfc2VjcmV0'
    if (store.getters.token) {
      config.headers['token'] = getToken()
    }
    return config
  },
  error => {
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

service.interceptors.response.use(
  response => {
    const res = response.data
    if (res.code != 0) {
      Message({
        message: res.msg || 'Error',
        type: 'error',
        duration: 5 * 1000
      })
      
      if (res.code === 40001 || res.code === 50012 || res.code === 50014) {
        // to re-login
        MessageBox.confirm('You have been logged out, you can cancel to stay on this page, or log in again', 'Confirm logout', {
          confirmButtonText: 'Re-Login',
          cancelButtonText: 'Cancel',
          type: 'warning'
        }).then(() => {
          store.dispatch('user/resetToken').then(() => {
            location.reload()
          })
        })
      }
      return {
        code: res.code,
        data: null,
        msg: res.msg
      }
    } else {
      return res
    }
  },
  error => {
    Message({
      message: error.message,
      type: 'error',
      duration: 5 * 1000
    })
    return Promise.reject(error)
  }
)

export default service
