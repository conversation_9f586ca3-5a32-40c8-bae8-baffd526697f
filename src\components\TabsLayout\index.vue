<template>
  <div class="varietiesBan-list-container">
    <div class="varietiesBan-list-tabs-wrapper">
      <div class="varietiesBan-list-tabs" v-if="tabs.length > 0">
        <template v-for="(tab, index) in tabs">
          <div
            class="tab"
            :class="{ active: currentTab == index }"
            v-if="!tab.hide"
            :key="index"
            @click="handleChangeTab(index, tab)"
          >
            {{ tab.name }}
          </div>
        </template>
      </div>
      <div class="operations">
        <slot name="button"></slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "TabsLayout",
  model: {
    prop: "value",
    event: "update"
  },
  props: {
    tabs: {
      type: Array,
      default: () => []
    },
    value: {
      type: Number | String,
      default: 0
    }
  },
  data() {
    return {
      currentTab: 0
    };
  },
  mounted() {
    const currentIndex = this.tabs.findIndex(item => item.value === this.value);
    this.currentTab = currentIndex === -1 ? 0 : currentIndex;
  },
  methods: {
    handleChangeTab(index, tab) {
      this.currentTab = index;
      this.$emit("update", tab.value);
      this.$emit("change", tab);
    },
    reset() {
      this.currentTab = 0;
    }
  }
};
</script>

<style lang="scss" scoped></style>
