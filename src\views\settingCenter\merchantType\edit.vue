<template>
  <el-dialog v-if="visible" :title="(row.id>0?'编辑':'新增')+'企业类型'" :visible.sync="visible"  :show-close="true"  :before-close="clearFun"  :width="'600px'" :close-on-click-modal="false" >
  <div class="merchantTypeEditContent">
     
    <el-form class="form" :model="query" ref="ruleForm" label-width="110px">
      <el-form-item
        class="formItem"
        prop="name"
        label="企业类型名称:"
        :rules="[{ required: true, message: '请填写企业类型名称',trigger: 'blur' }]"
      >
        <el-input
          clearable
          style="width:250px"
          v-model="query.name"
          placeholder="请填写企业类型名称"
        ></el-input>
      </el-form-item>
      <!-- <el-form-item
        class="formItem"
        label="资质类型:"
      >
        <el-select style="width:250px" v-model="type" placeholder="" @change="getLicenseBaseTypes()">
          <el-option key="PRODUCT" value="PRODUCT" label="产品"></el-option>
          <el-option key="BUYER"  value="BUYER" label="采购员"></el-option>
          <el-option key="MERCHANT"  value="MERCHANT" label="经销商"></el-option>
        </el-select>
         
      </el-form-item> -->
      <el-form-item
        class="formItem"
        prop="licenseBaseIds"
        label="企业资质文件:"
      >
         <el-checkbox-group v-model="query.licenseBaseIds">
          <el-checkbox v-for="(item,index) in licenseBaseCheckBox" :key="index"  :label="item.id"  name="licenseName">{{item.name}}</el-checkbox>
        </el-checkbox-group> 
      </el-form-item>
       
    </el-form>
  </div>
    <span slot="footer" class="dialog-footer">
           <el-button @click="clearFun()">取 消</el-button>
        <el-button type="primary" @click="submitFun('ruleForm')"
          >确 定</el-button
        >
      </span>
  </el-dialog>
</template>
<script>
import { getLicenseBaseByType,editApi } from '@/api/setting/merchantType'
export default {
  data() { 
    return {
      type:"BUYER",
      licenseBaseCheckBox:null,
      query: this.initQuery()
    };
  },
  props: {
    row: {
      type: Object
    },
    visible: {
      type: Boolean,
      default: false,
      required: true
    },
    isReload: {
      type: Boolean,
      default: false,
      required: true
    }
  },
  methods: {
    initQuery(){
      return {
        id:0,
        "isDelete": "",
        "licenseBaseIds": [],
        "name": ""
      };
    },
    clearFun: function() {
      this.$emit("update:visible", false);
      this.$emit("update:row", {});
    },
    async submitFun(ruleForm) {
      var _this=this;
      _this.$refs[ruleForm].validate(async (valid) => {
        if (valid) {
          var data= await editApi(this.query);
          if(data.code==0){
            _this.$emit("update:visible", false);
            _this.$emit("update:isReload", true);
          }
        } else {
          return false;
        }
      });
    },
    async getLicenseBaseTypes(){
      var {data}= await getLicenseBaseByType(this.type)
      this.licenseBaseCheckBox=data
    }
  },
  mounted() {
    this.getLicenseBaseTypes()
    this.query=Object.assign(this.query,this.row)
    if(this.query.id!=0){
      this.query.isDelete=this.query.isDelete.code
    }
    if(this.query.licenseBases!=undefined&&this.query.licenseBases.length!=0){
        var licenseBaseIds=[]
        this.query.licenseBases.forEach(function(item){
           if(item!=null){
               licenseBaseIds.push(item.id)
           } 
        }) 
        this.query.licenseBaseIds=licenseBaseIds
    }

  },
  beforeDestroy() {}
};
</script>
<style lang="less" scoped>
.merchantTypeEditContent {
  margin: -30px -20px;
  border-top: 1px solid #ebecee;
  padding: 30px 20px;
}
</style>
