<template>
 <section>
   <el-table
     border
     :data="tableData"
     @selection-change="handleSelectionChange">
     <el-table-column type="index" align="center" width="50">
       <template slot="header">
         <i class="el-icon-menu pointer" @click="showTransfer=true"></i>
       </template>
     </el-table-column>
     <el-table-column type="selection" align="center" width="50" v-if="selection"/>
     <!-- 表单 -->
     <template v-for="column in renderColumns">
       <slot v-if="column.slot" :name="column.prop"></slot>
       <el-table-column
         :key="column.prop"
         v-else
         :prop="column.prop"
         :label="column.label"
         :width="column.width"
         :class-name="column.className"
         :align="column.align || 'left'"
       />
     </template>
   </el-table>
   <el-dialog
     title="显示隐藏"
     :visible.sync="showTransfer"
     width="60%">
     <div class="transfer-body">
       <el-transfer
         :titles="['显示表', '隐藏列']"
         v-model="hideColumns"
         :props="{
          key: 'prop',
          label: 'label'
       }"
         :data="tableOptions">
       </el-transfer>
     </div>
     <div slot="footer" class="dialog-footer">
       <el-button @click="showTransfer=false">取 消</el-button>
       <el-button type="primary" @click="showTransfer=false">确 定</el-button>
     </div>
   </el-dialog>
 </section>
</template>

<script>
  export default {
    name: 'transfer-table',
    props: {
      tableOptions: { // 表单列
        type: Array,
        required: true
      },
      tableData: { // 表单数据
        type: Array,
        required: true
      },
      border: {
        type: Boolean,
        default: true
      },
      selection: { // 是否显示选项
        type: Boolean,
        default: true
      },
    },
    data () {
      return {
        hideColumns: [], // 隐藏的列
        showTransfer: false, // 显示选择框
      };
    },
    computed: {
      renderColumns () {
        let hideColumns = this.hideColumns;
        let allColumns = this.tableOptions || [];
        let ret = [];
        allColumns.forEach(column => {
          // 固定显示
          if (column.show) {
            return ret.push(column)
          }
          //查看是否在隐藏列表中
          if (hideColumns.indexOf(column.prop) > -1) {
            return;
          }
          ret.push(column)
        });
        return ret;
      },
    },
    methods: {
      handleSelectionChange (selects) {
        this.$emit('selection-change', selects)
      },
    }
  }
</script>

<style lang="scss" scoped>
  .pointer {
    cursor: pointer;
  }
  .transfer-body {
    display: flex;
    justify-content: center;
  }
</style>
