import requestAxios from '@/utils/requestAxios'
import request from '@/utils/request'

export function tempList(data) {
  return requestAxios({
    url: '/api/msgs/smsTemplate/page',
    method: 'POST',
    data
  })
  

}
export function deleteApi(id) {
  return requestAxios({
    url: '/api/msgs/smsTemplate?ids[]=' + id,
    method: 'delete'
  })
}

export function editSmsTemplate(data) {
  return requestAxios({
    url: '/api/msgs/smsTemplate',
    method: data.id > 0 ?"PUT":"POST",
    data
  })
}

export function getApi(id) {
  return requestAxios({
    url: '/api/msgs/smsTemplate/' + id,
    method: "get"
  })
}
