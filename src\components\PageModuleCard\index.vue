<template>
  <div class="detail-item">
    <page-module-title :title="title" :tips="tip">
      <slot name="button-group"></slot>
    </page-module-title>
    <div class="content"><slot></slot></div>
  </div>
</template>

<script>
export default {
  name: "PageModuleCard",
  props: {
    title: {
      type: String,
      default: ''
    },
    tip: {
      type: String,
      default: ''
    }
  }
}
</script>

<style lang="scss" scoped>
  .detail-item {
    padding: 13px 0 20px;
    .content {
      font-size: 14px;
    }
  }
</style>
