<template>
  <div class="list—index">
    <!--搜索Form-->
    <im-search-pad :has-expand="false" :is-expand.sync="isExpand" :model="model" @reset="reload" @search="searchLoad">
      <im-search-pad-item prop="saleMerchantName">
        <el-input v-model="model.saleMerchantName" @keyup.enter.native="searchLoad" placeholder="请输入商家名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="personName">
        <el-input v-model="model.personName" @keyup.enter.native="searchLoad" placeholder="请输入打卡人姓名" />
      </im-search-pad-item>
      <im-search-pad-item prop="purMerchantName">
        <el-input v-model="model.purMerchantName" @keyup.enter.native="searchLoad" placeholder="请输入客户名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="exceptionReason">
        <el-select v-model="model.exceptionReason" placeholder="是否超范围打卡">
          <el-option label="正常范围" value="0"></el-option>
          <el-option label="超范围" value="1"></el-option>
          <el-option label="未打卡" value="2"></el-option>
          <el-option label="迟到打卡" value="3"></el-option>
          <el-option label="早退" value="4"></el-option>
        </el-select>
      </im-search-pad-item>
    </im-search-pad>
    <!--  -->
    <div class="tab_bg">
       <!--Tabs布局-->
      <tabs-layout ref="tabs-layout" :tabs="[{name:'拍照签到'}]">
        <!--tabs右上角相关按钮-->
        <template slot="button">
          <el-button @click="reload">刷新</el-button>
        </template>
      </tabs-layout>

      <!-- 分页tab -->
      <table-pager ref="pager-table" :options="tableColumns" :remote-method="load" :data.sync="tableData"
        :selection="false" :pageSize="pageSize"
        :operation-width="150" :isNeedButton="true">

        <el-table-column label="打卡类型"  width="120" slot="siginType">
          <slot slot-scope="{ row }">
            <div>{{ row.siginType == 1 ? '签到' : '签退' }}</div>
          </slot>
        </el-table-column>

        <el-table-column label="打卡偏差"  width="120" slot="offset">
          <slot slot-scope="{ row }">
            <div>{{ row.offset }} 米</div>
          </slot>
        </el-table-column>

        <el-table-column label="图片说明"  width="130" slot="imageIds">
          <slot slot-scope="{row}">
            <!-- <img style="width:50px;height:50px;margin-right:5px" v-for="(i,indexs) in row.imageIds.split(',')" :key="indexs" :src="i" alt=""> -->
            <div class="img-cell">
              <div v-if="row.imageIds.split(',').length < 2">
                <img style="width:50px;height:50px;margin-right:5px" v-for="(i,indexs) in row.imageIds.split(',')"
                  :key="indexs" :src="i" alt="">
              </div>
              <div class="img_box" v-else>
                <img style="width:50px;height:50px;margin-right:5px" :src="row.imageIds.split(',')[0]" alt="">
                <div class="img-posi">
                  <img style="width:50px;height:50px;margin-right:5px" :src="row.imageIds.split(',')[1]" alt="">
                  <div class="img_mask"> +{{ row.imageIds.split(',').length - 1 }}</div>
                </div>
              </div>
            </div>
          </slot>
        </el-table-column>
        <!--操作栏-->
        <div slot-scope="{row}">
          <el-row class="table-edit-row">
            <span class="table-edit-row-item">
              <el-button v-if="checkPermission(['admin', 'admin-business-sign:photographPreView'])" type="text" @click="handleBigImage(row)">预览图片</el-button>
              <el-image :ref="`ref${row.id}`" style="width:0;height:0" :src="previewDetail[0]"
                :preview-src-list="previewDetail"></el-image>
            </span>
          </el-row>
        </div>
      </table-pager>
    </div>
  </div>
</template>


<script>
const TableColumns = [
  {
    label:'商家名称',
    name:'saleMerchantName',
    prop:'saleMerchantName',
    width:'150'
  },
  {
      label: "打卡人编码",
      name: "personCode",
      prop: "personCode",
      width: "130"
    },
    {
      label: "打卡人姓名",
      name: "personName",
      prop: "personName",
      width: "110"
    },
    {
      label: "打卡类型",
      name: "siginType",
      prop: 'siginType',
      width: "130",
      slot: true
    },
    {
      label: "客户名称",
      name: "purMerchantName",
      prop: 'purMerchantName',
      width: "170",

    },
    {
      label: "目的地",
      name: "destination",
      prop: 'destination',
      width: "150"
    },
    {
      label: "打卡时间",
      name: "updateTime",
      prop: 'updateTime',
      width: "160"
    },
    {
      label: "打卡位置",
      name: "address",
      prop: 'address',
      width: "180"
    },
    {
      label: "打卡偏差",
      name: "offset",
      prop: 'offset',
      width: "140",
      slot: true
    },
    {
      label: "补充说明",
      name: "remarks",
      prop: 'remarks',
      width: "150",
    },
    {
      label: "图片说明",
      name: "imageIds",
      prop: 'imageIds',
      width: "200",
      slot: true
    }
  ];
  const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({
      key: i,
      ...TableColumns[i]
    });
  }
  import {
    punchSiginlist
  } from '@/api/salemanCenter/index' // TODO 替换成对应用的列表api
  import checkPermission from '@/utils/permission';
  export default {
    //import引入的组件
    components: {},

    data() {
      return {
        isExpand: false,
        model: {
          exceptionReason: '',
          personName: '',
          purMerchantName: '',
          saleMerchantName: ''
        },
        tableData:[],
        previewDetail:[],
        tableColumns: TableColumnList,
        pageSize:10,
      };
    },
    //生命周期 - 挂载完成（可以访问DOM元素）
    mounted() {},

    computed: {},

    created() {},

    filters: {},

    //方法集合
    methods: {
      checkPermission,
      async load(params) {
        let listQuery = {
          model: {
            ...this.model
          }
        };
        Object.assign(listQuery, params);
        return await punchSiginlist(listQuery);
      },
      // 预览图片
      async handleBigImage(row) {
        this.previewDetail = [];
        if (row.imageIds != null && row.imageIds != undefined && row.imageIds != 'null') {
          if (row.imageIds.split(',').length == 0) {
            this.$message.warning('无图片可查看');
            return
          }
          row.imageIds.split(',').forEach(item => {
            this.previewDetail.push(item);
          });
          this.$refs[`ref${row.id}`].showViewer = true;
        }
      },
      searchLoad() {
        this.handleRefresh({
          page: 1,
          pageSize: this.pageSize
        })
      },
      reload() {
        this.$refs['tabs-layout'].reset();
        this.handleRefresh({
          page: 1,
          pageSize: this.pageSize
        })
      },
      handleRefresh(pageParams) {
        this.$refs['pager-table'].doRefresh(pageParams)
      },
    },
  };

</script>


<style lang='scss' scoped>
  .img-cell {
      display: flex;
      align-items: center;
      .img_box {
        display: flex;
        align-items: center;
        position: relative;
        .img-posi {
          width: 50px;
          height: 50px;
        }
        .img_mask {
          position: absolute;
          width: 50px;
          height: 50px;
          top: 0;
          left: 55px;
          line-height: 50px;
          text-align: center;
          background-color: rgba($color: #000000, $alpha: 0.6);
          color: #fff;
        }
      }
    }
</style>
