<template>
  <div class="archivesPageContent">
    <im-search-pad
      :has-expand="false"
      :model="listQuery"
      @reset="resetForm"
      @search="onSearchSubmitFun"
    >
      <im-search-pad-item prop="merchantsName">
        <el-input v-model="listQuery.model.merchantsName" placeholder="请输入商家名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="shopName">
        <el-input v-model="listQuery.model.shopName" placeholder="请输入店铺名称" />
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        v-model="tabType"
        :tabs="[{ name: '商家余额' }]"
      />
      <div class="table">
        <el-table
          ref="table"
          v-if="list"
          @selection-change="selectTableItemFun"
          v-loading="listLoading"
          :data="list"
          row-key="id"
          border
          fit
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column
            align="center"
            width="65"
            :render-header="renderHeader"
            fixed
          >
            <template slot-scope="scope">
              <span>{{ scope.$index + 1 }} </span>
            </template>
          </el-table-column>
          <el-table-column
            type="selection"
            width="55"
            align="center"
          ></el-table-column>
          <el-table-column
            v-for="(item, index) in tableTitle"
            :key="index"
            :min-width="item.width ? item.width : '350px'"
            :label="item.label"
            show-overflow-tooltip
            align="left"
          >
            <template slot-scope="{ row }">
            <span
              v-if="item.name == 'status'"
              :style="row[item.name].code == 'ENABLED' ? '' : 'color:#ff0036'"
            >{{ row[item.name].code == "ENABLED" ? "已启用" : "已冻结" }}
            </span>
              <span v-else-if="item.name == 'amount'" style="color: #70b603">{{
                  row[item.name] | getDecimals
                }}</span>
              <span v-else>{{ row[item.name] }}</span>
            </template>
          </el-table-column>

          <el-table-column
            fixed="right"
            align="center"
            label="操作"
            width="80"
            class="itemAction"
          >
            <template slot-scope="{ row }">
              <!-- 充值和扣减暂时不做 -->
              <!-- <el-button type="text"  @click="addFun(row)"
                >充值</el-button
              >
              <el-button type="text"  @click="reduceFun(row)"
                >扣减</el-button
              > -->
              <el-row class="table-edit-row">
                <span class="table-edit-row-item" v-if="checkPermission(['admin', 'admin-finance-merchantAccount:detail'])">
                  <el-button type="text"  @click="detailFun(row)">查看记录</el-button>
                </span>
              </el-row>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-if="total > 0"
          :pageSizes="[10, 20, 50, 100]"
          :total="total"
          :page.sync="listQuery.current"
          :limit.sync="listQuery.size"
          @pagination="getlist"
        />
      </div>
    </div>
    <el-dialog
      append-to-body
      title="充值"
      :visible.sync="rechargeFormFlag"
      width="30%"
      @close="close"
      :close-on-click-modal="false"
    >
      <el-form ref="recharge" :model="rechargeForm" label-width="100px">
        <el-form-item
          class="formItem"
          prop="pLabel"
          label="充值余额:"
          :rules="[
            { required: true, message: '请输入充值余额', trigger: 'blur' },
          ]"
        >
          <el-input
            style="width: 90%; min-width: 200px"
            v-model="rechargeForm.recharge"
            placeholder="请输入大于 0 的整数"
          >
            <template slot="prepend">增加</template>
            <template slot="append">元</template></el-input
          >
        </el-form-item>
        <el-form-item
          class="formItem"
          prop="pLabel"
          label="备注信息:"
          :rules="[
            { required: true, message: '请输入备注信息', trigger: 'blur' },
          ]"
        >
          <el-input
            style="width: 90%; min-width: 200px"
            type="textarea"
            rows="4"
            v-model="rechargeForm.recharge"
            placeholder="请输入大于 0 的整数"
          >
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="rechargeFormFun">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog
      append-to-body
      title="扣减"
      :visible.sync="reduceFormFlag"
      width="30%"
      @close="close"
      :close-on-click-modal="false"
    >
      <el-form ref="reduce" :model="reduceForm" label-width="100px">
        减少
        <el-form-item
          class="formItem"
          prop="pLabel"
          label="扣减余额:"
          :rules="[
            { required: true, message: '请输入扣减余额', trigger: 'blur' },
          ]"
        >
          <el-input
            style="width: 90%; min-width: 200px"
            v-model="reduceForm.reduce"
            placeholder="请输入大于 0 的整数"
          >
            <template slot="prepend">减少</template>
            <template slot="append">元</template></el-input
          >
        </el-form-item>
        <el-form-item
          class="formItem"
          prop="pLabel"
          label="备注信息:"
          :rules="[
            { required: true, message: '请输入备注信息', trigger: 'blur' },
          ]"
        >
          <el-input
            style="width: 90%; min-width: 200px"
            type="textarea"
            rows="4"
            v-model="reduceForm.reduce"
            placeholder="请输入大于 0 的整数"
          >
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="reduceFormFun">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { list } from "@/api/finance/merchantsBalance";
import Pagination from "@/components/Pagination";
import { areas } from "@/api/enterprise";
import tableInfo from "@/views/finance/merchantsBalance/tableInfo";
import detailTable from "@/views/finance/merchantsBalance/detailTable";
import { setContextData, getContextData } from "@/utils/auth";
import TabsLayout from '@/components/TabsLayout'
import checkPermission from '@/utils/permission';
export default {
  data() {
    return {
      listLoading: false,
      list: [],
      tabType: "list",
      listQuery: {
        current: 1,
        size: 10,
        model: {},
      },
      detailType: [
        {
          index: 0,
          value: "ORDER",
          label: "订单交易",
        },
        {
          index: 1,
          value: "REFUND",
          label: "退货退款",
        },
        {
          index: 2,
          value: "RECHARGE",
          label: "余额充值",
        },
        {
          index: 3,
          value: "CASH",
          label: "余额提现",
        },
        {
          index: 4,
          value: "CASHCHONGZHI",
          label: "提现冲正",
        },
        {
          index: 5,
          value: "SERVICE",
          label: "交易服务费",
        },
        {
          index: 6,
          value: "PROCEDURES",
          label: "交易手续费",
        },
        {
          index: 7,
          value: "COMMISSION",
          label: "品种推广佣金",
        },
      ],
      paymentsType: [
        {
          index: 0,
          value: "COLLECT",
          label: "收入(收款)",
        },
        {
          index: 1,
          value: "PAY",
          label: "支出(付款)",
        },
      ],
      total: 100,
      cityValue: [],
      tableTitle: [],
      timePicker: [],
      tableSelectTitle: [0, 1, 2, 3],
      multipleSelection: [],
      multipleSelectionId: [],
      showSelectTitle: false,
      rechargeForm: {},
      rechargeFormFlag: false,
      reduceForm: {},
      reduceFormFlag: false,
      detailTableFlag: false,
      detailTableReload: false,
      row: {},
      props: {
        lazy: true,
        async lazyLoad(node, resolve) {
          const { level } = node;
          let id = node.data ? node.data.id : "";
          let res = await areas({ parentId: id });
          let list = res.data;
          list.forEach((item) => {
            item.value = item.id;
            item.leaf = level >= 2;
          });
          resolve(list);
        },
      },
      pickerOptions: {
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
    };
  },
  methods: {
    checkPermission,
    resetForm() {
      this.cityValue = [];
      this.list = [];
      this.listQuery = {
        current: 1,
        size: 10,
        model: {},
      };
      this.getlist();
    },
    cityChange(e) {
      this.listQuery.model.provinceId = e[0];
      this.listQuery.model.cityId = e[1];
      this.listQuery.model.countyId = e[2];
    },
    selectTableItemFun: function (val) {
      // let arr = [];
      // val.forEach((item) => {
      //   arr.push(item.id);
      // });
      // this.multipleSelection = val;
      // this.multipleSelectionId = arr;
    },
    detailFun(row) {
      setContextData("merchantsBalance_detail", this.listQuery);
      this.$router.push({
        path: "/finance/merchantsBalance/detail",
        query: {
          id: row.merchantsId,
          name: row.merchantsName,
        },
      });
      // this.detailTableFlag = true;
      // this.row = row;
    },
    reduceFormFun(e) {},
    reduceFun(e) {
      this.reduceFormFlag = true;
    },
    rechargeFormFun() {},
    addFun(row) {
      this.rechargeFormFlag = true;
    },
    rechargeFun() {},
    close() {
      this.rechargeFormFlag = false;
      this.reduceFormFlag = false;
      this.$refs.reduce.resetField();
      this.$refs.recharge.resetField();
    },
    selectTime() {
      this.listQuery.model.startTime = e[0];
      this.listQuery.model.endTime = e[1];
    },
    onSearchSubmitFun() {
      this.getlist();
    },
    async getlist() {
      let { data } = await list(this.listQuery);
      this.total = data.total;
      this.list = data.records;
    },
    initTbaleTitle() {
      this.tableSelectTitle = [];
      this.tableTitle = tableInfo[this.tabType];
    },
    renderHeader(h, { column }) {
      var titles = tableInfo[this.tabType];
      var titlesName = ["显示字段项", "隐藏字段项"];
      return (
        <div style="position:relative">
          <div onClick={this.showHeaer}>
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="640px"
            center
            append-to-body={true}
          >
            <el-transfer
              vModel={this.tableSelectTitle}
              data={titles}
              titles={titlesName}
              onChange={this.setleftTitleFun}
            ></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button onClick={this.closeHeaer}>取消</el-button>
              <el-button type="primary" onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      );
    },
    setleftTitleFun(val) {
      this.tableSelectTitle = val;
    },
    showHeaer: function () {
      this.showSelectTitle = true;
    },
    closeHeaer: function () {
      this.showSelectTitle = false;
      this.tableSelectTitle = [];
    },
    setHeaer: function () {
      var titles = tableInfo[this.tabType];
      var listinfo = titles.filter((element, index, self) => {
        return !this.tableSelectTitle.includes(element.key);
      });
      this.tableTitle = listinfo;
      this.showSelectTitle = !this.showSelectTitle;
    },
  },
  created() {},
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (from.path == "/finance/merchantsBalance/detail") {
        if (getContextData("merchantsBalance_detail") != "") {
          vm.listQuery = getContextData("merchantsBalance_detail");
        }
      }
      vm.initTbaleTitle();
      vm.getlist();
    });
  },
  components: {
    Pagination,
    detailTable,
    TabsLayout
  },
};
</script>


<style lang="scss" scoped>
.archivesPageContent {
  padding: 0;
  .temp_searchBox {
    height: 64px;
    overflow: hidden;
    margin-bottom: 0;
  }
  .form-inline {
    height: 60px;
    overflow: hidden;
  }
  .title {
    border-bottom: 2px solid #ebecee;
    margin-bottom: 16px;
    span {
      margin-bottom: -2px;
      padding: 0 15px;
      height: 40px;
      line-height: 30px;
      display: block;
      background: rgba(255, 255, 255, 0);
      border-bottom: 2px solid rgb(64, 158, 255);
      font-size: 16px;
      font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
        "PingFang SC"-400;
      font-weight: 400;
      color: rgb(64, 158, 255);
    }
  }
  .formItem {
    width: 586px;
  }
  .line {
    color: #dfe6ec;
    margin: 0 6px;
  }
  .typeTabs {
    height: 40px;
    margin-bottom: -2px;
  }
}
</style>
