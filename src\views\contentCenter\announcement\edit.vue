<template>
  <div class="main">
    <!-- <div class="el-dialog__header">
      <div class="el-dialog__title">编辑资讯</div>
    </div> -->
    <div class="title"><span>资讯信息</span></div>
    <el-form ref="form" :model="form" label-width="170px" style="padding: 20px;">
      <el-form-item label="标题：" prop="title" :rules="[{required: true,min: 2, message: '请至少输入2个字', trigger: 'blur'}]">
        <el-input v-model="form.title" style="width:500px"></el-input>
      </el-form-item>

      <el-form-item label="公告内容：" prop="content" :rules="[{required: true,message: '请输入资讯内容！', trigger: 'blur'}, {required: true,min:20, message: '内容请输入20字以上'}]">
        <Tinymce v-model="form.content" :width="1000" :height="200" />
      </el-form-item>

      <el-form-item label="创建人：" prop="title">
        <el-input :disabled='true' :value="$store.state.user.name" style="width:500px"></el-input>
      </el-form-item>

      <div class="title"><span>其它</span></div>
      <el-form-item label="是否发布：" prop="title">
        <el-radio-group v-model="form.publishStatus">
          <el-radio-button :label="'Y'" >是</el-radio-button>
          <el-radio-button :label="'N'" >否</el-radio-button>
        </el-radio-group>
        <!-- <el-switch v-model="form.publish_status" inactive-value="N" active-value="Y"> </el-switch> -->
      </el-form-item>
    </el-form>
    <div style="
        position: absolute;
        top: 2vh;
        right: 15px;
        background: #fff;
        height: 38px;
      ">
      <el-button @click="cancel">取消</el-button>
      <!-- <el-button @click="artShow">商城端预览</el-button> -->
      <el-button type="primary" @click="onSubmit">保存</el-button>
    </div>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import Tinymce from "@/components/Tinymce";
import { add, getitem, updateArticleContent } from "@/api/contentCenter/announcement";
export default {
  data() {
    return {
      form: {
        publishStatus: "Y",
      },
      imageUrl: "",
      insertProgram: {
        folderId: 0,
      },
      headersProgram: {
        token: getToken(),
        Authorization: "Basic YWRtaW5fdWk6YWRtaW5fdWlfc2VjcmV0",
      },
      submitReload: "",
    };
  },
  methods: {
    // artShow() {
    //   this.$emit('update:artshowFlag', true)
    //   this.$emit('update:row', this.form)
    // },
    cancel() {
      this.$emit('update:visible', false)
    },
    onSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          if (this.form.id) {
            this.form.publish_status = this.form.publishStatus
            let {data} = await updateArticleContent(this.form)
            this.form = {};
            this.$emit('update:visible', false)
            this.$emit('update:isReload', true)
          } else {
            let { data } = await add(this.form);
            this.form = {};
            this.$emit('update:visible', false)
            this.$emit('update:isReload', true)
          }
        }
      });
    },
    handleAvatarSuccess(res, file) {
      this.form.coverImg = URL.createObjectURL(file.raw);
    },
    beforeAvatarUpload(file) {
      console.log(file);
      // const isJPG = file.type === 'image/jpeg';
      const isJPG = true;
      const isLt2M = file.size / 1024 / 1024 < 5;

      if (!isJPG) {
        this.$message.error("上传头像图片只能是 JPG 格式!");
      }
      if (!isLt2M) {
        this.$message.error("上传头像图片大小不能超过 2MB!");
      }
      return isJPG && isLt2M;
    },
    async getitem() {
      let { data } = await getitem(this.row.id);
      data.publishStatus = data.publish_status.code
      this.form = data
    },
  },
  components: {
    Tinymce,
  },
  created() {
    if (JSON.stringify(this.row) != '{}') {
      this.getitem();
    }
  },
  props: {
    row: {
      type: Object,
      required: true,
    },
    artshowFlag: {
      type: Boolean,
      required: true
    }
  },
};
</script>

<style lang="less" scoped>
.main {
  padding: 30px 0px;
  // overflow: scroll;
  margin: -30px -20px;
  border-top: 1px solid #ebecee;
  padding: 30px 20px;
  .title {
    padding: 20px;
    span {
      font-size: 16px;
      padding-left: 10px;
      border-left: 4px solid rgba(64, 158, 255, 1);
    }
  }
  .upload_ms {
    color: #8c939d;
    line-height: 10px;
  }
  /deep/.avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  /deep/.avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }
  /deep/.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 120px;
    height: 120px;
    line-height: 120px;
    text-align: center;
  }
  /deep/.avatar {
    width: 120px;
    height: 120px;
    display: block;
  }
}
</style>
