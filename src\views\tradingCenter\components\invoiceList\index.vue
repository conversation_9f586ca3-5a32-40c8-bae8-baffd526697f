<template>
  <div>
    <im-search-pad
      :has-expand="false"
      :model="model"
      @reset="reload"
      @search="searchLoad"
    >
      <im-search-pad-item prop="orderDeliveryNo">
        <el-input v-model="model.orderDeliveryNo" placeholder="请输入发货单号" />
      </im-search-pad-item>
      <im-search-pad-item prop="during">
        <el-date-picker
          type="daterange"
          range-separator="至"
          v-model="model.during"
          value-format="yyyy-MM-dd"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </im-search-pad-item>
      <im-search-pad-item prop="during">
        <el-select v-model="model.deliveryStatus" placeholder="签收状态" clearable @change="getType">
          <el-option value="HAD_DELIVERY" label="待签收"/>
          <el-option value="HAD_SIGN" label="已签收"/>
          <el-option value="HAD_REJECTED" label="已拒收"/>
        </el-select>
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        :tabs="tabs"
      >
        <template slot="button">
          <!-- <el-button>导出单据</el-button> -->
          <el-button @click="reload">刷新</el-button>
        </template>
      </tabs-layout>
      <table-pager ref="todoTable" :pageSize="pageSize" :options="tableTitle" :remote-method="load" :data.sync="tableData" :isNeedButton="false"></table-pager>
    </div>
  </div>
</template>

<script>
  const TableColumns = [
    { label: "发货单号", name: "deliveryNo",prop: "deliveryNo",width: "190"},
    { label: "发货时间", name: "deliveryTime", prop:"deliveryTime",width: "150" },
    { label: "关联单号", name: "orderNo",prop: 'orderNo' },
    { label: "客户编码", name: "code", prop:'code' },
    { label: "客户名称", name: "name", prop:'name'},
    { label: "收货地址", name: "address",prop: 'address',width: "180"},
    { label: "签收状态", name: "deliveryStatus.desc",prop:'deliveryStatus.desc',width: "90" }
  ];
  const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({ key: i, ...TableColumns[i] });
  }
  import {orderDeliveryList } from '@/api/trade'
  import TabsLayout from '@/components/TabsLayout'
  export default {
    components: {
      TabsLayout
    },
    props: {
      commerceModel: {
        type: String,
        required: true
      }
    },
    data () {
      return {
        loading: '',
        search: '',
        controlType: '',
        currentTab: 0,
        tabs: [
          { name: '发货单' },
        ],
        tableData: [],
        page: 1,
        pageSize: 10,
        totalPage: 0,
        total: 0,
        tableTitle: TableColumnList,
        model: {
          orderDeliveryNo: '',
          during: '',
          deliveryStatus: ''
        },
        products: [],
        ids: []
      }
    },
    mounted() {
    },
    methods: {
      async load(params) {
        let listQuery = {
          model: {
            orderDeliveryNo: this.model.orderDeliveryNo,
            startTime: this.model.during[0],
            endTime: this.model.during[1],
            deliveryStatus: this.model.deliveryStatus,
            commerceModel: this.commerceModel
          }
        }
        Object.assign(listQuery, params)
        this.loading = true
        return await orderDeliveryList(listQuery)
      },
      searchLoad() {
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      reload() {
        this.model = {
          orderDeliveryNo: '',
          during: '',
          deliveryStatus: ''
        }
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      handleChangeTab (index,value) {
        this.currentTab = index
        this.model.orderStatus = value
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      getType(val) {
        this.model.deliveryStatus = val
      },
      handleRefresh(pageParams) {
        this.$refs.todoTable.doRefresh(pageParams)
      }
    }
  }
</script>
