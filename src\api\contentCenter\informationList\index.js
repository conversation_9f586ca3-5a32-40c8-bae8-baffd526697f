
import requestAxios from '@/utils/requestAxios'

// 分页数据获取
// /api/general/admin/contentRel/page
export function list(data) {
  return requestAxios({
    url: '/api/general/admin/article/page',
    method: 'post',
    data
  })
}





// 获取单一资讯文章
export function getitem(data) {
  return requestAxios({
    url: '/api/general/admin/article/' + data,
    method: 'get',
  })
}

// 修改
export function putItem(data) {
  return requestAxios({
    url: '/api/general/admin/article',
    method: 'put',
    data
  })
}

// 新增
export function add(data) {
  return requestAxios({
    url: '/api/general/admin/article',
    method: 'post',
    data
  })
}

//获取资讯分类
export function category(data) {
  data = {
    parentId: '19922317471706114'
  }
  return requestAxios({
    url: '/api/general/admin/articleCategory/getAllParentCodeOrChildren',
    method: 'get',
    params: data
  })
}

//获取章节分类
export function getcategory(data) {
  data = {
    parentId: '2'
  }
  return requestAxios({
    url: '/api/general/admin/articleCategory/getAllParentCodeOrChildren',
    method: 'get',
    params: data
  })
}

// 删除
export  function del(data) {
  return requestAxios({
    url: '/api/general/admin/article',
    method: 'delete',
    params: data
  })
}

// 启用资讯
export  function start(data) {
  return requestAxios({
    url: '/api/general/admin/article/updateArticleEnable/' + data,
    method: 'PUT',
  })
}

// 停用资讯
export  function disable(data) {
  return requestAxios({
    url: '/api/general/admin/article/updateArticleStop/' + data,
    method: 'PUT',
  })
}

export  function getTreeList() {
  return requestAxios({
    url: '/api/general/admin/articleCategory/getTreeList',
    method: 'get',
  })
}



