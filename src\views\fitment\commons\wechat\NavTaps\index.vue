<template>
     <div class="items"  @click="checkPermission(['admin','fitment-wechat:edit'])&&showDrawerFun()">
            <div class="navItems" >
                <el-row  style="width:100%">
                    <el-col  v-for="(item,index) in query.navItems"  :key="index" class="navItem" :style="{width:(100/query.lineSize)+'%'}">
                        <el-image
                            class="icon"
                            :src="item.image"
                            :fit="'cover'"></el-image>
                        <span>{{item.title}}</span>
                    </el-col>
                </el-row>
            </div>

            <el-drawer
            :destroy-on-close="true"
            :size="'550px'"
            append-to-body
            :wrapperClosable="false"
            :visible.sync="drawer"
            :with-header="false">
            <div class="flex_between_center top" >
                <div>图文导航</div> 
                <div>
                    <el-button @click="drawer=false" >取 消</el-button>
                    <el-button type="primary"  @click="submitFun()" >提交</el-button>
                </div>
            </div>
            <div class="tipBox">
                <p class="title">添加导航图标</p>
                <p class="tip">导航图标显示的顺序按照从左到右，从上到下的顺序排列，尺寸建议52x52，鼠标拖拽可调整导航顺序</p>
            </div>
            <el-form  label-width="175px" class="form">
                <el-form-item label="显示行数" label-width="85px" class="formItem">
                    <el-radio-group v-model="compomentLayout.ext1">
                        <el-radio label="1">一行</el-radio>
                        <el-radio label="2">两行</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="每行个数" label-width="85px">
                    <el-select v-model="compomentLayout.ext2" placeholder="placeholder">
                        <el-option label="5" value="5"></el-option>
                        <el-option label="10" value="10"></el-option>
                    </el-select>
                </el-form-item>
                <div class="fromBox">
                    <el-form-item class="setItemBox" :label-width="'0'" label=" "  v-for="(item,index) in navItems" :key="index"   v-dragging="{ item: item, list: navItems, group: 'item' }" >
                        <i class="el-icon-error closeI" @click="removeItem(index)"></i>
                        <div class="flex_start_start">
                            <div class="uploadImgBox" @click="uploadIndexItem(index)">
                                <el-upload
                                    class="avatar-uploader"
                                    :action="$uploadUrl"
                                        :data="insertProgram"
                                        :headers="headersProgram"
                                        :on-success="uploadSuccess"
                                        :before-upload="beforeUpload"
                                        :show-file-list="false"
                                        multiple
                                    >
                                    <el-image v-if="item.image!=''" style="width: 80px; height: 80px" :src="item.image" :fit="'cover'"></el-image>
                                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                                </el-upload>
                            </div>
                            <div class="inputBox">
                                <el-form-item label="标题"  class="inputItem"><el-input placeholder="请录入标题" v-model="item.title"></el-input></el-form-item>
                                <el-form-item label="链接类型" class="inputItem"> 
                                    <!-- <el-select v-model="item.linkUrl" placeholder="请选择链接类型" > -->
                                    <!-- <el-select v-model="item.linkUrl" placeholder="请选择链接类型" @change="changeLinkFun"> -->
                                    <el-select v-model="item.linkUrl" placeholder="请选择链接类型" @change="changeLinkFun($event,item)">
                                    <el-option value="">请选择</el-option>
                                    <el-option
                                        v-for="itemInfo in selectList"
                                        :key="itemInfo.describe"
                                        :label="itemInfo.name"
                                        :value="itemInfo.describe">
                                    </el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="链接" class="inputItem">{{item.linkUrl}}</el-form-item>
                                <el-form-item class="inputItem" v-for="(paraItem,i) in item.linkParam" :label="'链接值'+paraItem.key" >
                                    <el-input @change="change($event)" placeholder="录入值" v-model="item.linkParam[i].val">
                                        <!-- 商品详情 PRODUCT_DETAIL-->
                                        <ProductItemTable v-if="item.searchType=='PRODUCT_DETAIL'" slot="append" :width="'100px'"  :selectItems.sync="item.linkParam[i].val" ></ProductItemTable>
                                        <!-- 商品详情 end-->
                                        <!-- 商家首页 STORE_INDEX-->
                                        <StoreListTable v-if="item.searchType=='STORE_INDEX'" slot="append" :width="'100px'"  :selectItems.sync="item.linkParam[i].val" ></StoreListTable>
                                        <!-- 商家首页 end-->
                                        <!-- 分类列表页面  CATEGORYTYPE_LIST-->
                                        <ProductTypeItemTable v-if="item.searchType=='CATEGORYTYPE_LIST'" slot="append" :width="'100px'"  :selectItems.sync="item.linkParam[i].val" ></ProductTypeItemTable>
                                        <!-- 分类列表页面 end-->
                                        <!-- 专题页 -->
                                        <SpecialTable v-if="item.searchType=='SPECIAL_PAGE'" slot="append" :width="'100px'" :selectItems.sync="item.linkParam[i].val" />
                                        <!-- 专题页 end -->
                                    </el-input>
                                </el-form-item>
                            </div>
                        </div>
                    </el-form-item>
                 </div>
            </el-form>
            <div class="addbtn" @click="addItemBtnFun">
                + 添加图文导航
            </div>
        </el-drawer>
        </div>
</template>
<script>
import checkPermission from '@/utils/permission' 
import ProductItemTable from '@/components/eyaolink/Product/productItemCodeTable'
import ProductTypeItemTable from '@/components/eyaolink/Product/ProductTypeItemTable'
import StoreListTable from '@/components/eyaolink/Store/listTable'
import SpecialTable from '@/components/eyaolink/Product/SpecialTable'
import {param2Obj} from '@/utils/index' 
import {
  pageComponentList,
  pageComponentUpdate,
  deleteByPageComponentId,
  pageADList,
  pageADListAdd,
  pageADEdit
} from "@/api/fitment";
import { query } from "@/api/setting/data/dictionaryItem";
import { uploadFile } from "@/api/file";
import { getToken } from "@/utils/auth";
export default {
data() {
return {
    drawer:false,
    compomentLayout:{
        id:"",
        ext1:'1', // 显示行数
        ext2:'5', // 每行显示个数
    },
    query:{
       lineCount:"oneLine",
       lineSize:"5",
       navItems:[],
    },
    navItems:[],
    
    headersProgram: {
        token: getToken(),
        Authorization: "Basic YWRtaW5fdWk6YWRtaW5fdWlfc2VjcmV0"
    },
    insertProgram: {
        folderId: 0
    },
    selectList:[],
    pageComponentId:0,
    uploadIndex:0
};
},
props: {
    navTapCompomentObj:{
        type:Object,
        required: true
    },
    pagePlateId:{
        type:String,
        required:true
    }
},
components:{
    ProductItemTable,
    ProductTypeItemTable,
    StoreListTable,
    SpecialTable
},
watch:{
    navItems: {
        handler: function (val, oldVal) {
            console.info(val)
        },
        deep: true
    }
},
methods: {
    checkPermission,
    showDrawerFun(){
        this.drawer=true
        this.getSelectList()
       
    },
    addItemBtnFun(){
        if(this.navItems.length<10){
            this.navItems.push({
                id: "",
                pagePlateId:this.pagePlateId,
                showStatus:"Y",
                pageComponentId:this.pageComponentId,
                image: "",
                linkUrl: "",
                title:"",
                searchType: "",
                linkParam:[{key:"",val:""}],
                sortValue:this.navItems.length
            })
        }else {
            this.$message.error('最多设置10个')
        }
    },
    changeLinkFun(url, item){
        var selectItem = this.selectList.filter(itemInfo=>{
          return url == itemInfo.describe
        })
        
        if(selectItem!=null&&selectItem.length>0){
          item['searchType']=selectItem[0].code
        }else{
          item['searchType']=""
        }
        var propList=[];
        var linkPareObject =  param2Obj(url);
        Object.keys(linkPareObject).map(key => {
            var val=linkPareObject[key]
            propList.push({key,val:""}) 
        })
         
        if(propList.length>0){
            item.linkParam=propList
        }else{
             item.linkParam=null
        }
       
    },
    removeItem(index){
        this.navItems.splice(index,1)
    },
    change (e) {
      this.$forceUpdate()
    },
    async submitFun(){
        // 修改组件布局
        await pageComponentUpdate(this.compomentLayout);
        this.navItems.forEach((item,index)=>{
            item.sortValue=index;
        })
        this.query.navItems=[...[],...this.navItems]
        this.query.navItems.forEach(item=>{
            var itemValJson={}
            if(item.linkParam instanceof Array ){
                item.linkParam.forEach(keyVal=>{
                    itemValJson={...itemValJson,...{[keyVal.key]:keyVal.val}}
                })
                item.linkParam=JSON.stringify(itemValJson)
            }
        })
        // 修改组件广告内容
        await deleteByPageComponentId(this.pageComponentId)
        var data = await pageADListAdd(this.query.navItems)
        if (data.code == 0) {
            this.drawer=false
            this.initFun();
        } else {
            this.$message.error("提交图文导航失败！");
        }
    },
     
    
    
    initCompomentLayout(){
        this.compomentLayout=this.navTapCompomentObj;
    },
    
    async initFun() {
      var { data } = await pageComponentList({
        current: 1,
        map: {},
        model: {
          componentCode: "main"
        },
        order: "descending",
        size: 10,
        sort: "id"
      });
      this.pageComponentId = data.records[0].id;
      let adList = await pageADList({
        current: 1,
        map: {},
        model: {
          pageComponentId: this.pageComponentId
        },
        order: "descending",
        size: 10,
        sort: "id"
      });
      this.query.navItems = adList.data.records;
      
      this.query.navItems.forEach(item=>{
        var propList=[]
        var linkPareObject =  param2Obj(item.linkUrl);
        Object.keys(linkPareObject).map(key => {
            var val=linkPareObject[key]
            propList.push({key,val}) 
        })
        if(item.linkParam!=undefined &&item.linkParam!=""&&item.linkParam!=null){
            propList=[]
            var  linkParamObj=JSON.parse(item.linkParam);
            console.info(linkParamObj)
            Object.keys(linkParamObj).map(key => {
                var val=linkParamObj[key]
                propList.push({key,val}) 
            })
        }
        item.linkParam=propList
      })  
    },
    //  上传功能
    async getSelectList() {
      this.list = [];
      this.isLoading = true;
      let { data } = await query(
        {
          dictionaryId:"40083396470731372",
          // code:"WXCHARTLOCATION"
        }
      );
      this.selectList = data;
      //  this.navItems=[...[],...this.query.navItems]
      this.query.navItems.forEach(item=>{
         var selectItem = this.selectList.filter(itemInfo=>{
          return item.linkUrl == itemInfo.describe
        })
        if(selectItem!=null&&selectItem.length>0){
          item['searchType']=selectItem[0].code
        }else{
          item['searchType']=""
        }
      })

      this.navItems =JSON.parse(JSON.stringify( this.query.navItems));
    },

    uploadIndexItem:function(index){
      this.uploadIndex=index
    },
    beforeUpload(file) {
      let fileTypeList=["image/png", "image/pjpeg", "image/jpeg", "image/bmp"]
      const isJPG = fileTypeList.indexOf(file.type) >-1;
      const isLt2M = file.size / 1024 / 1024 < 5;

      if (!isJPG) {
        this.$message.error('上传图片格式错误!');
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!');
      }
      return isJPG && isLt2M;
    },
    uploadSuccess(res, file) {
      this.navItems[this.uploadIndex].image=res.data.url;
    },

  },
  mounted() {
    this.initCompomentLayout()  
    this.initFun();
  },
beforeDestroy() {}
};
</script>
<style lang="less" scoped>
.items{
    border:1px  dashed  red;
    margin-bottom: 12px;
    cursor: pointer;
} 
.navItems{
    display: flex;
    flex-wrap: wrap;
    margin: 0 auto;margin-top: 15px; width: 345px ;
}    
.navItems .navItem{
    width:20%;
    margin-bottom: 15px;
}
.navItems .icon{
    display: block;
    border-radius: 50%;
    margin: 0 auto;
    width: 40px; height:40px;
}
.navItems span{font-size: 12px; display: block; text-align: center; margin-top: 5px; }



.top{
    border-bottom: 1px solid #efefef;
    height:60px;
    padding: 0 15px;
}
.tipBox{
    margin-top: 16px;
    padding: 0 15px;
    width: 100%;
    p.title{
        font-size: 14px;
        height: 19px;
        font-size: 14px;
        font-weight: 400;
        margin:0;
    }
    p.tip{
        font-size: 14px;
        margin:0;
        height: 40px;
        font-family:  -400;
        font-weight: 400;
        color: #aaaaaa;
        line-height: 20px;

    }
}
.form{
     margin:0 auto;
     width:100%;
     padding:10px;
     position: relative;
    .formItem{margin-bottom: 5px;}
    .closeI{  
        opacity: 0;
        position: absolute;
        right: -18px;
        top: -25px;
        font-size: 20px;
        cursor: pointer;
    }
    .fromBox{padding-top:10px; max-height: calc(100vh - 320px); overflow-y: auto;}
    .setItemBox {border: 1px solid #efefef; padding:18px 10px; width:500px;}
    .setItemBox:hover .closeI{  
    opacity: 1;
}
}


.addbtn{
    cursor: pointer;
    margin:0 auto;
    text-align: center;
    margin-top: 16px;
    width:325px;
    height: 40px;
    line-height: 40px;
    background: #ffffff;
    border: 1px solid #409eff;
    font-size: 14px;
    font-weight: 400;
    color: #409eff;
}



.avatar-uploader{height:80px; width:80px;border: 1px solid #efefef; }
.avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 80px;
    height: 80px;
    line-height: 80px;
    text-align: center;
  }
  .avatar {
    width: 80px;
    height: 80px;
    display: block;
  }
  .inputItem{margin-bottom:12px;}
</style>