<template>
  <div>
    <div class="cards" v-if="canVisibleCards">
      <div class="item" v-if="cards.obnormalOrder">
        <p>
          <span>异常订单</span>
          <el-tooltip effect="dark" content="部分订单缺失客户ERP编码/商品ERP编码，请及时处理，否则影响下推至ERP" placement="top-start">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </p>
        <div class="flex items-center">
          <div class="flex-1 weight-600 text-16">{{ cards.obnormalOrder }}</div>
          <el-button @click="onFilterCard('obnormalOrder')" size="mini" type="danger">{{ model.obnormalOrder === 'Y' ?
              '取消' : '筛选'
          }}</el-button>
        </div>
      </div>
      <div class="item" v-if="cards.undeliveredOrder">
        <p>
          <span>超48小时未发货</span>
          <el-tooltip effect="dark" content="部分订单超48小时未发货，请尽快发货" placement="top-start">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </p>
        <div class="flex items-center">
          <div class="flex-1 weight-600 text-16">{{ cards.undeliveredOrder }}</div>
          <el-button @click="onFilterCard('undeliveredOrder')" size="mini" type="warning">{{ model.undeliveredOrder ===
              'Y' ? '取消' : '筛选'
          }}</el-button>
        </div>
      </div>
      <div class="item" v-if="cards.unpaidOrder">
        <p>
          <span>超30天未支付订单</span>
          <el-tooltip effect="dark" content="部分订单超30天未支付，请尽快联系客户支付或对收款单确收" placement="top-start">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </p>
        <div class="flex items-center">
          <div class="flex-1 weight-600 text-16">{{ cards.unpaidOrder }}</div>
          <el-button @click="onFilterCard('unpaidOrder')" size="mini" type="primary">{{ model.unpaidOrder === 'Y' ? '取消'
              : '筛选'
          }}</el-button>
        </div>
      </div>
      <div class="item" v-if="cards.pendingRefundOrder">
        <p>
          <span>待处理退款单</span>
          <el-tooltip effect="dark" content="部分订单发生退款，请及时处理" placement="top-start">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </p>
        <div class="flex items-center">
          <div class="flex-1 weight-600 text-16">{{ cards.pendingRefundOrder }}</div>
          <el-button @click="toRefundOrder" size="mini" type="primary">查看</el-button>
        </div>
      </div>
      <div class="item" v-if="cards.pendingReturnOrder">
        <p>
          <span>待处理退货单</span>
          <el-tooltip effect="dark" content="部分订单发生退货，请及时处理" placement="top-start">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </p>
        <div class="flex items-center">
          <div class="flex-1 weight-600 text-16">{{ cards.pendingReturnOrder }}</div>
          <el-button @click="toReturnOrder" size="mini" type="primary">查看</el-button>
        </div>
      </div>
    </div>
    <Search :model="model" :fields="searchFields" omit @search="searchLoad" @reset="reload" :commerceModel="commerceModel" />
    <div class="tab_bg">
      <div class="varietiesBan-list-container">
        <div class="varietiesBan-list-tabs-wrapper">
          <scroll-tabs @change="handleTabChange" class="tabs" :current="model.orderStatus" :tabs="tabs" />
        </div>
      </div>
      <div class="operations">
        <el-button @click="refresh">刷新</el-button>
        <el-button v-if="['WAIT_PAY', 'PENDING'].includes(model.orderStatus)" @click="onCancelOrders">取消订单
        </el-button>
        <paged-export controllable text-type="primary" :max-usable="total" :max="5000"
          :before-close="(params) => exportOrderList(params)" text="导出订单列表" />
        <order-details-paged-export controllable :model="model" text-type="primary" :max="5000" :max-usable="total"
          :before-close="(params) => exportOrderList(params, true)" text="导出订单明细">
        </order-details-paged-export>
      </div>
      <p style="padding: 14px 0;text-align: right;margin-bottom: 3px;font-size: 16px;color: #828591;font-weight: 600;">
        <span>订单总金额<span>（含取消）</span>：￥{{ statistics.allTotalMoney }}</span>，
        <span>订单总金额<span v-if="model.orderStatus !== 'CANCEL'">（不含取消）</span>：￥{{ statistics.totalMoney }}</span>，
        <span>实收总金额：￥{{ statistics.totalRealMoney }}（优惠金额：￥{{ statistics.discountMoneyReal }}，运费金额：￥{{ statistics.freightMoneyReal }}）</span>，
        <span>应收总金额：￥{{ statistics.shouldPayMoney }}（优惠金额：￥{{ statistics.discountMoneyShould }}，运费金额：￥{{ statistics.freightMoneyShould }}）</span>，
        <span>退款金额：￥{{ statistics.refundMoney }}</span>
      </p>
      <table-pager class="table" :show-hide="false" ref="todoTable" :options="tableTitle" :operation-width="100"
        :remote-method="load" :data.sync="tableData" :selection="true" @selection-change="handleSelectionChange"
        @selection-all="onAllSelect">
        <el-table-column slot="orderNo" label="订单编号">
          <template slot-scope="scope">
            {{ scope.row.saleMerchantName }}
            <div class="ellipsis text-red" v-if="scope.row.existProErp">缺失商品编码</div>
            <p>电商编码：{{ scope.row.orderNo }}</p>
            <p>ERP编码：{{ scope.row.outOrderNo }}</p>
            <p>订单来源：<span :class="{'text-blue': scope.row.orderChannel.includes('平台端'), 'text-green': scope.row.orderChannel.includes('采购商'), 'text-orange': scope.row.orderChannel.includes('业务员')}">{{ scope.row.orderChannel }}</span></p>
            <p v-if="scope.row.orderCreateContractStatus">合同编码
              <span>
                {{ scope.row.orderCreateContractStatus && scope.row.orderCreateContractStatus.desc }}
              </span>
              <span v-if="scope.row.contractNumber"> ({{ scope.row.contractNumber }})</span>
            </p>
            <div class="tags">
              <div v-if="scope.row.whetherFirstOrder && scope.row.whetherFirstOrder.code === 'Y'" class="item">首单</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column slot="customerInfo" label="客户信息">
          <template slot-scope="scope">
            <p><span class="member" v-if="scope.row.managerTag === 'Y'">会员</span>{{ scope.row.purMerchantName }}</p>
            <p>
              ERP客户编码：<template v-if="scope.row.customerCode">{{ scope.row.customerCode }}</template>
            </p>
            <div class="tags">

              <div class="item" v-for="tag in scope.row.customerTagList" :key="tag">{{ tag }}</div>
            </div>
            <p>所属业务员：{{ scope.row.salesmanName }}</p>
            <p>业务员手机：{{ scope.row.salesmanPhone }}</p>
          </template>
        </el-table-column>
        <el-table-column slot="orderAmountInfo" label="订单金额" width="170">
          <template slot-scope="scope">
            <p>实付金额：<span class="text-orange">{{ scope.row.totalRealMoney }}</span></p>
            <p>订单金额：<span>{{ scope.row.totalMoney }}</span></p>
            <p>商品金额：<span>{{ scope.row.productTotalMoney }}</span></p>
            <p>商品优惠：<span>{{ scope.row.discount }}</span></p>
            <p>运费：<span>{{ scope.row.freight }}</span></p>
            <p>退款金额：<span> {{ scope.row.refundTotalMoney }}</span></p>
          </template>
        </el-table-column>

        <!-- width="260" -->
        <el-table-column slot="orderHandling" label="订单处理">
          <template slot-scope="scope">
            <p>订单状态：<span>{{ scope.row.orderStatus ? scope.row.orderStatus.desc : '' }}</span></p>
            <p>下单时间：<span>{{ scope.row.orderCreateTime }}</span></p>
            <p v-if="scope.row.deliveryNumber">包裹数：<el-button type="text" @click="onPackages(scope.row)" size="mini">{{
                scope.row.deliveryNumber
            }}
              </el-button>
            </p>
            <p>最新处理时间：<span>{{ scope.row.updateTime }}</span></p>
            <p>订单备注：<span :class="{ 'text-red': !!scope.row.remark }">{{ scope.row.remark ? scope.row.remark : '无'
            }}</span>
            </p>
          </template>
        </el-table-column>
        <!-- width="260" -->
        <el-table-column slot="paymentInfo" label="支付信息">
          <template slot-scope="scope">
            <p>支付状态：<span>{{ scope.row.payStatus ? scope.row.payStatus.desc : '' }}</span></p>
            <p>支付方式：<span>{{ scope.row.payWay ? scope.row.payWay.desc : '' }}</span></p>
            <p>支付时间：<span>{{ scope.row.payTime }}</span></p>
          </template>
        </el-table-column>
        <!-- width="240" -->
        <el-table-column slot="receivingInfo" label="收货信息">
          <template slot-scope="scope">
            <p>{{ scope.row.receiverName }}</p>
            <p>{{ scope.row.receiverPhone }}</p>
            <p>{{ scope.row.fullAddress }}</p>
          </template>
        </el-table-column>
        <div slot-scope="props" v-if="checkPermission(['admin', 'admin-platform-order:detail', 'admin-saas-order:detail'])">
          <el-button type="text" @click="$router.push({ path: '/tradingCenter/orderList/detail', query: { id: props.row.id } })">查看详情</el-button>
        </div>
      </table-pager>
    </div>
  </div>
</template>

<script>
const TableColumnList = [
  { label: "订单编号", name: "orderNo", prop: "orderNo", width: '190', slot: true },
  { label: "客户信息", name: "customerInfo", prop: "customerInfo", width: '130', slot: true },
  { label: "订单金额", name: "orderAmountInfo", prop: "orderAmountInfo", width: '130', slot: true },
  { label: "订单处理", name: "orderHandling", prop: 'orderHandling', width: '220', slot: true },
  { label: "支付信息", name: "paymentInfo", prop: 'paymentInfo', width: '220', slot: true },
  { label: "收货信息", name: "receivingInfo", prop: 'receivingInfo', width: '220', slot: true },
].map((v, i) => {
  v.key = i;
  return v;
});
import { exoprtToExcel } from "@/utils/commons";
import { orderList, exportOrderListReq, exportOrderDetailsReq, orderStatistics, statisticsMoney } from '@/api/trade'
import checkPermission from '@/utils/permission';
import ExportPage from "./exportPage";
import ScrollTabs from "@/components/ScrollTabs/ScrollTabs.vue"
import Search from "./Search.vue";
import PagedExport from "@/components/PagedExport/index.vue"
import OrderDetailsPagedExport from './OrderDetailsPagedExport.vue';
import dayjs from "dayjs";

function getThreeMonths() {
  return [dayjs(new Date()).subtract(3, 'months').format('YYYY-MM-DD')+ ' 00:00:00', dayjs(new Date()).format('YYYY-MM-DD')+' 23:59:59']
}

export default {
  name: 'tradingCenterOrderList',
  props: {
    commerceModel: {
      type: String,
      required: true
    }
  },
  components: {
    ExportPage,
    ScrollTabs,
    Search,
    PagedExport,
    OrderDetailsPagedExport
  },
  data() {
    return {
      statistics: {
        allTotalMoney: 0,
        totalMoney: 0,
        totalRealMoney: 0,
        discountMoney: 0,
        freightMoney: 0,
      },
      searchModel: {
        during: [],
        orderNo: '',
        customerCodeOrName: '',
        productNameOrErpCode: '',
        salesmanNameOrPhone: '',
        whetherFirstOrder: ''
      },
      searchFields: [
        {
          type: 'DateRange',
          prop: 'during'
        },
        {
          prop: 'orderNo',
          placeholder: '订单编号',
        },
        {
          prop: 'customerCodeOrName',
          placeholder: '客户名称/ERP客户编码',
        },
        {
          prop: 'productNameOrErpCode',
          placeholder: '商品名称/商品ERP编码',
          omit: true,
        },
        {
          prop: 'salesmanNameOrPhone',
          placeholder: '业务员姓名/手机号',
          omit: true,
        },
        {
          prop: 'whetherFirstOrder',
          placeholder: '是否首单',
          type: 'Select',
          omit: true,
          options: [
            {
              value: 'Y',
              text: '是'
            },
            {
              value: 'N',
              text: '否'
            }
          ]
        },
        {
          prop: 'userNameOrPhone',
          placeholder: '下单人姓名/手机号',
          omit: true,
        },
        {
          prop: 'erpMarker',
          placeholder: '线上/线下',
          type: 'Select',
          omit: true,
          options: [
            { value: 'N', text: '线上' },
            { value: 'Y', text: '线下' }
          ]
        },
        {
          prop: 'saleMerchantName',
          placeholder: '请输入商家名称',
          omit: true,
        },
        {
          prop: 'orderCreateContractStatusList',
          placeholder: '合同状态',
          type: 'Select',
          omit: true,
          multiple: true,
          options: [
            { text: '待生成', value: 'PENDING_GENERATED' },
            { text: '生成中', value: 'BEING_GENERATED' },
            { text: '合同成功', value: 'CONTRACT_SUCCESSFUL' },
            { text: '合同失败', value: 'CONTRACT_FAILED' },
            { text: '合同作废', value: 'CONTRACT_CANCELED' },
          ]
        },
      ],
      canceOrderPopuplVisible: false,
      afterSalesPopupVisible: false,
      deliveryPopupVisible: false,
      auditPopuplVisible: false,
      expressPopuplVisible: false,
      currentOrder: {},
      currentOrders: [],
      cards: {
        obnormalOrder: 0,
        unpaidOrder: 0,
        undeliveredOrder: 0,
        pendingRefundOrder: 0,
        pendingReturnOrder: 0
      },

      isExpand: false,

      customerCodeDialogVisible: false, // 是否显示设置客户编码弹窗
      customerCodeForm: {
        id: '',
        customerCode: ''
      }, // ERP客户编码表单
      customerCodeFormRules: {
        customerCode: [{ required: true, message: '客户编码不能为空', trigger: 'blur' }]
      }, // ERP客户编码表单验证规则集合

      auditSettingsDialogVisible: false, // 是否显示审核设置弹窗
      auditSettingsForm: {
        auditStatus: ''
      }, // 审核设置表单
      auditSettingsFormRules: {
        auditStatus: { required: true, message: '请选审核类型', trigger: 'blur' }
      }, // 审核设置表单验证规则集合

      selects: [], // 当前选中列表项


      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      loading: '',
      search: '',
      controlType: '',
      currentTab: 0,
      tabs: [
        { text: '全部', value: '', count: 0, hide: !checkPermission(['admin', 'admin-platform-order:allView', 'admin-saas-order:allView']), countName: 'all' },
        { text: '待付款', value: 'WAIT_PAY', count: 0, hide: !checkPermission(['admin', 'admin-platform-order:pendingPayView', 'admin-saas-order:pendingPayView']), countName: 'waitPay' },
        { text: '待审核', value: 'PENDING', count: 0, hide: !checkPermission(['admin', 'admin-platform-order:pendingAuditView', 'admin-saas-order:pendingAuditView']), countName: 'pending' },
        { text: '正在开单', value: 'WAIT_BILL', count: 0, hide: !checkPermission(['admin', 'admin-platform-order:billingView', 'admin-saas-order:billingView']), countName: 'waitBill' },
        { text: '待发货', value: 'WAIT_DELIVERY', count: 0, hide: !checkPermission(['admin', 'admin-platform-order:pendingDeliveryView', 'admin-saas-order:pendingDeliveryView']), countName: 'waitDelivery' },
        { text: '发货中', value: 'PART_DELIVERY', count: 0, hide: !checkPermission(['admin', 'admin-platform-order:partDeliveryView', 'admin-saas-order:partDeliveryView']), countName: 'partDelivery' },
        { text: '已发货', value: 'HAD_DELIVERY', count: 0, hide: !checkPermission(['admin', 'admin-platform-order:hadDeliveryView', 'admin-saas-order:hadDeliveryView']), countName: 'hadDelivery' },
        { text: '已完成', value: 'SUCCESS', count: 0, hide: !checkPermission(['admin', 'admin-platform-order:finishedView', 'admin-saas-order:finishedView']), countName: 'finish' },
        { text: '待回款', value: 'BACK_PAY', count: 0, hide: !checkPermission(['admin', 'admin-platform-order:backPayView', 'admin-saas-order:backPayView']), countName: 'backPayOrder' },
        { text: '已取消', value: 'CANCEL', count: 0, hide: !checkPermission(['admin', 'admin-platform-order:cancelView', 'admin-saas-order:cancelView']), countName: 'cancel' },
      ],
      tableData: [],
      page: 1,
      pageSize: 10,
      totalPage: 0,
      total: 0,
      tableTitle: TableColumnList,
      model: {
        erpMarker: 'N',
        productNameOrErpCode: '',
        salesmanNameOrPhone: '',
        whetherFirstOrder: '',
        orderType: 100,
        customerCode: '',
        orderNo: '',
        purMerchantName: '',
        userName: '',
        customerCodeOrName: '',
        productErpCode: '',
        productName: '',
        salesmanName: '',
        mobile: '',
        during: getThreeMonths(),
        orderStatus: '',
        obnormalOrder: 'N',
        undeliveredOrder: 'N',
        unpaidOrder: 'N',
        pendingRefundOrder: 'N',
        clientName: '',
        orderCreateContractStatusList: []
      },
      products: [],
      ids: [],
      purMerchantName: [],
      purMerchantNameLoading: false,
      selecteds: [],
    }
  },
  computed: {
    canVisibleCards() {
      const KEYS = ['obnormalOrder', 'unpaidOrder', 'undeliveredOrder', 'pendingRefundOrder', 'pendingReturnOrder']
      return KEYS.some(key => {
        let num = Number(this.cards[key]) || 0
        return num > 0
      })
    }
  },
  methods: {
    getStatisticsMoney() {
      statisticsMoney(this.getModelData()).then(res => {
        this.statistics = res.data || {
          allTotalMoney: 0,
          totalMoney: 0,
          totalRealMoney: 0,
          discountMoney: 0,
          freightMoney: 0,
        }
      })
    },
    toRefundOrder() {
      this.$router.push({
        path: '/tradeCenter/refund/index',
        query: { index: 1 }
      });
    },
    toReturnOrder() {
      this.$router.push({
        path: '/tradeCenter/returnGood/index',
        query: { index: 1 }
      });
    },
    async getCount() {
      const { data } = await orderStatistics(this.getModelData())
      this.tabs.forEach(item => {
        item.count = data[item.countName]
      })
    },
    /** 批量取消订单 */
    async onCancelOrders() {
      if (this.selecteds.length < 1) {
        this.$message.warning('请先选择需要取消的订单');
        return
      }
      await this.$confirm(`确定取消这${this.selecteds.length}条订单吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      const loading = this.$loading({
        lock: true,
        text: '更新中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.4)'
      });
      try {
        const { code, msg } = await updateOrdersStatusToCancel({ orderIds: this.selecteds.map(v => v.id) });
        if (code !== 0) {
          throw new Error(msg)
        }
        this.refresh();
      } catch (error) {
        console.log('updateOrdersStatusToCancel', error)
      } finally {
        loading.close();
      }
    },
    onPackages(data) {
      this.currentOrder = data;
      this.expressPopuplVisible = true;
    },
    onFilterCard(key) {
      this.model[key] = this.model[key] === 'Y' ? 'N' : 'Y'
      this.handleRefresh({
        page: 1,
        pageSize: 10
      })
    },
    handleSelectionChange(val) {
      console.log('val--handleSelectionChange--->', val);
      this.selecteds = val;
    },
    // 导出订单列表/明细
    exportOrderList(params, isDetail = false) {
      const listParams = {
        current: this.page,
        map: {},
        model: {
          ...this.getModelData(),
          ...params,
        },
        order: "descending",
        size: this.pageSize,
      }
      delete listParams.model.during
      const exportOrderReq = isDetail ? exportOrderDetailsReq : exportOrderListReq;
      const fileName = isDetail ? '订单明细' : '订单列表'
      exportOrderReq(listParams).then(res => {
        exoprtToExcel(res.data, fileName)
      })
    },
    onAllSelect(val) {
      console.log('val---onAllSelect--->', val);
      this.selecteds = val;
    },
    checkPermission,
    statusFormatter(row) {
      if (row.orderStatus.code === 'WAIT_PROCESS') {
        return row.orderAuditStatus.desc
      } else {
        return row.orderStatus.desc
      }
    },
    // 获取提交数据，列表，导出，统计用
    getModelData() {
      return {
        ...this.model,
        purMerchantName: this.model.purMerchantName === '' ? null : this.model.purMerchantName,
        orderNo: this.model.orderNo,
        userName: this.model.userName === '' ? null : this.model.userName,
        customerCodeOrName: this.model.customerCodeOrName == '' ? null : this.model.customerCodeOrName,
        salesmanName: this.model.salesmanName == '' ? null : this.model.salesmanName,
        mobile: this.model.mobile === '' ? null : this.model.mobile,
        customerCode: this.model.customerCode,
        startTime: this.model.during[0],
        endTime: this.model.during[1],
        productName: this.model.productName,
        productErpCode: this.model.productErpCode,
        orderChannel: this.model.orderChannel,
        orderStatus: this.model.orderStatus,
        orderType: 100,
        commerceModel: this.commerceModel,
      }
    },
    async load(params) {
      let listQuery = {
        model: this.getModelData()
      }
      Object.assign(listQuery, params)
      this.loading = true

      const data = await orderList(listQuery)
      this.total = data.data?.total;
      this.getCount()
      this.getStatisticsMoney()
      return data || { data: {} }
    },
    refresh() {
      this.handleRefresh({
        page: 1,
        pageSize: 10
      })
    },
    reload() {
      this.purMerchantName = []
      this.currentTab = 0
      this.model = {
        purMerchantName: '',
        orderNo: '',
        salesmanName: '',
        salesmanMobile: '',
        orderStatus: this.model.orderStatus,
        during: getThreeMonths(),
        obnormalOrder: 'N',
        undeliveredOrder: 'N',
        unpaidOrder: 'N',
        pendingRefundOrder: 'N',
        clientName: ''
      }
      this.handleRefresh({
        page: 1,
        pageSize: 10
      })
    },
    handleTabChange(value) {
      this.model.orderStatus = value !== 'ALL' ? value : '';
      this.handleRefresh({
        page: 1,
        pageSize: 10
      })
    },
    searchLoad() {
      this.handleRefresh({
        page: 1,
        pageSize: 10
      })
    },
    getType(val) {
      this.model.orderChannel = val
    },
    handleRefresh(pageParams) {
      this.$refs.todoTable.doRefresh(pageParams)
      this.getStatisticsMoney()
    },
  },
}
</script>

<style lang="scss" scoped>
.cards {
  margin-bottom: 8px;

  .item {
    display: inline-block;
    width: 186px;
    padding: 16px 20px;
    margin-bottom: 8px;
    background-color: #fff;

    &:not(:last-child) {
      margin-right: 8px;
    }

    p {

      margin-bottom: 16px;
      font-size: 14px;
      color: #4E5766;
    }
  }
}

.member {
  display: inline-block;
  padding: 2px 6px;
  margin-right: 6px;
  line-height: 1;
  color: #FFF3B2;
  font-size: 12px;
  background-color: #303378;
  border-radius: 2px;
}

.tags {

  .item {
    padding: 2px 8px;
    display: inline-block;
    margin-bottom: 4px;
    border-radius: 2px;
    background: #ECEFFB;
    border: 1px solid #4062D8;
    font-size: 12px;
    line-height: 16px;
    color: #4062D8;

    &:not(:last-child) {
      margin-right: 4px;
    }
  }
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.flex-1 {
  flex: 1;
}

.mb-16 {
  margin-bottom: 16px;
}

.flex-none {
  flex: 0 0 auto;
}

.weight-600 {
  font-weight: 600;
}

.text-16 {
  font-size: 16px;
}

.text-left {
  text-align: left;
}

.text-red {
  color: #FF4245;
}

.text-orange {
  color: #FF5C00;
}
.text-green {
  color: #00BA81;
}
.text-blue {
  color: #4062D8;
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.varietiesBan-list-tabs-wrapper {
  display: flex;
  align-items: center;
  // border-bottom: 1px solid #ccc;

  .tabs {
    flex: 1;
    // margin-right: 16px;
  }
}

.table {
  ::v-deep {
    .el-table::before {
      background-color: #dfe6ec;
    }
  }
}

.operations {
  position: static;
  flex: 0 0 auto;
  // padding-bottom: 8px;
  padding: 8px 0 0;
  text-align: right;
}
</style>
