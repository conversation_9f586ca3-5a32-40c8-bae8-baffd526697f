<template>
    <div :class="[$style.container, $style['inline-block'], 'el-button']">
        <!-- 控制区域插槽（默认为按钮插槽） start -->
        <slot name="text" v-if="controllable">
            <el-button :type="textType" :disabled="disabled" @click="onControlClick" v-text="text"></el-button>
        </slot>
        <!-- 控制区域插槽（默认为按钮插槽）end -->

        <!-- 弹窗区域  start -->
        <el-dialog append-to-body :visible.sync="isVisible" :title="title" width="730px">
            <el-form label-width="100px">
                <el-form-item label="输入页数">
                    <el-row>
                        <!-- 开始页输入 start -->
                        <el-col :span="4">
                            <el-input-number v-model="startPage" @input.native="onInputChange($event, 'startPage')" :class="[$style.full]" :max="maxUsablePage"
                                :controls="false">
                            </el-input-number>
                        </el-col>
                        <!-- 开始页输入 end -->

                        <el-col :class="[$style['text-center']]" :span="1">到</el-col>

                        <!-- 结束页输入 start -->
                        <el-col :span="4">
                            <el-input-number v-model="endPage" @input.native="onInputChange($event, 'endPage')" :class="[$style.full]" :max="maxEndPage"
                                :controls="false">
                            </el-input-number>
                        </el-col>
                        <!-- 结束页输入 end -->

                        <!-- 提示显示 start -->
                        <el-col :span="15">
                            <div :class="[$style['text-right']]">
                                已选导出数据：<span :class="[$style['text-red']]">{{ total }}</span> 个(单次最多{{ max }}条)
                            </div>
                        </el-col>
                        <!-- 提示显示 end -->
                    </el-row>
                </el-form-item>

                <slot></slot>

            </el-form>
            <div slot="footer" :class="[$style['dialog-footer']]">
                <el-button @click="onCancel">取 消</el-button>
                <el-button type="primary" @click="onConfirm">确 定</el-button>
            </div>
        </el-dialog>
        <!-- 弹窗区域  end -->
    </div>
</template>

<script>
export default {
    props: {
        // 启用控制区域插槽
        controllable: {
            type: Boolean,
            default: false
        },

        // 显示弹窗 -> 只有在不启用控制区域插槽的时候生效
        visible: {
            type: Boolean,
            default: false
        },

        // 是否禁用按钮
        disabled: {
            type: Boolean,
            default: false
        },
        // 按钮名称
        text: {
            type: String,
            default: "导出"
        },
        textType: {
            type: String
        },
        // 最大导出数据量
        max: {
            type: Number,
            default: 5000
        },
        // 最大可导出数据量
        maxUsable: {
            type: Number,
            default: 0
        },
        // 每页条数
        size: {
            type: Number,
            default: 10
        },
        // 弹窗的title
        title: {
            type: String,
            default: '导出数据'
        },

        // 启用该函数，返回值用来判断是否关闭弹窗
        beforeClose: Function
    },
    data() {
        return {
            isVisible: false, // 弹窗显示
            startPage: 1, // 开始页
            endPage: 1 // // 结束页
        }
    },
    computed: {


        /**
         *  自动计算已选商品数量
         *  开始页大于结束页，则已选商品数量为0
         */
        total() {
            let count = 0;
            let maxUsable = this.maxUsable - ((this.startPage - 1) * this.size) // 计算出当前开始页，最大可导出数量
            // 两数都不等于0，结束页是否大于等于开始页
            if (this.endPage && this.startPage && this.endPage >= this.startPage) {
                count = this.endPage - this.startPage + 1
            }
            return Math.min(maxUsable, count * this.size);
        },

        /**
         * 计算最大可导出页数
         */
        maxUsablePage() {
            return Math.ceil(this.maxUsable / this.size)
        },

        // 最大结束页码
        maxEndPage() {
            let maxPage = Math.ceil(this.max / this.size);
            let endPage = Math.min(this.startPage + maxPage, this.maxUsablePage)
            // 总条数
            let max = (endPage - this.startPage + 1) * this.size
            if (max > this.max) {
                endPage -= Math.ceil((max % this.max) / this.size)
            }
            if (endPage < this.endPage) {
                this.endPage = endPage;
            }
            return endPage;
        }
    },
    watch: {
        visible: {
            immediate: true,
            handler(val) {
                if (!this.controllable) {
                    this.isVisible = val;
                }
            }
        }
    },
    methods: {

        // 验证输入数据，并给出提示
        validate() {
            let result = true;
            let message = '';
            if (this.maxUsable <= 0) {
                message = '没有可导出的数据';
                result = false;
            } else if (this.total <= 0) {
                message = '请输入正确的页数';
                result = false;
            }

            if (!result && message) this.$message.warning(message)

            return result;
        },
        onInputChange(e, key) {
            this[key] = e.target.value
        },

        onControlClick() {
            this._toggle(true)
        },
        onCancel() {
            this._toggle(false)

        },

        _toggle(isVisible) {
            if (this.controllable) {
                this.isVisible = isVisible;
            } else {
                this.$emit('update:visible', isVisible)
            }
        },
        async onConfirm() {
            if (!this.validate()) return;

            let isClosable = true

            // 需要返回的数据
            const data = { startPage: this.startPage, endPage: this.endPage };
            if (typeof this.beforeClose === 'function' || this.beforeClose instanceof Promise) {
                isClosable = await this.beforeClose(data)
            }
            isClosable && this._toggle(false)
            this.$emit('ok', data)
        }
    }
}
</script>

<style lang="scss" module>
.container {
    // margin-left: 10px;
    // margin-right: 10px;
    border: none;
    padding: 0;
}

.inline-block {
    display: inline-block;
}

.full {
    width: 100%;
}

.pr-10 {
    padding-right: 10px;
}

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.text-red {
    color: red;
}
</style>