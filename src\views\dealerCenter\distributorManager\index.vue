<template>
  <div class="archivesPageContent">
    <im-search-pad :model="listQuery" @reset="resetForm('searchForm')" @search="onSearchSubmitFun">
      <im-search-pad-item prop="name">
        <el-input clearable v-model="listQuery.model.name" placeholder="请输入经销商名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="areaArr">
        <el-cascader
          v-model="areaArr"
          style="width: 200px"
          placeholder="请选择所在区域"
          :props="{ value: 'id', label: 'label' }"
          @change="cityChange"
          :options="areasTree"
          clearable
        />
      </im-search-pad-item>
      <im-search-pad-item prop="publishStatus">
        <el-select clearable v-model="listQuery.model.publishStatus" placeholder="请选择经销商状态">
          <el-option label="已启用" value="Y"></el-option>
          <el-option label="已冻结" value="N"></el-option>
        </el-select>
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout v-model="listQuery.model.approvalStatus.code" :tabs="approvalList">
        <template slot="button">
          <div>
            <el-button :disabled="multipleSelectionId.length == 0" @click="updateSalePublishStatus('Y')">批量启用</el-button>
            <el-button :disabled="multipleSelectionId.length == 0" @click="updateSalePublishStatus('N')"
              >批量冻结</el-button
            >
            <el-button @click="refresh">刷新</el-button>
            <el-button type="primary" @click="showEditPurchaserDialog('')">+新增经销商</el-button>
          </div>
        </template>
      </tabs-layout>
      <el-table
        ref="table"
        v-if="list"
        @select="onSelect"
        @select-all="onAllSelect"
        @selection-change="selectTableItemFun"
        v-loading="listLoading"
        :data="list"
        row-key="id"
        border
        fit
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column align="center" width="65" fixed :render-header="renderHeader">
          <template slot-scope="scope">
            <span>{{ scope.$index + 1 }} </span>
          </template>
        </el-table-column>
        <el-table-column type="selection" align="center" fixed />
        <el-table-column v-for="(item, index) in tableTitle" :key="index" :label="item.label" align="left">
          <template slot-scope="{ row }">
            <span v-if="item.name == 'publishStatus'" :style="row[item.name].code == 'N' ? 'color:#ff0066' : ''">
              {{ row[item.name].code == 'Y' ? '已启用' : '已冻结' }}
            </span>
            <span v-else-if="item.name == 'loginAccount'"> {{ row['loginAccount'] }} / {{ row['userMobile'] }} </span>
            <span v-else>{{ row[item.name] }}</span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" align="center" label="操作" width="100" class="itemAction">
          <template slot-scope="{ row }">
            <el-button @click="showEditPurchaserDialog(row)" type="text">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-if="total > 0"
        :pageSizes="[2, 10, 20, 50]"
        :total="total"
        :page.sync="listQuery.current"
        :limit.sync="listQuery.size"
        @pagination="getlist"
      />
    </div>
    <EditPurchaserDialog ref="editPurchaserDialogRef" @setSuccess="getlist" />
  </div>
</template>

<script>
import checkPermission from '@/utils/permission'
import Pagination from '@/components/Pagination'
import { updateSalePublishStatus, areas } from '@/api/businessCenter/businessList'
import tableInfo from './tableInfo'
import TabsLayout from '@/components/TabsLayout'
import EditPurchaserDialog from './components/editPurchaserDialog'
import { getResellerList } from '@/api/dealerManagement'

export default {
  name: 'distributorManager',
  components: {
    Pagination,
    TabsLayout,
    EditPurchaserDialog
  },
  data() {
    return {
      list: [],
      total: 0,
      multipleSelection: [],
      multipleSelectionId: [],
      listLoading: false,
      tableTitle: [],
      areasTree: [],
      areaArr: [],
      listQuery: {
        current: 1,
        size: 10,
        model: {
          name: '',
          publishStatus: '',
          cityId: '',
          countyId: '',
          provinceId: 0,
          approvalStatus: { code: 'all' }
        }
      },
      row: {},
      submitReload: '',
      tableSelectTitle: [1],
      showSelectTitle: false
    }
  },
  watch: {
    submitReload: function (newVal, oldVal) {
      if (newVal) {
        this.getlist()
        this.submitReload = false
      }
    }
  },
  computed: {
    approvalList() {
      return [
        {
          name: '经销商列表',
          value: 'all'
        }
      ]
    }
  },
  methods: {
    checkPermission,
    getareas() {
      areas().then((res) => {
        if (res.code !== 0) return
        this.areasTree = res.data.filter((item => item.id !== '0'))
      })
    },
    cityChange(e) {
      if (Array.isArray(e) && e[0] && e[1] && e[2]) {
        this.listQuery.model.provinceId = e[0]
        this.listQuery.model.cityId = e[1]
        this.listQuery.model.countyId = e[2]
      }
    },
    refresh() {
      this.listQuery = {
        ...this.listQuery,
        current: 1,
        size: 10
      }
      this.initTbaleTitle()
      this.getlist()
    },
    updataResolve(data) {
      if (data.code === 0) {
        this.getlist()
      }
    },
    checkSelect() {
      if (this.multipleSelection.length < 1) {
        this.$alert('请至少选择一个商家')
        return true
      }
    },
    // 批量启用商家
    async updateSalePublishStatus(param) {
      this.$confirm('此操作将批量操作商家，是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        if (this.checkSelect()) return
        let data = await updateSalePublishStatus({
          ids: this.multipleSelectionId,
          publishStatus: param
        })
        this.updataResolve(data)
      })
    },
    async getlist() {
      this.listLoading = true
      const { data } = await getResellerList(this.listQuery)
      this.list = data.records
      this.total = data.total
      this.listLoading = false
    },
    onSearchSubmitFun() {
      this.listQuery.current = 1
      this.getlist()
    },
    resetForm(search) {
      this.areaArr = []
      this.listQuery = {
        current: 1,
        size: 10,
        model: {
          approvalStatus: this.listQuery.model.approvalStatus
        }
      }
      this.getlist()
    },
    selectTableItemFun: function (val) {
      let arr = []
      val.forEach((item) => {
        arr.push(item.id)
      })
      this.multipleSelection = val
      this.multipleSelectionId = arr
    },
    onAllSelect(selection) {
      this.onSelect(selection)
    },
    onSelect: function (val) {
      this.multipleSelection = val
    },
    initTbaleTitle() {
      let arr = []
      tableInfo.forEach((item) => {
        if (item.name != 'id') {
          arr.push(item)
        }
      })
      this.tableTitle = arr
    },
    renderHeader(h, { column }) {
      var titles = tableInfo[this.listQuery.model.approvalStatus.code]
      var titlesName = ['显示字段项', '隐藏字段项']
      return (
        <div style="position:relative">
          <div onClick={this.showHeaer}>
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="640px"
            center
            append-to-body={true}
          >
            <el-transfer
              vModel={this.tableSelectTitle}
              data={titles}
              titles={titlesName}
              onChange={this.setleftTitleFun}
            ></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button onClick={this.closeHeaer}>取消</el-button>
              <el-button type="primary" onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      )
    },
    setleftTitleFun(val) {
      this.tableSelectTitle = val
    },
    showHeaer: function () {
      this.showSelectTitle = true
    },
    closeHeaer: function () {
      this.showSelectTitle = false
      this.tableSelectTitle = []
    },
    setHeaer: function () {
      var titles = tableInfo[this.listQuery.model.approvalStatus.code]
      var listinfo = titles.filter((element, index, self) => {
        return !this.tableSelectTitle.includes(element.key)
      })
      this.tableTitle = listinfo
      this.showSelectTitle = !this.showSelectTitle
    },
    // 显示编辑采购商dialog
    showEditPurchaserDialog(row) {
      this.$refs.editPurchaserDialogRef.show(row, this.areasTree)
    }
  },
  mounted() {
    this.initTbaleTitle()
    this.getareas()
    this.getlist()
  }
}
</script>

<style lang="scss" scoped>
.archivesPageContent {
  padding: 0;
  .temp_searchBox {
    height: 64px;
    overflow: hidden;
    margin-bottom: 0;
  }
  .form-inline {
    height: 60px;
    overflow: hidden;
  }
  .title {
    border-bottom: 2px solid #ebecee;
    margin-bottom: 16px;
    span {
      margin-bottom: -2px;
      padding: 0 15px;
      height: 40px;
      line-height: 30px;
      display: block;
      background: rgba(255, 255, 255, 0);
      border-bottom: 2px solid rgb(64, 158, 255);
      font-size: 16px;
      font-family: 'PingFangSC-Regular', 'PingFang SC', 'PingFangSC-Regular', 'PingFang SC'-400;
      font-weight: 400;
      color: rgb(64, 158, 255);
    }
  }

  .formItem {
    width: 586px;
  }
  .line {
    color: #dfe6ec;
    margin: 0 6px;
  }
  .typeTabs {
    height: 40px;
    margin-bottom: -2px;
    margin-left: 30px;
  }
}
</style>
