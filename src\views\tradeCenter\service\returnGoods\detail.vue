<template>
  <div class="detail-wrapper">
    <page-title title="退货单详情" />
    <div class="order-detail-items">
      <ul class="orders"><li>订单编号：{{detail.orderNo}}</li><li>下单时间：{{detail.orderCreateTime}}</li><li>下单渠道：{{detail.orderChannel}}</li></ul>
      <div class="clearfix"></div>
      <el-steps :active="active" align-center v-if="detail.rejectionStatusEnum !=='已驳回'||detail.rejectionStatusEnum !=='已关闭'">
        <el-step title="待审核" :description="detail.pendingTime"></el-step>
        <el-step title="待入库" :description="detail.warehousingTime"></el-step>
        <el-step title="已完成" :description="detail.acceptedTime"></el-step>
      </el-steps>
      <div class="order-status">
        <h3 v-if="detail.rejectionStatusEnum">
          <img src="../../../../assets/imgs/order_icon.png">{{detail.rejectionStatusEnum.desc}}
          <span style="margin-left: 20px;">退货总金额：<span class="text-warning">
            ￥<span class="text-warning" v-text="detail.salesReturnMoney"></span></span>
          </span>
          <span style="margin-left: 20px;" v-if="detail.rejectionStatusEnum&&detail.rejectionStatusEnum.code==='WAREHOUSING'||detail.rejectionStatusEnum.code==='ACCEPTED'">总退款金额：
            <span class="text-warning">
            ￥<span class="text-warning" v-text="detail.totalRefundMoney"></span>
            </span>
            <small>
              <span v-if="detail.rejectionStatusEnum&&detail.rejectionStatusEnum.code==='WAREHOUSING'" class="text-warning">需退款</span>
              <span v-if="detail.rejectionStatusEnum&&detail.rejectionStatusEnum.code==='ACCEPTED'" class="text-warning">退款已成功</span>
            </small>
          </span>
        </h3><div class="fr" style="margin-top: -30px;">
        <!--待审核：导出单据、审核；待入库：导出单据、确认收货；-->
        <!-- <el-button v-if="checkPermission(['admin','returnDetail:export'])">导出单据</el-button> -->
      </div>
        <p v-if="detail.rejectionStatusEnum&&detail.rejectionStatusEnum.code === 'PENDING'">订单包裹已提交退款申请，等待管理员进行审核</p>
        <p v-if="detail.rejectionStatusEnum&&detail.rejectionStatusEnum.code === 'WAREHOUSING'">已同意退货申请，等待退货商品收货入库</p>
        <p v-if="detail.rejectionStatusEnum&&detail.rejectionStatusEnum.code === 'ACCEPTED'">退货已完成</p>
        <p v-if="detail.rejectionStatusEnum&&detail.rejectionStatusEnum.code === 'REFUSE'">拒绝原因：</p>
        <p v-if="detail.rejectionStatusEnum&&detail.rejectionStatusEnum.code === 'CANCEL'">关闭原因：</p>

      </div>
      <page-module-card title="退货凭证" v-if="detail.salesReturnVoucher">
        <ul class="salesReturnVoucher">
          <li v-for="(item,index) in detail.salesReturnVoucher.split(',')" :key="index">
            <el-image
              style="width: 100px; height: 100px"
              :src="`https://eyaolink-dev-bucket.oss-cn-shenzhen.aliyuncs.com${item}`"
              :preview-src-list="detail.salesReturnVoucher.split(',').map(i=>`https://eyaolink-dev-bucket.oss-cn-shenzhen.aliyuncs.com${item}`)"
              >
              <!--:preview-src-list="[item]">-->
            </el-image>
          </li>
        </ul>
      </page-module-card>
      <page-module-card title="退货信息">
        <ul class="client-info" v-if="detail.purMerchant||detail.salesReturnType">
          <li>退货单号：{{detail.salesReturnNo}}</li>
          <li>客户编码：{{detail.purMerchant.code}}</li>
          <li>退货方式：{{detail.salesReturnMode}}</li>
          <li>申请人：{{detail.applicant}}</li>
          <li>客户名称：{{detail.purMerchant.name}}</li>
          <li>物流公司：{{detail.logisName}}</li>
          <li>退货时间：{{detail.salesReturnTime}}</li>
          <li>退货类型：{{detail.salesReturnType.desc}}</li>
          <li>物流单号：{{detail.logisticsNo}}</li>
          <li style="width:100%;">退货原因：{{detail.rejection}}</li>
        </ul>
      </page-module-card>

      <page-module-card title="退货商品信息">
        <el-table border :data="detail.salesReturnItemList" >
          <el-table-column type="index"></el-table-column>
          <el-table-column label="商品主图" prop="picturePath" class-name="img-cell">
            <template slot-scope="scope">
              <img :src="scope.row.picturePath | imgFilter" class="productImg" width="50px" height="50px" />
            </template>
          </el-table-column>
          <el-table-column label="商品编码" prop="productCode" width="200" show-overflow-tooltip></el-table-column>
          <el-table-column label="商品名称" prop="productName" width="200" show-overflow-tooltip></el-table-column>
          <el-table-column label="规格" prop="spec" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column label="生产厂家" prop="saleMerchantName" min-width="200" show-overflow-tooltip></el-table-column>
          <el-table-column label="单价" prop="unitMoney" width="120" show-overflow-tooltip />
         <!-- <el-table-column label="购买数量" prop="buyNum">
          </el-table-column>-->
          <el-table-column label="退货数量" prop="totalNum" width="100" show-overflow-tooltip></el-table-column>
        </el-table>
        <template>
          <tabs-layout
            :tabs="[{ name: '退货日志', value: 'first'},{ name:'物流信息', value:'second' }]"
            v-model="activeName"
          />
          <template v-if="activeName === 'first'">
            <el-timeline>
              <el-timeline-item
                hide-timestamp
                v-for="(activity, index) in activities"
                :key="index"
                :icon="activity.icon"
                :type="activity.type"
                :color="activity.color"
                :size="activity.size">
                {{activity.time}}
                <span class="active-content">{{activity.content}}</span>
              </el-timeline-item>
            </el-timeline>
          </template>
          <template v-if="activeName === 'second'">
            <el-collapse v-model="activeNames" @change="handleChange">
              <el-collapse-item name="1">
                <ul>
                  <li v-for="(logis,index) in logisNoList" :key="index" class="logis-item" style="width:100%">
                    <h3><span v-if="logisNoList.length > 1">【包裹{{ index+1 }}】<span style="display:inline-block;margin-right: 20px;" /></span>物流公司:{{ logis.logisName }}<span style="display:inline-block;margin-right: 20px;" />运单号：{{ logis.logisNo }}</h3>
                    <!-- <el-timeline>
                      <el-timeline-item
                        v-for="(activity, index) in logis.logisData"
                        :key="index"
                        :icon="activity.icon"
                        :type="activity.type"
                        :color="activity.color"
                        :size="activity.size"
                      >
                        {{ activity.time }}
                        <span class="active-content">{{ activity.context }}</span>
                      </el-timeline-item>
                    </el-timeline> -->
                    <el-timeline>
                      <el-timeline-item
                        v-for="(activity, index) in logis.logisData"
                        :key="index"
                        :icon="activity.icon"
                        :type="activity.type"
                        :color="activity.color"
                        :size="activity.size"
                        :timestamp="activity.time">
                        <span class="active-content">{{ activity.context }}</span>
                      </el-timeline-item>
                    </el-timeline>
                  </li>
                </ul>
                <template slot="titles">
                  <p class="text-primary">{{ titles }}<i v-if="titles === '收起'" class="header-icon el-icon-arrow-up" />
                    <i v-if="titles === '打开'" class="header-icon el-icon-arrow-down" />
                  </p>
                </template>
              </el-collapse-item>
            </el-collapse>
          </template>
        </template>
      </page-module-card>
      <verify  v-bind:title="title" :verifyVisible="verifyVisible" @changeShow="changeVerify" @verifyRefuse="verifyRefuse" @verifyAgree="verifyAgree" :isRefund="true"></verify>
      <el-dialog title="图片预览" width="60%" :visible.sync="imgVisible">
        <img :src="dialogImageUrl" style="margin: 0 auto;display: block;width: 100%;" >
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="imgVisible = false">关 闭</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
  import { returnDetail,refuseSalesReturn,agreeSalesReturn,saleComfirm,getExpressInfo } from "@/api/trade";
  import verify from "../dialogs/verify";
  import checkPermission from "@/utils/permission";
  export default {
    name: "detail",
    components: {
      verify
    },
    data(){
      return {
        activeName: 'first',
        visible: true,
        title: '该退货单是否通过申请',
        verifyVisible: false,//审核弹出框
        remarkVisible: false,
        remarks: '',
        innerVisible: false,
        agreeVisible: false,//同意申请
        reason: '',
        cancelVisible: false,
        currentComponent: '',
        detail: {},
        productData: [],
        activities: [],
        agreeForm: {},
        titles: '收起',
        logisNoList: [],
        activeNames: '1',
        rules: {},
        active: 1,
        imgVisible: false,
        dialogImageUrl: ''
      }
    },
    mounted() {
      this.getDetail()
    },
    methods: {
      checkPermission,
      getImgUrl(url) {
        this.dialogImageUrl = url
        this.imgVisible = true
      },
      //确认收货
      handleComfirm() {
        saleComfirm(this.$route.query.id).then(res=>{
          this.$message.success('确认收货成功！')
          this.getDetail()
        })
      },
      handleChange(val) {
        if(Object.keys(val).length === 0) {
          this.title='打开'
        } else {
          this.title='收起'
        }
      },
      changeVerify(data) {
        if (data === 'false') {
          this.verifyVisible = false
        } else {
          this.verifyVisible = true
        }
      },
      //审核拒绝
      verifyRefuse(reason) {
        refuseSalesReturn(this.$route.query.id,reason).then(res=>{
          this.$message.success('审核已拒绝！')
          this.verifyVisible = false
          this.getDetail()
        })
      },
      //审核通过
      verifyAgree(agreeReason,freight) {
        agreeSalesReturn({
          agreeReason: agreeReason,
          freight: freight,
          id: this.$route.query.id}).then(res=>{
          this.$message.success('审核已通过！')
          this.verifyVisible = false
          this.getDetail()
        })
      },
      arraySpanMethod({ row, column, rowIndex, columnIndex }) {
        if(columnIndex === 1) {
          return [1, 10]
        }
      },
      async getDetail() {
        let id = this.$route.query.id
        const {data} = await returnDetail(id)
        this.detail = data
        this.activities = data.rejectionJournal
        if(data.rejectionStatusEnum.code === 'PENDING') {
          this.active = 0
        } else if(data.rejectionStatusEnum.code === 'WAREHOUSING') {
          this.active = 1
        } else {
          this.active = 2
        }
        if(data.logisticsNo !='null' && data.logisticsNo.trim().length!=0){
          this.getExpress();
        }
      },
       //获取物流
      getExpress() {
        getExpressInfo(this.detail.logisticsNo).then(res=>{
          if(res.code == 0){

            this.logisNoList = res.data.map((item,inedx)=>{
              let obj = {};
              obj.logisName = item.logisticName;
              obj.logisNo = item.nu;
              obj.logisData = item.data;
              console.log('obj---->',obj);
              return obj;
            });
          }
          console.log('this.logisNoList',this.logisNoList);
        })
      },
      handleClick() {},
      componentResult() {
      },
      handleVeriry() {
      },
      handleRefuse() {
      },
      //备注提交
      remarkSubmit() {},
      //取消订单
      cancelSubmit() {},
      formatterMoney(s, n) {
        n = n > 0 && n <= 20 ? n : 2;
        s = parseFloat((s + "").replace(/[^\d\.-]/g, "")).toFixed(n) + "";
        let l = s.split(".")[0].split("").reverse(), r = s.split(".")[1];
        let t = "";
        for (let i = 0; i < l.length; i++) {
          t += l[i] + ((i + 1) % 3 == 0 && (i + 1) != l.length ? "," : "");
        }
        return t.split("").reverse().join("") + "." + r;
      }
    }
  }
</script>

<style lang="scss">
  .detail-wrapper {
    padding: 0 20px;
  }
</style>
