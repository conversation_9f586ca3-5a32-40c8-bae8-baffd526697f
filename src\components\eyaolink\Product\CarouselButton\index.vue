<template>
  <div style="display:inline; margin-right:6px;">
    <el-button type="text"  @click="showDialogFun">{{
      buttonName
    }}</el-button>
    <el-dialog
      :title="imglist[index]?imglist[index].licenseType.desc:'资质'"
      append-to-body
      :visible.sync="dialogStatus"
      :before-close="closeDialogFun"
    >
      <el-carousel
        v-if="imglist.length>0"
        :initial-index="index"
        arrow="always"
        height="50vh"
        :autoplay="false"
      >
        <el-carousel-item v-for="(item, index) in imglist" :key="index">
          <el-image
            style="width:100%;height:100%;"
            :fit="'contain'"
            :src="item.fileIds"
          ></el-image>
        </el-carousel-item>

      </el-carousel>
      <div v-else style="width:100%;height:100%;  text-align: center;">无上传资质</div>
      <div slot="footer">
        <el-button @click="dialogStatus = false">取 消</el-button>
        <DowloadButton v-if="checkPermission(['admin','product-qualifications:download'])&&imglist.length>0" :buttonType="'primary'" :imgList="imglist"></DowloadButton>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import checkPermission from '@/utils/permission'
import DowloadButton from "@/components/eyaolink/DowloadButton";
import { getForProductLicense } from '@/api/productLicenseRel'
export default {
  data() {
    return {
      index:0,
      imglist:[],
      dialogStatus: false
    };
  },
  props: {
    buttonName: {
      type: String,
      default: ""
    },
    dialogWidth: {
      type: String,
      default: "50%"
    },
    id: {
      type: String,
      default: "0",
      required:true
    }
  },

  methods: {
    checkPermission,
    showDialogFun(){
      this.dialogStatus = true;
      this.getForProductLicenseFun()
    },
    closeDialogFun: function() {
      this.dialogStatus = false;
    },
    async getForProductLicenseFun(){
      this.imglist = []
      var  _this=this;
      var {code,data} = await getForProductLicense(this.id)
      if(code==0){

        if(data!=null&&data.productLicenseRelVoList!=null){
          data.productLicenseRelVoList.forEach(function(item){
            _this.imglist.push(item)
          })

        }
      }
      // console.info(data);
    }
  },
  components:{
      DowloadButton
  },
  mounted() {

  },
  beforeDestroy() {}
};
</script>
<style lang="less" scoped></style>
