<template>
  <div class="productTypeListPageContent">
    <im-search-pad
      :has-expand="false"
      :model="listQuery"
      @reset="resetForm"
      @search="onSearchSubmitFun"
    >
      <im-search-pad-item prop="model.label">
        <el-input v-model="listQuery.model.label" placeholder="请输入分类" />
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        :tabs="[{ name: '产品分类', value: ''}]"
      >
        <template slot="button">
          <div>
            <el-button  @click="reloadFun()">刷新</el-button>
            <el-button  type="primary" @click="editRootProductType({})" v-if="checkPermission(['admin','admin-platformProduct-category:add'])">+新增产品分类</el-button>
          </div>
        </template>
      </tabs-layout>
      <table-pager
        ref="pager-table"
        :data.sync="list"
        row-key="id"
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
        :options="tableTitles"
        :remoteMethod="getList"
        lazy
        :load="load"
        :operationWidth="200"
      >
        <template slot="pictIdS" slot-scope="{row}">
          <el-popover placement="right" trigger="hover">
            <el-image
              style="width: 200px; height: 200px"
              fit="contain"
              :src="row.pictIdS | imgFilter"
            ></el-image>
            <el-image
              slot="reference"
              style="width: 30px; height: 30px"
              fit="cover"
              :src="row.pictIdS | imgFilter"
            ></el-image>
          </el-popover>
        </template>
        <template slot="whetherShowFrontend" slot-scope="{ row }">
          <span v-if="row['whetherShowFrontend']==null"></span>
          <span v-else-if="row['whetherShowFrontend'].code == 'Y'" class="el-tag el-tag--success">{{row['whetherShowFrontend'].desc}}</span>
          <span v-else class="el-tag el-tag--danger">{{row['whetherShowFrontend'].desc}}</span>
        </template>
        <el-table-column prop="sortValue" label="排序" sortable width="100" slot="sortValue"></el-table-column>
        <div slot-scope="scope">
          <!-- 根分类 -->
          <el-button v-if="scope.row.level==0&&checkPermission(['admin','admin-platformProduct-category:childrenAdd'])" @click="editRootProductType({parentId:scope.row.id,parentCategoryCode:scope.row.categoryCode,pName:scope.row.pName,path:scope.row.path,level:scope.row.level+1})" type="text" >新增子分类</el-button>
          <template v-if="checkPermission(['admin','admin-platformProduct-category:edit'])">
            <span class="line" v-if="scope.row.level==0">|</span>
            <el-button v-if="scope.row.level==0" @click="editRootProductType(scope.row)" type="text" >编辑</el-button>
          </template>
          <!-- 根分类 end-->
          <!-- 二级栏目 新增子分类 -->
          <el-button v-if="scope.row.level==2&&checkPermission(['admin','admin-platformProduct-category:customAttribute'])" @click="setPropFun(scope.row)" type="text" >自定义属性</el-button>
          <el-button v-if="(scope.row.level==1)&&checkPermission(['admin','admin-platformProduct-category:childrenAdd'])"   @click="editRootProductType({parentId:scope.row.id,parentCategoryCode:scope.row.categoryCode,pName:scope.row.pName,path:scope.row.path,level:scope.row.level+1})" type="text" >新增子分类</el-button>
          <template v-if="checkPermission(['admin','admin-platformProduct-category:edit'])">
            <span class="line" v-if="(scope.row.level==1)">|</span>
            <el-button v-if="(scope.row.level==1||scope.row.level==2)" @click="editRootProductType(scope.row)" type="text" >编辑</el-button>
          </template>
          <!-- 二级栏目 新增子分类 end -->
          <template v-if="checkPermission(['admin', 'admin-platformProduct-category:del'])">
            <span class="line">|</span>
            <el-button type="text" @click="deleteRootFun(scope.row)">删除</el-button>
          </template>
        </div>
      </table-pager>
    </div>
    <!-- New ProductType -->
        <new-root v-if="showRootProductType" :visible.sync="showRootProductType" :actionItem.sync="actionItem" :isReload.sync="submitReload" :row.sync="row"></new-root>
    <!-- New ProductType -->
     <!-- New ProductType -->
        <setProp v-if="showPropType" :visible.sync="showPropType" :row.sync="row"></setProp>
    <!-- New ProductType -->
  </div>
</template>
<script>
import checkPermission from '@/utils/permission'
import { list,getChildren,deleteApi } from "@/api/products/categoryPlatform";
import newRoot from "@/views/products/productType/newRoot";
import setProp from "@/views/products/productType/setProp";
import Pagination from "@/components/Pagination";
import TabsLayout from '@/components/TabsLayout'
const TableColumns = [
  { label: "分类名称", name: "label", prop: 'label', width: "160px" },
  { label: "分类编码", name: "categoryCode", prop: 'categoryCode', width: "160px" },
  { label: "分类图片", name: "pictIdS", prop: 'pictIdS', width: "100px", columnSlot: true },
  { label: "排序", name: "sortValue", prop: 'sortValue', width: "100px", slot: true },
  { label: "前端显示", name: "whetherShowFrontend", prop: 'whetherShowFrontend', width: "120", columnSlot: true }
]
const TableColumnList = [];
for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({ key: i, ...TableColumns[i] });
}

export default {
  components:{
    Pagination,
    setProp,
    newRoot,
    TabsLayout
  },
  data() {
    return {
      showPropType: false,
      loadNodeMap: new Map(),
      showRootProductType: false,
      showEdit: false,
      submitReload: false,
      listLoading: false,
      tableTitles: TableColumnList,
      listQuery: {
          model:{
            parentId: 0,
            label: ''
          },
          current:1,
          size:10
      },
      actionItem:null,
      list: [],
      total: 0
    };
  },
  watch:  {
    submitReload:function(newVal,oldVal){
      if(newVal){
        this.submitReload=false;
        this.updateEditRootFun(this.actionItem)
      }
    }
  },
  props: {},
  methods: {
    checkPermission,
    editRootProductType:function(row){
        this.row = row
        this.showRootProductType = true
    },
    editChildrenFun(row){
        this.row = row
        this.showEdit = true
    },
    setPropFun(row) {
        this.row = row
        this.showPropType = true
    },
     deleteRootFun(row){
      this.$confirm('此操作将永久删除该信息, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        let data = await deleteApi(row.id);
        if(data.code == 0 && row.parentId != '0'){
          this.reloadTreeNode(row.parentId)
        }else{
          this.onSearchSubmitFun()
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })

    },
    onSearchSubmitFun() {
      this.list = [];
      this.$refs['pager-table'].doRefresh()
    },
    async updateEditRootFun(row){
      if(row.parentId!='0'){
        await this.reloadTreeNode(row.parentId)
        this.actionItem=null;
      }else{
        this.onSearchSubmitFun()
      }
    },
    resetForm() {
      this.reloadFun()
    },
    reloadFun() {
      this.listQuery={
          model: {
            parentId: 0,
            label: ''
          },
          current: 1,
          size: 10
      }
      this.$refs['pager-table'].doRefresh()
    },
    async reloadTreeNode(parentId){
      const {tree, resolve} = this.loadNodeMap.get(parentId);
      this.$set(this.$refs['pager-table'].getTable().store.states.lazyTreeNodeMap, parentId, []);
      const formData = new FormData();
      formData.append('id', tree.id);
      const { data } = await getChildren(formData);
      data.forEach(element => {
        element['pName']= tree.pName+"/"+element.label
        element['path']=tree.categoryCode+"/"+element.categoryCode
        element['hasChildren']=(tree.level+1)<3
        element['level']=tree.level+1
      });
      resolve(data)
    },
    async getList(params) {
      Object.assign(this.listQuery, params)
      const { data } = await list(this.listQuery);
      data.records.forEach(element => {
        element['pName']=element.label
        element['path']=element.categoryCode
        element['hasChildren']=true
        element['level']=0
      });
      return { data }
    },
    async load(tree, treeNode, resolve) {
        this.loadNodeMap.set(tree.id, {tree, treeNode, resolve})
        const formData = new FormData();
        formData.append('id', tree.id);
        const { data } = await getChildren(formData);
        data.forEach(element => {
          element['pName']= tree.pName+"/"+element.label
          element['path']=tree.categoryCode+"/"+element.categoryCode
          element['hasChildren']=(tree.level+1)<3
          element['level']=tree.level+1
        });
        resolve(data)
    }
  },
  mounted() {
  },
  beforeDestroy() {}
};
</script>
<style lang="less" scoped>
.productTypeListPageContent{
  .line{color:#dfe6ec; margin:0 6px;}
}
</style>
