<template>
  <div>
    <im-search-pad
      :is-expand.sync="isExpand"
      :model="model"
      @reset="reload"
      @search="searchLoad"
    >
      <im-search-pad-item prop="id">
        <el-input
          v-model="model.id"
          placeholder="请输入结算单编号"
        />
      </im-search-pad-item>
      <im-search-pad-item prop="settlementSerialNumber">
        <el-input
          v-model="model.settlementSerialNumber"
          placeholder="请输入结款流水号"
        />
      </im-search-pad-item>
      <im-search-pad-item prop="merchantSn">
        <el-input v-model="model.merchantSn" placeholder="请输入商家编码" />
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="merchantName">
        <el-input v-model="model.merchantName" placeholder="请输入商家名称" />
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="during">
        <el-date-picker
          v-model="model.during"
          type="datetimerange"
          align="right"
          unlink-panels
          range-separator="至"
          start-placeholder="起始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd hh:mm:ss"
        >
        </el-date-picker>
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout ref="tabs-layout" :tabs="tabList" @change="handleChangeTab">
        <template slot="button">
          <!-- <el-button v-if="checkPermission(['admin', 'bussinessList:export'])"
            >导出</el-button
          > -->
          <el-button @click="reload">刷新</el-button>
        </template>
      </tabs-layout>
      <table-pager
        ref="todoTable"
        :options="tableTitle"
        :remote-method="load"
        :data.sync="tableData"
        :pageSize="pageSize"
        :isNeedButton="false"
      >
        <template slot="realAmount">
          <el-table-column label="实收金额（元）" width="120">
            <slot slot-scope="{ row }">
              {{ row.realAmount | getDecimals }}
            </slot>
          </el-table-column>
        </template>
        <template slot="settlementAmount">
          <el-table-column label="应结金额（元）" width="120">
            <slot slot-scope="{ row }">
              {{ row.settlementAmount | getDecimals }}
            </slot>
          </el-table-column>
        </template>
        <template slot="settlementServiceAmount">
          <el-table-column label="交易服务费（元）" width="140">
            <slot slot-scope="{ row }">
              {{ row.settlementServiceAmount | getDecimals }}
            </slot>
          </el-table-column>
        </template>
        <template slot="settlementCommissionAmount">
          <el-table-column label="业务员品种推广佣金（元）" width="190">
            <slot slot-scope="{ row }">
              {{ row.settlementCommissionAmount | getDecimals }}
            </slot>
          </el-table-column>
        </template>
        <template slot="settlementChargedAmount">
          <el-table-column label="交易手续费（元）" width="170">
            <slot slot-scope="{ row }">
              {{ row.settlementChargedAmount | getDecimals }}
            </slot>
          </el-table-column>
        </template>
        <template slot="settlementCount">
          <el-table-column label="业务账单数" width="110">
            <slot slot-scope="{ row }">
              <el-button
                type="text"
                @click="
                  $router.push({
                    path: '/finance/businessStatement/bussiness/detail',
                    query: {
                      merchantsId: row.merchantsId,
                      currentTab: 2,
                      settlementOrderId: row.id
                    }
                  })
                "
                >{{ row.settlementCount }}</el-button
              >
            </slot>
          </el-table-column>
        </template>
        <template
          slot="settlementTime"
          v-if="model.merchantsStatus == 'FINISH'"
        >
          <el-table-column label="结款时间" width="170">
            <slot slot-scope="{ row }">
              <span>{{ row.settlementTime }}</span>
            </slot>
          </el-table-column>
        </template>
        <template
          slot="settlementSerialNumber"
          v-if="model.merchantsStatus == 'FINISH'"
        >
          <el-table-column label="结款流水号" width="170">
            <slot slot-scope="{ row }">
              <span>{{ row.settlementSerialNumber }}</span>
            </slot>
          </el-table-column>
        </template>
      </table-pager>
    </div>
  </div>
</template>

<script>
const TableColumns = [
  { label: "结算单编号", name: "id", prop: "id", width: "190" },
  {
    label: "结款流水号",
    name: "settlementSerialNumber",
    prop: "settlementSerialNumber",
    slot: true
  },
  { label: "商家编码", name: "merchantsSn", prop: "merchantsSn", width: "150" },
  {
    label: "商家名称",
    name: "merchantsName",
    prop: "merchantsName",
    width: "180"
  },
  { label: "实收金额(元)", name: "realAmount", prop: "realAmount", slot: true },
  {
    label: "应收金额(元)",
    name: "settlementAmount",
    prop: "settlementAmount",
    slot: true
  },
  {
    label: "交易服务费(元)",
    name: "settlementServiceAmount",
    prop: "settlementServiceAmount",
    slot: true
  },
  {
    label: "业务员品种推广佣金(元)",
    name: "settlementCommissionAmount",
    prop: "settlementCommissionAmount",
    slot: true
  },
  {
    label: "交易手续费(元)",
    name: "settlementChargedAmount",
    prop: "settlementChargedAmount",
    slot: true
  },
  {
    label: "业务账单数",
    name: "settlementCount",
    prop: "settlementCount",
    slot: true
  },
  {
    label: "结款方式",
    name: "settlementType.desc",
    prop: "settlementType.desc",
    width: "110"
  },
  {
    label: "结款时间",
    name: "settlementTime",
    prop: "settlementTime",
    slot: true
  },

  { label: "制单人", name: "createUserName", prop: "createUserName" },
  { label: "制单时间", name: "createTime", prop: "createTime", width: "170" }
];
const TableColumnList = [];
for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({ key: i, ...TableColumns[i] });
}
import checkPermission from "../../../utils/permission";
import { financeSettlementOrder, fsoDetailCountStatus } from "@/api/finance";
import TabsLayout from "@/components/TabsLayout";
export default {
  components: {
    TabsLayout
  },
  data() {
    return {
      isExpand: false,
      loading: "",
      search: "",
      controlType: "",
      currentTab: 0,
      tabs: [
        {
          name: "结算中",
          value: "PROCESS",
          count: 0,
          permission: "bussinessList-process:view"
        },
        {
          name: "已结算",
          value: "FINISH",
          count: 0,
          permission: "bussinessList-finish:view"
        }
      ],
      tableData: [],
      page: 1,
      pageSize: 10,
      totalPage: 0,
      total: 0,
      tableTitle: TableColumnList,
      model: {
        id:'',
        settlementSerialNumber: "",
        during: "",
        merchantName: "",
        merchantSn: "",
        merchantsStatus: "PROCESS"
      },
      products: [],
      ids: []
    };
  },
  computed: {
    tabList() {
      return [
        {
          name: `结算中（${this.tabs[0].count}）`,
          value: "PROCESS",
          hide: !checkPermission(["admin", "admin-finance-merchantSettlement:handlingView"])
        },
        {
          name: `已结算（${this.tabs[1].count}）`,
          value: "FINISH",
          hide: !checkPermission(["admin", "admin-finance-merchantSettlement:handledView"])
        }
      ];
    }
  },
  mounted() {
    this.getCount();
  },
  methods: {
    checkPermission,
    async getCount() {
      const { data } = await fsoDetailCountStatus({});
      // const { data } = await fsoDetailCountStatus({
      //   ...{
      //     settlementSerialNumber: this.model.settlementSerialNumber,
      //     merchantName: this.model.merchantName,
      //     merchantSn: this.model.merchantSn
      //   },
      //   startTime: this.model.during[0],
      //   endTime: this.model.during[1]
      // });
      this.tabs[0].count = data.process;
      this.tabs[1].count = data.finish;
    },
    async load(params) {
      const listQuery = {
        model: {
          ...this.model,
          startTime: this.model.during[0],
          endTime: this.model.during[1]
        }
      };
      delete listQuery.model.during;
      Object.assign(listQuery, params);
      this.loading = true;
      const { data } = await financeSettlementOrder(listQuery);
      this.loading = false;
      return { data };
    },
    searchLoad() {
      this.getCount();
      this.handleRefresh({
        page: 1,
        pageSize: 10
      });
    },
    reload() {
      // this.$refs['tabs-layout'].reset();
      this.getCount();
      this.model={
        id:'',
        settlementSerialNumber: "",
        during: "",
        merchantName: "",
        merchantSn: "",
        merchantsStatus: this.model.merchantsStatus
      },
      // this.model = {
      //   ...this.model,
      //   ...{
      //     settlementSerialNumber: "",
      //     during: "",
      //     merchantName: "",
      //     merchantSn: ""
      //   }
      // };
      // delete this.model.during;
      this.handleRefresh({
        page: 1,
        pageSize: 10
      });
    },
    handleChangeTab(tab) {
      this.model.merchantsStatus = tab.value;
      this.handleRefresh({
        page: 1,
        pageSize: 10
      });
    },
    handleRefresh(pageParams) {
      this.$refs['todoTable'].doRefresh(pageParams)
    },
  }
};
</script>
