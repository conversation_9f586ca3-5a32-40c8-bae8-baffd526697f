<template>
  <div class="app-container">
    <el-dialog
      title="添加客户"
      showClose
      :visible.sync="addUservisible"
      width="45%"
      @close="handleClose"
    >
      <search-pad @search="search" @reset="reset" style="margin-bottom: 20px;">
        <!--<el-form-item>
          <el-input placeholder="请输入内容" v-model="input" class="input-with-select">
            <el-select v-model="select" slot="prepend" placeholder="请选择" @change="getSelect" style="width: 120px;">
              <el-option label="客户编码" value="1"></el-option>
              <el-option label="名称" value="2"></el-option>
            </el-select>
          </el-input>
        </el-form-item>-->
        <el-form-item>
          <el-input placeholder="请输入客户编码" v-model="listQuery.model.purMerchantCode" class="input-with-select" />
        </el-form-item>
        <el-form-item>
          <el-input placeholder="请输入名称" v-model="listQuery.model.purMerchantName" class="input-with-select" />
        </el-form-item>
      </search-pad>
      <table-pager ref="todoTable" :options="tableTitle" :data.sync="tableData" :remote-method="load" :selection="true" @selection-change="handleSelectionChange">
        <div slot-scope="props">
          <el-link @click="add(props.row)">添加</el-link>
        </div>
      </table-pager>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addUservisible = false">取 消</el-button>
        <el-button type="primary" @click="adds">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { queryPageSaleMerchantGroupCustomerListDTO,updateMerchantPurSaleRelMerchantGroup } from "@/api/group";

const TableColumns = [
  { label: "客户编码", prop: "code" },
  { label: "客户名称", prop: "name" },
  { label: "企业类型", prop: "merchantType" },
  { label: "联系人", prop: "ceoName" },
  { label: "联系电话", prop: "ceoMobile" },
  { label: "所在区域", prop: "region" },
];
const TableColumnList = [];
for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({ key: i, ...TableColumns[i] });
}
export default {
  name: "DragTable",
  props: ["visible",'saleMerchantId','row'],
  data() {
    return {
      tableTitle: TableColumnList,
      list: null,
      total: null,
      listLoading: false,
      sortable: null,
      tableData: [],
      ids: [],
      listQuery: {
        model:{
          "purMerchantCode": '',
          "purMerchantName": '',
          "saleMerchantId": '',
          merchantGroupId: 1
        }
      },
      addUservisible: false,
      merchantGroupId: ''
    };
  },
  created() {
    //this.getList();
  },
  methods: {

    search() {
      this.handleRefresh({
        page: 1,
        pageSize: 10
      })
      this.load()
    },
    reset() {
      this.handleRefresh({
        page: 1,
        pageSize: 10
      })
      this.listQuery.model.purMerchantCode = ''
      this.listQuery.model.purMerchantName = ''
      this.load()
    },
    handleRefresh(pageParams) {
      this.$refs.todoTable.doRefresh(pageParams)
    },
    async load(params) {
      this.listQuery.model.saleMerchantId = this.saleMerchantId
      this.listLoading = true;
      Object.assign(this.listQuery, params)
      return await queryPageSaleMerchantGroupCustomerListDTO(this.listQuery);
    },
    async add(row) {
      let params = {
        ids: '',
        merchantGroupId: ''
      }
      await updateMerchantPurSaleRelMerchantGroup([row.id],this.merchantGroupId)
      this.$message.success('添加成功！')
      this.handleRefresh({
        page: 1,
        pageSize: 10
      })
    },
    async adds() {
      const {data} = await updateMerchantPurSaleRelMerchantGroup([this.ids.join(',')],this.merchantGroupId)
      this.$message.success('批量添加成功！')
      this.handleRefresh({
        page: 1,
        pageSize: 10
      })
    },
    handleSelectionChange(val) {
      this.ids = val.map(function(item,index) {
        return item.id
      })

    },
  },
  watch: {
    visible() {
      this.addUservisible = this.visible
    },
    row() {
      this.merchantGroupId = this.row.id
    }
  }
};
</script>
