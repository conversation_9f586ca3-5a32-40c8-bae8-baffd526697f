<template>
  <div class="container">
    <!-- 当前部门 -->
    <!-- <div class="department">小卫广东省</div> -->
    <div class="departmentBreadcrumb">
      <el-breadcrumb separator-class="el-icon-arrow-right" v-if="node.length>=2">
        <el-breadcrumb-item  v-for="(item,index) in node" class="breadcrumbItem" :key="index">{{item.name }}</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div class="tab_bg" v-if="node.length>0">
      <tabs-layout :tabs="[{ name: departmentForm && departmentForm.parentId=='0'?`${node[node.length-1].name}组织`:`${node[node.length-1].name}部门`  }]">
        <template slot="button"> </template>
      </tabs-layout>
      <el-form v-if="departmentForm!=null">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="主管:">
              <p class="textStyle">
                <el-link class="" type="primary" :underline="false"
                  >{{ departmentForm.salesmanName||"未设置" }} </el-link
                ><i v-if="checkPermission(['admin', 'admin-team-orgaStructure:setViceManager'])" class="el-icon-edit" @click="showEditOrganizationFun()"></i>
              </p>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="主管手机:">
              <p class="textStyle">
                {{
                  departmentForm.mobile||"未设置"
                }}
                <!-- <el-link class="" disabled :underline="false"></el-link> -->
              </p>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="departmentForm && departmentForm.parentId=='0'?'组织成员(人):':'部门成员(人):'">
              <p class="textStyle">
                {{departmentForm.salesmanCount||"0"}}
              </p>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="副主管:">
              <p class="textStyle assistant-supervisor-all">
                <span class="assistant-supervisor">
                  <span class="second-in-command"  v-for="(item, index) in departmentForm.deputyLeaderList" :key="index">
                    <template  v-if="departmentForm.deputyLeaderList && departmentForm.deputyLeaderList.length > 0">
                      {{ item.salesmanName }}（{{ item.mobile }}) <i @click="handleDeleteAssistant(item.depId, item.organizationId,item.salesmanId)" class="delete">X</i>
                    </template>
                  </span>
                  <span v-if="checkPermission(['admin', 'admin-team-orgaStructure:addViceManager'])" class="add-btn" @click="handleAddCommand">+ 添加</span>
                </span>
                
              </p>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      
    </div>
    <!-- 当前部门 end -->

    <!-- <ChooseProduct  :visible.sync="visibleChooseProduct" :departmentId="departmentId"  ></ChooseProduct> -->


    <!-- 区域 -->
    <area-tree-dialog
      ref="areaDialog"
      @on-confirm="getEreaData"
      :regionList="areaList"
    />
    <!-- 区域 end -->

    <!--当前组织部门 编辑弹出  -->
    <!-- <EditOrganizationDialog v-if="departmentForm!=null&&departmentForm.parentId=='0'" ref="EditOrganizationDialog" @confirm="confirmSubmitFun"  :editForm="editFormInfo"></EditOrganizationDialog> -->
    <!-- <EditDepartmentDialog 
      v-if="departmentForm!=null&&departmentForm.parentId!='0'" 
      ref="editDepartmentDialog" 
      :crateSource="'content'"
      :editForm="editFormInfo"
      @confirm="confirmSubmitFun"
      ></EditDepartmentDialog> -->
    <EditOrganizationDialog v-if="editOrganizationDialogVisible"  :visible.sync="editOrganizationDialogVisible" @confirm="confirmSubmitFun"  :editForm="editFormInfo"></EditOrganizationDialog>
    <!-- 当前部门编辑 -->
    <EditDepartmentDialog 
      v-if="editDepartmentDialogVisible"  :visible.sync="editDepartmentDialogVisible"
      :crateSource="'content'"
      :editForm="editFormInfo"
      @confirm="confirmSubmitFun"
      ></EditDepartmentDialog>
      <!-- 添加副主管的弹窗 -->
      <AddSecondCommand :visible.sync="isSecondCommandVisible" @onsuccess="handleSuccess" :organizationId="organizationId" :departmentId="departmentId" />
  </div>
</template>
<script>
import { mapState,mapGetters,mapActions} from 'vuex'
import {orgaStructure,getTreeNew,areaEdit, deleteDeputyDirector} from "@/api/organization/index";
import areaTreeDialog from "@/views/team/index/temp/organization/organization-area-tree";
import EditOrganizationDialog from '@/views/team/index/temp/organization/editOrganizationDialog';
import EditDepartmentDialog from '@/views/team/index/temp/organization/editDepartmentDialog';
import ChooseProduct from '@/views/team/index/temp/organization/chooseProduct';
import AddSecondCommand from './AddSecondCommand.vue'
import checkPermission from '@/utils/permission';
// import { query } from '@/api/';
export default {
  data() {
    return {
      editOrganizationDialogVisible:false,
      editDepartmentDialogVisible:false,
      isSecondCommandVisible: false,
      editFormInfo:null,
      areaList: [],
      // 部门信息
      departmentForm: {},
      selectDepartmentTableItemIds: null,
      departmentList: [],
      departmentListQuery: {
        current: 1,
        model: {},
        size: 10,
      },
      sellAreaStr:"",
      // 部门内容
      // 显示商品列表
      visibleChooseProduct:false,
      // 显示商品列表end
      organizationId: '',
      departmentId: ''
    };
  },
  props: {
    
    /**
     * @param {Array} node  当前 部门信息
     */
    organizationFormDate: {
        type: Object,
        default: null
    },
    /**
     * @param {Array} node  根节点到选中节点
     */
    node: {
        type: Array,
        default: function(){
          return [];
        },
        required: true,
    },
    
  },
  components: {
    // 组件
    areaTreeDialog,
    ChooseProduct,
    EditOrganizationDialog,
    EditDepartmentDialog,
    AddSecondCommand
  },
  computed: {
    // 计算属性computed :
    // 1. 支持缓存，只有依赖数据发生改变，才会重新进行计算
    // 2. 不支持异步，当computed内有异步操作时无效，无法监听数据的变化
    // 3. computed 属性值会默认走缓存，计算属性是基于它们的响应式依赖进行缓存的，也就是基于data中声明过或者父组件传递的props中的数据通过计算得到的值
    // 4. 如果一个属性是由其他属性计算而来的，这个属性依赖其他属性，是一个多对一或者一对一，一般用computed
    // 5.如果computed属性属性值是函数，那么默认会走get方法；函数的返回值就是属性的属性值；在computed中的，属性都有一个get和一个set方法，当数据变化时，调用set方法。
    ...mapGetters([
      'organizationNavNode',
      "organizationInfo",
      "paremDepartmentInfo",
      "departmentInfo",
    ]),
  
  },
  filters: {},
  watch: {
    // 监听属性watch：
    // 1. 不支持缓存，数据变，直接会触发相应的操作；
    // 2. watch支持异步；
    // 3. 监听的函数接收两个参数，第一个参数是最新的值；第二个参数是输入之前的值；
    // 4. 当一个属性发生变化时，需要执行对应的操作；一对多；
    // 5. 监听数据必须是data中声明过或者父组件传递过来的props中的数据，当数据变化时，触发其他操作，函数有两个参数，
    // 　　immediate：组件加载立即触发回调函数执行，
    // 　　deep: 深度监听，为了发现对象内部值的变化，复杂类型的数据时使用，例如数组中的对象内容的改变，注意监听数组的变动不需要这么做。注意：deep无法监听到数组的变动和对象的新增，参考vue数组变异,只有以响应式的方式触发才会被监听到。
    // 　　deepdemo：
    //      obj:{
    //          handler(){
    //              console.log('obj 变了')
    //          },
    //          deep:true
    //      }
    organizationFormDate: {
        deep: true,
        handler(val) {
          console.info("organizationFormDate")
          if(val!=null&&val.list!=null&&val.list.length>0){
            this.areaList= [...[],...this.getSelectAreaId(val.list)]
          }else{
            this.areaList=[]
          }
          this.sellAreaStr= val.areaName||''
          this.departmentForm = Object.assign({},val)  ;
        },
    },
  },
  methods: {
    checkPermission,
    handleDeleteAssistant(departmentId, organizationId, salesmanId) {
        this.$confirm('确认要删除该副主管吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          const query = {departmentId, organizationId,salesmanId};
          deleteDeputyDirector({...query}).then(res => {
            if(res.code === 0) {
              this.$message.success('删除成功');
              this.updateComponentsFun()
            }
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });          
        });
    },
    //添加副主管的回调函数
    handleSuccess(){
      this.updateComponentsFun()
    },
    /**
     * @description 添加副主管的方法
     */
    handleAddCommand() {
      this.organizationId = this.organizationInfo.id;
      this.departmentId = this.departmentForm.id;
      this.isSecondCommandVisible = true
    },

    // 销售区域  
    openAreaDialog() {
      this.$refs.areaDialog.show();
    },
    getEreaData(data, flag) {
      console.info(data, flag)
      if(this.departmentForm.area == undefined) {
        this.departmentForm['area'] =[]
      }
      if (flag) {
        // 全国
        let departmentForm = this.departmentForm;
        let allObj = {
          allType: 1,
          id: 0,
          label: "全国",
          isAllNational: 1, //是全国
        };
        this.sellAreaStr = '全国';
        this.departmentForm.area=[allObj];
        // 设置选中值
        this.areaList =this.getSelectAreaId(this.departmentForm.area)
      } else {
        if (data.length == 0) {
          // 没有选中任何数据
          this.sellAreaStr="";
          this.departmentForm.area = [];
          this.areaList =this.getSelectAreaId(this.departmentForm.area)
          return;
        }
        let str = this.getSelectAreaNames(data);
        this.sellAreaStr = str;
        let list = JSON.parse(JSON.stringify(this.departmentForm.area)).filter((item)=>{item.id!=0});
        let newList = [];
        let oldList = [];
        data.forEach((item) => {
          let index = list.findIndex((_) => _.id == item.id);
          if (index == -1) {
            // 新增的省市区
            let newObj = {
              ...item,
              allType: 1,
              isAllNational: 0, //不是全国
            };
            newList.push(newObj);
          } else {
            // 修改的省市区
            let oldObj = {
              ...item,
              allType: this.departmentForm.area[index].allType,
              isAllNational: 0, //不是全国
            };
            oldList.push(oldObj);
          }
        });
        let listData = [...oldList, ...newList];
        // console.info(this.getSelectAreaId(listData))
        //选中 树形图
        console.info(JSON.stringify(listData))
        this.departmentForm.area = listData
        //选中区
        this.areaList =this.getSelectAreaId(listData)
        // this.departmentForm.area =  this.dataDeal(listData);
      }

      let areaSubmit = {
        "areaNames": this.sellAreaStr,
        "departmentId": 0,
        "isAll": false,  
        "list":[],
        "organizationId": 0
      }
      if(flag){
        areaSubmit.isAll =true
      }
      areaSubmit.list = this.departmentForm.area 
      if(this.departmentInfo==null){
        delete areaSubmit.departmentId;
        areaSubmit.organizationId= this.organizationInfo.id;
        areaSubmit.organizationId= this.organizationInfo.id;
      } else{
        delete areaSubmit.organizationId
          areaSubmit.departmentId= this.departmentInfo.id
      }

      // 提交更新
      areaEdit(areaSubmit).then(res=>{
          let {code,data,msg} = res;
          if(code==0){
            // 回调更新部门信息
            console.info("this.updateComponentsFun()")
            this.updateComponentsFun()
          } 
      });
      

    },
    dataDeal(list) {
      let arr = JSON.parse(JSON.stringify(list));
      arr = arr.map((item) => {
        let obj = {
          ...item,
          label: item.label,
          isAll: item.isAll,
          id: item.id,
        };
        return obj;
      });
      return arr;
    },

    //递归名字
    getSelectAreaNames(list, str = "") {
      let _this = this;
      list.forEach(function (item, index) {
        if (item.isAll == true) {
          if (str != "") {
            str += "、";
          }
          str += item.label;
        } else if (item.children.length > 0) {
          if (str != "") {
            str += "、";
          }
          str += `${item.label}(${_this.getSelectAreaNames(item.children)})`;
        } else {
          if (str != "") {
            str += "、";
          }
          str += item.label;
        }
      });
      return str;
    },
    
     //递归循环区ID
    getSelectAreaId(list, selectList  = []) {
      let _this = this;
      list.forEach(function (item, index) {
        if (item.children!=null&&item.children.length > 0) {
          _this.getSelectAreaId(item.children,selectList)
        }else{
          selectList.push( item.id);
        }
      });
      return selectList;
    },


    // 销售区域 end 

    // 当前部门编辑
    showEditOrganizationFun(){
      console.info("showEditOrganizationFun")
      this.editFormInfo = null;
      if(this.departmentForm.parentId == null||this.departmentForm.parentId == '0'){
        this.editFormInfo= Object.assign({},this.departmentForm)
        // this.$refs.EditOrganizationDialog.show()
        this.editOrganizationDialogVisible=true
      }else{
        this.editFormInfo= Object.assign({},this.departmentForm)
        this.editDepartmentDialogVisible=true
      }
    },
    confirmSubmitFun(data){
      console.info("confirmSubmitFun",data)
      if(this.departmentForm.parentId == null||this.departmentForm.parentId == '0'){
        // this.$refs.EditOrganizationDialog.close()
        this.editOrganizationDialogVisible=false
      }else{
        // this.$refs.editDepartmentDialog.close()
        this.editDepartmentDialogVisible=false
      }
       // 更新 整个页面的
      this.updateComponentsFun()
    },
    // 当前部门编辑 end
    // 回调更新信息
    // updateComponentsFun(){
    //   // 更新 页面的组件数据
    //   this.$emit("onUpdateData","ALL")
    // },
    updateComponentsFun() {
      this.$parent.$parent.load('Department')
      this.$parent.getDepartmentInfo()
    },
    // 全数据更新方法
    updateAllFun(){
      if(this.organizationInfo!=null){
        this.getTreeNewFun()
      }
    },
    // 获取部门区域
    getTreeNewFun(){
      getTreeNew({
        // 当前组织;
        organizationId:this.organizationInfo.id,
        // 当前上级部门
        departmentId:(this.paremDepartmentInfo==null)?undefined:this.paremDepartmentInfo.id ,
        // 当前部门
        presentDepartmentId	:(this.departmentInfo==null)?undefined:this.departmentInfo.id,		
        //	是否为第一级组织标识(1=是 0=否)
        tag:(this.organizationInfo!=null&&this.paremDepartmentInfo==null&&this.departmentInfo.id == null)?1:0 
      }).then(res=>{
          let {code ,data,msg} =res;
          if(code==0){
            
          }
        })
      }

  },
  mounted() {
    // 方法调用
  //  this.getOrganizationFun()
  },
  beforeDestroy() {},
};
</script>

<style lang="scss" scoped>
@import "@/styles/index";
.oneLine{
  overflow:hidden; /* 超出一行文字自动隐藏 */
  text-overflow:ellipsis;/* 文字隐藏后添加省略号 */
  white-space:nowrap; /* 强制不换行 */
}
.container {
  background: #fff;
  .department {
    font-size: 16px;
    padding: 16px 0 0 16px;
  }
  .departmentBreadcrumb {
    padding: 16px 0 0 16px;

    ::v-deep .breadcrumbItem {
      font-size: 14px;
      & > span {
        color: #0056E5;
      }
      & > i {
        color: #0056E5;
      }
      &:last-child > span {
        color: #303133;
      }
    }
  }
  ::v-deep .es-table-pager .page-row {
    justify-content: flex-end;
  }
  .memberSearchInput {
    margin-right: 10px;
  }
  .textStyle {
    @extend  .flex_start_center;
    i {
      margin-left: 20px;
      color: #0056e5;
      cursor: pointer;
    }
  }
}
.assistant-supervisor-all {
  display: flex;
  align-items: center;
}
.assistant-supervisor{
  
}
.add-btn {
    display: inline-block;
    border: 1px dashed #0056E5;
    height: 22px;
    text-align: center;
    line-height: 22px;
    font-size: 14px;
    width: 60px;
    border-radius: 4px;
    // vertical-align: middle;
    cursor: pointer;
}
.second-in-command {
    display: inline-block;
    background: #0056E5;
    height: 22px;
    text-align: center;
    line-height: 22px;
    font-size: 14px;
    color: #fff;
    padding: 0 25px 0 10px;
    border-radius: 4px;
    margin-right: 10px;
    position: relative;
    .delete {
      position: absolute;
      width: 15px;
      text-align: right;
      height: 22px;
      line-height: 22px;
      color: #fff !important;
      right: 5px;
      z-index:100
    }
}


</style>
