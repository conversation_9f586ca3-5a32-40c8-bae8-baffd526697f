
import requestAxios from '@/utils/requestAxios'

// 获取列表数据
export function list(data) {
  return requestAxios({
    url: "/api/finance/admin/financeMerchantsPlatformMoney/page",
    method: 'post',
    data
  })
}

// 数据统计
export function statistics(data) {
  return requestAxios({
    url: "/api/finance/admin/financeMerchantsPlatformMoney/countStatus",
    method: 'post',
    data
  })
}
//取消
export function updateCashRemarks(data) {
  return requestAxios({
    url: "/api/finance/admin/financeMerchantsPlatformMoney/cancel",
    method: 'post',
    params:data
  })
}
