<template>
  <el-dialog v-loading="loading" title="选择移动部门" :visible.sync="visible" width="30%" append-to-body :before-close="handleClose">
    <div class="dialogContent">
      <el-tree 
          ref="tree"
          default-expand-all
          highlight-current	
          :data="treeData"
          node-key="id"
          :current-node-key="departmentId"
          @node-click="handleNodeClick"
          :expand-on-click-node="false"
          icon-class=" "
        >
          <template #default="{ node, data }">
              <el-row type="flex" justify="space-between" align="center"  :style="data.parentId=='0'?'width:100%':''">
                  <el-col :span="data.parentId=='0'?24:20">
                      <p v-if="data.parentId=='0'" class="level0">
                          <template v-if="data.children!=undefined">
                              <i  @click.stop="expandFun(node)" :class="node.expanded?'el-icon-caret-bottom':'el-icon-caret-right'"></i>
                          </template>
                          <svg-icon style="margin:0 5px;" icon-class="tree-table" />
                          <span class="treefontColor"><i class="el-icon-folder-opened iconColor"></i>{{data.name}}({{data.totalCount}}人)</span>
                          <i class="el-icon-refresh" @click.stop="refreshFun()"></i>
                      </p>
                      <p v-else>
                          <template v-if="data.children!=undefined">
                              <i  @click.stop="expandFun(node)"  :class="node.expanded?'el-icon-caret-bottom':'el-icon-caret-right'"></i>
                          </template>
                          <span class="treefontColor"><i class="el-icon-folder-opened iconColor"></i>{{data.name}}({{data.totalCount}}人)</span>
                      </p>
                  </el-col>
              </el-row>
          </template>
      </el-tree>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose()">取 消</el-button>
      <el-button type="primary" :loading="loading" @click="handleConfirm">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { mapState,mapGetters,mapActions} from 'vuex'
import { getOrganizationTreeById,batchMove } from '@/api/organization/index';
  export default {
    data() {
      return {
        loading: false,
        treeData: [],
        departmentId:0
      };
    },
    props:{
     /**
    * @description  编辑Form 对象  显示
    * @param {Boolean} visible  
    */
      visible:{
          type:Boolean,
          default:false
      }
    },
    computed: {
      // 计算属性computed :
      // 1. 支持缓存，只有依赖数据发生改变，才会重新进行计算
      // 2. 不支持异步，当computed内有异步操作时无效，无法监听数据的变化
      // 3. computed 属性值会默认走缓存，计算属性是基于它们的响应式依赖进行缓存的，也就是基于data中声明过或者父组件传递的props中的数据通过计算得到的值
      // 4. 如果一个属性是由其他属性计算而来的，这个属性依赖其他属性，是一个多对一或者一对一，一般用computed
      // 5.如果computed属性属性值是函数，那么默认会走get方法；函数的返回值就是属性的属性值；在computed中的，属性都有一个get和一个set方法，当数据变化时，调用set方法。
        ...mapGetters([
        'organizationNavNode',
        "organizationInfo",
        "paremDepartmentInfo",
        "departmentInfo",
      ]),
    
    },
    mounted () {
      this.getOrganizationTreeByIdFun(this.organizationNavNode[0].id)
      this.departmentId = this.organizationNavNode[this.organizationNavNode.length - 1].id 
    },
    watch:{
      
    },
    methods: {
      ...mapActions({
        setOrganizationNavNode: "organization/setOrganizationNavNode"
      }),
      expandFun(node){
        // 展开空值
        node.expanded = !node.expanded;
      },
    getOrganizationTreeByIdFun(id){
        getOrganizationTreeById(id).then(res=>{
          let {code,data,msg} = res;
          if(code == 0 ){
            this.treeData = data;
          }
        })
      },
      handleNodeClick(node){
        this.departmentId=node.id
      },
      handleClose(){
        this.$emit('update:visible',false)
      },
      handleConfirm(){
        this.$emit("confirm",this.departmentId)
        this.$emit('update:visible',false)
      }
    },
  };

</script>

<style scoped>
.dialogContent {
  max-height: 50vh;
  white-space: nowrap;
  overflow-x: auto;
}
</style>
