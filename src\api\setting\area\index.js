import requestAxios from '@/utils/requestAxios'
import request from '@/utils/request'

export function list(parentId) {
    return requestAxios({
        url: '/api/authority/area/linkage?parentId=' + parentId,
        method: 'get'
    })
}

export function getApi(id) {
    return requestAxios({
        url: '/api/authority/area/' + id,
        method: "get"
    })
}

export function editArea(data) {
    return requestAxios({
        url: '/api/authority/area',
        method: data.id > 0 ? "PUT" : "POST",
        data
    })
}
export function deleteArea(parentId) {
    return requestAxios({
        url: '/api/authority/area?ids[]=' + parentId,
        method: "delete"
    })
}

export function areaRefreshCache() {
    return requestAxios({
        url: '/api/authority/area/refreshCache',
        method:"POST"
    })
}