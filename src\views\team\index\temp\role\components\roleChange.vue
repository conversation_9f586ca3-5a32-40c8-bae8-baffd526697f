<template>
  <div>
    <el-dialog title="角色变更" :visible.sync="dialogVisible" width="400px" :before-close="handleClose">
      <el-form :model="roleForm" label-width="80px" class="demo-ruleForm">
        <el-form-item label="角色名称:">
          <el-select v-model="roleForm.orgRoleId" placeholder="请选择角色名称:">
            <el-option v-for="(item, index) in roleList" :key="index" :value="item.id" :label="item.name"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submitConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>


<script>
  import {
    mapGetters
  } from 'vuex'
  import {
    orgRoleList
  } from '@/api/organization/index';
  import {
    updateSalesMan
  } from '@/api/organization/role'
  export default {
    //import引入的组件
    components: {},

    data() {
      return {
        dialogVisible: false,
        roleForm: {
          orgRoleId: ''
        },
        roleList: []
      }
    },
    //生命周期 - 挂载完成（可以访问DOM元素）
    mounted() {},

    computed: {
      ...mapGetters([
        'organizationInfo'
      ]),
    },

    created() {
      // this.initData();
    },

    filters: {},

    //方法集合
    methods: {
      openDia(row) {
        this.dialogVisible = true;
        this.currentRole = row;
        this.roleForm.orgRoleId = row.id;
        this.initData();
      },
      handleClose() {
        this.dialogVisible = false;
      },
      async initData() {
        let params = {
          organizationId: this.organizationInfo.id
        }
        let result = await orgRoleList(params);
        if (result.code != 0 && result.msg != 'ok') {
          return
        };
        console.log('result', result);
        this.roleList = result.data || [];
      },
      submitConfirm() {
        let ids = [];
        this.currentRole.list.forEach(element => {
          ids.push(element.id);
        });
        let params = {
          orgRoleId: this.roleForm.orgRoleId,
          ids
        };
        updateSalesMan(params).then(res => {
          if (res.code == 0 && res.msg == 'ok') {
            this.$message.success('批量更改角色成功');
            this.handleClose();
            this.$emit('handleReload');
          }
        })
      },
    },

  }

</script>


<style lang='scss' scoped>

</style>
