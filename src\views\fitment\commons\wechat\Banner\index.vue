<template>
  <div class="items" @click="checkPermission(['admin','fitment-wechat:edit'])&&showDrawerFun()">
    <div class=" bannerItems">
      <el-carousel indicator-position="none" :height="'75px'">
        <el-carousel-item
          v-for="(item, index) in queryBannerItems"
          :key="index"
        >
          <el-image
            style="width: 100%; height: 75px"
            :src="item.image"
            :fit="'cover'"
          ></el-image>
        </el-carousel-item>
      </el-carousel>
    </div>

    <el-drawer
      :destroy-on-close="true"
      :size="'550px'"
      append-to-body
      :wrapperClosable="false"
      :visible.sync="drawer"
      :with-header="false"
    >
      <div class="flex_between_center top">
        <div>图片广告</div>
        <div>
          <el-button @click="drawer = false">取 消</el-button>
          <el-button type="primary" @click="submitFun()">提交</el-button>
        </div>
      </div>
      <div class="tipBox">
        <p class="title">添加图片</p>
        <p class="tip">
          最多添加10个图片广告，建议尺寸686x274，鼠标拖拽可调整广告顺序
        </p>
      </div>
      <div class="fromBox">
        <el-form
          :label-width="'180px'"
          class="form"
          v-for="(item, index) in bannerItems"
          :key="index"
          v-dragging="{ item: item, list: bannerItems, group: 'sortValue' }"
        >
          <i class="el-icon-error" @click="removeItem(index)"></i>
          <el-form-item :label-width="'0'" label=" " class="bannerItemInput"  >
              <div class="flex_between_start">
                  <div class="uploadImgBox" @click="uploadIndexItem(index)">
                      <el-upload
                          class="avatar-uploader"
                           :action="$uploadUrl"
                            :data="insertProgram"
                            :headers="headersProgram"
                            :on-success="uploadSuccess"
                            :before-upload="beforeUpload"
                            :show-file-list="false"
                            multiple
                         >
                          <el-image v-if="item.image!=''" style="width: 80px; height: 80px" :src="item.image" :fit="'cover'"></el-image>
                          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                      </el-upload>
                  </div>
                  <div class="inputBox">
                      <el-form-item label="链接类型" class="inputItem"> 
                        <el-select v-model="item.linkUrl" placeholder="请选择链接类型" @change="changeLinkFun($event,item)">
                          <el-option value="">请选择</el-option>
                          <el-option
                            v-for="itemInfo in selectList"
                            :key="itemInfo.describe"
                            :label="itemInfo.name"
                            :value="itemInfo.describe">
                          </el-option>
                        </el-select>
                      </el-form-item>
                      <el-form-item label="链接" class="inputItem">{{item.linkUrl}}</el-form-item>
                      <el-form-item class="inputItem" v-for="(paraItem,i) in item.linkParam" :label="'链接值'" >
                          <el-input @change="change($event)" placeholder="请选择" v-model="item.linkParam[i].val">
                            <!-- 商品详情 PRODUCT_DETAIL-->
                            <ProductItemTable v-if="item.searchType=='PRODUCT_DETAIL'" slot="append" :width="'100px'"  :selectItems.sync="item.linkParam[i].val" />
                            <!-- 商品详情 end-->
                            <!-- 商家首页 STORE_INDEX-->
                            <StoreListTable v-if="item.searchType=='STORE_INDEX'" slot="append" :width="'100px'"  :selectItems.sync="item.linkParam[i].val" />
                            <!-- 商家首页 end-->
                            <!-- 分类列表页面  CATEGORYTYPE_LIST-->
                            <ProductTypeItemTable v-if="item.searchType=='CATEGORYTYPE_LIST'" slot="append" :width="'100px'"  :selectItems.sync="item.linkParam[i].val" />
                            <!-- 分类列表页面 end-->
                            <!-- 专题页 -->
                            <SpecialTable v-if="item.searchType=='SPECIAL_PAGE'" slot="append" :width="'100px'" :selectItems.sync="item.linkParam[i].val" />
                            <!-- 专题页 end-->
                          </el-input>
                      </el-form-item>
                      <el-form-item label="是否显示"  class="inputItem">
                          <el-radio-group v-model="item.showStatus">
                              <el-radio :label="'Y'">是</el-radio>
                              <el-radio :label="'N'">否</el-radio>
                          </el-radio-group>
                      </el-form-item>
                  </div>
              </div>
          </el-form-item>
        </el-form>
      </div>
      <div class="addbtn" @click="addItemBtnFun">
        + 添加轮播图
      </div>
    </el-drawer>
    
  </div>
</template>
<script>
import checkPermission from '@/utils/permission' 
import ProductItemTable from '@/components/eyaolink/Product/productItemCodeTable'
import ProductTypeItemTable from '@/components/eyaolink/Product/ProductTypeItemTable'
import StoreListTable from '@/components/eyaolink/Store/listTable'
import SpecialTable from '@/components/eyaolink/Product/SpecialTable'

import {param2Obj} from '@/utils/index' 
import {
  pageComponentList,
  pageADList,
  deleteByPageComponentId,
  pageADListAdd,
  pageADEdit
} from "@/api/fitment";
import { query } from "@/api/setting/data/dictionaryItem";
import { uploadFile } from "@/api/file";
import { getToken } from "@/utils/auth";
export default {
  data() {
    return {
      queryBannerItems: [],
      bannerItems: [],
      drawer: false,
      showSelectTableVisBle:false,
      headersProgram: {
        token: getToken(),
        Authorization: "Basic YWRtaW5fdWk6YWRtaW5fdWlfc2VjcmV0"
      },
      insertProgram: {
        folderId: 0
      },
      selectList:[],
      pageComponentId:0,
      uploadIndex:0
    };
  },
  props: {
    pagePlateId:{
        type:String,
        required:true
    }
  },
  components:{
    ProductItemTable,
    ProductTypeItemTable,
    StoreListTable,
    SpecialTable
},
  methods: {
    checkPermission,
    showDrawerFun() {
      this.drawer = true;
      this.getSelectList()
      
    },
    addItemBtnFun() {
      if (this.bannerItems.length < 10) {
        this.bannerItems.push({
          id: "",
          pagePlateId:this.pagePlateId,
          showStatus:"Y",
          pageComponentId:this.pageComponentId,
          image: "",
          linkUrl: "",
          searchType: "",
          linkParam:[{key:"",val:""}],
          sortValue:this.bannerItems.length,
        });
      }
    },
    changeLinkFun(url, item){
        var selectItem = this.selectList.filter(itemInfo=>{
          return url == itemInfo.describe
        })
        
        if(selectItem!=null&&selectItem.length>0){
          item['searchType']=selectItem[0].code
        }else{
          item['searchType']=""
        }
        var propList=[];
        var linkPareObject =  param2Obj(url);
        Object.keys(linkPareObject).map(key => {
            var val=linkPareObject[key]
            propList.push({key,val:""}) 
        })
         
        if(propList.length>0){
            item.linkParam=propList
        }else{
             item.linkParam=null
        }  
    },
    showSelectVal:function(item){
      console.info(item)
    },
    change (e) {
      this.$forceUpdate()
    },
    removeItem(index) {
      this.bannerItems.splice(index, 1);
    },
    async submitFun() {
      await deleteByPageComponentId(this.pageComponentId)
      this.bannerItems.forEach((item,index)=>{
        item.sortValue=index;
      })
      this.queryBannerItems = [...[], ...this.bannerItems];
      this.queryBannerItems.forEach(item=>{
            var itemValJson={}
            if(item.linkParam instanceof Array ){
                item.linkParam.forEach(keyVal=>{
                    itemValJson={...itemValJson,...{[keyVal.key]:keyVal.val}}
                })
                item.linkParam=JSON.stringify(itemValJson)
            }
        })
      var data = await pageADListAdd(this.queryBannerItems)
      if (data.code == 0) {
          this.drawer=false
          this.initFun();
      } else {
          this.$message.error("提交轮播图失败！");
      }
      
    },
    async initFun() {
      var { data } = await pageComponentList({
        current: 1,
        map: {},
        model: {
          componentCode: "carousel"
        },
        order: "descending",
        size: 10,
        sort: "id"
      });
      if(data!=null){
        this.pageComponentId = data.records[0].id;
        let adList = await pageADList({
          current: 1,
          map: {},
          model: {
            pageComponentId: this.pageComponentId
          },
          order: "descending",
          size: 10,
          sort: "id"
        });
        if(adList.data!=null){
          adList.data.records.forEach(item=>{
            item.showStatus=item.showStatus.code
          })
          this.queryBannerItems = adList.data.records;

          this.queryBannerItems.forEach(item=>{
            var propList=[]
            var linkPareObject =  param2Obj(item.linkUrl);
            Object.keys(linkPareObject).map(key => {
                var val=linkPareObject[key]
                propList.push({key,val}) 
            })
            if(item.linkParam!=undefined &&item.linkParam!=""&&item.linkParam!=null){
                propList=[]
                var  linkParamObj=JSON.parse(item.linkParam);
                Object.keys(linkParamObj).map(key => {
                    var val=linkParamObj[key]
                    propList.push({key,val}) 
                })
            }
            item.linkParam=propList
          })    

        }
      }
    },
    //  上传功能
    async getSelectList() {
      this.list = [];
      this.isLoading = true;
      let { data } = await query(
        {
          dictionaryId:"40083396470731372",
          // code:"WXCHARTLOCATION"
        }
      );
      this.selectList = data;
      this.queryBannerItems.forEach(item=>{
         var selectItem = this.selectList.filter(itemInfo=>{
          return item.linkUrl == itemInfo.describe
        })
        if(selectItem!=null&&selectItem.length>0){
          item['searchType']=selectItem[0].code
        }else{
          item['searchType']=""
        }
      })

      this.bannerItems =JSON.parse(JSON.stringify( this.queryBannerItems));
    },

    uploadIndexItem:function(index){
      this.uploadIndex=index
    },
    beforeUpload(file) {
      let fileTypeList=["image/png", "image/pjpeg", "image/jpeg", "image/bmp"]
      const isJPG = fileTypeList.indexOf(file.type) >-1;
      const isLt2M = file.size / 1024 / 1024 < 5;

      if (!isJPG) {
        this.$message.error('上传图片格式错误!');
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!');
      }
      return isJPG && isLt2M;
    },
    uploadSuccess(res, file) {
      this.bannerItems[this.uploadIndex].image=res.data.url;
    },

  },
  mounted() {
    this.initFun();
  },
  beforeDestroy() {}
};
</script>
<style lang="less" scoped>
.items {
  border: 1px dashed red;
  margin-bottom: 12px;
  cursor: pointer;
}
.bannerItems {
  width: 343px;
  margin: 7px auto;
  height: 75px;
}

.top {
  border-bottom: 1px solid #efefef;
  height: 60px;
  padding: 0 15px;
}
.form {
  margin: 0 auto;
  margin-top: 16px;
  width: 100%;
  padding: 10px;
  position: relative;
  border: 1px solid #efefef;
  i.el-icon-error {
    opacity: 0;
    position: absolute;
    right: -10px;
    top: -10px;
    font-size: 20px;
    cursor: pointer;
  }
 
}
.fromBox {
  max-height: calc(100vh - 260px);
  overflow-y: auto;
  padding: 0 15px;
  width: 100%;
}
.form:hover i {
  opacity: 1;
}
.addbtn {
  cursor: pointer;
  margin: 0 auto;
  text-align: center;
  margin: 0 auto;
  margin-top: 16px;
  width: 315px;
  height: 40px;
  line-height: 40px;
  background: #ffffff;
  border: 1px solid #409eff;
  font-size: 14px;
  font-weight: 400;
  color: #409eff;
}
.tipBox {
  margin: 0 auto;
  margin-top: 16px;
  padding: 0 15px;
  width: 100%;
  p.title {
    font-size: 14px;
    height: 19px;
    font-size: 14px;
    font-weight: 400;
  }
  p.tip {
    font-size: 14px;
    height: 40px;
    font-family: -400;
    font-weight: 400;
    color: #aaaaaa;
    line-height: 20px;
  }
}



.inputItem{margin-bottom:12px;}
.inputBox{min-width: 320px;}
.uploadImgBox{ width:80px; height: 80px;}
.avatar-uploader{height:80px; width:80px;border: 1px solid #efefef; }
.avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 80px;
    height: 80px;
    line-height: 80px;
    text-align: center;
  }
  .avatar {
    width: 80px;
    height: 80px;
    display: block;
  }

</style>
