import data from '@/components/eyaolink/CascaderOfBusinessCategory/data'
import requestAxios from '@/utils/requestAxios'

// 组织列表
export function getOrganizationList(data) {
  //submitType:  ADD 添加  DEL移除 
  return requestAxios({
      url: '/api/authority/orgaStructure/list',
      method: 'post',
      data
  })
}

// 根据组织ID 获取  组织树
export function getOrganizationTreeById(id) {
  //submitType:  ADD 添加  DEL移除 
  return requestAxios({
      url: `/api/authority/orgaStructure/orgaTree/${id}`,
      method: 'post',
      data:{
        id:id
      }
  })
}


/**
 * 根据组织/部门ID 获取  当前部门和下级部门信息
 * @param {*} id 传参
 * @returns 
 */
 export function getDepartmentDetailById(id) {
  return requestAxios({
      url: `/api/authority/orgaStructure/departmentList/${id}`,
      method: 'post',
      data:{id:id}
  })
}


// 组织架构省市区树
/**
 * {
 *organizationId	组织id			
 *departmentId	上级部门id			
 *presentDepartmentId	当前部门id		
 *tag	是否为第一级组织标识(1=是 0=否)
}
 */
export function getTreeNew(data) {
    return requestAxios({
      url: '/api/authority/area/anno/treeNew',
      method: 'post',
      data
    })
  }


// 修改省市区
export function areaEdit(data) {
  return requestAxios({
    url: '/api/authority/orgaDepartment/areaAddAndUpdate',
    method: 'post',
    data
  })
}

/**
 * 编辑组织
 * @param {*} data 传参
 * @param {*} method //DELETE 删除  PUT 修改  Post 新增  GET  查询
 * @returns 
 */
export function orgaStructure(data,method = 'post') {
  //submitType:  ADD 添加  DEL移除 
  let url ='/api/authority/orgaStructure'
  if(method == 'GET'){
    url = url+"/"+data
    return requestAxios({
        url: url,
        method: 'GET'
    })
  }else{
    return requestAxios({
        url: url,
        method: method,   //DELETE 删除  PUT 修改  Post 新增
        data
    })
  }
}







/**
 * 业务员查询
 * @param {*} data 传参
 * @returns 
 */
 export function getSalesManList(data) {
  //submitType:  ADD 添加  DEL移除 
  return requestAxios({
      url: '/api/authority/orgaStructure/searchSalesmanByName',
      method: 'post',
      data
  })
}





/**
 * 关联业务员查询
 * @param {*} id 业务员ID
 * @returns 
 */
 export function checkSalesManById(id) {
    return requestAxios({
        url: `/api/authority/orgaSalesman/getSalesman/${id}`,
        method: 'GET'
    })
}




/**
 * 编辑部门
 * @param {*} data 传参
 * @param {*} method //DEL 删除  PUT 修改  Post 新增  GET  查询
 * @returns 
 */
 export function orgaDepartment(data,method = 'post') {
  //submitType:  ADD 添加  DEL移除 
  let url ='/api/authority/orgaDepartment'
  if(method == 'GET'){
    url = url+"/"+data
    return requestAxios({
        url: url,
        method: 'GET'
    })
  }else if(method == 'DEL'){
    return requestAxios({
        url: url,
        method: 'DELETE',   //DELETE 删除  PUT 修改  Post 新增
        params: {
          'ids[]': data
        }
    })
    
  }
  else{
    return requestAxios({
        url: url,
        method: method,   //DELETE 删除  PUT 修改  Post 新增
        data
    })
  }
}



/**
 * 移除部门
 * @param {*} data 传参
 * @returns 
 */
 export function deleteDepartment(id) {
  //  
  let url =`/api/authority/orgaDepartment/deleteSalesman/${id}`
  return requestAxios({
        url: url,
        method: 'DELETE'
    })
}
/**
 * 单个移除部门成员
 * @param {*} data 传参
 * @returns 
 */
 export function deleteDepartmentSalesman(id) {
  //submitType:  ADD 添加  DEL移除 
  let url =`/api/authority/orgaSalesman/deleteSalesman/${id}`
  return requestAxios({
        url: url,
        method: 'DELETE'
    })
}
/**
 * 批量移除部门成员
 * @param {*} data 传参
 * @returns 
 */
 export function deleteDepartmentSalesmans(id) {
  //submitType:  ADD 添加  DEL移除 
  let url =`/api/authority/orgaSalesman`
  return requestAxios({
        url: url,
        method: 'DELETE',
        params: {
          'ids[]': id
        }
    })
}
/**
 * 批量移除部门成员
 * @param {*} data 传参
 * @returns 
 */
 export function batchMove(data) {
  //submitType:  ADD 添加  DEL移除 
  let url =`/api/authority/orgaSalesman/batchMove`
  return requestAxios({
        url: url,
        method: 'POST',
        params: data
    })
}


/**
 * 组织、部门成员 
 * @param {*} data 
 * @returns 
 */
 export function peopleList(data) {
  return requestAxios({
      url: '/api/authority/orgaStructure/peopleList',
      method: 'post',
      data
  })
}
/**
 * 新增组织、部门成员 
 * @param {*} data 
 * @returns 
 */
 export function addOrgaSalesman(data) {
  return requestAxios({
      url: '/api/authority/orgaSalesman',
      method: 'post',
      data
  })
}
/**
 * 组织解散
 * @param {*} data 
 * @returns 
 */
 export function deleteOrga(id) {
  return requestAxios({
      url: `/api/authority/orgaStructure/deleteOrga/${id}`,
      method: 'DELETE',   //DELETE 删除  PUT 修改  Post 新增
      params: {
        'id': id
      }
  })
}




// 








//组织状态统计
export function getProductCount(data) {
  return requestAxios({
    url: '/api/merchant/admin/purMerchant/getPurMerhcantCount',
    method: 'post',
    data
  })
}

// 组织角色全部查询
export function orgRoleList(data) {
  return requestAxios({
    url: '/api/authority/orgRole/list',
    method: 'post',
    data
  })
}

// 组织角色修改推广费比例
export function updateRoleRatio(data) {
  return requestAxios({
    url: '/api/authority/orgPopularize/updateRoleRatio',
    method: 'post',
    data
  })
}

// 根据组织id查询商品分组
export function getOrganizationIdPopularize(id) {
  return requestAxios({
    url: `/api/authority/orgPopularize/getOrganizationIdPopularize/${id}`,
    method: 'get',
    data
  })
}

// 商品分组修改推广费比例
export function updateProductGroupRatio(data) {
  return requestAxios({
    url: '/api/authority/orgPopularize/updateProductGroupRatio',
    method: 'post',
    data
  })
}

// 按单品
export function findByBindProduct(data) {
  return requestAxios({
    url: '/api/authority/orgPopularize/findByBindProduct',
    method: 'post',
    data
  })
}

// 商品修改推广费比例
export function updateProductRatio(data) {
  return requestAxios({
    url: '/api/authority/orgPopularize/updateProductRatio',
    method: 'post',
    data
  })
}



/**组织、部门  商品 相关接口*/
// 根据部门ID获取部门销售商品
export function getProductsDepartment(data) {
  //relevanceStatus:    Y 已关联商品  N 未关联商品 
  return requestAxios({
      url: "/api/product/admin/productPlatform/page",
      method: 'post',
      data
  })
}
// 编辑部门销售商品
export function editSellProductByDepartmentId(data) {
  //submitType:  ADD 添加  DEL移除 
  return requestAxios({
      url: '/api/product/admin/organization/editSellProductByDepartmentId',
      method: 'post',
      data
  })
}









/**组织、部门  商品 相关接口end*/

// 批量移除商品绑定推广  --  批量移除按单品
export function removeBind(data){
  return requestAxios({
    url: '/api/authority/orgPopularize/removeBind',
    method: 'post',
    data
  })
}

// 分页查询全平台商品 -- 已废弃
// export function findByProduct(data) {
//   return requestAxios({
//     url: '/api/authority/orgPopularize/findByProduct',
//     method: 'post',
//     data
//   })
// }

// 商品绑定推广
export function bindPopularize(data) {
  return requestAxios({
    url: '/api/authority/orgPopularize/bindPopularize',
    method: 'post',
    data
  })
}

/**
 * @description 新增副主管
 * @param {*} data 
 * @returns 
 */
export const addDeputyDirector = data => {
  return requestAxios({
    url: '/api/authority/orgaSalesman/saveDeputyLeader',
    method: 'put',
    data
  })
}

/**
 * @description 删除副主管
 * @param {*} data 
 * @returns 
 */
export const deleteDeputyDirector = data => {
  return requestAxios({
    url: '/api/authority/orgaSalesman/removeDeputyLeader',
    method: 'put',
    data
  })
}



