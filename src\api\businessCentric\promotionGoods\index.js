import requestAxios from '@/utils/requestAxios'
// import requestAxios from "@/views/businessCentric/request"
// 获取详情
export function getList(data) {
  return requestAxios({
    url: '/api/agent/agentProduct/platform/query/agencyProducts',
    method: 'post',
    data
  })
}
// 获取商品代理区域
export function getAreas(id) {
  return requestAxios({
    url: '/api/agent/agentProduct/platform/queryArea/' + id,
    method: 'post',
  })
}

// 获取业务员
export function queryByProductId(data) {
  return requestAxios({
    url: '/api/agent/agentProduct/merchant/queryByProductId/' + data.id,
    data: data.data,
    method: 'post',
  })
}