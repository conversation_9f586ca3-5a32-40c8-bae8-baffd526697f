import requestAxios from '@/utils/requestAxios'
import request from '@/utils/request'

export function query(data) {
  return requestAxios({
    url: '/api/authority/dictionaryItem/query',
    method: 'post',
    data
  })
}

export function editApi(data) {
  return requestAxios({
    url: '/api/authority/dictionaryItem',
    method: data.id == 0 ? 'post' : 'put',
    data
  })
}

export function deleteApi(id) {
  return requestAxios({
    url: '/api/authority/dictionaryItem?ids[]=' + id,
    method: 'delete'
  })
}

export function getAllAd(data) {
  return requestAxios({
    url: '/api/merchant/admin/pageDataFoot/anno/getAllAd',
    method: 'POST',
    data
  })
}

// 获取专题页
export function getList(data) {
  return request({
    url: '/merchant/admin/featuredPage/page',
    method: 'post',
    data
  })
}