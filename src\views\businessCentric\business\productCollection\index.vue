<template>
  <div class="list—index">
    <!--搜索Form-->
    <im-search-pad :has-expand="false" :is-expand.sync="isExpand" :model="model" @reset="reload" @search="searchLoad">
      <im-search-pad-item prop="saleMerchantName">
        <el-input v-model="model.saleMerchantName" @keyup.enter.native="searchLoad" placeholder="请输入商家名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="saleManName" v-if="tabCode == 'RECORD'">
        <el-input v-model="model.saleManName" @keyup.enter.native="searchLoad" placeholder="请输入采集人姓名" />
      </im-search-pad-item>
      <im-search-pad-item prop="purMerchantName">
        <el-input v-model="model.purMerchantName" @keyup.enter.native="searchLoad" placeholder="请输入客户名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="manufacturer" v-if="tabCode == 'COLLECTDATA'">
        <el-input v-model="model.manufacturer" @keyup.enter.native="searchLoad" placeholder="请输入生产厂家" />
      </im-search-pad-item>
      <im-search-pad-item prop="during" v-if="tabCode == 'COLLECTDATA'">
        <el-date-picker v-model="during" type="daterange" range-separator="至" value-format="yyyy-MM-dd"
          start-placeholder="开始日期" end-placeholder="结束日期" />
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <!--Tabs布局-->
      <tabs-layout ref="tabs-layout" v-model="tabCode" :tabs="tabList" @change="handleChangeTab">
        <!--tabs右上角相关按钮-->
        <template slot="button">
          <el-button @click="reload">刷新</el-button>
        </template>
      </tabs-layout>
      <!-- 分页table -->
      <table-pager ref="pager-table" :options="tableColumns" :remote-method="load" :data.sync="tableData"
        :selection="true" @selection-change="onSelect" @selection-all="onAllSelect" :pageSize="pageSize"
        :operation-width="150">
        <el-table-column label="采集商品数" width="140" slot="itemCount">
          <slot slot-scope="{ row }">
            <el-button v-if="checkPermission(['admin', 'admin-business-productCollection:detail'])" type="text" @click="openProduct(row.id)">{{ row.itemCount }}</el-button>
            <span v-else>{{ row.itemCount }}</span>
          </slot>
        </el-table-column>

        <!--操作栏-->
        <div slot-scope="{ row }">
          <el-row class="table-edit-row">
            <span class="table-edit-row-item">
              <el-button v-if="checkPermission(['admin', 'admin-business-productCollection:detail']) && tabCode == 'RECORD'" type="text" @click="openProduct(row.id)">查看采集商品</el-button>
              <template v-else>
                <el-button v-if="checkPermission(['admin', 'admin-business-productCollection:detail'])" type="text" @click="handleHistory(row.productId)">查看采集历史</el-button>
              </template>
            </span>
          </el-row>
        </div>
      </table-pager>
    </div>
    <product-modal ref="product-modal"></product-modal>
  </div>
</template>


<script>
  import {
    productCollectItem, // 采集数据
    productCollectPage, //采集记录
  } from "@/api/salemanCenter/index"; // TODO 替换成对应用的列表api
  import tableInfo from "@/views/businessCentric/business/productCollection/tableInfo";
  import productModal from "@/views/businessCentric/business/productCollection/productModal";
  import checkPermission from '@/utils/permission';
  export default {
    //import引入的组件
    components: {
      productModal,
    },

    data() {
      return {
        isExpand: false,
        model: {
          saleMerchantName:'',
          saleManName: "",
          purMerchantName: "",
          productName: "",
          manufacturer: "",
        },
        during: [],
        tabCode: "RECORD",
        tableColumns: [],
        tableData: [],
        pageSize: 10,
      };
    },
    //生命周期 - 挂载完成（可以访问DOM元素）
    mounted() {},

    computed: {
      tabList() {
        return [{
            name: "采集记录",
            value: "RECORD",
            hide: !checkPermission(['admin', 'admin-business-productCollection:view'])
          },
          {
            name: "采集数据",
            value: "COLLECTDATA",
            hide: !checkPermission(['admin', 'admin-business-productCollection:itemView'])
          },
        ];
      },
    },

    created() {
      this.tableColumns = tableInfo["RECORD"];
    },

    filters: {},

    //方法集合
    methods: {
      checkPermission,
      async load(params) {
        console.log('------------>',this.model);
        if (this.tabCode == "RECORD") {
          console.log('this.tabCode',this.tabCode);
          let listQuery = {
            model: {
              saleMerchantName: this.model.saleMerchantName,
              productName: this.model.productName,
              purMerchantName: this.model.purMerchantName,
              saleManName: this.model.saleManName
            },
          };
          console.log('listQuery',listQuery);
          Object.assign(listQuery, params);
          console.log('listQuery',listQuery);
          return await productCollectPage(listQuery);
        } else {
          console.log('this.tabCode',this.tabCode);
          let listQuery = {
            model: {
              saleMerchantName: this.model.saleMerchantName,
              beginCreateTime: this.during[0],
              endCreateTime: this.during[1],
              manufacturer: this.model.manufacturer,
              productName: this.model.productName,
              purMerchantName: this.model.purMerchantName
            },
          };
          Object.assign(listQuery, params);
          console.log('listQuery',listQuery);
          return await productCollectItem(listQuery);
        }
      },
      // table 选中
      onAllSelect(selection) {
        this.onSelect(selection);
      },
      onSelect(val) {
        this.multipleSelection = val;
      },
      // 查看采集商品
      openProduct(id) {
        // let currentId = id;
        // console.log("--------", currentId);
        this.$refs["product-modal"].initData(id);
      },
      // 查看采集历史
      handleHistory(productId) {
        this.$router.push({
          path: "/businessCentric/business/productCollection/detail",
          query: {
            productId,
          },
        });
      },
      handleChangeTab(tab) {
        this.tableColumns = tableInfo[tab.value];
        this.model = {
          saleManName: "",
          purMerchantName: "",
          productName: "",
          manufacturer: "",
        }
        this.during = [];
        this.handleRefresh({
          page: 1,
          pageSize: this.pageSize,
        });
      },
      searchLoad() {
        console.log('------------>',this.model);
        this.handleRefresh({
          page: 1,
          pageSize: this.pageSize,
        });
      },
      reload() {
        this.$refs["tabs-layout"].reset();
        this.tabCode = "RECORD";
        this.tableColumns = tableInfo["RECORD"];
        this.during = [];
        this.handleRefresh({
          page: 1,
          pageSize: this.pageSize,
        });
      },
      
      handleRefresh(pageParams) {
        this.$refs["pager-table"].doRefresh(pageParams);
      },
    },
  };

</script>


<style lang='scss' scoped>
  .table {
    .table_box {
      line-height: 40px;
      padding-right: 30px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-left: 1px solid #ccc;
      border-top: 1px solid #ccc;
      border-right: 1px solid #ccc;
      background-color: #fafafa;

      .table_left {
        //  border-left: 1px solid #ccc;
        //  border-top: 1px solid #ccc;
        display: flex;

        .table_index {
          width: 50px;
          text-align: center;
          border-right: 1px solid #ccc;
          // border-bottom: 1px solid #ccc;
        }

        .table_item {
          margin: 0 30px;
        }
      }

      // &:hover {
      //   background-color: #ecf5ff;
      // }
    }

    &:nth-last-child(1) {
      border-bottom: 1px solid #ccc;
    }
  }

  .page-row {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #505465;
    font-size: 13px;
    margin-top: 16px;
  }

</style>
