<template>
  <div class="list—index">
    <!--搜索Form-->
    <im-search-pad :has-expand="false" :is-expand.sync="isExpand" :model="model" @reset="reload" @search="searchLoad">
      <im-search-pad-item prop="keyword">
        <el-input v-model.trim="model.keyword" placeholder="请输入姓名/手机" @keyup.enter.native="searchLoad" />
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <!--Tabs布局-->
      <tabs-layout ref="tabs-layout" :tabs="tabs">
        <!--tabs右上角相关按钮-->
        <template slot="button">
          <el-button @click="reload">刷新</el-button>
          <el-button v-if="checkPermission(['admin', 'admin-salesman:sendSafety'])" @click="handleSendProtocol" :disabled="multipleSelection.length === 0">发送协议</el-button>
          <el-button v-if="checkPermission(['admin', 'admin-salesman:add'])" type="primary" @click="handleAdd">+ 新增业务员</el-button>
        </template>
      </tabs-layout>
      <!-- 分页table -->
      <table-pager ref="pager-table" :options="tableColumns" :remote-method="load" :data.sync="tableData"
        :selection="true" :page-size="pageSize" :operation-width="150" @selection-change="onSelect"
        @selection-all="onAllSelect">
        <el-table-column slot="whetherOnJob" label="员工状态" width="80" align="center">
          <template slot-scope="{row}">
            <span>{{row.whetherOnJob && row.whetherOnJob.desc}}</span>
          </template>
        </el-table-column>
        <el-table-column slot="area" label="所在区域" min-width="180">
          <slot slot-scope="{ row }">
            <div v-if="row.provinceName != null && row.cityName != null && row.districtName != null">{{ row.provinceName
            }} - {{ row.cityName }} - {{ row.districtName }}</div>
          </slot>
        </el-table-column>
        <el-table-column slot="role" label="角色" width="140">
          <slot slot-scope="{row}">
            <div v-if="row.role && row.role.code">{{ dealRole(row.role.code) }}</div>
          </slot>
        </el-table-column>
        <el-table-column slot="serviceType" label="发送协议" width="140">
          <slot slot-scope="{row}">
            {{ row.sendProtocol.code === 'NOT_SENT' ? '未发送' : '已发送' }}
          </slot>
        </el-table-column>
        <el-table-column slot="isSign" label="协议状态（业务员）" width="160">
          <slot slot-scope="{row}">
            {{ row.sendProtocol.code === 'NOT_SENT' ? '--' : row.isSign.code === 'ZERO' ? '未发起' : '已发起' }}
          </slot>
        </el-table-column>
        <el-table-column slot="inOfficeCompanyName" label="任职公司">
          <slot slot-scope="{row}">
            {{ row.inOfficeCompanyName || '' }}
          </slot>
        </el-table-column>
        <el-table-column slot="saleCount" label="所在商业公司" width="140">
          <slot slot-scope="{row}">
            <el-button type="text" @click="openCustomer(row.id)">{{ row.saleCount }}</el-button>
          </slot>
        </el-table-column>

        <!--操作栏-->
        <div slot-scope="{row}">
          <el-row class="table-edit-row">
            <span class="table-edit-row-item">
              <el-button type="text" v-if="checkPermission(['admin', 'admin-salesman:edit'])" @click="handleEdit(row.id, 'edit')">编辑</el-button>
              <el-button type="text" v-if="checkPermission(['admin', 'admin-salesman:detail'])" @click="handleEdit(row.id, 'see')">查看</el-button>
              <del-el-button v-if="checkPermission(['admin', 'admin-salesman:del'])" style="margin-left:5px" :target-id="row.id" :text="delText" @handleDel="delSalesman" />
            </span>
          </el-row>
        </div>
      </table-pager>
    </div>
  </div>
</template>

<script>
const TableColumns = [{
  label: '业务员编码',
  name: 'code',
  prop: 'code',
  width: '150'
},
{
  label: '真实姓名',
  name: 'name',
  prop: 'name',
  width: '170'
},
{
  label: '联系方式',
  name: 'contactWay',
  prop: 'contactWay',
  width: '150'
},
{
  label: '所在区域',
  name: 'area',
  prop: 'area',
  width: '100',
  slot: true
},
{
  label: '注册手机号',
  name: 'mobile',
  prop: 'mobile',
  width: '150'
},
{
  label: '任职公司',
  name: 'inOfficeCompanyName',
  prop: 'inOfficeCompanyName',
  slot: true
},
{
  label: '所在商业公司',
  name: 'saleCount',
  prop: 'saleCount',
  width: '140',
  slot: true
},
{
  label: '服务类型',
  name: 'serviceType',
  prop: 'serviceType',
  width: '140',
  slot: true
},
{
  label: '协议状态（业务员）',
  name: 'isSign',
  prop: 'isSign',
  width: '190',
  slot: true
}
]
const TableColumnList = []
for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({
    key: i,
    ...TableColumns[i]
  })
}
import {
  salesmanPage,
  deleteSalesman,
  batchSendProtocol
} from '@/api/salemanCenter/index' // TODO 替换成对应用的列表api
// import { areas } from "@/api/enterprise";
import checkPermission from '@/utils/permission'
import delElButton from '@/components/eyaolink/delElButton'
export default {
  name: 'ListIndex',
  components: {
    delElButton
  },
  props: {},
  data() {
    return {
      cityValue: [],
      isExpand: false,
      model: {
        keyword: ''
      },
      delText: '您确定删除此业务员数据吗？',
      multipleSelection: [],
      search: '',
      tableData: [],
      pageSize: 10,
      tableColumns: TableColumnList,
      tabNum: {
        accept: 0,
        wait: 0,
        reject: 0
      }
    }
  },
  computed: {
    tabs() {
      return [
        { name: '业务员列表' }
      ]
    }
  },
  watch: {
    addSalesmanVisible(val) {
      if (!val) {
        this.salesmanForm = {
          phone: ''
        }
        this.salesmanOne = true
        this.salesmanTwo = false
        this.salesmanThree = false
      }
    }
  },
  methods: {
    checkPermission,
    async load(params) {
      const listQuery = {
        model: {
          ...this.model
        }
      }
      Object.assign(listQuery, params)
      return await salesmanPage(listQuery)
    },
    /**
     * @description 发送协议
     * <AUTHOR>
     */
    handleSendProtocol() {
      let list = [];
      this.multipleSelection.forEach(item => {
        list.push(item.id)
      })
      this.$confirm('您确定要给这些业务员发送协议吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          batchSendProtocol({ ids: [...list] }).then(res => {
            this.$message.success('发送成功!');
            this.searchLoad()
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消发送'
          });          
        });
    },
    // 新增业务员
    handleAdd() {
      this.$router.push({
        path: '/businessCentric/salesmanManage/detail',
        query: {
          type: 'add'
        }
      })
    },
    // table 选中
    onAllSelect(selection) {
      this.onSelect(selection)
    },
    onSelect(val) {
      this.multipleSelection = val
    },
    searchLoad() {
      this.handleRefresh({
        page: 1,
        pageSize: this.pageSize
      })
    },
    openCustomer(id) {
      this.$router.push({
        path: '/businessCentric/salesmanManage/businessCompany',
        query: {
          id
        }
      })
    },
    // 删除
    delSalesman(id) {
      deleteSalesman(id).then(res => {
        if (res.code == 0 && res.msg == 'ok') {
          this.$message.success('删除成功')
          this.reload()
        }
      })
    },
    // tab切换
    // handleChangeTab(tab) {
    //   this.model.tabCode = tab.value
    //   this.handleRefresh({
    //     page: 1,
    //     pageSize: this.pageSize
    //   })
    // },
    reload() {
      this.$refs['tabs-layout'].reset()
      // this.model = {
      //   phone: '',
      //   area: '',
      // }
      this.handleRefresh({
        page: 1,
        pageSize: this.pageSize
      })
    },
    handleRefresh(pageParams) {
      this.$refs['pager-table'].doRefresh(pageParams)
    },
    dealApprovalStatus(type) {
      let text = ''
      switch (type) {
        case 'UNVERIFIED':
          text = '未认证'
          break
        case 'NOT_PASS':
          text = '自动审核未通过'
          break
        case 'UNDER_REVIEW':
          text = '审核中'
          break
        case 'REJECTED':
          text = '已驳回'
          break
        case 'PASS':
          text = '通过'
          break
        case 'BE_ABOUT_TO_EXPIRE':
          text = '即将过期'
          break
        case 'EXPIRED':
          text = '已过期'
          break
      }
      return text
    },
    dealRole(type) {
      let text = ''
      switch (type) {
        case 'BOSS':
          text = '老板'
          break
        case 'LEADER':
          text = '领导'
          break
        case 'EMPLOYEE':
          text = '员工'
          break
      }
      return text
    },
    // 编辑或者查看
    handleEdit(id, type) {
      console.log('编辑或者查看', type)
      this.$router.push({
        path: '/businessCentric/salesmanManage/detail',
        query: {
          id,
          type
        }
      })
    }
  }
}

</script>

<style lang="scss" scoped>
.item_customer {
  display: flex;
  align-content: center;
  margin-top: 15px;
}
</style>
