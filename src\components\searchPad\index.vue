<!-- 搜索栏 -->
<template>
  <div class="dyx-search-pad">
    <el-form ref="searchForm" :inline="true" :model="model" style="font-size: 0">
      <slot/>
      <el-button type="primary" style="margin-left: 10px; width: 80px" @click="onSearch">搜索</el-button>
      <el-button  style="width: 80px" @click="onReset">重置</el-button>
    </el-form>
  </div>
</template>
<script>
  export default {
    name: 'SearchPad',
    props: {
      model: {
        type: Object,
        default: () => {
          return {}
        }
      },
      showReset: {
        type: Boolean,
        default: true,
      },
    },
    data() {
      return {}
    },
    methods: {
      onSearch() {
        this.$emit('search')
      },
      onReset() {
        this.$refs['searchForm'].resetFields()
        this.$emit('reset')
      }
    }
  }
</script>
<style lang="scss">
  .dyx-search-pad {
    padding-bottom: 0;

    .el-form-item {
      margin-bottom: 0;
    }
  }
</style>
