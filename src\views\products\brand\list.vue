<template>
  <div class="staffPageContent">
    <im-search-pad
      :has-expand="false"
      :model="listQuery"
      @reset="resetForm"
      @search="onSearchSubmitFun"
    >
      <im-search-pad-item prop="model.brandName">
        <el-input v-model="listQuery.model.brandName" placeholder="请输入品牌名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="model.manufacturer">
        <el-input v-model="listQuery.model.manufacturer" placeholder="输入厂家名称" />
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        :tabs="[{ name: '品牌管理', value: ''}]"
      >
        <template slot="button">
          <div>
            <el-button type="primary" @click="newFun"   v-if="checkPermission(['admin','admin-platformProduct-brand:add'])" >+新增品牌</el-button>
          </div>
        </template>
      </tabs-layout>
      <table-pager
        ref="pager-table"
        :options="tableTitle"
        :data.sync="list"
        :remoteMethod="getList"
        :operationWidth="110"
      >
        <template slot="brandLogo" slot-scope="{ row }">
          <el-popover placement="right" trigger="hover" style="margin: 0; padding: 0;" >
            <el-image
              style="width: 200px; height: 200px"
              fit="contain"
              :src="row.brandLogo | imgFilter"
            ></el-image>
            <el-image
              slot="reference"
              style="width: 50px; height: 50px"
              fit="cover"
              :src="row.brandLogo | imgFilter"
            ></el-image>
          </el-popover>
        </template>
        <template slot="joinProductCount" slot-scope="{ row }">
          <el-row v-if="checkPermission(['admin','admin-platformProduct-category:relatedProducts'])" class="table-edit-row">
            <span class="table-edit-row-item">
              <el-button type="text" @click="showJoinTableFun(row)">查看关联商品</el-button>
            </span>
          </el-row>
          <span v-else>{{ row[item.name] }}</span>
        </template>
        <div slot-scope="scope" v-if="checkPermission(['admin','admin-platformProduct-brand:edit','admin-platformProduct-brand:del'])">
          <el-row class="table-edit-row">
                <span v-if="checkPermission(['admin','admin-platformProduct-brand:edit'])" class="table-edit-row-item">
                  <el-button  @click="editClickFun(scope.row)" type="text" >编辑</el-button>
                </span>
            <span v-if="checkPermission(['admin','admin-platformProduct-brand:del'])" class="table-edit-row-item">
                  <el-button  slot="reference" type="text"  @click="showDeleteItemBoxFun(scope.row)">删除</el-button>
                </span>
          </el-row>
        </div>
      </table-pager>
    </div>
    <el-dialog :close-on-click-modal="false" v-if="showJoinTable" :title="'查看关联商品'" :visible.sync="showJoinTable" width="70%">
        <joinList :visible.sync="showJoinTable" :isReload.sync="submitReload" :row.sync="row"></joinList>
    </el-dialog>
    <el-dialog  :title="'删除品牌'" :visible.sync="showDeleteItemBox" width="350px" :close-on-click-modal="false">
        <div class="deleteItemDialog">
            <div class="deleteItemContent">
              <div class="deleteItemDetail">
                  请输入删除品牌名称，删除品牌后，品牌所绑定的产品自动解除绑定关系，请谨慎操作
              </div>
              <div class="checkDeleteItemName">
                  <el-input v-model="deleteItemName" placeholder="请输入品牌名称"></el-input>
              </div>
              <div class="deleteItembtns">
                <el-button  @click="showDeleteItemBox=false">取消</el-button>
                <el-button type="primary" @click="deleteItemFun()">确认</el-button>
              </div>
            </div>
        </div>
    </el-dialog>
  </div>
</template>
<script>
import {setContextData,getContextData} from '@/utils/auth'
import checkPermission from '@/utils/permission'
import { list,deleteApi } from '@/api/products/brand'
import joinList from '@/views/products/brand/joinList'
import Pagination from '@/components/Pagination'
import TabsLayout from '@/components/TabsLayout'
export default {
  components: {
    Pagination,
    TabsLayout,
    joinList
  },
  data() {
    return {
      showEdit:false,
      showJoinTable:false,
      row:{},
      tableTitle:[
        {
          key: 0,
          label: 'LOGO',
          name: 'brandLogo',
          width: '80px',
          columnSlot: true,
          align: 'center'
        },
        {
          key: 1,
          label:'品牌编码',
          name: "brandCode",
          width:"150px"
        },
        {
          key: 2,
          label:'品牌名称',
          name: 'brandName',
          width: '150px'
        },
        {
          key: 3,
          label:'关联商品',
          name: 'joinProductCount',
          width: '200px',
          columnSlot: true
        },
        {
          key: 4,
          label: '生产厂家',
          name: 'manufacturer',
          width: '250px'
        },
        {
          key: 5,
          label: '品牌故事',
          name: 'brandStory',
          width: '250px'
        },
        {
          key: 6,
          label: '品牌网站地址',
          name: 'brandAddr',
          width: '250px'
        }
      ],
      showDeleteItemBox:false,
      deleteItemName:"",
      submitReload:false,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        model:{
          brandName:'',
          manufacturer:''
        },
        current:1,
        size:10
      },

    };
  },
  watch:  {
    submitReload(newVal,oldVal)  {
      if(newVal){
        this.submitReload = false;
        this.$refs['pager-table'].doRefresh()
      }
    },
    showDeleteItemBox:function(newVal,oldVal){
      if(newVal === false){
        this.deleteItemName = '';
      }
    },
  },
  beforeRouteEnter (to, from, next) {
    next(vm => {
      if(from.path === '/productCenter/brand/edit'){
        vm.listQuery = getContextData('products_brand_list')
      }
      vm.$refs['pager-table'].doRefresh()
    })
  },
  methods: {
    checkPermission,
    newFun() {
      setContextData('products_brand_list', this.listQuery)
      this.$router.push({ path:'/productCenter/brand/edit' })
    },
    editClickFun(row) {
      setContextData('products_brand_list', this.listQuery)
      this.$router.push({
        path:'/productCenter/brand/edit',
        query: {
          id: row.id,
        }
      })
    },
    showJoinTableFun(row) {
        this.row = row
        this.showJoinTable = true
    },
    async getList(params) {
      Object.assign(this.listQuery, params)
      return await list(this.listQuery)
    },
    onSearchSubmitFun() {
      this.list = []
      this.$refs['pager-table'].doRefresh()
    },
    resetForm() {
      this.$refs['pager-table'].doRefresh()
    },
    showDeleteItemBoxFun(row){
      this.row = row;
      this.showDeleteItemBox = true;
    },
   async deleteItemFun(){
      if(this.row.brandName === this.deleteItemName){
          let res = await deleteApi(this.row.id);
          if(res.code === 0){
            this.list = []
            this.listQuery.current=1
            // this.getList()
            this.resetForm();
            this.showDeleteItemBox=false;
          }
      }else{
         this.$message.error('品牌名称核实错误!');
      }
    }
  },
  mounted() {
  },
  beforeDestroy() {}
};
</script>
<style lang="scss" scoped>
.deleteItemDetail {
  margin-bottom: 20px;
}
.checkDeleteItemName {
  margin-bottom: 20px;
}
.deleteItembtns {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
