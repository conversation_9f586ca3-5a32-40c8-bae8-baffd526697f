<template>
  <div v-if="show">
      <el-cascader placeholder="请选择经营类目" 
      :style="{width:width}"
      v-model="modelVal" 
      :options="treeDatas"   
      :show-all-levels="false" 
      :props="{ checkStrictly: true,emitPath:false }" 
      clearable></el-cascader>
  </div>
</template>
 

<script>
import { list,getAllParentCodeOrChildren } from "@/api/setting/businessCategory";
export default {
  data() {
    return {
      show:false,
      listQuery: {
          model:{
            // parentId:0,
            label:""
          },
        "order": "descending",
        // "size": 10,
        "sort": "id",
          // current:1,
          size:99999
      },
      treeDatas:[]
    };
  },
  props: {
    width:{
      type:String,
      default:"250px"
    },
    selectId:{
      type:String,
      default:"0",
      required: true,
    }
  },
  computed: {
    modelVal: {
      get() {
        return this.selectId
      },
      set(val) {
        this.$emit('update:selectId', val+"")
        // this.$emit('update:selectId', val[val.length-1]+"")
      }
    }
  },
  methods: {
    generateOptions(params) {//生成Cascader级联数据
        var result = [];
        for (let param of params) {
            if (param.parentId == "0") {//判断是否为顶层节点
                var parent = {
                    'label': param.label,
                    'value': param.id+""
                }
                parent.children = this.getchilds(param.id, params);//获取子节点
                result.push(parent);
            }
        }
        return result;
    },
    getchilds(id, array) {
        let childs = new Array();
        for (let arr of array) {//循环获取子节点
            if (arr.parentId == id) {
                childs.push({
                    'label': arr.label,
                    'value': arr.id+""
                });
            }
        }
        for (let child of childs) {//获取子节点的子节点
            let childscopy = this.getchilds(child.value, array);//递归获取子节点
            if (childscopy.length > 0) {
                child.children = childscopy;
            }
        }
        return childs;
    },
    // async reloadTreeNode(parentId){
    //     const { data } = await getAllParentCodeOrChildren(parentId);
        
    //     this.treeDatas=data
    // },
     async listFun(){
        const { data } = await list(this.listQuery);
        if(data!=null){
          this.treeDatas=this.generateOptions(data.records)
        }
       
    },
  },
  mounted() {
     this.show=true;
     this.listFun()
  },
  beforeDestroy() {}
};
</script>
<style lang="less" scoped></style>
