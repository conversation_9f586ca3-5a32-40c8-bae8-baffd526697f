<template>
  <el-dialog :title="type + '服务机构'" :visible.sync="visible" :close-on-click-modal="false" width="550px">
    <el-form ref="form" :model="form" label-width="98px" :rules="rules">
      <div class="sku_setting">
        <el-form-item label="机构名称：" prop="orgId">
          <el-select filterable v-model="form.orgId" placeholder="请选择">
            <el-option v-for="item in orgList" :key="item.orgId" :label="item.name" :value="item.orgId" />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="服务类型：">
          <el-input v-model="form.total" />
        </el-form-item> -->
        <el-form-item label="服务期限：" prop="timeRange">
          <el-date-picker
            v-model="form.timeRange"
            range-separator="-"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetimerange"
            start-placeholder="开始时间"
            :default-time="['00:00:00', '23:59:59']"
            unlink-panels
            style="width: 100%"
            end-placeholder="结束时间"
          />
        </el-form-item>
        <el-form-item label="服务内容：" prop="note">
          <el-input :rows="4" type="textarea" v-model="form.note" placeholder="请输入服务内容" />
        </el-form-item>
      </div>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="hide">取消</el-button>
      <el-button :loading="loading" type="primary" @click="confirm">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { modifyAgencyOrgInfo, getOrgInfo } from '@/api/dealerManagement'
const initForm = () => {
  return {
    id: '',
    agencyId: '',
    beginTime: '',
    orgId: '',
    timeRange: [],
    endTime: '',
    note: ''
  }
}

export default {
  name: 'editServiceOrg',
  data() {
    return {
      visible: false,
      loading: false,
      type: '',
      orgList: [],
      form: initForm(),
      rules: {
        orgId: [{ required: true, message: '请选择机构', trigger: 'change' }],
      }
    }
  },
  methods: {
    setOrgList() {
      getOrgInfo(this.$route.query.agencyId).then((res) => {
        if (res.code !== 0) return
        this.orgList = res.data
      })
    },
    show(row) {
      this.form = initForm()
      delete this.form.id
      if (this.$refs.form) this.$refs.form.clearValidate()
      this.setOrgList()
      this.type = '新增'
      if (row) {
        this.type = row.id ? '编辑' : '新增'
        Object.keys(this.form).forEach((key) => {
          this.$set(this.form, key, row[key])
        })
        this.$set(this.form, 'id', row.id)
        if (row.beginTime && row.endTime) this.form.timeRange = [row.beginTime, row.endTime]
      }
      this.visible = true
    },
    hide() {
      this.visible = false
    },
    getForm() {
      const { timeRange } = this.form
      const orgItem = this.orgList.find((item) => item.orgId === this.form.orgId)
      if (Array.isArray(timeRange) && timeRange.length > 0) {
        return {
          ...orgItem,
          ...this.form,
          beginTime: timeRange[0],
          endTime: timeRange[1],
          agencyId: this.$route.query['agencyId']
        }
      }
      return this.form
    },
    confirm() {
      this.$refs.form.validate((valid) => {
        if (!valid) return
        this.loading = true
        modifyAgencyOrgInfo(this.getForm())
          .then((res) => {
            if (res.code !== 0) return
            this.$message.success('修改成功')
            this.hide()
            this.$emit('setSuccess')
          })
          .finally(() => {
            this.loading = false
          })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.sku_setting {
  display: flex;
  flex-direction: column;
}
</style>
