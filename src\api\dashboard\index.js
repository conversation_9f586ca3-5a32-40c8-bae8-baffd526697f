import requestAxios from '@/utils/requestAxios'

export function index() {
  return requestAxios({
    url: "/api/authority/common/generateId",
    method: 'get',
  })
}

// 待办事项统计
export function getBacklog(commerceModelEnum) {
  return requestAxios({
    url: `/api/product/admin/home/<USER>
    method: 'get',
  })
}

/* 数据概况统计 */
// 支付
export function dataProfilePay(timeType, commerceModelEnum) {
  return new Promise((resolve, reject) => {
    requestAxios({
      url: `/api/product/admin/home/<USER>/${timeType}?commerceModelEnum=${commerceModelEnum}`,
      method: 'get',
    }).then(res => {
      if (res.isSuccess) {
        resolve(res.data)
      } else {
        reject(res)
      }
    }).catch(err => {
      reject(err)
    })
  })
}
// 商品
export function dataProfileProduct (timeType, commerceModelEnum) {
  return new Promise((resolve, reject) => {
    requestAxios({
      url: `/api/product/admin/home/<USER>/${timeType}?commerceModelEnum=${commerceModelEnum}`,
      method: 'get',
    }).then(res => {
      if (res.isSuccess) {
        resolve(res.data)
      } else {
        reject(res)
      }
    }).catch(err => {
      reject(err)
    })
  })
}
// 采购商
export function dataProfilePurMerchant(timeType, commerceModelEnum) {
  return new Promise((resolve, reject) => {
    requestAxios({
      url: `/api/product/admin/home/<USER>/${timeType}?commerceModelEnum=${commerceModelEnum}`,
      method: 'get',
    }).then(res => {
      if (res.isSuccess) {
        resolve(res.data)
      } else {
        reject(res)
      }
    }).catch(err => {
      reject(err)
    })
  })
}
// 折线图
export function dataProfileLineChart (timeType, commerceModelEnum) {
  return new Promise((resolve, reject) => {
    requestAxios({
      url: `/api/product/admin/home/<USER>/${timeType}?commerceModelEnum=${commerceModelEnum}`,
      method: 'get',
    }).then(res => {
      if (res.isSuccess) {
        resolve(res.data)
      } else {
        reject(res)
      }
    }).catch(err => {
      reject(err)
    })
  })
}

// 商品看板统计
export function goodsCard(commerceModelEnum) {
  return requestAxios({
    url: `/api/product/admin/home/<USER>/info?commerceModelEnum=${commerceModelEnum}`,
    method: 'get',
  })
}

// 订单看板图
export function spectaculars(data, commerceModelEnum) {
  return requestAxios({
    url: `/api/order/admin/orderHome/spectaculars?commerceModelEnum=${commerceModelEnum}`,
    method: 'post',
    data: {
      endTime: data.endTime + ' 23:59:59',
      naturalWeek: data.naturalWeek,
      orderStatus: data.orderStatus,
      startTime: data.startTime + ' 00:00:00',
    }
  })
}

// 商品看板列表
export function productList(data) {
  return requestAxios({
    url: `/api/order/admin/orderHome/product/list?commerceModelEnum=SAAS_PLATFORM`,
    method: 'post',
    data
  })  
}

// 客户看板列表
export function merchantData(commerceModelEnum) {
  return requestAxios({
    url: `/api/merchant/admin/merchantHome/spectaculars?commerceModelEnum=${commerceModelEnum}`,
    method: 'post',
    data: {}
  })
}
// 客户消费排行
export function merchantList(data, commerceModelEnum) {
  return requestAxios({
    url: `/api/merchant/admin/merchantHome/consumerRankings?commerceModelEnum=${commerceModelEnum}`,
    method: 'post',
    data
  })
}

// 商品报表详情
export function productDetail(id,data) {
  return requestAxios({
    url: `/api/order/admin/orderHome/product/${id}`,
    method: 'get',
    params: data
  })
}

// 商品报表详情走势
export function productLine(id,data) {
  return requestAxios({
    url: `/api/order/admin/orderHome/product/trend/${id}`,
    method: 'post',
    data
  })
}

// 商品报表详情列表
export function productDetailList(id,data) {
  return requestAxios({
    url: `/api/order/admin/orderHome/product/purMerchantAnalyse/${id}`,
    method: 'post',
    data
  })
}

// 商品区域展示全国
export function getAllArea(id,data) {
  return requestAxios({
    url: `/api/order/admin/orderHome/product/areaAnalysis-nationwide/${id}`,
    method: 'post',
    data
  })
}
// 商品区域展示省
export function getItemArea(id,data) {
  return requestAxios({
    url: `/api/order/admin/orderHome/product/areaAnalysis-province/${id}`,
    method: 'post',
    data
  })
}


// export function productDetail(id,data) {
//   console.log(data)
//   return requestAxios({
//     url: `/api/order/admin/orderHome/product/trend/${id}`,
//     method: 'post',
//     data
//   })
// }


