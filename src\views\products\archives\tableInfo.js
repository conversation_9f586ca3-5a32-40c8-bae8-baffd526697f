export default {
  PENDING: [{
      key: 0,
      label: '产品主图',
      name: "pictIdS",
      width: '80px',
      columnSlot: true
    },
    {
      key: 1,
      label: '产品编码',
      name: "productCode",
      width: '150px',
      disabled: true
    },
    {
      key: 2,
      label: '产品名称',
      name: "productName",
      width: '200px',
      disabled: true
    },
    {
      key: 3,
      label: '规格',
      name: "spec",
      width: '150px',
      disabled: true
    },
    {
      key: 4,
      label: '批准文号',
      name: "approvalNumber",
      width: '150px'
    },
    {
      key: 5,
      label: '剂型',
      name: "agentiaType",
      width: '120px'
    },
    {
      key: 98,
      label: '产地',
      name: "area",
      width: '120px'
    },
    {
      key: 6,
      label: '生产厂家',
      name: "manufacturer",
      width: '120px'
    },
    {
      key: 7,
      label: '经营类目',
      name: "businessRangeName",
      width: '200px'
    },
    {
      key: 8,
      label: '产品分类',
      name: "categoryPathName",
      width: '200px'
    },
    {
      key: 9,
      label: '产品条形码',
      name: "barCode",
      width: '180px'
    },
    {
      key: 99,
      label: '操作时间',
      name: "updateTime",
      width: '180px'
    }
  ],
  ACCEPTED: [{
      key: 0,
      label: '产品主图',
      name: "pictIdS",
      width: '80px',
      columnSlot: true
    },
    {
      key: 1,
      label: '产品编码',
      name: "productCode",
      width: '120px',
      disabled: true
    },
    {
      key: 2,
      label: '产品名称',
      name: "productName",
      width: '200px',
      disabled: true
    },
    {
      key: 3,
      label: '规格',
      name: "spec",
      width: '150px',
      disabled: true
    },
    {
      key: 4,
      label: '批准文号',
      name: "approvalNumber",
      width: '150px'
    },
    {
      key: 5,
      label: '剂型',
      name: "agentiaType",
      width: '120px'
    },
    {
      key: 6,
      label: '产地',
      name: "area",
      width: '120px'
    },
    {
      key: 7,
      label: '生产厂家',
      name: "manufacturer",
      width: '120px'
    },
    {
      key: 8,
      label: '经营类目',
      name: "businessRangeName",
      width: '200px'
    },
    {
      key: 9,
      label: '产品分类',
      name: "categoryPathName",
      width: '200px'
    },
    {
      key: 10,
      label: '产品条形码',
      name: "barCode",
      width: '180px'
    },
    {
      key: 11,
      label: '渠道来源',
      name: "saleMerchantName",
      width: '180px'
    },
    {
      key: 12,
      label: '审批人',
      name: "approvalUserName",
      width: '180px'
    },
    {
      key: 13,
      label: '审批时间',
      name: "approvalTime",
      width: '200px'
    },
    {
      key: 99,
      label: '操作时间',
      name: "updateTime",
      width: '180px'
    }
  ],

  REJECTED: [{
      key: 0,
      label: '产品主图',
      name: "pictIdS",
      width: '80px',
      columnSlot: true
    },
    {
      key: 1,
      label: '产品编码',
      name: "productCode",
      width: '150px',
      disabled: true
    },
    {
      key: 2,
      label: '产品名称',
      name: "productName",
      width: '200px',
      disabled: true
    },
    {
      key: 3,
      label: '规格',
      name: "spec",
      width: '150px',
      disabled: true
    },
    {
      key: 4,
      label: '批准文号',
      name: "approvalNumber",
      width: '150px'
    },
    {
      key: 5,
      label: '剂型',
      name: "agentiaType",
      width: '120px'
    },
    {
      key: 98,
      label: '产地',
      name: "area",
      width: '120px'
    },
    {
      key: 6,
      label: '生产厂家',
      name: "manufacturer",
      width: '120px'
    },
    {
      key: 7,
      label: '经营类目',
      name: "businessRangeName",
      width: '200px'
    },
    {
      key: 8,
      label: '产品分类',
      name: "categoryPathName",
      width: '200px'
    },
    {
      key: 9,
      label: '产品条形码',
      name: "barCode",
      width: '180px'
    },
    {
      key: 10,
      label: '审批人',
      name: "approvalUserName",
      width: '180px'
    },
    {
      key: 11,
      label: '审批时间',
      name: "approvalTime",
      width: '200px'
    },
    {
      key: 99,
      label: '操作时间',
      name: "updateTime",
      width: '180px'
    }
  ],

  SITE: [{
      key: 0,
      label: '账号状态',
      name: "publishStatus.code",
      width: '150px',
      prop: "publishStatus.code",
      slot: true,
    },
    {
      key: 1,
      label: '站点编号',
      name: "siteNum",
      width: '200px',
      prop: "siteNum",
      slot: true,
    },
    {
      key: 2,
      label: '站点名称',
      name: "siteName",
      width: '200px',
      disabled: true
    },
    {
      key: 3,
      label: '负责人姓名',
      name: "managerName",
      width: '200px',
      disabled: true
    },
    {
      key: 4,
      label: '负责人手机',
      name: "managerMobile",
      width: '200px',
      disabled: true
    },
    {
      key: 5,
      label: '登录账户',
      name: "account",
      width: '200px',
      disabled: true
    },
    {
      key: 6,
      label: '注册手机号',
      name: "userMobile",
      width: '200px',
      disabled: true
    },
    {
      key: 7,
      label: '站点区域',
      name: "provinceIds",
      width: '200px',
      prop: "provinceIds",
      slot: true,
    },
    {
      key: 8,
      label: '创建时间',
      name: "createTime",
      width: '200px',
      disabled: true
    },
  ],
  packageList: [{
    "id": 0,
    "label": "盒",
    "value": "盒"
  }, {
    "id": 1,
    "label": "包",
    "value": "包"
  }, {
    "id": 2,
    "label": "瓶",
    "value": "瓶"
  }, {
    "id": 3,
    "label": "支",
    "value": "支"
  }, {
    "id": 4,
    "label": "贴",
    "value": "贴"
  }, {
    "id": 5,
    "label": "件",
    "value": "件"
  }, {
    "id": 6,
    "label": "箱",
    "value": "箱"
  }, {
    "id": 7,
    "label": "套",
    "value": "套"
  }, {
    "id": 8,
    "label": "条",
    "value": "条"
  }, {
    "id": 9,
    "label": "片",
    "value": "片"
  }, {
    "id": 10,
    "label": "大盒",
    "value": "大盒"
  }, {
    "id": 11,
    "label": "中盒",
    "value": "中盒"
  }, {
    "id": 12,
    "label": "小盒",
    "value": "小盒"
  }, {
    "id": 13,
    "label": "袋",
    "value": "袋"
  }, {
    "id": 14,
    "label": "小袋",
    "value": "小袋"
  }, {
    "id": 15,
    "label": "小包",
    "value": "小包"
  }, {
    "id": 16,
    "label": "罐",
    "value": "罐"
  }, {
    "id": 17,
    "label": "个",
    "value": "个"
  }, {
    "id": 18,
    "label": "碗",
    "value": "碗"
  }, {
    "id": 19,
    "label": "付",
    "value": "付"
  }, {
    "id": 20,
    "label": "块",
    "value": "块"
  }, {
    "id": 21,
    "label": "头",
    "value": "头"
  }, {
    "id": 22,
    "label": "台",
    "value": "台"
  }, {
    "id": 23,
    "label": "件",
    "value": "件"
  }, {
    "id": 24,
    "label": "次",
    "value": "次"
  }, {
    "id": 25,
    "label": "对",
    "value": "对"
  }, {
    "id": 26,
    "label": "只",
    "value": "只"
  }, {
    "id": 27,
    "label": "管",
    "value": "管"
  }, {
    "id": 28,
    "label": "本",
    "value": "本"
  }, {
    "id": 29,
    "label": "条",
    "value": "条"
  }, {
    "id": 30,
    "label": "桶",
    "value": "桶"
  }, {
    "id": 31,
    "label": "中",
    "value": "中"
  }, {
    "id": 32,
    "label": "KG",
    "value": "KG"
  }, {
    "id": 33,
    "label": "G",
    "value": "G"
  }, {
    "id": 34,
    "label": "克",
    "value": "克"
  }, {
    "id": 35,
    "label": "粒",
    "value": "粒"
  }, {
    "id": 36,
    "label": "合",
    "value": "合"
  }, {
    "id": 37,
    "label": "把",
    "value": "把"
  }, {
    "id": 38,
    "label": "根",
    "value": "根"
  }, {
    "id": 39,
    "label": "抽",
    "value": "抽"
  }, {
    "id": 40,
    "label": "卷",
    "value": "卷"
  }, {
    "id": 41,
    "label": "提",
    "value": "提"
  }, {
    "id": 42,
    "label": "筒",
    "value": "筒"
  }, {
    "id": 43,
    "label": "片",
    "value": "片"
  }, {
    "id": 44,
    "label": "册",
    "value": "册"
  }, {
    "id": 45,
    "label": "张",
    "value": "张"
  }, {
    "id": 46,
    "label": "箱",
    "value": "箱"
  }, {
    "id": 47,
    "label": "其它",
    "value": "其它"
  }],
  dosageList: [{
    "id": "1",
    "label": "请选择",
    "value": ""
  }, {
    "id": "2",
    "label": "片剂",
    "value": "片剂"
  }, {
    "id": "3",
    "label": "颗粒剂",
    "value": "颗粒剂"
  }, {
    "id": "4",
    "label": "胶囊剂",
    "value": "胶囊剂"
  }, {
    "id": "5",
    "label": "膏剂",
    "value": "膏剂"
  }, {
    "id": "6",
    "label": "软膏剂",
    "value": "软膏剂"
  }, {
    "id": "7",
    "label": "洗剂",
    "value": "洗剂"
  }, {
    "id": "8",
    "label": "中药饮片",
    "value": "中药饮片"
  }, {
    "id": "9",
    "label": "喷剂",
    "value": "喷剂"
  }, {
    "id": "10",
    "label": "溶液剂",
    "value": "溶液剂"
  }, {
    "id": "11",
    "label": "注射剂",
    "value": "注射剂"
  }, {
    "id": "12",
    "label": "水剂",
    "value": "水剂"
  }, {
    "id": "13",
    "label": "粉剂",
    "value": "粉剂"
  }, {
    "id": "14",
    "label": "冻干粉",
    "value": "冻干粉"
  }, {
    "id": "15",
    "label": "栓剂",
    "value": "栓剂"
  }, {
    "id": "16",
    "label": "油剂",
    "value": "油剂"
  }, {
    "id": "17",
    "label": "锭剂",
    "value": "锭剂"
  }, {
    "id": "18",
    "label": "搽剂",
    "value": "搽剂"
  }, {
    "id": "19",
    "label": "乳剂",
    "value": "乳剂"
  }, {
    "id": "20",
    "label": "胶剂",
    "value": "胶剂"
  }, {
    "id": "21",
    "label": "中药饮片",
    "value": "中药饮片"
  }, {
    "id": "22",
    "label": "粉吸入剂",
    "value": "粉吸入剂"
  }, {
    "id": "23",
    "label": "混悬剂",
    "value": "混悬剂"
  }, {
    "id": "24",
    "label": "糊剂",
    "value": "糊剂"
  }, {
    "id": "25",
    "label": "滴鼻剂",
    "value": "滴鼻剂"
  }, {
    "id": "26",
    "label": "溶胶",
    "value": "溶胶"
  }, {
    "id": "27",
    "label": "酒剂",
    "value": "酒剂"
  }, {
    "id": "28",
    "label": "溶胶剂",
    "value": "溶胶剂"
  }, {
    "id": "29",
    "label": "糖浆剂",
    "value": "糖浆剂"
  }, {
    "id": "30",
    "label": "散剂",
    "value": "散剂"
  }, {
    "id": "31",
    "label": "芳香水剂",
    "value": "芳香水剂"
  }, {
    "id": "32",
    "label": "气雾剂",
    "value": "气雾剂"
  }, {
    "id": "33",
    "label": "其他",
    "value": "其他"
  }]
}
