<template>
  <div class="goodsContain">
    <div class="goodsDetail" v-loading="detailLoading">
      <div class="title">
        <div class="title_ms">
          <img
            :src="goods.productPath | imgFilter"
            style="width: 40px; height: 40px"
            alt=""
          />
          <span>{{ goods.productName }}</span>
        </div>
        <div>规格： {{ goods.spec }}</div>
        <div>生产厂家：{{ goods.manufacturer }}</div>
        <selectTime
          @getDetail="getAll"
          :orderTypes="false"
          :Query.sync="query"
        ></selectTime>
      </div>
      <div class="ms">
        <div class="item">
          <div>客户数量</div>
          <div>{{ goods.purMerchantCount }}</div>
        </div>

        <div class="item">
          <div>区域数量（市）</div>
          <div>{{ goods.cityCount }}</div>
        </div>

        <div class="item">
          <div>销售数量</div>
          <div>{{ goods.salesQuantity }}</div>
        </div>

        <div class="item">
          <div>销售金额（元）</div>
          <div>{{ goods.salesAmount }}</div>
        </div>
      </div>
    </div>

    <div class="goodsDetail">
      <div class="title">
        <div class="title_ms"><span>客户分析</span></div>
        <div class="togtap">
          <el-radio @change="getlist" v-model="togTab" label="1"
            >按销售数量排行</el-radio
          >
          <el-radio @change="getlist" v-model="togTab" label="2"
            >按销售金额排行</el-radio
          >
        </div>
      </div>
      <div class="table">
        <el-table
          ref="table"
          v-if="list"
          v-loading="listLoading"
          :data="list"
          row-key="id"
          border
          fit
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column align="center" width="80" label="排名">
            <template slot-scope="scope">
              <span>{{ scope.$index + 1 }} </span>
            </template>
          </el-table-column>
          <el-table-column
            v-for="(item, index) in tableTitle"
            :key="index"
            :min-width="item.width ? item.width : '350px'"
            :label="item.label"
            show-overflow-tooltip
            align="left"
          >
            <template slot-scope="{ row }">
              <span>{{ row[item.name] }}</span>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="listQuery.current"
          :limit.sync="listQuery.size"
          @pagination="getlist"
        />
      </div>
    </div>

    <div class="goodsDetail" v-loading="mapLoading">
      <div class="title">
        <div class="title_ms"><span>区域分析</span></div>
      </div>
      <div>
        <el-button type="text" @click="resetMap">全国</el-button>
        <template v-if="mapType != 'china'">
          /<el-button type="text" disabled>{{ itemName }}</el-button></template
        >
      </div>
      <div class="mapContain">
        <div ref="mapChart" :style="{ width: '800px', height: '600px' }"></div>
        <div class="table">
          <el-table
            ref="table"
            v-if="mapChartData"
            :data="mapChartData"
            row-key="id"
            border
            fit
            highlight-current-row
          >
            <el-table-column align="center" width="80" label="排名">
              <template slot-scope="scope">
                <span>{{ scope.$index + 1 }} </span>
              </template>
            </el-table-column>
            <el-table-column
              v-for="(item, index) in mapTableInfo"
              :key="index"
              :min-width="item.width ? item.width : '350px'"
              :label="item.label"
              show-overflow-tooltip
              align="left"
            >
              <template slot-scope="{ row }">
                <span>{{ row[item.name] }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <div class="goodsDetail">
      <div class="title">
        <div class="title_ms"><span>销售走势分析</span></div>
      </div>
      <div>
        <div
          ref="macarons"
          class="macarons"
          :style="{ height: '300px', width: '100%' }"
        />
      </div>
    </div>
  </div>
</template>

<script>
var provinces = {
  // 23个省
  台湾: "taiwan",
  河北: "hebei",
  山西: "shanxi",
  辽宁: "liaoning",
  吉林: "jilin",
  黑龙江: "heilongjiang",
  江苏: "jiangsu",
  浙江: "zhejiang",
  安徽: "anhui",
  福建: "fujian",
  江西: "jiangxi",
  山东: "shandong",
  河南: "henan",
  湖北: "hubei",
  湖南: "hunan",
  广东: "guangdong",
  海南: "hainan",
  四川: "sichuan",
  贵州: "guizhou",
  云南: "yunnan",
  陕西: "shanxi1",
  甘肃: "gansu",
  青海: "qinghai",
  // 5个自治区
  新疆: "xinjiang",
  广西: "guangxi",
  内蒙古: "neimenggu",
  宁夏: "ningxia",
  西藏: "xizang",
  // 4个直辖市
  北京: "beijing",
  天津: "tianjin",
  上海: "shanghai",
  重庆: "chongqing",
  // 2个特别行政区
  香港: "xianggang",
  澳门: "aomen",
};
var provinceCode = {
  // 23个省
  台湾: "710000",
  河北: "130000",
  山西: "140000",
  辽宁: "210000",
  吉林: "220000",
  黑龙江: "230000",
  江苏: "320000",
  浙江: "330000",
  安徽: "340000",
  福建: "350000",
  江西: "360000",
  山东: "370000",
  河南: "410000",
  湖北: "420000",
  湖南: "430000",
  广东: "440000",
  海南: "460000",
  四川: "510000",
  贵州: "520000",
  云南: "530000",
  陕西: "610000",
  甘肃: "620000",
  青海: "630000",
  // 5个自治区
  新疆: "650000",
  广西: "450000",
  内蒙古: "150000",
  宁夏: "640000",
  西藏: "540000",
  // 4个直辖市
  北京: "110000",
  天津: "120000",
  上海: "310000",
  重庆: "500000",
  // 2个特别行政区
  香港: "810000",
  澳门: "820000",
};
const tableInfo = [
  { label: "客户名称", name: "purMerchantName", width: "170px" },
  { label: "销售数量", name: "salesQuantity", width: "100px" },
  { label: "销售金额", name: "salesAmount", width: "100px" },
  { label: "销售占比", name: "salesProportion", width: "120px" },
];
const mapTableInfo = [
  { label: "城市", name: "name", width: "170px" },
  { label: "销售数量", name: "salesQuantity", width: "100px" },
  { label: "销售金额", name: "salesAmount", width: "100px" },
  { label: "销售占比", name: "value", width: "120px" },
];
require("echarts/theme/macarons"); // echarts theme
import echarts from "echarts";
import china from "echarts/map/json/china.json";
import resize from "@/views/dashboard/admin/components/mixins/resize";
import Pagination from "@/components/Pagination";
import axios from "axios";
import {
  productDetail,
  productLine,
  productDetailList,
  getAllArea,
  getItemArea,
} from "@/api/dashboard/index";
import selectTime from "@/views/dashboard/cards/components/selectTime";
export default {
  mixins: [resize],
  data() {
    return {
      query: {},
      togTab: "1",
      list: [],
      tableTitle: tableInfo,
      mapTableInfo,
      total: 10,
      listQuery: {
        current: 1,
        size: 10,
        model: {},
      },
      goods: {},
      detailLoading: false,
      listLoading: false,
      chart: null,
      mapChart: null,
      chartDatas: {},
      mapChartData: [],
      mapType: "china",
      mapData: china,
      mapLoading: false,
      itemName: "",
    };
  },
  methods: {
    getAll() {
      this.getDetails();
      this.getProductLine();
      this.getlist();
      this.getPersent();
    },
    async getPersent() {
      this.mapLoading = true;
      let { data } = await getAllArea(this.$route.query.id, {
        endTime: this.query.endTime + " 23:59:59",
        startTime: this.query.startTime + " 00:00:00",
      });
      this.mapLoading = false;
      data.forEach((item) => {
        item.value = parseInt(item.value);
      });
      console.log(data);
      this.mapChartData = data;
      this.initMapChart();
    },
    async getDetails() {
      this.detailLoading = true;
      let { data } = await productDetail(this.$route.query.id, {
        endTime: this.query.endTime + " 23:59:59",
        startTime: this.query.startTime + " 00:00:00",
      });
      this.detailLoading = false;
      this.goods = data;
    },
    async getlist() {
      this.listLoading = true;
      this.listQuery.model = {
        endTime: this.query.endTime + " 23:59:59",
        salesMoney: this.togTab == "2" ? "Y" : "N",
        salesQuantity: this.togTab == "1" ? "Y" : "N",
        startTime: this.query.startTime + " 00:00:00",
      };
      let { data } = await productDetailList(
        this.$route.query.id,
        this.listQuery
      );
      this.listLoading = false;
      this.list = data.records;
    },
    async getProductLine() {
      let { data } = await productLine(this.$route.query.id, {
        endTime: this.query.endTime + " 23:59:59",
        startTime: this.query.startTime + " 00:00:00",
      });
      this.chartData = data[0];
      this.initChart();
    },
    resetMap() {
      this.mapData = china;
      this.mapType = "china";
      this.getPersent();
    },
    async provincesData(id) {
      this.mapLoading = true;
      let { data } = await getItemArea(this.$route.query.id, {
        endTime: this.query.endTime + " 23:59:59",
        provinceId: id,
        startTime: this.query.startTime + " 00:00:00",
      });
      this.mapLoading = false;
      this.mapChartData = data;
      this.setMapOptions();
    },
    initMapChart() {
      this.mapChart = echarts.init(this.$refs.mapChart, "mapChart");
      echarts.registerMap("china", china);
      this.setMapOptions();
      this.mapChart.on("click", (param) => {
        this.mapChart.off();
        if (param.name in provinces) {
          let names = param.name;
          this.itemName = names;
          for (let key in provinces) {
            if (names == key) {
              axios
                .get("/js/" + provinces[param.name] + ".json")
                .then((res) => {
                  this.mapType = provinces[param.name];
                  this.mapData = res.data;
                  this.provincesData(provinceCode[param.name]);
                  echarts.registerMap(provinces[param.name], res.data);
                });
            }
          }
        }
      });
    },
    setMapOptions() {
      let that = this;
      this.mapChart.setOption({
        tooltip: {
          // 鼠标移到图里面的浮动提示框
          // formatter详细配置： https://echarts.baidu.com/option.html#tooltip.formatter
          formatter(params, ticket, callback) {
            // console.log(params);
            let value;
            if (params.data) {
              value = params.data.value;
            } else {
              // 为了防止没有定义数据的时候报错写的
              value = 0;
            }
            let htmlStr = `
              <div style='font-size:18px;'> ${params.name}</div>
              <p style='text-align:left;margin-top:-4px;'>
                  销售占比：${value}<br/>
              </p>
            `;
            return htmlStr;
          },
          backgroundColor: "#ff7f50", //提示标签背景颜色
          textStyle: { color: "#fff" }, //提示标签字体颜色
        },
        legend: {
          orient: "vertical",
          left: "left",
          data: [""],
        },
        visualMap: {
          type: "piecewise",
          left: "1%",
          min: 0,
          max: 100,
          dimensions: [],
          top: "bottom",
          text: ["高", "低"],
          calculable: true,
          color: ["#0b50b9", "#c3e2f4"],
        },
        selectedMode: "single",
        series: [
          {
            name: "",
            type: "map",
            mapType: that.mapType,
            itemStyle: {
              normal: {
                borderColor: "rgba(0, 0, 0, 0.2)",
              },
              emphasis: {
                shadowOffsetX: 0,
                shadowOffsetY: 0,
                shadowBlur: 20,
                borderWidth: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
            showLegendSymbol: false,
            label: {
              normal: {
                show: false,
              },
              emphasis: {
                show: true,
              },
            },
            data: that.mapChartData,
          },
        ],
      });
    },
    initChart() {
      this.chart = echarts.init(this.$refs.macarons, "macarons");
      this.setOptions(this.chartData);
    },
    setOptions({ salesAmountList, salesQuantityList, time } = {}) {
      let that = this;
      this.chart.setOption({
        tooltip: {
          trigger: "axis",
        },
        legend: {
          data: ["销售金额", "销售数量"],
          left: "0",
        },
        color: ["#FF6E1B", "#0056E5"],
        icon: "circle",
        grid: {
          left: "3%",
          right: "3%",
          bottom: "14",
          top: "54",
          containLabel: true,
        },
        toolbox: {
          feature: {
            saveAsImage: {
              show: false, //控制保存按钮显示隐藏
            },
          },
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: time,
        },
        yAxis: [
          {
            type: "value",
            name: "销售金额",
            min: 0,
            position: "left", // y轴在左侧
            axisLine: {
              // y轴的颜色和按y轴刻度画的曲线的颜色
              show: false,
              lineStyle: {
                color: "#7C8492",
              },
            },
            axisTick: { show: false }, // 设置y轴上的刻度
            splitLine: { show: false }, // 设置坐标中的虚线
            axisLabel: {
              formatter: "{value}",
            },
          },
          {
            type: "value",
            name: "销售数量",
            min: 0,
            position: "right",
            axisTick: { show: false },
            // y轴的颜色和按y轴刻度画的曲线的颜色
            axisLine: {
              show: false,
              lineStyle: {
                color: "#7C8492",
              },
            },
            axisLabel: {
              color: "#7C8492",
              fontSize: 12,
              formatter: function (value, index) {
                // var value;
                // if (value >= 1000) {
                //   value = value / 1000 + "k";
                // } else if (value < 1000) {
                //   value = value;
                // }
                var value;
                if (value % 1 == 0) {
                  return value;
                } else {
                  return "";
                }
              },
            },
          },
        ],
        series: [
          {
            name: "销售金额",
            type: "line",
            smooth: false,
            data: salesAmountList,
            yAxisIndex: 0,
            yAxis: "金额",
          },
          {
            name: "销售数量",
            type: "line",
            yAxisIndex: 1,
            smooth: false,
            data: salesQuantityList,
          },
        ],
      });
    },
  },
  mounted() {

  },
  components: {
    selectTime,
    Pagination,
  },
};
</script>

<style lang="scss" scoped>
.goodsContain {
  background-color: #f2f3f4;
  .goodsDetail:nth-of-type(1) {
    margin-top: 0;
  }
  .goodsDetail {
    margin-top: 10px;
    background-color: #fff;
    padding: 20px 15px;
    border-radius: 4px;
    .mapContain {
      display: flex;
      .table {
        flex: 1;
      }
    }
    .title {
      margin-bottom: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;
      color: #1e2439;
      .title_ms {
        font-size: 20px;
        font-weight: 700;
        display: flex;
        align-items: center;
        color: #333;
        span {
          padding-left: 10px;
          display: inline-block;
          max-width: 300px;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
      }
    }
    .ms {
      padding: 20px;
      border-radius: 5px;
      background-color: #f7f7f8;
      display: flex;
      justify-content: space-between;
      .item {
        color: #1e2439;
        font-size: 14px;
        min-width: 300px;
        div:nth-of-type(2) {
          font-size: 20px;
          padding-top: 4px;
          color: #333;
          font-weight: 600;
        }
      }
    }
  }
}
</style>
