<template>
  <!-- 修改 -->
  <div class="archivesEditContent">
    <div class="top_title flex_between_center">
      <div>商家信息</div>
      <div>
        <el-popover v-model.trim="rejectFlag" placement="bottom-end" title="取消提醒" width="300" trigger="click">
          <el-button slot="reference">取消</el-button>
          确定取消编辑?取消后编辑内容将不被保存!
          <div style="text-align: right; margin: 0;padding-top:14px">
            <el-button size="mini" @click="rejectFlag = false">取消</el-button>
            <el-button type="primary" size="mini" @click="clearFun()">确定</el-button>
          </div>
        </el-popover>
        <el-button :loading="loading" v-if="checkPermission(['admin', 'business-qualification:submit'])" @click="edit('editForm')" type="primary">提交</el-button>
      </div>
    </div>
    <el-form :inline="true" label-width="140px" :model="query" ref="editForm" :rules="rules">
      <div class="item">
        <div class="title"><span>基础信息</span></div>
        <div>
          <el-form-item class="formItem" prop="commerceModel" label="商家类型:" :rules="[
              { required: true, message: '请选择商家类型', trigger: 'change' }
            ]">
            <el-select :disabled="queryId" v-model="query.commerceModel" clearable placeholder="请选择商家类型">
              <el-option v-for="item in merchantTypes" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item class="formItem" prop="code" label="商家编码:">
            <el-input disabled clearable style="width: 200px" v-model.trim="query.code" placeholder="商家编码由系统生成"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="name" label="商家名称:" :rules="[
              { required: true, message: '请填写商家名称', trigger: 'blur' }
            ]">
            <el-input clearable style="width: 200px" v-model.trim="query.name" placeholder="请填写商家名称"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="identifyCode" label="商家识别码:" :rules="[
              {
                required: true,
                min:4,
                max:4,
                message: '请输入4位客户识别码，如： ZKYY',
                trigger: 'blur',
              },
            ]">
            <el-input clearable style="width: 200px" v-model.trim="query.identifyCode" placeholder="请填写商家识别码"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="socialCreditCode" label="社会统一信用代码:">
            <el-input clearable style="width: 200px" v-model.trim="query.socialCreditCode" placeholder="请填写社会统一信用代码"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="legalPerson" label="法定代表人:" :rules="[
              { required: false, message: '请填写法定代表人', trigger: 'blur' },
            ]">
            <el-input clearable style="width: 200px" v-model.trim="query.legalPerson" placeholder="请填写法定代表人"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="ceoName" label="负责人:" :rules="[
              { required: true, message: '请填写负责人', trigger: 'blur' },
            ]">
            <el-input clearable style="width: 200px" v-model.trim="query.ceoName" placeholder="请填写负责人"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="ceoMobiles" label="负责人手机:">
            <el-input clearable style="width: 200px" v-model.trim="query.ceoMobile" placeholder="请填写负责人手机"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="regionId" label="所在区域:" :rules="[
              { required: true, message: '请选择所在区域', trigger: 'blur' },
            ]">
            <el-cascader ref="city" v-model.trim="query.regionId" style="width: 200px" placeholder="请选择所在区域" :props="{ value: 'id', label: 'label'}" @change="cityChange" :options="areasTree" clearable>
            </el-cascader>
          </el-form-item>
          <el-form-item class="formItem" prop="registerAddress" label="注册地址:" :rules="[
              { required: true, message: '请填写注册地址', trigger: 'blur' },
            ]">
            <el-input clearable style="width: 200px" v-model.trim="query.registerAddress" placeholder="请填写注册地址"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="registerCapital" label="注册资金:">
            <el-input style="width: 200px" type="number" v-model.trim="query.registerCapital" placeholder="请填写注册资金">
              <b slot="suffix">万</b>
            </el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="shopName" label="店铺名称:" :rules="[
              { required: true, message: '请填写店铺名称', trigger: 'blur' },
            ]">
            <el-input clearable style="width: 200px" v-model.trim="query.shopName" placeholder="请填写店铺名称"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="managementStartDate" label="经营开始时间:" :rules="[
              {
                required: true,
                message: '请选择经营开始时间',
                trigger: 'blur',
              },
            ]">
            <el-date-picker v-model.trim="query.managementStartDate" type="datetime" style="width: 200px" placeholder="选择日期" value-format="yyyy-MM-dd HH:mm:ss">
            </el-date-picker>
          </el-form-item>

          <el-form-item class="formItem" prop="managementEndDate" label="经营结束时间:" :rules="[
              {
                required: true,
                message: '请选择经营结束时间',
                trigger: 'blur',
              },
            ]">
            <el-date-picker v-model.trim="query.managementEndDate" type="datetime" style="width: 200px" placeholder="选择日期" value-format="yyyy-MM-dd HH:mm:ss">
            </el-date-picker>
          </el-form-item>
        </div>
      </div>
      <div class="item">
        <div class="title"><span>账户信息</span></div>
        <div>
          <el-form-item class="formItem" prop="loginAccount" label="登录账号:" :rules="[
              { required: true, message: '请填写登录账号', trigger: 'blur' },
            ]">
            <el-input clearable style="width: 200px" :disabled="queryId" v-model.trim="query.loginAccount" placeholder="请填写登录账号"></el-input>
          </el-form-item>
          <el-form-item v-if="!queryId" class="formItem" prop="userMobile" label="手机号码:">
            <el-input clearable style="width: 200px" v-model.trim="query.userMobile" placeholder="请填写手机号码"></el-input>
          </el-form-item>
          <el-form-item v-if="!queryId" class="formItem" prop="password" label="登录密码:">
            <el-input style="width: 200px" placeholder="请输入密码" v-model.trim="query.password" show-password></el-input>
          </el-form-item>
          <el-form-item v-if="!queryId" class="formItem" prop="confirmPassword" label="确认密码:">
            <el-input style="width: 200px" placeholder="请输入密码" v-model.trim="query.confirmPassword" show-password></el-input>
          </el-form-item>
        </div>
      </div>
      <div class="item">
        <div class="title"><span>结款信息</span></div>
        <el-row>
          <el-col :span="6">
            <el-form-item class="formItem" prop="bankAccount" label="银行账户:" :rules="[{ required: true, message: '请填写银行账户',trigger: 'blur' }]">
              <el-input style="width: 200px" placeholder="请填写银行账户" v-model.trim="query.bankAccount" ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" prop="bankNumber" label="银行账号:" :rules="[{ required: true, message: '请填银行账号',trigger: 'blur' }]">
              <el-input style="width: 200px" @input="inputVal($event, 'bankNumber')" placeholder="请填银行账号" v-model.trim="query.bankNumber" ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" prop="bankName" label="开户银行:" :rules="[{ required: true, message: '请填写开户银行',trigger: 'blur' }]">
               <el-input style="width: 200px" placeholder="请填写开户银行" v-model.trim="query.bankName" ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <div class="item">
        <div class="title"><span>经营类目</span></div>
        <div>
          <el-checkbox-group v-model.trim="checkList" @change="checkListFun">
            <div v-for="item in businessScope" :key="item.id" class="findCategory">
              <div style="width:120px;font-size:14px;text-align:right;line-height:23px;padding-right:10px">{{item.label}} :</div>
              <div style="flex:1;line-height:25px">
                <el-checkbox v-for="ids in item.children" :key="ids.id" :label="ids.id">
                  <span>{{ids.label}}</span>
                </el-checkbox>
              </div>
            </div>
          </el-checkbox-group>
        </div>
      </div>
      <div class="item">
        <div class="title"><span>结算规则</span></div>
        <div>
          <el-form-item label-width="150px" label="佣金计算规则: " :rules="[{message: '请选择佣金计算规则'}]">
            <el-radio-group v-model.trim="query.statementRule" >
              <el-radio label="N">不收取佣金</el-radio>
              <el-radio label="Y">按订单金额比例</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div>
          <el-form-item v-if="query.statementRule=='Y'" label-width="150px" class="formItem" prop="orderAmountRate" label="金额比例:">
            <el-input style="width: 200px" type="number" v-model.trim="query.orderAmountRate" placeholder="请填写金额比例">
              <i slot="suffix">%</i>
            </el-input>
          </el-form-item>
        </div>
      </div>
      <div class="item">
        <div class="title"><span>商家资质</span></div>
        <lisence-table v-if="lisenceTableDate.length!=0" :lisenceTableDate.sync="lisenceTableDate" />
      </div>
      <div class="item">
        <div class="title"><span>采购资质</span></div>
        <procurement-table v-if="newLisenceTableDate.length!=0" :newLisenceTableDate.sync="newLisenceTableDate" :edit="['admin','business-qualification:edit']" :confirm="['admin','business-qualification:submit']" />
      </div>
      <div class="item">
        <div class="title"><span>发票信息</span></div>
        <template>
          <div>
            <el-form-item class="formItem" prop="isInvoiceInfo" label="发票类型:"
                          v-model="isInvoiceInfo"
                          >
              <el-radio  v-model="isInvoiceInfo" label="VATINVOICE">普通发票</el-radio>
              <el-radio  v-model="isInvoiceInfo" label="SPECIALINVOICE">增值税专用发票</el-radio>
            </el-form-item>
          </div>
          <div>
            <el-form-item class="formItem" prop="invoiceInfo.name" label="发票抬头:"
                          :rules="[{ required: true, message: '请填写发票抬头', trigger: 'blur' },]">
              <el-input :disabled="isEdit" clearable style="width: 400px" v-model.trim="query.invoiceInfo.name" placeholder="请填写发票抬头"></el-input>
            </el-form-item>
          </div>
          <div>
            <el-form-item class="formItem" prop="invoiceInfo.taxNumber" label="税号:"
                          :rules="[{ required: true, message: '请填写税号', trigger: 'blur' },]">
              <el-input :disabled="isEdit" clearable style="width: 400px" v-model.trim="query.invoiceInfo.taxNumber" placeholder="请填写税号"></el-input>

            </el-form-item>
          </div>

          <div v-if="isInvoiceInfo== 'SPECIALINVOICE'">
            <el-form-item class="formItem" prop="invoiceInfo.registerAddress" label="注册地址:"
                          :rules="[{ required: true, message: '请填写注册地址', trigger: 'blur' },]">
              <el-input :disabled="isEdit" clearable style="width: 400px" v-model.trim="query.invoiceInfo.registerAddress" placeholder="请填写注册地址"></el-input>
            </el-form-item>
          </div>

          <div v-if="isInvoiceInfo== 'SPECIALINVOICE'">
            <el-form-item class="formItem" prop="invoiceInfo.registerMobile" label="注册电话:">
              <el-input :disabled="isEdit" clearable style="width: 400px" v-model.trim="query.invoiceInfo.registerMobile" placeholder="请填写注册电话"></el-input>
            </el-form-item>
          </div>

          <div v-if="isInvoiceInfo== 'SPECIALINVOICE'">
            <el-form-item class="formItem" prop="invoiceInfo.bankNumber" label="银行账号:" :rules="[{ required: true, message: '请填写银行账号', trigger: 'blur' }]">
              <el-input :disabled="isEdit" clearable style="width: 400px" v-model.trim="query.invoiceInfo.bankNumber" placeholder="请填写银行账号"></el-input>
            </el-form-item>
          </div>

          <div v-if="isInvoiceInfo=='SPECIALINVOICE'">
            <el-form-item class="formItem" prop="invoiceInfo.depositBank" label="开户银行:"
                          :rules="[{ required: true, message: '请填写开户银行', trigger: 'blur' },]">
              <el-input :disabled="isEdit" clearable style="width: 400px" v-model.trim="query.invoiceInfo.depositBank" placeholder="请填写开户银行"></el-input>

            </el-form-item>
          </div>
        </template>
      </div>

      <div class="item">
        <div class="title"><span>发货地址</span></div>
        <template>
          <addrTable :addrtableDate.sync="addrtableDate" :id="$route.query.id" />
        </template>
      </div>
    </el-form>
  </div>
</template>
<script>
import checkPermission from '@/utils/permission'
import { socialCreditCode } from "@/utils/rules";
import { setContextData } from "@/utils/auth";
import rule from "@/utils/rules";
import addrTable from "./addrTable";
import ProcurementTable from "./procurementTable.vue";

import {
  getitem,
  findSaleScope,
  saleMerchant,
  listByLicenseBaseType,
  areas, listByLicenseMerChan,
} from "@/api/businessCenter/businessList";
import LisenceTable from "./lisenceTable.vue";
import { add } from "@/api/businessCenter/businessList/index";
export default {
  name: 'editBusinessItem',
  data() {
    var checkNumPot3 = (rule, value, callback) => {
      // const reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
      const reg = /^[1-9]\d*$/;
      if (!value) {
        return callback(new Error("请填写数字"));
      } else if (!reg.test(value)) {
        return callback(new Error("请填写正整数"));
      } else if (value <= 0 || value > 100) {
        return callback(new Error("请填写0-100以内的数"));
      } else {
        callback();
      }
    };
    var validatePass2 = (rule, value, callback) => {
      if (value !== this.query.password) {
        callback(new Error("两次输入密码不一致!"));
      }
      callback();
    };
    return {
      loading: false,
      arealist: [],
      newLisenceTableDate: [],
      isInvoiceInfo: "",
      isEdit:false,
      rules: {
        password: [
          { required: true, message: "密码不能为空", trigger: "blur" },
          {
            required: true,
            min: 6,
            max: 20,
            message: "长度在 6 到 20 个字符",
            trigger: "blur",
          },
        ],
        // userMobile: rule.phone,
        confirmPassword: [
          { required: true, message: "确认密码不能为空", trigger: "blur" },
          {
            required: true,
            min: 6,
            max: 20,
            message: "长度在 6 到 20 个字符",
            trigger: "blur",
          },
          { validator: validatePass2, trigger: "blur" },
        ],
        // registerCapital: [
        //   { validator: checkNumPot2, trigger: "blur", required: true },
        // ],
        ceoMobile: rule.phone,
        orderAmountRate: [
          { validator: checkNumPot3, trigger: "blur", required: true },
        ],
        socialCreditCode: [
          { validator: socialCreditCode, trigger: "blur", required: true },
        ],
      },
      rejectFlag: false,
      listLoading: false,
      checkList: [],
      businessScope: {},
      query: {
        invoiceType:"VATINVOICE",
        commerceModel: 'SAAS',
        invoiceInfo:{}
      },
      statementRule: "N",
      rejectText: {},
      addrtableDate: [],
      lisenceTableDate: [],
      areasTree: [],
      // 商家类型
      merchantTypes: [
        { label: 'SaaS商家', value: 'SAAS' },
      ],
    };
  },
  methods: {
    checkPermission,
    async getareas() {
      let { data } = await areas();
      this.areasTree = data;
    },
    inputVal(val, name) {
      this.query[name] = val.replace(/\s/g, '')
    },
    getsrc(str) {
      if (!str) {
        return [];
      } else {
        let arr = str.split(",");
        let list = [];
        arr.forEach((item) => {
          let obj = {
            response: {
              data: {
                url: "",
              },
            },
          };
          obj.response.data.url = item;
          obj.url = item;
          list.push(obj);
        });
        return list;
      }
    },
    checkListFun(e) {
      this.query.businessCategoryIds = e;
    },
    cityChange(e) {
      this.query.provinceId = e[0];
      this.query.cityId = e[1];
      this.query.countyId = e[2];
      this.regionId = e;
    },

    async getjyfw() {
      let { data } = await findSaleScope();
      this.businessScope = data;
    },
    async getitem() {
      this.listLoading = true;
      if (!this.queryId) {
        let tableDate = (await listByLicenseBaseType()).data;
        let newTableDate = (await listByLicenseMerChan()).data;

        tableDate.forEach((item) => {
          let obj = {
            licenseBaseId: item.id,
            licenseEndTime: "",
            filePath: "",
            licenseNumber: "",
            label: item.name,
            isEdit: false,
            filePathList: this.getsrc(item.filePath),
            limit: item.multiple.code == "Y" ? 5 : 1,
            isForever: "N"
          };
          this.$set(this.query, "statementRule", "N");
          this.lisenceTableDate.push(obj);
        });
        newTableDate.forEach((item) => {
          let obj = {
            licenseBaseId: item.id,
            licenseEndTime: "",
            filePath: "",
            licenseNumber: "",
            label: item.name,
            isEdit: false,
            filePathList: this.getsrc(item.filePath),
            limit: item.multiple.code == "Y" ? 5 : 1,
          };
          this.newLisenceTableDate.push(obj);
        });
        this.listLoading = false;
        this.query.deliveryAddressSaveDTOList = [];
        return;
      }
      let { data } = await getitem(this.$route.query.id);
      if (data.businessCategoryDetailList) {
        data.businessCategoryDetailList.forEach((item) => {
          this.checkList.push(item.id);
        });
      }
      let list = (await listByLicenseBaseType()).data;
      let newList = (await listByLicenseMerChan()).data;
      list.forEach((item) => {
        let obj = {
          licenseBaseId: item.id,
          licenseEndTime: "",
          filePath: "",
          isForever: "",
          licenseNumber: "",
          label: item.name,
          isEdit: false,
          id: "",
          limit: item.multiple.code == "Y" ? 5 : 1,
        };
        data.merchantLicenses.find((ids) => {
          if (item.id == ids.licenseBaseId) {
            obj.isForever = ids.isForever.code === 'Y'
            obj.licenseEndTime = ids.isForever.code === 'Y' ? '' :ids.licenseEndTime;
            obj.filePath = ids.filePath;
            obj.filePathList = this.getsrc(ids.filePath);
            obj.licenseNumber = ids.licenseNumber;
            obj.label = item.name;
            obj.merchantId = ids.merchantId;
            obj.id = ids.id;
          }
        });
        this.lisenceTableDate.push(obj);
      });
      newList.forEach((item) => {
        let obj = {
          licenseBaseId: item.id,
          licenseEndTime: "",
          filePath: "",
          isForever: "",
          licenseNumber: "",
          label: item.name,
          isEdit: false,
          id: "",
          filePathList: [],
          limit: item.multiple.code == "Y" ? 5 : 1,
        };
        data.merchantLicenses.find((ids) => {
          if (item.id == ids.licenseBaseId) {
            obj.licenseEndTime = ids.isForever.code === 'Y' ? '' :ids.licenseEndTime;
            obj.filePath = ids.filePath;
            obj.filePathList = this.getsrc(ids.filePath);
            obj.licenseNumber = ids.licenseNumber;
            obj.label = item.name;
            obj.merchantId = ids.merchantId;
            obj.id = ids.id;
            obj.isForever = ids.isForever.code === 'Y'
          }
        });
        this.newLisenceTableDate.push(obj);
      });

      this.query = data;
      this.query.statementRule = data.statementRule.code;
      try {
        this.query.invoiceInfo = data.invoiceInfo==null? {} :data.invoiceInfo;
        this.isInvoiceInfo = data.invoiceInfo.name==undefined?'VATINVOICE':data.invoiceInfo.invoiceType.code; 
      } catch (error) {
        console.log('----error--->',error);
      }
      this.query.regionId = [data.provinceId, data.cityId, data.county_id];
      this.query.deliveryAddressSaveDTOList = [];
      this.tableDate = list;
      this.listLoading = false;
    },
    async edit(content) {
      if (this.isInvoiceInfo == "VATINVOICE") {
        this.query.invoiceInfoSaveDTO = {
          invoiceType: this.isInvoiceInfo,
          name: this.query.invoiceInfo.name,
          taxNumber: this.query.invoiceInfo.taxNumber,
        };
      } else {
        this.query.invoiceInfoSaveDTO = {
          invoiceType:this.isInvoiceInfo,
          name: this.query.invoiceInfo.name,
          taxNumber: this.query.invoiceInfo.taxNumber,
          registerMobile:this.query.invoiceInfo.registerMobile,
          bankNumber: this.query.invoiceInfo.bankNumber,
          depositBank:this.query.invoiceInfo.depositBank,
          registerAddress:this.query.invoiceInfo.registerAddress
        };
      }
      if (content == "editForm") {
        let validFlag = false;
        this.$refs[content].validate(async (valid) => {
          if (valid) {
            validFlag = true;
          } else {
            validFlag = false;
          }
        });
        if(!validFlag) {
          return
        }
        if (this.query.id) {
          if (this.checkList.length == 0) {
            this.$message.error("请选择经营类目");
            return;
          }
          this.query.businessCategoryRelUpdateDTOList = [];
          this.checkList.forEach((item) => {
            this.query.businessCategoryRelUpdateDTOList.push({
              businessCategoryId: item,
              merchantId: this.query.id,
            });
          });
          // this.query.businessCategoryId = this.checkList;
          delete this.query.businessCategoryDetailList;
          delete this.query.businessCategoryId;
          delete this.query.deliveryAddressList;
          delete this.query.businessCategoryList;
          delete this.query.merchantLicenses;
          delete this.query.deliveryAddressSaveDTOList;
          delete this.query.userMobile;
          this.loading = true
          saleMerchant(this.query).then(res => {
            if (res.code !== 0) return
            this.$message.success("修改成功");
            let listQuery = {
              current: 1,
              size: 10,
              model: {
                approvalStatus: { code: "PENDING" },
              },
            };
            this.$store.dispatch("tagsView/delView", this.$route);
            setContextData("businessArchives_list", listQuery);
            this.$router.push("/businessCenter/businessArchives/list");
          }).finally(() => {
            this.loading = false
          })
        } else {
          let arr = [];
          this.lisenceTableDate.forEach((item) => {
            if (item.filePath) {
              arr.push({
                licenseBaseId: item.licenseBaseId,
                licenseEndTime: item.licenseEndTime,
                filePath: item.filePath,
                licenseNumber: item.licenseNumber,
              });
            }
          });
          if(this.addrtableDate.length==0) {
            this.query.deliveryAddressSaveDTOList = null
          } else {
            this.query.deliveryAddressSaveDTOList= []
            this.addrtableDate.forEach((item) => {
              this.query.deliveryAddressSaveDTOList.push({
                cityId: item.address[1],
                provinceId: item.address[0],
                countyId: item.address[2],
                fixedPhone: item.fixedPhone,
                mobilPhone: item.mobilPhone,
                name: item.name,
                detailedAddress: item.detailedAddress,
              });
            });
          }
          this.query.merchantLicenseSaveDTOListDTOList = arr;
          this.loading = true
          add(this.query).then(res => {
            if (res.code !== 0) return
            this.$message.success("添加成功");
            let listQuery = {
              current: 1,
              size: 10,
              model: {
                approvalStatus: { code: "PENDING" },
              },
            };
            this.$store.dispatch("tagsView/delView", this.$route);
            setContextData("businessArchives_list", listQuery);
            this.$router.push("/businessCenter/businessArchives/list");
          }).finally(() => {
            this.loading = false
          })
        }
      }
    },
    clearFun() {
      this.$router.go(-1);
      this.$store.dispatch("tagsView/delView", this.$route);
    },
  },
  computed: {
    queryId() {
      return !!this.$route.query.id || this.$route.query.id == 0
    }
  },
  created() {
    this.getareas();
    this.getjyfw();
    this.getitem();
  },
  components: {
    LisenceTable,
    addrTable,
    ProcurementTable
  },
  mounted() {},
  beforeDestroy() {},
};
</script>
<style lang="less" scoped>
.archivesEditContent {
  // margin: 30px 20px;
  border-top: 1px solid #ebecee;
  padding: 0 20px 20px 20px;
  background: #fff;
  .item {
    width: 100%;
    margin-bottom: 30px;
    border-bottom: 1px solid #eeeeee;
    .title {
      padding: 0 0 15px;
      span {
        font-size: 16px;
        padding-left: 10px;
        border-left: 4px solid rgba(64, 158, 255, 1);
      }
    }
  }
  .top_title {
    height: 56px;
    line-height: 56px;
    font-family: "PingFangSC-Regular", "PingFang SC", sans-serif;
    font-size: 18px;
    text-align: left;
    border-bottom: 1px solid #eeeeee;
    margin-bottom: 20px;
    .el-button {
      margin-left: 10px;
    }
  }
  .findCategory {
    padding-bottom: 20px;
    display: flex;
    flex-wrap: wrap;
  }
  /deep/ .el-col {
    line-height: 40px;
  }

  /deep/ .el-upload {
    width: 40px;
    height: 40px;
    position: relative;
  }
  /deep/ .el-upload > i {
    font-size: 16px;
    position: absolute;
    left: 50%;
    top: 50%;
    -webkit-transform: translateX(-50%) translateY(-50%);
    transform: translateX(-50%) translateY(-50%);
  }
  /deep/ .el-upload-list .el-upload-list__item {
    width: 40px;
    height: 40px;
  }
  /deep/ .hide .el-upload--picture-card {
    display: none;
  }
}
</style>
