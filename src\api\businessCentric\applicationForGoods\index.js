import requestAxios from '@/utils/requestAxios'
// import requestAxios from "@/views/businessCentric/request"
// 获取列表
export function getList(data) {
  return requestAxios({
    url: '/api/agent/agentProduct/platform/query/agencyProductsApply',
    method: 'post',
    data
  })
}

// 同意
export function agree(data) {
  return requestAxios({
    url: '/api/agent/agentProduct/platform/consent/' + data,
    method: 'put'
  })
}

// 批量同意
export function acceptedList(data) {
  return requestAxios({
    url: '/api/agent/agentProduct/platform/batchConsent/' + data,
    method: 'put'
  })
}

// 驳回
export function rejected(data) {
  return requestAxios({
    url: '/api/agent/agentProduct/platform/reject/' + data.id,
    method: 'put',
    data: data.data
  })
}

// 批量驳回
export function rejectList(data) {
  return requestAxios({
    url: '/api/agent/agentProduct/platform/batchReject/' + data,
    method: 'put'
  })
}