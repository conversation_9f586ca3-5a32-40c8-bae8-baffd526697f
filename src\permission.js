import router from './router'
import store from './store'
import { getToken } from '@/utils/auth' // get token from cookie
import { Message } from 'element-ui'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import getPageTitle from '@/utils/get-page-title'
import Layout from '@/layout'

NProgress.configure({ showSpinner: false }) // NProgress Configuration

const whiteList = ['/login', '/auth-redirect'] // no redirect whitelist

router.beforeEach(async (to, from, next) => {
  // console.info(to, from)
  // start progress bar
  NProgress.start()

  // set page title
  document.title = getPageTitle(to.meta.title)
  // determine whether the user has logged in
  const hasToken = getToken()
  if (hasToken) {
    if (to.path === '/login') {
      // if is logged in, redirect to the home page
      next({ path: '/' })
      NProgress.done() // hack: https://github.com/PanJiaChen/vue-element-admin/pull/2939
    } else {
      // determine whether the user has obtained his permission roles through getInfo
      const hasRoles = store.getters.roles && store.getters.roles.length > 0
      const routerAlreadyInit = store.getters.alreadyAddRouts
      if (!hasRoles) {
        // store.dispatch('user/getRoleS')
        if(to.path != '/dashboard'){
          store.dispatch('user/getRoleS');
        }
      }
      if (routerAlreadyInit) {
        next()
      } else {
        try {
          const accessRoutes = await store.dispatch('permission/generateRoutes', store.getters.roles) // 构建路由无需前端告诉后端用户角色
          const hastIndex = accessRoutes.filter(item => {
            return item.path === '/dashboard'
          })
          if (hastIndex.length === 0 && accessRoutes.length >= 1) {
            accessRoutes.push({
              path: "/dashboard",
              hidden: true,
              redirect: accessRoutes[0].path
            })
          }
          // dynamically add accessible routes
          router.addRoutes(accessRoutes)
          if(accessRoutes.length> 0) {
            next({...to, replace: true})
          } else {
            Message.error('该用户没有权限！请联系其管理员设置！')
          }
        } catch (error) {
          // remove token and go to login page to re-login
          await store.dispatch('user/resetToken')
          Message.error(error || 'Has Error')
          next(`/login`)
          // next(`/login?redirect=${to.path}`)
          NProgress.done()
        }
      }
    }
  } else {
    /* has no token*/

    if (whiteList.indexOf(to.path) !== -1) {
      // in the free login whitelist, go directly
      next()
    } else {
      // other pages that do not have permission to access are redirected to the login page.
      next(`/login`)
      // next(`/login?redirect=${to.path}`)
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  // finish progress bar
  NProgress.done()
})
