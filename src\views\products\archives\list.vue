<template>
  <div class="archivesPageContent">
    <im-search-pad :is-expand.sync="isExpand" :model="searchForm" @reset="resetForm('searchForm')"
      @search="onSearchSubmitFun">
      <im-search-pad-item prop="productName">
        <el-input v-model="searchForm.productName" placeholder="请输入产品名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="productCode">
        <el-input v-model="searchForm.productCode" placeholder="请输入产品编码" />
      </im-search-pad-item>
      <im-search-pad-item prop="approvalNumber">
        <el-input v-model="searchForm.approvalNumber" placeholder="请输入批准文号" />
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="manufacturer">
        <el-input v-model="searchForm.manufacturer" placeholder="请输入生产厂家" />
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="item2">
        <CascaderOfProductType :selectId.sync="searchForm.item2"></CascaderOfProductType>
      </im-search-pad-item>
    </im-search-pad>

    <div class="tab_bg">
      <tabs-layout ref="tabs-layout" v-model="listQuery.model.approvalStatus" :tabs="approvalStatusList"
        @change="handleChangeTab">
        <template slot="button">
          <div>
            <el-button
              v-if="checkPermission(['admin','admin-platformProduct:reject'])&&listQuery.model.approvalStatus=='PENDING'"
              :disabled="multipleSelection.length==0" @click="setRejectReasonListFun">批量驳回</el-button>
            <el-button v-if="checkPermission(['admin','admin-platformProduct:accept'])&&listQuery.model.approvalStatus=='PENDING'"
              :disabled="multipleSelection.length==0" @click="submitProductPlatformPassListFun">批量通过</el-button>
            <!-- <el-button v-if="checkPermission(['admin','archives:export'])"  :disabled="multipleSelection.length==0" @click="outExcel">导出档案</el-button> -->
            <el-button @click="reloadPageFun">刷新</el-button>
            <el-button type="primary" @click="newFun" v-if="checkPermission(['admin','admin-platformProduct:add'])">+新增产品档案
            </el-button>
          </div>
        </template>
      </tabs-layout>
      <table-pager ref="pager-table" :options="tableTitle" :data.sync="list" :selection="true"
        @selection-change="onSelect" @selection-all="onAllSelect" :remoteMethod="getList" :operationWidth="150"
        @change-page="multipleSelection = []">
        <template slot="pictIdS" slot-scope="{ row }">
          <el-popover placement="right" trigger="hover">
            <el-image style="width: 200px; height: 200px" fit="contain" :src="row.pictIdS|imgFilter"></el-image>
            <el-image slot="reference" style="width: 50px; height: 50px" fit="cover" :src="row.pictIdS|imgFilter">
            </el-image>
          </el-popover>
        </template>
        <div slot-scope="scope" v-if="checkPermission(['admin','admin-platformProduct:detail','admin-platformProduct:audit'])">
          <el-row class="table-edit-row">
            <span v-if="checkPermission(['admin','admin-platformProduct:detail'])" class="table-edit-row-item">
              <el-button @click="detailFun(scope.row)" type="text">查看详情</el-button>
            </span>
            <span v-if="listQuery.model.approvalStatus == 'PENDING'&&checkPermission(['admin','admin-platformProduct:audit'])"
              class="table-edit-row-item">
              <ExamineButton :isReload.sync="submitReload" :row.sync="scope.row"></ExamineButton>
            </span>
          </el-row>
        </div>
      </table-pager>
    </div>
    <!-- 设置 编辑 -->
    <!-- <el-dialog v-if="showEditPage" :title="(row.id>0?'产品档案详情':'新增产品档案')" :visible.sync="showEditPage" :close-on-click-modal="false" :width="'1200px'" :show-close="false" >
        <edit :visible.sync="showEditPage" :isReload.sync="submitReload" :row.sync="row"></edit>
    </el-dialog> -->
    <!-- 设置 编辑 -->
    <!-- 详情 -->
    <el-dialog v-if="showDetailPage" title="审核产品" :visible.sync="showDetailPage" :width="'300px'">
      <div style="margin-top:-20px">
        <div style="padding:0 0 15px">
          该产品是否确认通过？
        </div>
        <div style="text-align:right">
          <el-popover placement="top" width="270" v-model="showSetRejectReasonStatus">
            <div style="line-height:36px;">驳回理由</div>
            <div>
              <el-input type="textarea" v-model="rejectReason" placeholder="" rows="4"></el-input>
            </div>
            <div style="text-align: right; margin: 0;padding-top:10px;">
              <el-button type="text" @click="showSetRejectReasonStatus = false">取消</el-button>
              <el-button type="primary" @click="setRejectReasonFun(row)">确认</el-button>
            </div>
            <el-button slot="reference" style="margin-right:10px;">驳回</el-button>
          </el-popover>
          <el-button type="primary" @click="submitProductPlatformPassFun(row)">通过</el-button>
        </div>
      </div>
    </el-dialog>
    <!-- 详情 -->
  </div>
</template>
<script>
  import {
    downloadFile
  } from '@/utils/commons'
  import {
    setContextData,
    getContextData
  } from '@/utils/auth'
  import checkPermission from '@/utils/permission'
  import CascaderOfProductType from '@/components/eyaolink/CascaderOfProductType'
  import {
    list,
    pageCount,
    rejectProductPlatformApi,
    acceptProductPlatformApi
  } from '@/api/products/archives'
  import tableInfo from '@/views/products/archives/tableInfo'
  import ExamineButton from './ExamineButton'
  import edit from '@/views/products/archives/edit'
  import detail from '@/views/products/archives/detail'
  import Pagination from '@/components/Pagination'
  import TabsLayout from '@/components/TabsLayout'
  export default {
    components: {
      TabsLayout,
      Pagination,
      edit,
      detail,
      CascaderOfProductType,
      ExamineButton
    },
    data() {
      return {
        isExpand: false,
        pageCountInfo: {
          "acceptedCount": "0",
          "pendingCount": "0",
          "rejectedCount": "0"
        },
        showSelectTitle: false,
        showEditPage: false,
        showDetailPage: false,
        row: {},
        multipleSelection: [],
        tableSelectTitle: [0, 1, 2, 3],
        tableTitle: [],
        submitReload: false,
        list: [],
        total: 0,
        listLoading: true,
        listQuery: {
          model: {
            approvalStatus: "PENDING"
          },
          current: 1,
          size: 10,
          sort: "updateTime,id"
        },
        showSetRejectReasonStatus: false,
        rejectReason: "",
        searchForm: {
          // item1:{
          //   type:"productName",
          //   name:""
          // },
          productName: "",
          productCode: "",
          approvalNumber: "",
          manufacturer: "",
          item2: "0"
        },
      };
    },
    computed: {
      approvalStatusList() {
        return [{
            name: '待审核' + ((+this.pageCountInfo.pendingCount) > 0 ? '(' + this.pageCountInfo.pendingCount + ')' : ''),
            value: 'PENDING',
            hide: !checkPermission(['admin', 'admin-platformProduct:penddingView'])
          },
          {
            name: '已审核' + ((+this.pageCountInfo.acceptedCount) > 0 ? '(' + this.pageCountInfo.acceptedCount + ')' :
              ''),
            value: 'ACCEPTED',
            hide: !checkPermission(['admin', 'admin-platformProduct:applyAcceptedView'])
          },
          {
            name: '已驳回' + ((+this.pageCountInfo.rejectedCount) > 0 ? '(' + this.pageCountInfo.rejectedCount + ')' :
              ''),
            value: 'REJECTED',
            hide: !checkPermission(['admin', 'admin-platformProduct:rejectedView'])
          }
        ]
      }
    },
    watch: {
      submitReload: function (newVal, oldVal) {
        if (newVal) {
          this.submitReload = false;
          this.getPageCount()
          this.$refs['pager-table'].doRefresh()
        }
      }
    },
    filters: {
      imgFilter: function (value) {
        if (value != "" && value != null) {
          return value.split(",")[0]
        } else {
          return require("@/assets/img/index/product_default.png");
        }
      }
    },
    beforeRouteEnter(to, from, next) {
      next(vm => {
        if (from.path == "/productCenter/archives/edit") {
          if (getContextData("products_archives_list") != "") {
            vm.listQuery = getContextData("products_archives_list")
          }
        };
        vm.listQuery = {
            model: {
              approvalStatus: "PENDING"
            },
            current: 1,
            size: 10
          },
          vm.initTbaleTitle()
        vm.getPageCount()
        vm.$refs['pager-table'].doRefresh()
      })
    },
    methods: {
      checkPermission,
      newFun: function () {
        setContextData("products_archives_list", this.listQuery)
        this.$router.push({
          path: "/productCenter/archives/edit",
          query: {
            tabType: this.listQuery.model.approvalStatus
          }
        })
      },
      detailFun(row) {
        setContextData("products_archives_list", this.listQuery)
        this.$router.push({
          path: "/productCenter/archives/edit",
          query: {
            tabType: this.listQuery.model.approvalStatus,
            id: row.id,
          }
        })
      },
      reviewedFun(row) {
        this.row = row
        this.showDetailPage = true
      },
      async getList(params) {
        this.listLoading = true
        Object.assign(this.listQuery, params)
        return await list(this.listQuery)
      },
      async getPageCount() {
        const {
          data
        } = await pageCount({
          "current": 1,
          "map": {},
          "model": {},
          "size": 10
        })
        this.pageCountInfo = Object.assign(this.pageCountInfo, data)
      },
      onSearchSubmitFun() {
        this.listQuery.model.approvalStatus = this.listQuery.model.approvalStatus;
        this.listQuery.current = 1;
        this.listQuery.model["productName"] = this.searchForm.productName;
        this.listQuery.model["productCode"] = this.searchForm.productCode;
        this.listQuery.model["approvalNumber"] = this.searchForm.approvalNumber;
        this.listQuery.model["manufacturer"] = this.searchForm.manufacturer;
        if (this.searchForm.item2 != '0') {
          this.listQuery.model["categoryId"] = this.searchForm.item2
        } else {
          this.listQuery.model["categoryId"] = ""
        }
        this.$refs['pager-table'].doRefresh()
      },
      handleChangeTab(tab) {
        this.tableSelectTitle = [0, 1, 2, 3]
        this.multipleSelection = []
        this.listQuery.pag = 1;
        this.initTbaleTitle()
        this.$refs['pager-table'].doRefresh()
      },
      resetForm(formName) {
        this.searchForm = {
          productName: '',
          productCode: '',
          item2: '',
          approvalNumber: '',
          manufacturer: '',
          item2: ''
        }
        this.onSearchSubmitFun()
      },
      initTbaleTitle() {
        this.tableSelectTitle = []
        this.tableTitle = tableInfo[this.listQuery.model.approvalStatus]
      },
      // table 选中
      onAllSelect(selection) {
        this.onSelect(selection);
      },
      onSelect(val) {
        this.multipleSelection = val;
      },
      async outExcel() {
        if (this.multipleSelection.length > 0) {
          const tHeader = ['id']
          const filterVal = ['id']
          this.tableTitle.forEach(function (item) {
            tHeader.push(item.label)
            filterVal.push(item.name)
          })
          let exportData = this.formatJson(this.multipleSelection, filterVal)
          downloadFile({
            tHeader: tHeader,
            fileName: "产品档案",
            exportData: exportData
          })
        } else {
          this.$message.error('请在产品档案中勾选需要导出的产品')
        }
      },
      formatJson(dataList, filterVal) {
        return dataList.map(v => filterVal.map(j => {
          if (j === 'approvalStatus') {
            return v[j].desc
          } else if (j === 'publishStatus') {
            return v[j].desc
          } else if (j === 'stockQuantityStatus') {
            return v[j].desc
          } else {
            return v[j]
          }
        }))
      },
      async setRejectReasonFun(row) {
        //单个驳回提交
        let _this = this;
        let subObj = {
          "list": [{
            id: row.id,
            approvalStatus: "REJECTED",
            rejectReason: _this.rejectReason
          }]
        }
        this.submitRejectProductPlatformApiFun(subObj)
      },
      async submitProductPlatformPassFun(row) {
        // 当个 通过
        let _this = this;
        let subObj = {
          "list": [{
            id: row.id,
            approvalStatus: "ACCEPTED",
            rejectReason: "通过"
          }]
        }
        _this.submitAcceptProductPlatformApiFun(subObj)
      },
      async setRejectReasonListFun() {
        let _this = this;
        //批量驳回提交
        this.$confirm("此操作将审核驳回产品，是否继续？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(async () => {
          if (_this.multipleSelection.length > 0) {
            let subObj = {
              "list": []
            }
            this.multipleSelection.forEach(function (item) {
              subObj.list.push({
                id: item.id,
                approvalStatus: "REJECTED",
                rejectReason: "批量驳回"
              })
            })
            _this.submitRejectProductPlatformApiFun(subObj)
          } else {
            _this.$message.error('请在产品档案中勾选需要驳回的产品')
          }
        })
      },
      async submitProductPlatformPassListFun() {
        //批量通过提交
        let _this = this;
        this.$confirm("此操作将审核通过产品，是否继续？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(async () => {
          if (_this.multipleSelection.length > 0) {
            let subObj = {
              "list": []
            }
            _this.multipleSelection.forEach(function (item) {
              subObj.list.push({
                id: item.id,
                approvalStatus: "ACCEPTED",
                rejectReason: "通过"
              })
            })
            _this.submitAcceptProductPlatformApiFun(subObj)
          } else {
            _this.$message.error('请在产品档案中勾选需要通过的产品')
          }
        })
      },
      async submitRejectProductPlatformApiFun(subObj) {
        let _this = this;
        this.$confirm("此操作将审核通过商品，是否继续？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: 'warning',
        }).then(async () => {
          var data = await rejectProductPlatformApi(subObj);
          if (data.code == 0) {
            _this.showDetailPage = false;
            _this.rejectReason = "";
            _this.getPageCount()
            _this.$refs['pager-table'].doRefresh()
          } else {
            _this.$message.error("驳回提交失败!")
          }
        })
      },
      async submitProductPlatformDownListFun(row) {
        //批量下架提交
        let _this = this;
        this.$confirm("此操作将审核下架商品，是否继续？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: 'warning',
        }).then(async () => {
          if (this.multipleSelection.length > 0) {
            let subObj = {
              "list": []
            }
            this.multipleSelection.forEach(function (item) {
              subObj.list.push({
                id: item.id,
                approvalStatus: "PENDING",
                rejectReason: "下架"
              })
            })
            _this.submitAcceptProductPlatformApiFun(subObj)
          } else {
            _this.$message.error('请在产品档案中勾选需要下架的产品')
          }
        })
      },
      async submitAcceptProductPlatformApiFun(subObj) {
        let _this = this;
        this.$confirm("此操作将审核通过商品，是否继续？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: 'warning',
        }).then(async () => {
          var data = await acceptProductPlatformApi(subObj)
          if (data.code == 0) {
            _this.showDetailPage = false;
            _this.rejectReason = "";
            _this.getPageCount()
            _this.$refs['pager-table'].doRefresh()
          } else {
            _this.$message.error("批量通过提交失败!")
          }
        })
      },
      reloadPageFun() {
        this.getPageCount()
        this.$refs['pager-table'].doRefresh()
      }
    },
    mounted() {},
    beforeDestroy() {}
  };

</script>
<style lang="scss" scoped>
  @import "@/styles/element-variables.scss";

</style>
