<template>
  <div class="editContent">
    <div class="temptop_title flex_between_center">
      <div>{{this.$route.query.id ==null?'新增':'编辑'}}品牌</div>
      <div>
        <div>
          <el-button @click="clearFun()">取 消</el-button>
          <el-button v-if="checkPermission(['admin','brand:add','brand:edit'])" type="primary" @click="submitFun('ruleForm')">确 定</el-button>
        </div>
      </div>
    </div>
    <el-form class="form"  :model="query" ref="ruleForm" label-width="110px">
        <el-form-item
        class="formItem"
         prop="brandCode"
        label="品牌编码:"
      >
        <el-input
          clearable
          :disabled="true"
          style="width:100%"
          v-model="query.brandCode"
          placeholder="系统生成品牌编码"
        ></el-input>
      </el-form-item>
        <el-form-item
        class="formItem"
        prop="brandName"
        label="品牌名称:"
        :rules="[{ required: true, message: '请填写品牌名称' }]"
      >
        <el-input
          clearable
          style="width:100%"
          v-model="query.brandName"
          placeholder="请填写品牌名称"
        ></el-input>
      </el-form-item>
      <el-form-item
        class="formItem"
        prop="manufacturer"
        label="生产厂家:"
      >
        <el-input
          clearable
          style="width:100%"
          v-model="query.manufacturer"
          placeholder="请填写生产厂家"
        ></el-input>
      </el-form-item>
      <el-form-item
        class="formItem"
        prop="brandAddr"
        label="品牌网站地址:"
      >
        <el-input
          clearable
          style="width:100%"
          v-model="query.brandAddr"
          placeholder="请填写品牌网站地址"
        ></el-input>
      </el-form-item>
      <el-form-item
        class="formItem"
         prop="brandStory"
        label="品牌故事:"
      >
        <el-input
          type="textarea"
          clearable
          v-model="query.brandStory"
          rows="5"
          placeholder="请填写品牌故事"
        ></el-input>
      </el-form-item>

      <el-form-item
        class="formItem"
        label="Logo:"
        prop="brandLogo"
      >
        <el-upload
          class="avatar-uploader"
          :action="$uploadUrl"
          :data="insertProgram"
          :headers="headersProgram"
          :show-file-list="false"
          :on-success="uploadSuccess"
          :before-upload="beforeUpload"
          accept=".jpg,.png,.bmp,.jpeg"
          >
          <el-image style="width: 120px; height: 120px" v-if="query.brandLogo!=''" :src="query.brandLogo" class="avatar" fit="contain"></el-image>
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          <div class="el-upload__tip" slot="tip">
            <div>- 支持JPG、JPEG、PNG、BMP格式，大小不超过2M</div>
            <div>- 请保证请保证图片质量，分辨率至少为600*600</div>
          </div>
        </el-upload>
      </el-form-item>

    </el-form>
  </div>
</template>
<script>
import checkPermission from '@/utils/permission'
import { uploadFile } from "@/api/file";
import { getToken } from '@/utils/auth'
import {editApi,getApi } from '@/api/products/brand'
export default {
  data() {
    return {
       headersProgram:{
        token:getToken(),
        Authorization:'Basic YWRtaW5fdWk6YWRtaW5fdWlfc2VjcmV0'
      },
      insertProgram:{
        folderId:0
      },
      query: this.initQuery(),
    };
  },
  methods: {
    checkPermission,
    initQuery(){
      return {
        id:0,
        "brandAddr": "",
        "brandCode": "",
        "brandLogo": "",
        "brandName": "",
        "brandStory": "",
        "manufacturer": "",
        "operation": ""
      }
    },
    goBackFun(){
      this.$router.go(-1)
      this.$store.dispatch("tagsView/delView", this.$route);
    },
    clearFun: function() {
      let _this=this;
      _this.goBackFun()
    },
    submitFun: function(ruleForm) {
      let _this=this;
      _this.$refs[ruleForm].validate( async valid => {
        if (valid) {
          let data = await editApi(_this.query);
          if(data.code == 0 && data.msg == 'ok'){
            _this.goBackFun()
          }
        } else {
          return false;
        }
      });
    },
    uploadSuccess(res, file) {
      this.query.brandLogo=res.data.url;
    },
    beforeUpload(file) {
      let fileTypeList=["image/png", "image/pjpeg", "image/jpeg", "image/bmp"]
      const isJPG = fileTypeList.indexOf(file.type) >-1;
      const isLt2M = file.size / 1024 / 1024 < 5;

      if (!isJPG) {
        this.$message.error('上传图片格式错误!');
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!');
      }
      return isJPG && isLt2M;
    },
    async getFun() {
      const { data } = await getApi(this.$route.query.id);
      this.query = Object.assign(this.query,data);
    }
  },
  mounted() {
    if (this.$route.query.id > 0) {
      this.getFun();
    }
  },
  beforeDestroy() {}
};
</script>
<style lang="less" scoped>
.editContent {
  // margin: -30px -20px;
  border-top: 1px solid #ebecee;
  padding: 0px 20px;
  background-color: #fff;
  .avatar-uploader {
    // width: 120px;
    // height: 120px;

    border-radius: 6px;
    cursor: pointer;
    position: relative;

  }
  /deep/ .avatar-uploader .el-upload{
    border-radius: 6px;
    overflow: hidden;
    cursor: pointer;
    border: 1px dashed #d9d9d9;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 120px;
    height: 120px;
    line-height: 120px;
    text-align: center;
  }
  .avatar {
    width:120px;
    height: 120px;
    display: block;
  }
}
</style>
