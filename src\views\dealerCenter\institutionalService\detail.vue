<template>
  <div class="archivesEditContent">
    <div style="background-color: #fff; padding: 0 20px 20px; margin-bottom: 20px">
      <page-title :title="routerQuery.agencyName" />
      <div class="statistics">
        <div class="statistics_item">
          <span class="text">可服务机构数(个)</span>
          <span class="num">{{ routerQuery.total }}</span>
        </div>
        <div class="statistics_item">
          <span class="text">已服务机构数（个）</span>
          <span class="num">{{ routerQuery.usedTotal }}</span>
        </div>
        <div class="statistics_item">
          <span class="text">剩余服务机构数（个）</span>
          <span class="num">{{ routerQuery.total - routerQuery.usedTotal }}</span>
        </div>
      </div>
    </div>
    <div class="tab_bg">
      <tabs-layout ref="tabs-layout" :tabs="approvalStatusList" v-model="listQuery.model.approvalStatus">
        <template slot="button">
          <div>
            <el-button @click="getList()">刷新</el-button>
            <el-input style="width: 240px;margin: 0 10px;" placeholder="请输入机构名称" v-model="listQuery.model.orgName">
              <el-button slot="append" icon="el-icon-search" @click="getList" />
            </el-input>
            <el-button type="primary" @click="edit('')">+ 新增服务机构</el-button>
          </div>
        </template>
      </tabs-layout>
      <div class="table">
        <el-table
          v-if="list"
          ref="table"
          @select="onSelect"
          @select-all="onAllSelect"
          v-loading="listLoading"
          :data="list"
          row-key="id"
          border
          fit
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column align="center" width="65" show-overflow-tooltip :render-header="renderHeader" fixed>
            <template slot-scope="scope">
              <span>{{ scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column :reserve-selection="true" align="center" type="selection" width="55" fixed>
          </el-table-column>
          <el-table-column
            v-for="(item, index) in tableTitle"
            :key="index"
            :min-width="item.width ? item.width : '180px'"
            :width="item.name == 'pictIdS' ? item.width : ''"
            :label="item.label"
          >
            <template slot-scope="{ row }">
              <span v-if="item.name === 'accountTotal'" style="color: #0056E5">
                {{ row[item.name] }}
              </span>
              <span v-else-if="item.name === 'status'">
                {{ row[item.name] ? row[item.name].desc : '' }}
              </span>
              <span v-else-if="item.name === 'beginTime'">
                {{ row['beginTime'] +' 至 '+ row['endTime'] }}
              </span>
              <span v-else>{{ row[item.name] }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" align="center" label="操作" class="itemAction" width="130">
            <template slot-scope="{row}">
              <el-row class="table-edit-row">
                <span class="table-edit-row-item">
                  <el-link type="primary" @click="edit(row)" style="margin-right: 10px;">编辑</el-link>
                  <el-link type="primary" @click="showAssociatedDialog(row.orgId, row.id)">关联账号</el-link>
                </span>
              </el-row>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="listQuery.current"
          :limit.sync="listQuery.size"
          @pagination="getList"
        />
      </div>
    </div>
    <EditServiceOrg ref="editServiceOrgRef" @setSuccess="onSearchSubmitFun" />
    <AssociatedAccount ref="associatedAccountRef" @setSuccess="onSearchSubmitFun" />
  </div>
</template>
<script>
import { getInstitutionsDetailList } from '@/api/dealerManagement'
import SetServiceOrgNumDialog from './components/setServiceOrgNumDialog.vue'
import ExamineButton from '@/views/products/product/ExamineButton'
import tableInfo from './tableInfo_detail'
import Pagination from '@/components/Pagination'
import TabsLayout from '@/components/TabsLayout'
import EditServiceOrg from './components/editServiceOrg.vue'
import AssociatedAccount from './components/associatedAccount.vue'

export default {
  name: 'institutionalServiceDetail',
  components: {
    Pagination,
    ExamineButton,
    TabsLayout,
    SetServiceOrgNumDialog,
    EditServiceOrg,
    AssociatedAccount
  },
  data() {
    return {
      isExpand: false,
      tableTitle: [],
      list: [],
      total: 0,
      listLoading: true,
      tableSelectTitle: [0, 1, 2, 3],
      listQuery: {
        model: {
          agencyId: '',
          agencyName: '',
          enabled: '',
          orgId: '',
          orgName: ''
        },
        current: 1,
        size: 10
      },
      routerQuery: {
        total: 0,
        usedTotal: 0,
        id: '',
        agencyName: '',
        agencyId: ''
      }
    }
  },
  computed: {
    approvalStatusList() {
      return [
        {
          name: '已服务的机构',
          value: 'ALL'
        }
      ]
    }
  },
  created() {
    Object.keys(this.routerQuery).forEach((key) => {
      const queryItem = this.$route.query[key]
      if (queryItem || queryItem == 0) this.routerQuery[key] = queryItem
      if (key === 'agencyId') this.listQuery.model.agencyId = queryItem
    })
  },
  methods: {
    // 编辑
    edit(row) {
      this.$refs.editServiceOrgRef.show(row)
    },
    // 查看详情
    showAssociatedDialog(orgId, orgInfoId) {
      this.$refs.associatedAccountRef.show(orgId, orgInfoId)
    },
    // 显示可服务机构数dialog
    showSetServiceOrgNumDialog(row) {
      this.$refs.SetServiceOrgNumRef.show(row)
    },
    renderHeader(h, { column }) {
      var titles = tableInfo[this.listQuery.model.approvalStatus]
      var titlesName = ['显示字段项', '隐藏字段项']
      return (
        <div style="position:relative">
          <div onClick={this.showHeaer}>
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            append-to-body
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="640px"
            center
          >
            <el-transfer
              vModel={this.tableSelectTitle}
              onChange={this.setleftTitleFun}
              data={titles}
              titles={titlesName}
            ></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button onClick={this.closeHeaer}>取消</el-button>
              <el-button type="primary" onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      )
    },
    setleftTitleFun(val) {
      this.tableSelectTitle = val
    },
    setHeaer: function () {
      var titles = tableInfo[0]
      var listinfo = titles.filter((element, index, self) => {
        return !this.tableSelectTitle.includes(element.key)
      })
      this.tableTitle = listinfo
      this.showSelectTitle = !this.showSelectTitle
    },
    showHeaer: function () {
      this.showSelectTitle = true
    },
    closeHeaer: function () {
      this.showSelectTitle = false
      this.tableSelectTitle = []
    },
    async getList() {
      this.listLoading = true
      const { data } = await getInstitutionsDetailList(this.listQuery)
      this.list = data.records
      this.total = data.total
      this.listLoading = false
    },
    onSearchSubmitFun: function () {
      this.list = []
      this.getList()
    },
    resetForm() {
      this.listQuery.model.agencyName = ''
      this.getList()
    },
    initTbaleTitle() {
      this.tableSelectTitle = [99]
      this.tableTitle = tableInfo.ALL
      this.tableTitle = this.tableTitle.filter((item) => {
        return item.key != 99
      })
    },
    // table 选中
    onAllSelect(selection) {
      this.onSelect(selection)
    },
    onSelect: function (val) {
      this.multipleSelection = val
    }
  },
  mounted() {
    this.initTbaleTitle()
    this.getList()
  }
}
</script>
<style lang="less" scoped>
.archivesEditContent {
  .statistics {
    display: flex;
    background-color: #f8f8f8;
    box-sizing: border-box;
    padding: 20px 30px;
    .statistics_item {
      display: flex;
      flex-direction: column;
      margin-right: 80px;
      .text {
        color: #666;
      }
      .num {
        margin-top: 15px;
        font-size: 24px;
      }
    }
  }
}
</style>
