import Layout from '@/layout'
const finance = {
  path: '/finance',
  name: 'finance',
  redirect: 'finance',
  meta: {
    title: '财务中心',
    icon: 'component'
  },
  component: Layout,
  children: [
    {
      path: 'capitalAccount/index',
      name: 'capitalAccoun',
      meta: { title: '资金账户' },
      component: () => import('@/views/finance/capitalAccount/index')
    },
    {
      path: 'merchantsBalance/index',
      name: 'merchantsBalance',
      meta: { title: '商家余额' },
      component: () => import('@/views/finance/merchantsBalance/index')
    },
    {
      path: 'merchantsBalance/detail',
      name: 'merchantsBalancedetail',
      hidden:true,
      meta: { title: '商家余额明细', activeMenu: '/finance/merchantsBalance/index'},
      component: () => import('@/views/finance/merchantsBalance/detail')
    },
    {
      path: 'salesmanBalanc/index',
      name: 'salesmanBalanc',
      meta: { title: '业务员余额' },
      hidden:true,
      component: () => import('@/views/finance/salesmanBalanc/index')
    },
    {
      path: 'salesmanBalanc/detail',
      name: 'salesmanBalancdetail',
      hidden: true,
      meta: { title: '业务员余额明细' ,activeMenu: '/finance/salesmanBalanc/index' },
      component: () => import('@/views/finance/salesmanBalanc/detail')
    },
    {
      path: 'collectionAndPaymen',
      name: 'collectionAndPaymen',
      meta: { title: '收付款管理' },
      redirect: '/finance/collectionAndPaymen/receipt/list',
      component: resolve => (require(["@/views/nested"], resolve)),
      children: [
        {
          path: 'receipt/list',
          name: 'receiptList',
          meta: { title: '收款单管理' },
          roles: ['admin'],
          component: () => import('@/views/finance/collectionAndPaymenList/receipt/index')
        },
        {
          path: 'receipt/detail',
          name: 'receiptDetail',
          hidden: true,
          meta: { title: '收款单详情', activeMenu: '/finance/collectionAndPaymen/receipt/list' },
          component: () => import('@/views/finance/collectionAndPaymenList/receipt/detail')
        },
        {
          path: 'paymentOrder/list',
          name: 'paymentOrderList',
          meta: { title: '付款单管理' },
          component: () => import('@/views/finance/collectionAndPaymenList/paymentOrder/index')
        },
        {
          path: 'paymentOrder/detail',
          name: 'paymentOrderDetail',
          hidden: true,
          meta: { title: '付款单详情', activeMenu: '/finance/collectionAndPaymen/paymentOrder/list' },
          component: () => import('@/views/finance/collectionAndPaymenList/paymentOrder/detail')
        },
      ]
    }, {
      path: 'businessStatement',
      name: 'businessStatement',
      meta: { title: '业务账单管理' },
      component: resolve => (require(["@/views/nested"], resolve)),
      children: [
        {
          path: 'business/list',
          name: 'businessList',
          meta: { title: '商家业务账单' },
          component: () => import('@/views/finance/businessStatement/index')
        },
        {
          path: 'business/detail/:merchantsId',
          name: 'businessDetail',
          hidden: true,
          meta: { title: '商家业务账单详情' },
          component: () => import('@/views/finance/businessStatement/businessDetail')
        },
        {
          path: 'salesman/list',
          name: 'salesmanList',
          meta: { title: '业务员账单', },
          component: () => import('@/views/finance/businessStatement/salesmanList')
        },
        {
          path: 'salesman/detail/:salesmanId',
          name: 'salesmanDetail',
          hidden: true,
          meta: { title: '业务员账单详情' },
          component: () => import('@/views/finance/businessStatement/salesmanDetail')
        }]
    },{
      path: 'statementManagement',
      name: 'statementManagement',
      meta: {title: '业务结算单管理'},
      component: resolve => (require(["@/views/nested"], resolve)),
      children: [
        {
          path: 'business/list',
          name: 'businessList',
          meta: { title: '商家结算单管理' },
          component: () => import('@/views/finance/statementManagement/businessList')
        },
        {
          path: 'salesman/list',
          name: 'salesmanList',
          meta: { title: '业务员结算单管理' },
          component: () => import('@/views/finance/statementManagement/salesmanList')
        }
      ]
    },{
      path: 'withdraw',
      name: 'withdraw',
      meta: { title: '提现管理' },
      component: () => import('@/views/finance/withdraw/index')
    },{
      path: 'recharge',
      name: 'recharge',
      meta: { title: '充值管理' },
      component: () => import('@/views/finance/recharge/index')

    },{
      path: 'merchantsDeposit',
      name: 'merchantsDeposit',
      meta: { title: '商家保证金管理' },
      component: resolve => (require(["@/views/nested"], resolve)),
      children: [
        {
          path: 'pay',
          name: 'pay',
          meta: { title: '商家保证金缴纳' },
          component: () => import('@/views/finance/merchantsDeposit/pay')
        },
        {
          path: 'giveBack',
          name: 'giveBack',
          meta: { title: '商家保证金退还' },
          component: () => import('@/views/finance/merchantsDeposit/giveBack')
        }]
    },{
      path: 'productDeposit',
      name: 'productDeposit',
      meta: { title: '品种保证金管理' },
      component: resolve => (require(["@/views/nested"], resolve)),
      children: [
        {
          path: 'pay',
          name: 'pay',
          meta: { title: '品种保证金缴纳' },
          component: () => import('@/views/finance/productDeposit/pay')
        },
        {
          path: 'giveBack',
          name: 'giveBack',
          meta: { title: '品种保证金退还' },
          component: () => import('@/views/finance/productDeposit/giveBack')
        }]
    },{
      path: 'royalty',
      name: 'royalty',
      meta: {title: '平台使用费管理'},
      component: () => import('@/views/finance/royaltyManagement/index')
    }]
}
export default finance
