import requestAxios from '@/utils/requestAxios'
import requestExport from '@/utils/requestExport'

export function list(data, needApprovalStatus=false ) {
  let dataFormat = data
  if (dataFormat.model?.approvalStatus && !needApprovalStatus) {
    delete dataFormat.model.approvalStatus
  }
  return requestAxios({
    method: 'post',
    url: '/api/product/admin/product/page',
    data: dataFormat
  })
}

export function editApi(data) {
  return requestAxios({
    url: '/api/product/admin/product',
    method: data.id > 0 ? 'put' : 'post',
    data
  })
}

export function deleteApi(id) {
  return requestAxios({
    url: '/api/product/admin/product?ids[]=' + id,
    method: 'delete'
  })
}

export function getDetailApi(id) {
  return requestAxios({
    url: '/api/product/admin/product/detail/' + id,
    method: 'get'
  })
}

export function getExportApi() {
  return requestAxios({
    url: '/api/product/admin/product/export' + id,
    method: 'get'
  })
}

export function pageCount(data) {
  return requestAxios({
    method: 'post',
    url: '/api/product/admin/product/activity/productStatistics',
    data
  })
}

// 驳回
export function setBatchRejectProduct(data) {
  return requestAxios({
    method: 'post',
    url: '/api/product/admin/product/batchRejectProduct',
    data
  })
}

// 审核通过
export function setBatchAcceptProduct(data) {
  return requestAxios({
    method: 'post',
    url: '/api/product/admin/product/batchAcceptProduct',
    data
  })
}
// 审核通过
export function bannerProductList(data) {
  return requestAxios({
    method: 'post',
    url: '/api/product/admin/product/searchListByBrand',
    data
  })
}

// 产品导入记录接口
export function importProductList(data) {
  return requestAxios({
    method: 'post',
    url: '/api/product/admin/productUpload/page',
    data
  })
}

// 产品导入接口
export function importProductFile(data) {
  return requestAxios({
    method: 'post',
    url: '/api/product/admin/productPlatform/import',
    data
  })
}

// 产品导入记录删除接口
export function importProductDelete(id) {
  return requestAxios({
    method: 'delete',
    url: '/api/product/admin/productUpload?ids[]=' + id
  })
}

// 产品导出记录
export function exportProductLog(data) {
  return requestExport({
    method: 'post',
    url: '/api/product/admin/productUploadLog/export',
    data,
    headers: { responseType: 'blob' }
  })
}

// 获取商品标签
export function fetchLabslList(data) {
  return requestAxios({
      method: 'post',
      url: '/api/product/admin/productLabel/page',
      data
  })
}

