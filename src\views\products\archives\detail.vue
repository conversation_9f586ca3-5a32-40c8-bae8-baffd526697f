<template>
  <div class="archivesEditContent">

      <div class="item">
        <module-title title="基础信息" />
        <div>
          <el-form :inline="true" label-width="100px" :model="query" >
            <el-form-item class="formItem"  label="产品编码:" >
               {{query.productNumber}}
            </el-form-item>
            <el-form-item class="formItem" prop="productNumber" label="产品名称:" :rules="[{ required: true, message: '请填写产品名称',trigger: 'blur' }]" >
              {{query.productNumber}}
            </el-form-item>
            <el-form-item  class="formItem" prop="productNumber" label="助记码:" :rules="[{ required: true, message: '请填写助记码',trigger: 'blur' }]" >
              {{query.productNumber}}
            </el-form-item>
            <el-form-item  class="formItem" prop="productNumber" label="经营类目:" :rules="[{ required: true, message: '请选择经营类目',trigger: 'blur' }]" >
             {{query.type}}
            </el-form-item>
            <el-form-item  class="formItem" prop="productNumber" label="通用名称:" :rules="[{ required: true, message: '请填写通用名称',trigger: 'blur' }]" >
             {{query.productNumber}}
            </el-form-item>
            <el-form-item  class="formItem" prop="productNumber" label="产品分类:" :rules="[{ required: true, message: '请填写产品分类',trigger: 'blur' }]" >
              {{query.productNumber}}
            </el-form-item>
            <el-form-item  class="formItem" prop="productNumber" label="规格:" :rules="[{ required: true, message: '请填写产品规格',trigger: 'blur' }]" >
              {{query.productNumber}}
            </el-form-item>
            <el-form-item  class="formItem" prop="productNumber" label="品牌:" :rules="[{ required: true, message: '请填写产品品牌',trigger: 'blur' }]" >
              {{query.productNumber}}
            </el-form-item>
            <el-form-item  class="formItem" prop="productNumber" label="剂型:" :rules="[{ required: true, message: '请填写产品剂型',trigger: 'blur' }]" >
              {{query.productNumber}}
            </el-form-item>
            <el-form-item  class="formItem" prop="productNumber" label="包装单位:" :rules="[{ required: true, message: '请填写产品包装单位',trigger: 'blur' }]" >
              {{query.productNumber}}
            </el-form-item>
             <el-form-item  class="formItem" prop="productNumber" label="生产厂家:" :rules="[{ required: true, message: '请填写生产厂家',trigger: 'blur' }]" >
              {{query.productNumber}}
            </el-form-item>
            <el-form-item  class="formItem" prop="productNumber" label="计量单位:" :rules="[{ required: true, message: '请填写产品计量单位',trigger: 'blur' }]" >
              {{query.productNumber}}
            </el-form-item>


            <el-form-item  class="formItem" prop="productNumber" label="产地:" :rules="[{ required: true, message: '请填写产地',trigger: 'blur' }]" >
             {{query.productNumber}}
            </el-form-item>
            <el-form-item  class="formItem" prop="productNumber" label="批准文号:" :rules="[{ required: true, message: '请填写批准文号',trigger: 'blur' }]" >
              {{query.productNumber}}
            </el-form-item>
            <el-form-item  class="formItem" prop="productNumber" label="中包装:" :rules="[{ required: true, message: '请填写中包装',trigger: 'blur' }]" >
              {{query.productNumber}}
            </el-form-item>
            <el-form-item  class="formItem" prop="productNumber" label="件包装:" :rules="[{ required: true, message: '请填写件包装',trigger: 'blur' }]" >
              {{query.productNumber}}
            </el-form-item>



            <el-form-item  class="formItem" prop="productNumber" label="产品等级:" :rules="[{ required: true, message: '请填写产品等级',trigger: 'blur' }]" >
              {{query.productNumber}}
            </el-form-item>
            <el-form-item  class="formItem" prop="prescription" label="是否处方:" :rules="[{ required: true, message: '请填写是否处方',trigger: 'blur' }]" >
               {{query.productNumber}}
            </el-form-item>
            <el-form-item  class="formItem" prop="productNumber" label="条形码:" :rules="[{ required: true, message: '请填写条形码',trigger: 'blur' }]" >
             {{query.productNumber}}
            </el-form-item>
            <el-form-item  class="formItem" prop="productNumber" label="本位码:" :rules="[{ required: true, message: '请填写本位码',trigger: 'blur' }]" >
             {{query.productNumber}}
            </el-form-item>
            <el-form-item  class="formItem" prop="productNumber" label="质量标准:" :rules="[{ required: true, message: '请填写质量标准',trigger: 'blur' }]" >
             {{query.productNumber}}
            </el-form-item>
            <el-form-item  class="formItem" prop="productNumber"  label-width="130px" label="上市许可持有人:" :rules="[{ required: true, message: '请填写上市许可持有人',trigger: 'blur' }]" >
             {{query.productNumber}}
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="item">
        <module-title title="内容信息" />
        <el-form  :inline="false" label-width="100px" :model="query">
          <el-form-item label="产品图片">
            <div class="productPicContent">
              {{query.productNumber}}
            </div>
          </el-form-item>
          <el-form-item label="产品详情">
            <p>{{query.detail}}</p>
          </el-form-item>
          <el-form-item label="说明书">
               <p>{{query.document}}</p>
          </el-form-item>
        </el-form>
      </div>
      <div class="item">
        <module-title title="商品资质" />
        <div>
          <div class="detailMsg">上传材料为复印件加盖企业原印公章且或质管章，支持JPG、JPEG、PNG、BMP格式，大小不超过2M</div>
          <el-table :data="query.tablelist"  border fit highlight-current-row  style="width: 100%" >
            <el-table-column
              v-for="(item, index) in zhengjianliebiao"
              :key="index"
              :width="item.width"
              :min-width="(item.width?item.width:'350px')"
              :label="item.label"
              show-overflow-tooltip
              align="center"
            >
              <template slot-scope="{row}">
                  <span >{{row[item.productNumber]}}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div class="item">
        <module-title title="GSP质量" />
        <div>
            <el-form :inline="true" label-width="100px" :model="query" >
            <el-form-item  class="formItem" prop="productNumber" label="养护类型:" :rules="[{ required: true, message: '请选择养护类型',trigger: 'blur' }]" >
             {{query.productNumber}}
            </el-form-item>
            <el-form-item class="formItem" prop="productNumber" label="养护周期:" :rules="[{ required: true, message: '请填写养护周期',trigger: 'blur' }]" >
              {{query.productNumber}}
            </el-form-item>
            <el-form-item  class="formItem" prop="productNumber" label-width="120px" label="存储方式:" :rules="[{ required: true, message: '请填写存储方式',trigger: 'blur' }]" >
              {{query.productNumber}}
            </el-form-item>
            <el-form-item  class="formItem" prop="productNumber" label="存储条件:" :rules="[{ required: true, message: '请填写存储条件',trigger: 'blur' }]" >
              {{query.productNumber}}
            </el-form-item>
            <el-form-item  class="formItem" prop="prescription"  label="是否医保:" :rules="[{ required: true, message: '请填写是否医保',trigger: 'blur' }]" >
              {{query.productNumber}}
            </el-form-item>
            <el-form-item  class="formItem" prop="productNumber" label="医保号:" :rules="[{ required: true, message: '请填写医保号',trigger: 'blur' }]" >
              {{query.productNumber}}
            </el-form-item>
            <el-form-item  class="formItem" prop="productNumber"  label-width="120px" label="特殊管理药品:" :rules="[{ required: true, message: '请填写特殊管理药品',trigger: 'blur' }]" >
              {{query.productNumber}}
            </el-form-item>
            <el-form-item  class="formItem" prop="prescription"  label-width="120px" label="是否冷藏冷链:" :rules="[{ required: true, message: '请填写是否冷藏冷链',trigger: 'blur' }]" >
              {{query.productNumber}}
            </el-form-item>
            <el-form-item  style="width:45%" class="formItem" prop="productNumber" label="性能:" :rules="[{ required: true, message: '请填写性能',trigger: 'blur' }]" >
              {{query.productNumber}}
            </el-form-item>
            <el-form-item  style="width:45%" class="formItem" prop="productNumber" label="用途:" :rules="[{ required: true, message: '请填写用途',trigger: 'blur' }]" >
              {{query.productNumber}}
            </el-form-item>
          </el-form>
        </div>
      </div>

      <el-form   :inline="false" label-width="100px" :model="query" style="  position:absolute;top:2vh; right:15px; background:#fff; height:38px;" >
        <el-form-item  label="">
          <el-button @click="clearFun()">取 消</el-button>
          <el-button @click="downFun()">下 架</el-button>
          <el-popover placement="top" width="250" v-model="query.showRefuteContent">
            <div style="line-height:36px;">驳回理由</div>
            <div>
              <el-input type="textarea" v-model="query.textarea" placeholder=""  rows="4"></el-input>
            </div>
            <div style="text-align: right; margin: 0;padding-top:10px;" >
              <el-button  type="text" @click="query.showRefuteContent = false">取消</el-button>
              <el-button type="primary" @click="submitFun('ruleForm')">确认</el-button>
            </div>
            <el-button slot="reference" style="margin-left:10px;" >驳 回</el-button>
          </el-popover>
        </el-form-item>
      </el-form>
  </div>
</template>
<script>
// import rules from '@/utils/rules'
import Tinymce from '@/components/Tinymce'
import ModuleTitle from '@/components/PageModuleTitle'
export default {
  components:{
    Tinymce,
    ModuleTitle
  },
  data() {
    return {
      editCertificates:false,
      typeOptions: [
        {
          value: 1,
          label: "药品1"
        },
        {
          value: 2,
          label: "药品2"
        },
        {
          value: 3,
          label: "药品3"
        },
        {
          value: 4,
          label: "药品4"
        }
      ],
      dialogImageUrl:"",
      isShowBigPic:false,
      certificatesForm:{
         id:1,
        certificatesType:"",
        outTime:"",
        annex:""
      },
      query: {
        productNumber: "",
        type: "",
        uploadPics:[],
        tablelist:[
          {
            id:1,
            certificatesType:"注册批件",
            outTime:"",
            annex:""
          },
          {
            id:1,
            certificatesType:"质量标准",
            outTime:"",
            annex:""
          },
          {
            id:1,
            certificatesType:"样盒及说明书",
            outTime:"",
            annex:""
          },
          {
            id:1,
            certificatesType:"质检报告",
            outTime:"",
            annex:""
          },
          {
            id:1,
            certificatesType:"必要的药品补充批件、标准颁布件",
            outTime:"",
            annex:""
          }
        ],
      },
      zhengjianliebiao:[
      {
          label: '证件类型',
          productNumber: "certificatesType",
          width:'300px'
      },
      {
          label: '过期时间',
          productNumber: "outTime",
          width:'200px'
      },
      {
          label: '附件',
          productNumber: "annex"
      },
      ]
    }
  },
  props: {
    row: {
      type: Object
    },
    visible: {
      type: Boolean,
      default: false,
      required: true
    },
    isReload: {
      type: Boolean,
      default: false,
      required: true
    }
  },
  methods: {
    clearFun: function() {
      this.$emit("update:visible", false);
      this.$emit("update:row", {});
    },
    downFun() {
      console.log("下架提交");
    },
    picRemove(file) {
      console.log(file);
    },
    showBigPic(file) {
      this.isShowBigPic = true;
      console.info(file.url)
      this.dialogImageUrl = file.url;
    },
    editCertificatesFun(file) {
      this.editCertificates = true;
    },
    submitFun: function(archivesForm) {
      this.$refs[archivesForm].validate(valid => {
        if (valid) {
          this.$emit("update:visible", false);
          console.info("提交修改");
          this.$emit("update:isReload", true);
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    }
  },
  mounted() {
    this.query = this.row;
    this.query.tablelist=[
          {
            id:1,
            certificatesType:"注册批件",
            outTime:"",
            annex:""
          },
          {
            id:1,
            certificatesType:"质量标准",
            outTime:"",
            annex:""
          },
          {
            id:1,
            certificatesType:"样盒及说明书",
            outTime:"",
            annex:""
          },
          {
            id:1,
            certificatesType:"质检报告",
            outTime:"",
            annex:""
          },
          {
            id:1,
            certificatesType:"必要的药品补充批件、标准颁布件",
            outTime:"",
            annex:""
          }
        ]
  },
  beforeDestroy() {}
};
</script>
<style lang="less" scoped>
.archivesEditContent {
  margin: -30px -20px;
  border-top: 1px solid #ebecee;
  padding: 30px 20px;
  .item {
    width: 100%;
    margin-bottom: 30px;
    border-bottom: 1px solid #eeeeee;
  }

  .uploadPic{padding-bottom: 100%;margin-bottom: -100%;display: flex;justify-content: center;align-items: center; flex-wrap: wrap;
    >div{min-width: 100%; height:25px;}
  }
  .productPicContent .text p{
    font-family: 'PingFangSC-Regular', 'PingFang SC', sans-serif, 'PingFangSC-Regular', 'PingFang SC', sans-serif-400;
    font-weight: 400;
    color: #aaaaaa;
    line-height: 20px;
    font-size: 13px;
    margin:0;
  }
  .formItem{width:250px;}
  .detailMsg{font-family: 'PingFangSC-Regular', 'PingFang SC', sans-serif, 'PingFangSC-Regular', 'PingFang SC', sans-serif-400;
    font-weight: 400;
    color: #aaaaaa;
    line-height: 20px;
    padding-bottom: 20px;

    font-size: 13px;
  }
}
</style>
