<template>
  <div class="archivesEditContent">
    <!-- 顶部按钮模块 -->
    <div class="top_title flex_between_center">
      <div>商家信息</div>
      <div>
        <el-popover v-model.trim="rejectFlag" placement="bottom-end" title="取消提醒" width="300" trigger="click">
          <el-button slot="reference">取消</el-button>
          确定取消编辑?取消后编辑内容将不被保存!
          <div style="text-align: right; margin: 0;padding-top:14px">
            <el-button size="mini" @click="rejectFlag = false">取消</el-button>
            <el-button type="primary" size="mini" @click="clearFun()">确定</el-button>
          </div>
        </el-popover>
        <el-button @click="edit('editForm')" type="primary">生成档案</el-button>
      </div>
    </div>
    <!-- 顶部按钮模块结束 -->
    <el-form :inline="true" label-width="140px" :model="query" ref="editForm" :rules="rules">
      <div class="item">
        <div class="title"><span>基础信息</span></div>
        <div>
          <el-form-item class="formItem" prop="code" label="企业编码:">
            <el-input :disabled="true" clearable style="width: 200px" v-model.trim="query.code" placeholder="企业编码由系统生成">
            </el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="name" label="企业名称:"
            :rules="[{ required: true, message: '请填写企业名称', trigger: 'blur' }]">
            <el-input clearable style="width: 200px" v-model.trim="query.name" placeholder="请填写企业名称"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="creditCode" label="信用代码:">
            <el-input clearable style="width: 200px" v-model.trim="query.creditCode" placeholder="请填写信用代码"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="legalPerson" label="法定代表人:">
            <el-input clearable style="width: 200px" v-model.trim="query.legalPerson" placeholder="请填写法定代表人"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="registerCapital" label="注册资金(万):">
            <el-input clearable style="width: 200px" v-model.trim="query.registerCapital" placeholder="请填写注册资金"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="ceoName" label="负责人:">
            <el-input clearable style="width: 200px" v-model.trim="query.ceoName" placeholder="请填写负责人"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="enterPhone" label="负责人手机:">
            <el-input clearable style="width: 200px" v-model.trim="query.enterPhone" placeholder="请填写负责人手机"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="registerAddress" label="注册地址:">
            <el-input clearable style="width: 200px" v-model.trim="query.registerAddress" placeholder="请填写注册地址"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="source" label="来源:">
            <el-input clearable style="width: 400px" v-model.trim="query.source" placeholder="请输入来源"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="regionId" label="所在区域:" :rules="[{ required: true, message: '请选择所在区域', trigger: 'blur' }]">
            <el-cascader ref="city" v-model.trim="query.regionId" style="width: 200px" placeholder="请选择所在地区"
              :props="{ value: 'id', label: 'label'}" @change="cityChange" :options="areasTree" clearable>
            </el-cascader>
          </el-form-item>
          <div class="address-selector">
            <el-form-item prop="detailLocation" label="企业地址">
              <el-input clearable style="width: 400px" v-model.trim="query.detailLocation" placeholder="可手动填写地址或者地图点选">
              </el-input>
            </el-form-item>
            <el-form-item class="formItem" prop="latitude" label="">
              - <el-input clearable style="width: 380px" :value="coordinate" placeholder="经纬度坐标" readonly
                @click.native="handleShowMap">
                <template slot="append">
                  <span style="cursor: pointer;">地图选择</span>
                </template>
              </el-input>
              <map-location-select v-if="mapVisible" class="location-select" :center="currentPosition"
                @click="handleGetPosition" @close="handleClose" />
            </el-form-item>
          </div>
        </div>
      </div>
    </el-form>
  </div>
</template>


<script>
  import {
    areas
  } from "@/api/businessCenter/businessList";
  import { deleteEnterprise, addEnterprise, editEnterprise } from "@/api/enterprise";
  import MapLocationSelect from '@/components/MapLocationSelect'
import { query } from '@/api/products/categoryPlatform';
  export default {
    //import引入的组件
    components: {
      MapLocationSelect
    },
    data() {
      return {
        rejectFlag: false,
        areasTree: [],
        regionId: [],
        mapVisible: false,
        rules:{},
        id:'',
        query: {
          code: '',
          name: '',
          regionId:[],
          creditCode:'',
          legalPerson:'',
          ceoName:'',
          registerCapital:'',
          enterPhone:'',
          detailLocation:'',
          provinceId: '',
          cityId: '',
          countyId: '',
          registerAddress: '',
        }
      }
    },
    //生命周期 - 挂载完成（可以访问DOM元素）
    mounted() {
      if(this.$route.query && this.$route.query.id){
        this.id = this.$route.query.id;
        console.log('---id-->',this.$route.query.id);
        this.getDetail();
      }
    },

    computed: {
      currentPosition() {
        return [this.query.longitude, this.query.latitude]
      },
      coordinate() {
        if (this.query.latitude && this.query.longitude) {
          return this.query.latitude + ',' + this.query.longitude
        }
        return ''
      }
    },

    created() {
      this.getareas();
    },

    filters: {},

    //方法集合
    methods: {
      getDetail(){
        deleteEnterprise(this.id).then(res=>{
          if(res.code == 0) {
            this.query = res.data || {};
            this.regionId = [res.data.provinceId,res.data.cityId,res.data.countyId];
            this.query.regionId = this.regionId;
          }
        })
      },
      clearFun() {
        this.$router.go(-1);
      },
      async getareas() {
        let {
          data
        } = await areas();
        let list = data.filter(item => {
          return item.id != '0';
        })
        this.areasTree = list;
      },
      cityChange(e) {
        this.query.provinceId = e[0];
        this.query.cityId = e[1];
        this.query.countyId = e[2];
        this.regionId = e;
      },
      handleShowMap() {
        this.mapVisible = true
      },
      handleClose() {
        this.mapVisible = false
      },
      handleGetPosition({ lat, lng, address }) {
        this.$set(this.query, 'latitude', lat)
        this.$set(this.query, 'longitude', lng)
        this.$set(this.query, 'detailLocation', address)
        this.$refs['editForm'].clearValidate(['latitude'])
        this.$refs['editForm'].clearValidate(['detailLocation'])
      },
      edit(content) {
        console.log('content----->',this.query);
        if (content == 'editForm') {
          this.$refs[content].validate(async (valid) => {
            console.log('验证', valid);
            if (valid) {
              delete this.query.code;
              if(this.id.length > 0) {
                // 编辑
                editEnterprise(this.query).then(res=>{
                  if(res.code == 0){
                    this.$message.success('修改企业档案成功');
                    this.clearFun();
                  }
                })
              } else {
                addEnterprise(this.query).then(res=>{
                  if(res.code == 0) {
                    this.$message.success('新增企业档案成功');
                    this.clearFun();
                  }
                })
              }
            }
          })
        }
      }
    },

  }

</script>


<style lang='less' scoped>
  .archivesEditContent {
    // margin: 30px 20px;
    border-top: 1px solid #ebecee;
    padding: 0 20px 20px 20px;
    background: #fff;

    .item {
      width: 100%;
      margin-bottom: 30px;
    //   border-bottom: 1px solid #eeeeee;

      .title {
        padding: 0 0 15px;

        span {
          font-size: 16px;
          padding-left: 10px;
          border-left: 4px solid rgba(64, 158, 255, 1);
        }
      }
    }

    .top_title {
      height: 56px;
      line-height: 56px;
      font-family: "PingFangSC-Regular", "PingFang SC", sans-serif;
      font-size: 18px;
      text-align: left;
      border-bottom: 1px solid #eeeeee;
      margin-bottom: 20px;

      .el-button {
        margin-left: 10px;
      }
    }

    .findCategory {
      padding-bottom: 20px;
      display: flex;
      flex-wrap: wrap;
    }

    .location-select {
      position: absolute;
      top: 40px;
      background: #fff;
      left: 0;
      width: 500px;
      z-index: 999;
      box-shadow: 0 0 4px 2px rgba(0, 0, 0, .1);
    }

    /deep/ .el-col {
      line-height: 40px;
    }

    /deep/ .el-upload {
      width: 40px;
      height: 40px;
      position: relative;
    }

    /deep/ .el-upload>i {
      font-size: 16px;
      position: absolute;
      left: 50%;
      top: 50%;
      -webkit-transform: translateX(-50%) translateY(-50%);
      transform: translateX(-50%) translateY(-50%);
    }

    /deep/ .el-upload-list .el-upload-list__item {
      width: 40px;
      height: 40px;
    }

    /deep/ .hide .el-upload--picture-card {
      display: none;
    }
  }

</style>
