<template>
  <div class="archivesPageContent">
    <im-search-pad
      :is-expand.sync="isExpand"
      :model="listQuery"
      @reset="resetForm('searchForm')"
      @search="onSearchSubmitFun"
    >
      <im-search-pad-item prop="productCode">
        <el-input v-model="listQuery.model.productCode" placeholder="请输入商品编码" />
      </im-search-pad-item>
      <im-search-pad-item prop="productName">
        <el-input v-model="listQuery.model.productName" placeholder="请输入商品名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="manufacturer">
        <el-input v-model="listQuery.model.manufacturer" placeholder="请输入生产厂家" />
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="timer">
        <el-date-picker
          v-model.trim="timer"
          type="daterange"
          align="right"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="pickerOptions"
          @change="selectTime"
          :default-time="['00:00:00', '23:59:59']"
          value-format="yyyy-MM-dd HH:mm:ss"
        >
        </el-date-picker>
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        v-model.trim="listQuery.model.publishStatusEnum"
        :tabs="approvalStatusList"
        @change="chageTabsFun"
      >
        <template slot="button">
          <el-button
            :disabled="multipleSelectionId.length == 0"
            v-if="listQuery.model.publishStatusEnum == 'PENDING'"
            @click="acceptedList"
          >批量通过</el-button>
          <el-button
            :disabled="multipleSelectionId.length == 0"
            v-if="listQuery.model.publishStatusEnum == 'PENDING'"
            @click="rejectList"
          >批量驳回</el-button>
        </template>
      </tabs-layout>
      <div class="table">
        <el-table
          ref="table"
          v-if="list"
          @selection-change="selectTableItemFun"
          v-loading="listLoading"
          :data="list"
          row-key="id"
          border
          fit
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column
            align="center"
            width="80"
            :render-header="renderHeader"
            fixed
          >
            <template slot-scope="scope">
              <span>{{ scope.$index + 1 }} </span>
            </template>
          </el-table-column>
          <el-table-column
            
            type="selection"
            width="55"
            align="center"
            fixed
          ></el-table-column>
          <el-table-column label="主图" prop="pictIdS" class-name="img-cell">
            <template slot-scope="scope">
              <img :src="scope.row.pictIdS | imgFilter" class="productImg">
              <!-- <img v-else src="@/assets/product.png" class="productImg"> -->
            </template>
          </el-table-column>
          <el-table-column
            v-for="(item, index) in tableTitle"
            :key="index"
            :min-width="item.width ? item.width : '350px'"
            :label="item.label"
            show-overflow-tooltip
            :align="item.name == 'pictIdS' ? 'center' : 'left'"
          >
            <template slot-scope="{ row }">
              <el-button
                v-if="item.name == 'area'"
                type="text"

                @click="showAreaFun(row)"
              >查看区域</el-button
              >
              <span v-else>{{ row[item.name] }}</span>
            </template>
          </el-table-column>

          <el-table-column
            v-if="listQuery.model.publishStatusEnum == 'PENDING'"
            fixed="right"
            align="center"
            label="操作"
            width="100"
            class="itemAction"
          >
            <template slot-scope="{ row }">
              <el-row class="table-edit-row">
                <span class="table-edit-row-item">
                  <el-button type="text"  @click="agreeFun(row)">通过</el-button>
                </span>
                <span class="table-edit-row-item">
                  <el-button type="text"  @click="rejectFun(row)">驳回</el-button>
                </span>
              </el-row>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-if="total > 0"
          :pageSizes="[10, 20, 50, 100]"
          :total="total"
          :page.sync="listQuery.current"
          :limit.sync="listQuery.size"
          @pagination="getlist"
        />
      </div>
    </div>
    <el-dialog
      :visible.sync="rejectFlag"
      title="驳回理由"
      width="500px"
      :show-close="true"
    >
      <el-input
        type="textarea"
        v-model.trim="rejectReason"
        :rows="5"
      ></el-input>
      <span slot="footer">
        <el-button @click="rejectFlag = false">取消</el-button>
        <el-button type="primary" @click="rejectConfirm">确定</el-button>
      </span>
    </el-dialog>
    <!-- 设置 编辑 -->
    <!-- <el-dialog v-if="showEditPage" :title="(row.id>0?'编辑':'新增')+'销售商档案'" :visible.sync="showEditPage" width="80%" :show-close="false">
      <edit :visible.sync="showEditPage" :isReload.sync="submitReload" :tabType.sync="listQuery.model.approvalStatus.code" :row.sync="row"></edit>
    </el-dialog> -->
    <!-- 设置 编辑 -->
    <el-dialog
      v-if="showAreaFlag"
      :visible.sync="showAreaFlag"
      width="80%"
      :close-on-click-modal="false"
      :append-to-body="true"
    >
      <div slot="title">
        <span>可代理销售区域</span>
        <el-button
          disabled

          plain
          type="primary"
          style="margin-left: 10px"
          >{{ row.productName }}</el-button
        >
      </div>
      <areasD :visible.sync="showAreaFlag" :row.sync="row"></areasD>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from "@/components/Pagination";
// import { areas } from "@/api/enterprise";
import tableInfo from "@/views/businessCentric/applicationForGoods/tableInfo";
import {
  getList,
  agree,
  rejected,
  acceptedList,
  rejectList
} from "@/api/businessCentric/applicationForGoods";
import areasD from "@/views/businessCentric/applicationForGoods/areas";
import TabsLayout from '@/components/TabsLayout'

export default {
  data() {
    return {
      isExpand: false,
      listLoading: false,
      list: [],
      listQuery: {
        current: 1,
        size: 10,
        model: {
          publishStatusEnum: "PENDING",
        },
      },
      total: 0,
      cityValue: [],
      tableTitle: [],
      tableSelectTitle: [],
      multipleSelection: [],
      multipleSelectionId: [],
      showSelectTitle: false,
      // props: {
      //   lazy: true,
      //   async lazyLoad(node, resolve) {
      //     const { level } = node;
      //     let id = node.data ? node.data.id : "";
      //     let res = await areas({ parentId: id });
      //     let list = res.data;
      //     list.forEach((item) => {
      //       item.value = item.id;
      //       item.leaf = level >= 2;
      //     });
      //     resolve(list);
      //   },
      // },
      row: {},
      rejectFlag: false,
      rejectReason: "",
      showAreaFlag: false,
      pickerOptions: {
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      timer: [],
    };
  },
  computed: {
    approvalStatusList() {
      return [
        {
          name: '待审核',
          value: 'PENDING'
        },
        {
          name: '已驳回',
          value: 'REJECTED'
        },
        {
          name: '已撤销',
          value: 'REPEAL'
        }
      ]
    }
  },
  methods: {
    async acceptedList() {
      this.$confirm("此操作将审核通过推广商品，是否继续？","提示",{
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: 'warning',
      }).then(async ()=>{
        let {data} = await acceptedList(this.multipleSelectionId)
        if(data) {
          this.$message.success('批量通过成功')
          this.$router.push("/businessCentric/promotionGoods");
        }
      })
    },
    async rejectList() {
      this.$confirm("此操作将审核驳回推广商品，是否继续？","提示",{
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: 'warning',
      }).then(async ()=>{
        let {data} = await rejectList(this.multipleSelectionId)
        if(data) {
          this.listQuery = {
            current: 1,
            size: 10,
            model: {
              publishStatusEnum: "REJECTED",
            },
          }
          this.$message.success('批量驳回成功')
          this.list = []
          this.initTbaleTitle()
          this.getlist()
        }
      })
    },
    selectTime(row) {
      this.listQuery.model.startTime = row[0];
      this.listQuery.model.endTime = row[1];
    },
    resetForm() {
      this.list = [];
      this.timer = [];
      this.listQuery = {
        current: 1,
        size: 10,
        model: {
          publishStatusEnum: this.listQuery.model.publishStatusEnum,
        },
      };
      this.getlist();
    },
    showAreaFun(row) {
      this.showAreaFlag = true;
      this.row = row;
    },
    async rejectConfirm() {
      if (!this.rejectReason) {
        this.$message.error("请输入驳回理由");
        return;
      }
      let obj = {
        id: this.row.id,
        data: this.rejectReason,
      };
      let { data } = await rejected(obj);
      if (data) {
        this.rejectFlag = false;
        this.rejectReason = "";
        this.$message.success("已驳回该商品的推广申请");
        this.listQuery.model.publishStatusEnum = "REJECTED";
        this.listQuery.current = 1;
        this.list = [];
        this.getlist();
      }
    },
    rejectFun(row) {
      this.rejectFlag = true;
      this.row = row;
    },
    async agreeFun(row) {
      let { data } = await agree(row.id);
      if (data) {
        this.$message.success("已通过该商品的推广申请");
        this.$router.push("/businessCentric/promotionGoods");
      }
    },
    chageTabsFun() {
      this.list = [];
      this.listQuery = {
        current: 1,
        size: 10,
        model: {
          publishStatusEnum: this.listQuery.model.publishStatusEnum,
        },
      };
      this.initTbaleTitle();
      this.getlist();
    },
    getSrc(str) {
      if (!str) {
        return "";
      } else {
        return str.split(",")[0];
      }
    },
    cityChange(e) {
      this.listQuery.model.provinceId = e[0];
      this.listQuery.model.cityId = e[1];
      this.listQuery.model.countyId = e[2];
    },
    selectTableItemFun: function (val) {
      let arr = [];
      val.forEach((item) => {
        arr.push(item.id);
      });
      this.multipleSelection = val;
      this.multipleSelectionId = arr;
    },
    onSearchSubmitFun() {
      this.list = [];
      this.getlist();
    },
    async getlist() {
      this.listLoading = true;
      let { data } = await getList(this.listQuery);
      this.listLoading = false;
      this.list = data.records;
      this.total = data.total;
    },
    initTbaleTitle() {
      this.tableSelectTitle = [];
      this.tableTitle = tableInfo[this.listQuery.model.publishStatusEnum];
    },
    renderHeader(h, { column }) {
      var titles = tableInfo[this.listQuery.model.publishStatusEnum];
      var titlesName = ["显示字段项", "隐藏字段项"];
      return (
        <div style="position:relative">
          <div onClick={this.showHeaer}>
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="640px"
            center
            append-to-body={true}
          >
            <el-transfer
              vModel={this.tableSelectTitle}
              data={titles}
              titles={titlesName}
              onChange={this.setleftTitleFun}
            ></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button onClick={this.closeHeaer}>取消</el-button>
              <el-button type="primary" onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      );
    },
    setleftTitleFun(val) {
      this.tableSelectTitle = val;
    },
    showHeaer: function () {
      this.showSelectTitle = true;
    },
    closeHeaer: function () {
      this.showSelectTitle = false;
      this.tableSelectTitle = [];
    },
    setHeaer: function () {
      var titles = tableInfo[this.listQuery.model.publishStatusEnum];
      var listinfo = titles.filter((element, index, self) => {
        return !this.tableSelectTitle.includes(element.key);
      });
      this.tableTitle = listinfo;
      this.showSelectTitle = !this.showSelectTitle;
    },
  },
  created() {
    this.getlist();
    this.initTbaleTitle();
  },
  components: {
    Pagination,
    areasD,
    TabsLayout
  },
};
</script>


<style lang="less" scoped>
.archivesPageContent {
  padding: 0;
  .temp_searchBox {
    height: 64px;
    overflow: hidden;
    margin-bottom: 0;
  }
  .form-inline {
    height: 60px;
    overflow: hidden;
  }
  .title {
    border-bottom: 2px solid #ebecee;
    margin-bottom: 16px;
    span {
      margin-bottom: -2px;
      padding: 0 15px;
      height: 40px;
      line-height: 30px;
      display: block;
      background: rgba(255, 255, 255, 0);
      border-bottom: 2px solid rgb(64, 158, 255);
      font-size: 16px;
      font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
        "PingFang SC"-400;
      font-weight: 400;
      color: rgb(64, 158, 255);
    }
  }
  .formItem {
    width: 586px;
  }
  .line {
    color: #dfe6ec;
    margin: 0 6px;
  }
  .typeTabs {
    height: 40px;
    margin-bottom: -2px;
  }
}
/deep/.el-dialog__footer {
  border-top: 1px solid #ddd;
}
</style>
