export default {
  list: [
    {
      key: 1,
      label: '销售状态',
      name: "publishStatus",
      width: '120px',
      disabled: true
    },
    {
      key: 2,
      label: '商品编码',
      name: "productCode",
      width: '170px',
      disabled: true
    },
    {
      key: 3,
      label: '商品名称',
      name: "productName",
      width: '170px',
      disabled: true
    },
    {
      key: 4,
      label: '规格',
      name: "spec",
      width: '170px'
    },
    {
      key: 5,
      label: '生产厂家',
      name: "manufacturer",
      width: '160px'
    },
    {
      key: 6,
      label: '销售价',
      name: "salePrice",
      width: '120px'
    },
    {
      key: 7,
      label: '推广费',
      name: "promotionExpenses",
      width: "170px"
    },
    {
      key: 8,
      label: '业务员',
      name: "salesman<PERSON><PERSON>",
      width: '120px'
    },
    {
      key: 9,
      label: '可代理销售区域',
      name: "area",
      width: '140px'
    },
    {
      key: 9,
      label: '所属商家',
      name: "merchantName",
      width: '168px'
    },
  ],
  PENDING: [
    {
      key: 1,
      label: '商品编码',
      name: "code",
      width: '140px',
      disabled: true
    },
    {
      key: 2,
      label: '商品名称',
      name: "productName",
      width: '200px',
      disabled: true
    },
    {
      key: 3,
      label: '规格',
      name: "name",
      width: '150px',
      disabled: true
    },
    {
      key: 4,
      label: '生产厂家',
      name: "socialCreditCode",
      width: '200px'
    },
    {
      key: 5,
      label: '销售价（元）',
      name: "legalPerson",
      width: '160px'
    },
    {
      key: 6,
      label: '推广费（元）',
      name: "ceoName",
      width: '160px'
    },
    {
      key: 7,
      label: '商品分类',
      name: "ceoMobile",
      width: "112px"
    },
    {
      key: 8,
      label: '可代理销售区域',
      name: "region",
      width: '168px'
    }
  ],
}
