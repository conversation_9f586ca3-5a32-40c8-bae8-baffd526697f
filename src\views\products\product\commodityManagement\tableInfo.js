import { deepClone } from "@/utils"

const initTableInfo = [
    {
      key: 0,
      label: '商品状态',
      name: 'whetherOnSale',
      width: '140px',
      disabled: true
    },
    {
      key: 1,
      label: '主图',
      name: 'pictIdS',
      width: '80px',
      disabled: true
    },
    {
      key: 4,
      label: '商品信息',
      name: 'productName',
      width: '200px'
    },
    {
      key: 5,
      label: '其他信息',
      name: 'otherInfo',
      width: '200px'
    },
    {
      key: 13,
      label: '商品库存',
      name: 'realStockQuantity',
      width: '200px'
    },
    {
      key: 14,
      label: '销售价',
      name: 'salePrice',
      width: '200px'
    },
    {
      key: 9,
      label: '单品毛利',
      name: 'grossProfitRate',
      width: '150px'
    },
    {
      key: 10,
      label: '平台分类',
      name: 'platformCategoryId',
      width: '150px'
    },
    {
      key: 11,
      label: '所属商家',
      name: 'saleMerchantName',
      width: '150px'
    },
    {
      key: 99,
      label: '商品ID',
      name: 'id'
    },
    {
      key: 100,
      label: '操作时间',
      name: 'updateTime',
      width: '200px'
    }
]

export default {
  ALL: deepClone(initTableInfo),
  SALEING: deepClone(initTableInfo),
  SALEPENDING: deepClone(initTableInfo),
  PENDING: deepClone(initTableInfo),
  REJECTED: deepClone(initTableInfo),
}