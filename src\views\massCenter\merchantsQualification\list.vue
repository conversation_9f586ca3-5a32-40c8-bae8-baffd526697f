<template>
  <div class="archivesPageContent">
    <im-search-pad
      :has-expand="false"
      :model="listQuery"
      @reset="resetForm"
      @search="onSearchSubmitFun"
    >
      <im-search-pad-item prop="name">
        <el-input v-model="listQuery.model.name" placeholder="请输入客户名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="cityValue">
        <el-cascader placeholder="请选择所在区域" v-model="cityValue" :props="props" @change="cityChange" clearable />
      </im-search-pad-item>
      <im-search-pad-item prop="ceoName">
        <el-input v-model="listQuery.model.ceoName" placeholder="请输入联系人" />
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout
        :tabs="[{ name: '客户档案' }]"
      >
        <template slot="button">
          <div></div>
        </template>
      </tabs-layout>
      <div class="table">
        <el-table v-if="list" v-loading="listLoading" :data="list" row-key="id" border fit highlight-current-row style="width: 100%">
          <el-table-column align="center" width="65" :render-header="renderHeader" fixed>
            <template slot-scope="scope">
              {{scope.$index+1}}
            </template>
          </el-table-column>
          <el-table-column v-for="(item, index) in tableTitle" :key="index" :min-width="(item.width?item.width:'350px')" :label="item.label" show-overflow-tooltip align="left">
            <template slot-scope="{row}">
                <el-badge :value="row.abnormalNumber" class="item" v-if="item.name=='licenseStatusIsNormal'">
                <el-button v-if="row[item.name] && row[item.name].code=='Y'" type="text" style="color:#409EFF; padding-top: 7px; padding-bottom: 7px;">正常</el-button>
                <el-button v-else-if="row[item.name] && row[item.name].code=='N'" type="text" style="color:#FF3C54; padding-top: 7px; padding-bottom: 7px;">异常</el-button>
              </el-badge>
              <span v-else>{{ row[item.name] }}</span>
            </template>
          </el-table-column>

          <el-table-column fixed="right" align="center" label="操作" width="80" class="itemAction">
            <template slot-scope="scope">
              <el-row class="table-edit-row">
                <span v-if="checkPermission(['admin', 'admin-client-qualification:detail'])" class="table-edit-row-item">
                  <el-button @click="detailFun(scope.row)" type="text" >查看</el-button>
                </span>
                <span v-if="listQuery.status == 'reviewed'" class="table-edit-row-item">
                  <el-button @click="reviewedFun(scope.row)" type="text" >审核</el-button>
                </span>
              </el-row>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-if="total>0" :pageSizes="[2, 10, 20, 50]" :total="total" :page.sync="listQuery.current" :limit.sync="listQuery.size" @pagination="getlist" />
      </div>
    </div>
    <el-dialog v-if="showEditPage" :title="row.name" :visible.sync="showEditPage" width="80%">
      <edit :visible.sync="showEditPage" :isReload.sync="submitReload" :row.sync="row"></edit>
    </el-dialog>
  </div>
</template>

<script>
import checkPermission from '@/utils/permission'
import { setContextData, getContextData } from "@/utils/auth";
import tableInfo from "@/views/massCenter/merchantsQualification/tableInfo";
import Pagination from "@/components/Pagination";
import { list } from "@/api/massCenter/merchantsQualification";
import edit from "@/views/massCenter/merchantsQualification/edit";
import { areas } from "@/api/enterprise";
import TabsLayout from '@/components/TabsLayout'
// import selectAddr from "@/components/eyaolink/selectAddr";
export default {
  data() {
    return {
      listQuery: {
        current: 1,
        size: 10,
        model: {},
      },
      cityValue: [],
      showEdit: false,
      list: [],
      listLoading: false,
      tableTitle: [],
      total: 0,
      row: {},
      submitReload: false,
      showEditPage: false,
      tableSelectTitle: [0, 1, 2, 3, 4],
      showSelectTitle: false,
      props: {
        lazy: true,
        checkStrictly: true,
        async lazyLoad(node, resolve) {
          const { level } = node;
          let id = node.data ? node.data.id : "";
          let res = await areas({ parentId: id });
          let list = res.data;
          list.forEach((item) => {
            item.value = item.id;
            item.leaf = level >= 2;
          });
          resolve(list);
        },
      },
    };
  },
  methods: {
    checkPermission,
    cityChange(e) {
      this.listQuery.model.provinceId = e[0];
      this.listQuery.model.cityId = e[1];
      this.listQuery.model.countyId = e[2];
    },
    async getlist() {
      this.listLoading = true;
      let { data } = await list(this.listQuery);
      this.list = data.records;
      this.total = data.total;
      this.listLoading = false;
    },
    onSearchSubmitFun() {
      this.list = [];
      this.getlist();
    },
    resetForm() {
      this.addr = {};
      this.listQuery = {
        current: 1,
        size: 10,
        model: {},
      };
      this.cityValue = []
      this.initTbaleTitle();
      this.getlist();
    },
    detailFun(item) {
      // this.row = item;
      // this.showEditPage = true;
      setContextData("merchantsQualification_detail", this.listQuery);
      this.$router.push({
        path: "/massCenter/merchantsQualification/detail",
        query: {
          id: item.id,
        },
      });
    },
    renderHeader(h, { column }) {
      var titles = tableInfo["list"];
      var titlesName = ["显示字段项", "隐藏字段项"];
      return (
        <div style="position:relative">
          <div onClick={this.showHeaer}>
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="640px"
            center
          >
            <el-transfer
              vModel={this.tableSelectTitle}
              data={titles}
              titles={titlesName}
              onChange={this.setleftTitleFun}
            ></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button onClick={this.closeHeaer}>取消</el-button>
              <el-button type="primary" onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      );
    },
    setleftTitleFun(val) {
      this.tableSelectTitle = val;
    },
    showHeaer: function () {
      this.showSelectTitle = true;
    },
    closeHeaer: function () {
      this.showSelectTitle = false;
      this.tableSelectTitle = [];
    },
    setHeaer: function () {
      var titles = tableInfo["list"];
      var listinfo = titles.filter((element, index, self) => {
        return !this.tableSelectTitle.includes(element.key);
      });
      this.tableTitle = listinfo;
      this.showSelectTitle = !this.showSelectTitle;
    },
    initTbaleTitle() {
      this.tableTitle = tableInfo["list"];
      this.tableSelectTitle = [];
    },
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (from.path == "/massCenter/merchantsQualification/detail") {
        if (getContextData("merchantsQualification_detail") != "") {
          vm.listQuery = getContextData("merchantsQualification_detail");
        }
      }
      vm.initTbaleTitle();
      vm.getlist();
    });
  },
  // watch: {
  //   addr(newl, old) {
  //     this.listQuery.model = Object.assign(newl);
  //   },
  // },
  components: {
    Pagination,
    edit,
    TabsLayout
    // selectAddr,
  },
};
</script>


<style lang="less" scoped>
.archivesPageContent {
  padding: 0;
  .temp_searchBox {
    height: 64px;
    overflow: hidden;
    margin-bottom: 0;
  }
  .form-inline {
    height: 60px;
    overflow: hidden;
  }
  .title {
    border-bottom: 2px solid #ebecee;
    margin-bottom: 16px;
    span {
      margin-bottom: -2px;
      padding: 0 15px;
      height: 40px;
      line-height: 30px;
      display: block;
      background: rgba(255, 255, 255, 0);
      border-bottom: 2px solid rgb(64, 158, 255);
      font-size: 16px;
      font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
        "PingFang SC"-400;
      font-weight: 400;
      color: rgb(64, 158, 255);
    }
  }
  .formItem {
    width: 586px;
  }
  .line {
    color: #dfe6ec;
    margin: 0 6px;
  }
  .typeTabs {
    height: 40px;
    margin-bottom: -2px;
    margin-left: 10px;
  }
  /deep/.el-badge__content.is-fixed {
    transform: translateY(0%) translateX(0%);
    right: unset;
  }
}
</style>
