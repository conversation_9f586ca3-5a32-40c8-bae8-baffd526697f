import axios from 'axios'
import { MessageBox, Message } from 'element-ui'
import store from '@/store'
import { getToken } from '@/utils/auth'

// create an axios instance
const service = axios.create({
  headers: {
    'Content-Type': 'application/json;charse=UTF-8'
  },
  baseURL: process.env.VUE_APP_ADMIN_API_URL +"/api",
  timeout: 15000
})

service.interceptors.request.use(
  config => {
    if (config.headers['Content-Type'] == undefined) {
      config.headers['Content-Type'] = 'application/json;charse=UTF-8'
    }
    config.headers['Authorization'] = 'Basic YWRtaW5fdWk6YWRtaW5fdWlfc2VjcmV0'
    if (store.getters.token) {
      config.headers['token'] = getToken()
    }
    return config
  },
  error => {
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

service.interceptors.response.use(
  response => {
    const res = response.data;
    if (res.code != 0) {
      if ([400, 40001, 50012, 50014, 40005, 40007, 40000, 40003].includes(res.code)) {
        
        MessageBox.confirm('登录状态已失效，请重新登录', {
          confirmButtonText: '前往登录',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          store.dispatch('user/resetToken').then(() => {
            location.reload()
          })
        })
      } else if (res.code == 20000) {
        store.dispatch('user/resetToken').then(() => {
          location.reload()
        })
      } else {
        Message({
          message: res.msg || 'Error',
          type: 'error',
          duration: 5 * 1000
        })
        return {
          code: res.code,
          data: null,
          msg: res.msg
        }
      }
    } else {
      return res
    }
  },
  error => {
    Message({
      message: error.message,
      type: 'error',
      duration: 5 * 1000
    })
    return Promise.reject(error)
  }
)

export default service
