{"name": "eyaolink_adminweb", "version": "4.4.0", "description": "A magical vue admin. An out-of-box UI solution for enterprise applications. Newest development stack of vue. Lots of awesome features", "author": "jin_<PERSON> <<EMAIL>>", "scripts": {"dev": " vue-cli-service serve ", "build:prod": "vue-cli-service build"}, "dependencies": {"awe-dnd": "^0.3.4", "axios": "0.18.1", "big.js": "^6.2.2", "clipboard": "2.0.4", "codemirror": "5.45.0", "core-js": "3.32.2", "dayjs": "^1.11.10", "driver.js": "0.9.5", "dropzone": "5.5.1", "e-icon-picker": "^1.0.8", "echarts": "4.2.1", "element-ui": "2.13.2", "file-saver": "2.0.1", "fuse.js": "3.4.4", "js-cookie": "2.2.0", "jsonlint": "1.6.3", "jszip": "3.2.1", "less": "^3.12.2", "less-loader": "^7.0.2", "lodash": "^4.17.21", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "screenfull": "4.2.0", "script-loader": "0.7.2", "sortablejs": "1.8.4", "vue": "2.6.10", "vue-amap": "^0.5.10", "vue-count-to": "1.0.13", "vue-fontawesome-elementui-icon-picker": "^0.1.9", "vue-router": "3.0.2", "vue-splitpane": "1.0.4", "vuedraggable": "2.20.0", "vuex": "3.1.0", "xlsx": "0.14.1"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.4", "@vue/cli-plugin-unit-jest": "4.4.4", "@vue/cli-service": "^4.4.4", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "9.5.1", "babel-jest": "23.6.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "2.4.2", "chokidar": "2.1.5", "compression-webpack-plugin": "^6.1.1", "connect": "3.6.6", "element-theme-chalk": "^2.13.2", "happypack": "^5.0.1", "html-webpack-plugin": "3.2.0", "lint-staged": "8.1.5", "mockjs": "1.0.1-beta3", "plop": "2.3.0", "runjs": "4.3.2", "sass": "1.26.2", "sass-loader": "8.0.2", "script-ext-html-webpack-plugin": "2.1.3", "serve-static": "1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.0", "terser-webpack-plugin": "4.0.0", "vue-template-compiler": "2.6.10"}, "browserslist": ["> 1%", "last 2 versions"], "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "license": "MIT"}