<template>
  <div class="systemPageContent">
    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        :tabs="[{ name: '系统参数' }]"
        @change="handleChangeTab"
      >
        <template slot="button">
          <el-button
            v-if="checkPermission(['admin', 'admin-setting-param:add'])"
            type="primary"

            @click="editFun({})"
          >新增系统参数</el-button>
          <el-button >刷新</el-button>
        </template>
      </tabs-layout>
      <div class="table">
        <el-table
          ref="tableDom"
          v-loading="listLoading"
          :data="list"
          row-key="id"
          border
          fit
          @filter-change="fliterChange"
        >
          <el-table-column
            align="center"
            width="60"
            show-overflow-tooltip
            :render-header="renderHeader"
          >
            <template slot-scope="scope">
              <span>{{ scope.$index+1 }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="name"
            width="150"
            show-overflow-tooltip
            label="参数名称"
          />
          <el-table-column
            prop="value"
            width="150"
            show-overflow-tooltip
            label="参数值"
          />
          <el-table-column
            prop="describe"
            show-overflow-tooltip
            label="描述"
            min-width="300"
          />
          <el-table-column
            width="100"
            show-overflow-tooltip
            label="状态"
            :filters="tableStutasList"
            :filter-multiple="false"
            column-key="status"
          >
            <template slot-scope="{ row }">
              <el-tag v-if="row['status'] == true" type="success" >启用</el-tag>
              <el-tag v-if="row['status'] == false" type="danger" >禁用</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="createTime"
            show-overflow-tooltip
            label="创建时间"
          />

          <el-table-column
            fixed="right"
            align="center"
            label="操作"
            width="100"
            class="itemAction"
          >
            <template slot-scope="scope">
              <el-row class="table-edit-row">
                <span v-if="checkPermission(['admin', 'admin-setting-param:edit'])" class="table-edit-row-item">
                  <el-button type="text"  @click.native="editFun(scope.row)">编辑</el-button>
                </span>
                <span v-if="checkPermission(['admin', 'admin-setting-param:del'])" class="table-edit-row-item">
                  <el-button type="text"  @click.native="deleteFun(scope.row)">删除</el-button>
                </span>
              </el-row>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="listQuery.current"
          :limit.sync="listQuery.size"
          @pagination="getList"
        />
      </div>
    </div>
    <edit
      v-if="showEdit"
      :visible.sync="showEdit"
      :is-reload.sync="submitReload"
      :row.sync="row"
    />

  </div>
</template>
<script>
import checkPermission from '@/utils/permission'
import { list, deleteApi } from '@/api/setting/system'
import edit from './edit'
import Pagination from '@/components/Pagination'
import TabsLayout from '@/components/TabsLayout'
export default {
  components: { Pagination, edit, TabsLayout },
  data() {
    return {
      tableStutasList: [
        {
          text: '启用',
          value: true
        },
        {
          text: '禁用',
          value: false
        }
      ],
      showEdit: false,
      showItemTable: false,
      row: {},
      submitReload: false,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        size: 10,
        model: {},
        current: 1
      }
    }
  },
  watch: {
    submitReload: function(newVal, oldVal) {
      if (newVal) {
        this.submitReload = false
        this.getList()
      }
    }
  },
  mounted() {
    this.getList()
  },
  beforeDestroy() {},

  methods: {
    checkPermission,
    renderHeader(h, { column }) {
      return (
        <div style='position:relative'>
          <div>
            <i class='el-icon-menu' />
          </div>
        </div>
      )
    },
    initQuery() {
      this.listQuery = {
        size: 10,
        model: {},
        current: 1
      }
    },
    async actionDeleteFun(row) {
      const data = await deleteApi(row.id)
      if (data.code == 0) {
        this.initQuery()
        this.getList()
      }
    },
    onSearchSubmitFun() {
      this.listQuery.size = 1
      this.getList()
    },
    async getList() {
      this.listLoading = true;
      let params = {
        ...this.listQuery,
      };
      params.model.saleMerchantId = 0;
      const { data } = await list(params)
      this.list = data.records
      this.total = data.total
      this.listLoading = false
    },
    showItemView(row) {
      this.row = row
      this.showItemTable = true
    },
    editFun(row) {
      this.row = row
      this.showEdit = true
    },
    deleteFun(row) {
      var _this = this
      this.$confirm('此操作将永久删除该信息, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        _this.actionDeleteFun(row)
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },

    actionsTableFun(row) {
      this.row = row
      this.showItemTable = true
    },
    fliterChange(filters) {
      for (var key in filters) {
        this.listQuery.model[key] = filters[key][0]
      }
      this.getList()
    }
  }
}
</script>
<style lang="scss" scoped>
@import "@/styles/element-variables.scss";
.temp_searchBox{height: 64px;overflow: hidden; margin-bottom: 0; }
.form-inline{height:60px; overflow:hidden;}
.systemPageContent {
  padding:0;
  // padding: 15px;
  .title {
    // border-bottom: 2px solid #ebecee;
     margin-bottom:16px;
    border-bottom:2px solid #EBECEE;
    span {
      margin-bottom: -2px;
      padding: 0 15px;
      height: 40px;
      line-height: 30px;
      display: block;
      background: rgba(255, 255, 255, 0);
      border-bottom: 2px solid rgb(64, 158, 255);
      font-size: 16px;
      font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
        "PingFang SC"-400;
      font-weight: 400;
      color: rgb(64, 158, 255);
    }
  }
  .formItem {
    width: 586px;
  }
  .line {
    color: #dfe6ec;
    margin: 0 6px;
  }
  .el-dropdown-link {
    margin-left: 12px;
    cursor: pointer;
    font-size: 12px;
    color: #0056e5;
  }
  .el-icon-arrow-down {
    font-size: 12px;
  }
  // .formItem{width:586px;}
  // .line{color:#dfe6ec; margin:0 6px;}
}
</style>
