import axios from 'axios'
import { Message } from 'element-ui'
import store from '@/store'
import { getToken } from '@/utils/auth'
import {
  handleTokenExpired,
  handleResponseTokenExpired,
  checkTokenInRequest
} from '@/utils/tokenRefresh'

axios.defaults.withCredentials = true

// create an axios instance
const service = axios.create({
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  },
  baseURL: process.env.VUE_APP_ADMIN_API_URL,
  timeout: 15000
})

service.interceptors.request.use(
  async config => {
    if (config.headers['Content-Type'] === undefined) {
      config.headers['Content-Type'] = 'application/json;charset=UTF-8'
    }
    if (config.headers['Authorization'] === undefined) {
      config.headers['Authorization'] = process.env.VUE_APP_AUTHORIZATION_CODE
    }

    // 检查token过期并自动刷新
    await checkTokenInRequest()

    // 添加token到请求头
    if (store.getters.token) {
      config.headers['token'] = getToken()
    }
    return config
  },
  error => {
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

service.interceptors.response.use(
  async response => {
    // 处理响应数据中的token过期
    const tokenExpiredResponse = await handleResponseTokenExpired(response, service)
    if (tokenExpiredResponse !== response) {
      return tokenExpiredResponse
    }

    const res = response.data;
    if (res.code != 0) {
      if (res.code == 20000) {
        store.dispatch('user/resetToken').then(() => {
          location.reload()
        })
      } else {
        Message({
          message: res.msg || 'Error',
          type: 'error',
          duration: 5 * 1000
        })
        return {
          code: res.code,
          data: null,
          msg: res.msg
        }
      }
    } else {
      return res
    }
  },
  error => {
    // 处理token过期错误
    const tokenExpiredPromise = handleTokenExpired(error, service)
    if (tokenExpiredPromise !== Promise.reject(error)) {
      return tokenExpiredPromise
    }

    Message({
      message: error.message,
      type: 'error',
      duration: 5 * 1000
    })
    return Promise.reject(error)
  }
)

export default service
