<template>
  <div class="page-module-title flex-between-center">
    <div class="module_title">{{ title }}<span class="tips">{{ tips }}</span></div>
    <div class="btn-group">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PageModuleTitle',
  props: {
    title: {
      type: String,
      default: ''
    },
    tips: {
      type: String,
      default: ''
    }
  }
}
</script>

<style lang="scss" scoped>
  .page-module-title {
    padding: 10px 0 15px;
    margin-bottom: 6px;
    .module_title {
      font-size: 16px;
      padding-left: 10px;
      border-left: 4px solid #0056E5;
      .tips {
        margin-left: 10px;
        font-size: 14px;
        color: rgba(0, 0, 0, .5);
      }
    }
  }
  .flex-between-center {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
</style>
