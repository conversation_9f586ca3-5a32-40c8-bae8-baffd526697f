<template>
  <div class="container" v-loading="isLoading">
    <div class="upLoadIdCard">
      <div>
        <el-upload
          class="avatar-uploader"
          :action="$uploadUrl"
          :data="insertProgram"
          :headers="headersProgram"
          :show-file-list="false"
          :on-success="frontIdCardUpFun"
          :before-upload="beforeUpload"
          accept=".jpg,.png,.bmp,.jpeg"
        >
          <img v-if="frontIdCard" :src="frontIdCard" class="avatar" />
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
        </el-upload>
        <div class="text">请上传身份证人像面</div>
      </div>
      <div>
        <el-upload
          class="avatar-uploader"
          :action="$uploadUrl"
          :data="insertProgram"
          :headers="headersProgram"
          :show-file-list="false"
          :on-success="reverseIdCardUpFun"
          :before-upload="beforeUpload"
          accept=".jpg,.png,.bmp,.jpeg"
        >
          <img v-if="reverseIdCard" :src="reverseIdCard" class="avatar" />
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
        </el-upload>
        <div class="text">请上传身份证国徽面</div>
      </div>
    </div>
    <div class="btn">
      <el-button type="primary" size="large" @click="upLoadIdcardFun"
        >识 别</el-button
      >
    </div>

    <div class="item">
      <div class="title"><span>提示</span></div>
      <div class="text">上传身份证件只能识别后，请仔细核对信息是否一致</div>
    </div>

    <div class="idForm">
      <el-form label-width="160px" :model="idCardForm" ref="idCardForm">
        <el-row>
          <el-col :span="12">
            <el-form-item
              class="formItem"
              prop="name"
              label="姓名:"
              :rules="[
                {
                  required: true,
                  message: '请填写身份证姓名',
                  trigger: 'blur',
                },
              ]"
            >
              <el-input
                clearable
                width="200px"
                v-model="idCardForm.name"
                placeholder="姓名"
              ></el-input> </el-form-item
          ></el-col>
          <el-col :span="12">
            <el-form-item
              class="formItem"
              prop="sex"
              label="性别:"
              :rules="[
                { required: true, message: '请填写性别', trigger: 'blur' },
              ]"
            >
              <el-input
                clearable
                v-model="idCardForm.sex"
                placeholder="请填写性别"
              ></el-input> </el-form-item
          ></el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item
              class="formItem"
              prop="idNumber"
              label="身份证号码:"
              :rules="[
                {
                  required: true,
                  message: '请填写身份证号码',
                  trigger: 'blur',
                },
              ]"
            >
              <el-input
                clearable
                v-model="idCardForm.idNumber"
                placeholder="请填写身份证号码"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              class="formItem"
              prop="expTime"
              label="身份证过期时间:"
              :rules="[
                {
                  required: true,
                  message: '请填写身份证过期时间',
                  trigger: 'blur',
                },
              ]"
            >
              <el-input
                clearable
                v-model="idCardForm.expTime"
                placeholder="请填写身份证过期时间"
              ></el-input> </el-form-item
          ></el-col>
        </el-row>
      </el-form>
    </div>
    <div class="footer">
      <!-- <el-button type="danger" @click="cancel">手动填入</el-button> -->
      <el-button
        type="primary"
        @click="setIdMsg"
        :disabled="JSON.stringify(idCardForm) == '{}'"
        >自动填入</el-button
      >
    </div>
  </div>
</template>

<script>
import qs from "qs";
import { getToken } from "@/utils/auth";
import { upLoadIdCard } from "@/api/businessCentric/salesmanList";
export default {
  data() {
    return {
      reverseIdCard: "",
      frontIdCard: "",
      headersProgram: {
        token: getToken(),
        Authorization: "Basic YWRtaW5fdWk6YWRtaW5fdWlfc2VjcmV0",
      },
      insertProgram: {
        folderId: 0,
      },
      idCardForm: {},
      isLoading: false,
    };
  },
  props: {
    visible: {
      require: true,
      type: Boolean,
    },
  },
  methods: {
    cancel() {
      this.$emit("update:visible", false);
    },
    setIdMsg() {
      this.$refs.idCardForm.validate((valid) => {
        if (valid) {
          this.$emit("setIdMsg", {
            msg: this.idCardForm,
            front: this.frontIdCard,
            back: this.reverseIdCard,
          });
          this.cancel();
        } else {
          this.$message.error('部分信息未被识别，请更正！')
        }
      });
    },
    beforeUpload(file) {
      let fileTypeList = [
        "image/png",
        "image/pjpeg",
        "image/jpeg",
        "image/bmp",
      ];
      const isJPG = fileTypeList.indexOf(file.type) > -1;
      const isLt2M = file.size / 1024 / 1024 < 5;

      if (!isJPG) {
        this.$message.error("上传图片格式错误!");
      }
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 2MB!");
      }
      return isJPG && isLt2M;
    },
    async upLoadIdcardFun() {
      if (this.frontIdCard == "") {
        this.$message.error("请输上传业务员身份证人面像！");
        return;
      }
      if (this.reverseIdCard == "") {
        this.$message.error("请输上传业务员身份证国徽像！");
        return;
      }
      this.isLoading = true;
      let { data } = await upLoadIdCard(
        qs.stringify({
          backUrl: this.reverseIdCard,
          frontUrl: this.frontIdCard,
        })
      );
      this.isLoading = false;
      let time = "";
      if (data.back.failureTime) {
        time =
          data.back.failureTime.substr(0, 4) +
          "-" +
          data.back.failureTime.substr(4, 2) +
          "-" +
          data.back.failureTime.substr(6, 2);
      }
      let obj = {};
      obj.name = data.front.name;
      obj.sex = data.front.sex;
      obj.idNumber = data.front.IDCard;
      obj.expTime = time;

      this.idCardForm = obj;
    },
    frontIdCardUpFun(res, file) {
      this.frontIdCard = res.data.url;
    },
    reverseIdCardUpFun(res, file) {
      this.reverseIdCard = res.data.url;
    },
  },
};
</script>

<style lang="less" scoped>
.container {
  .item {
    width: 100%;
    margin: 50px 0;
    border-bottom: 1px solid #eeeeee;
    margin-left: 20px;
    .title {
      padding: 0 0 15px;
      span {
        font-size: 16px;
        padding-left: 10px;
        border-left: 4px solid rgba(64, 158, 255, 1);
      }
    }
    .text {
      color: #ccc;
      margin-bottom: 20px;
    }
  }
  .footer {
    margin: 0 auto;
    text-align: center;
    margin-top: 40px;
  }
  .btn {
    margin: 30px auto;
    text-align: center;
  }
  .idForm {
    margin: 0 auto;
  }
  .upLoadIdCard {
    display: flex;
    justify-content: space-around;
    .text {
      text-align: center;
      color: #ccc;
      padding-top: 10px;
    }
    /deep/ .avatar-uploader .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }
    /deep/ .avatar-uploader .el-upload:hover {
      border-color: #409eff;
    }
    /deep/ .avatar-uploader-icon {
      font-size: 24px;
      color: #8c939d;
      width: 240px;
      height: 150px;
      line-height: 150px;
      text-align: center;
    }
    /deep/ .avatar {
      width: 240px;
      height: 150px;
      display: block;
    }
  }
}
</style>
