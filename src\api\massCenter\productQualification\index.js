import requestAxios from '@/utils/requestAxios'
import request from '@/utils/request'

// 获取列表数据
export function list(data) {
  return requestAxios({
    url: "/api/product/admin/productLicenseRel/pageForProductLicense",
    method: 'post',
    data
  })
}
// 获取商品详情
export function getitem(data) {
  return requestAxios({
    url: "/api/product/admin/productLicenseRel/getForProductLicense/" + data,
    method: 'get'
  })
}

// 修改商品资质
export function resetLicenseRel(data) {
  return requestAxios({
    url: "/api/product/admin/productLicenseRel",
    method: 'put',
    data
  })
}
