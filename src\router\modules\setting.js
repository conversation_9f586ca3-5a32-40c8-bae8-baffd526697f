
import Layout from '@/layout'
// resolve=>(require(["@/views/nested"],resolve))
const settingRouter = {
  path: '/settingCenter',
  name: 'settingCenter',
  redirect: '/settingCenter/permission',
  meta: {
    title: '配置中心',
    icon: 'list'
  },
  component: Layout,
  children: [
    {
      path: 'permission',
      name: 'permission',
      meta: { title: '权限配置' },
      component:resolve=>(require(["@/views/nested"],resolve)),
      redirect: '/settingCenter/permission/menu/index',
      children: [
        {
          path: 'menu/index',
          name: 'menuIndex',
          meta: { title: '栏目配置' },
          roles: ['admin', 'menuIndex:view', 'menuIndex:add', 'menuIndex:update', 'menuIndex:delete', 'resource:view', 'resource:add', 'resource:update', 'resource:delete'], 
          component: resolve => (require(['@/views/settingCenter/permission/menu/index'], resolve))
        }, 
        {
          path: 'menu/edit',
          name: 'menuEdit',
          hidden:true,
          meta: { title: '编辑栏目' },
          roles: ['admin', 'menuIndex:view', 'menuIndex:add', 'menuIndex:update', 'menuIndex:delete', 'resource:view', 'resource:add', 'resource:update', 'resource:delete'], 
          component: resolve => (require(['@/views/settingCenter/permission/menu/newPageEdit'], resolve))
        }, 
        {
          path: 'role/index',
          name: 'roleIndex',
          meta: { title: '角色管理' },
          roles: ['admin', 'roleIndex:view', 'roleIndex:add', 'roleIndex:update', 'roleIndex:delete'], 
          component: resolve => (require(['@/views/settingCenter/permission/role/index'], resolve))
        },
        {
          path: 'role/edit',
          name: 'roleEdit',
          hidden: true,
          meta: { title: '编辑角色' },
          roles: ['admin', 'roleIndex:view', 'roleIndex:add', 'roleIndex:update', 'roleIndex:delete'],
          component: resolve => (require(['@/views/settingCenter/permission/role/newPageEdit'], resolve))
        }, 
        {
          path: 'staff/index',
          name: 'staffIndex',
          meta: { title: '员工管理' },
          roles: ['admin', 'staffIndex:view', 'staffIndex:add', 'staffIndex:update','staffIndex:delete','staffIndex:recovery'], 
          component: resolve => (require(['@/views/settingCenter/permission/staff/index'], resolve))
        }
      ]
    }, 
    
    {
      path: 'base',
      name: 'base',
      meta: { title: '基础数据' },
      component: resolve => (require(["@/views/nested"], resolve)),
      redirect: '/settingCenter/base/system/index',
      children: [
        // {
        //   path: 'data/index',
        //   name: 'dataIndex',
        //   meta: {
        //     title: '数据字典' },
        //   roles: ['admin', 'dataIndex:view', 'dataIndex:update'], 
        //   component: resolve => (require(['@/views/settingCenter/base/data/index'], resolve))
        // }, 
        {
          path: 'system/index',
          name: 'systemIndex',
          meta: { title: '系统参数' },
          roles: ['admin', 'systemIndex:view', 'systemIndex:update'], 
          component: resolve => (require(['@/views/settingCenter/base/system/index'], resolve))
        }, 
        // {
        //   path: 'area/index',
        //   name: 'areaIndex',
        //   meta: { title: '地区信息维护' },
        //   roles: ['admin', 'areaIndex:view', 'areaIndex:add', 'areaIndex:update', 'areaIndex:delete'], 
        //   component: resolve => (require(['@/views/settingCenter/base/area/index'], resolve))
        // },
        {
          path: 'area/edit',
          name: 'areaEdit',
          hidden:true,
          meta: { title: '编辑地区信息' },
          roles: ['admin', 'areaIndex:view', 'areaIndex:add', 'areaIndex:update', 'areaIndex:delete'],
          component: resolve => (require(['@/views/settingCenter/base/area/edit'], resolve))
        }
      ]
    },
    
    {
      path: 'businessCategory/index',
      name: 'businessCategoryIndex',
      meta: {title: '经营范围' },
      roles: ['admin', 'businessCategoryIndex:view', 'businessCategoryIndex:add', 'businessCategoryIndex:update', 'businessCategoryIndex:delete', 'businessCategoryIndex:detail'], 
      component: resolve => (require(['@/views/settingCenter/businessCategory/index'], resolve))
    }, 

    {
      path: 'siteManagement/list',
      name: 'siteManagementList',
      meta: {title: '站点管理' },
      // roles: ['admin', 'businessCategoryIndex:view', 'businessCategoryIndex:add', 'businessCategoryIndex:update', 'businessCategoryIndex:delete', 'businessCategoryIndex:detail'], 
      component: resolve => (require(['@/views/settingCenter/siteManagement/list'], resolve))
    }, 
    
    {
      path: 'siteManagement/edit',
      name: 'siteManagementEdit',
      meta: {title: '编辑站点' },
      // roles: ['admin', 'businessCategoryIndex:view', 'businessCategoryIndex:add', 'businessCategoryIndex:update', 'businessCategoryIndex:delete', 'businessCategoryIndex:detail'], 
      component: resolve => (require(['@/views/settingCenter/siteManagement/edit'], resolve))
    }, 

    {
      path: 'merchantType/index',
      name: 'merchantTypeIndex',
      meta: { title: '企业类型' },
      roles: ['admin', 'merchantTypeIndex:view', 'merchantTypeIndex:add', 'merchantTypeIndex:update', 'merchantTypeIndex:delete', 'merchantTypeIndex:detail'], 
      component: resolve => (require(['@/views/settingCenter/merchantType/index'], resolve))
    }, 
    {
      path: 'sms',
      name: 'sms',
      meta: { title: '短信配置' },
      redirect: '/settingCenter/sms/smsTemplate/index',
      component:resolve=>(require(["@/views/nested"],resolve)),
      children: [
          {
            path: 'smsTemplate/index',
            name: 'smsTemplateIndex',
            meta: { title: '短信模板' },
            roles: ['admin', 'smsTemplateIndex:view', 'smsTemplateIndex:add', 'smsTemplateIndex:update', 'smsTemplateIndex:delete'],
            component: resolve => (require(['@/views/settingCenter/sms/smsTemplate/index'], resolve))
        }, 
        {
          path: 'smsTemplate/edit',
          name: 'smsTemplateEdit',
          hidden: true,
          meta: { title: '编辑短信模板' },
          roles: ['admin', 'smsTemplateIndex:view', 'smsTemplateIndex:add', 'smsTemplateIndex:update', 'smsTemplateIndex:delete'],
          component: resolve => (require(['@/views/settingCenter/sms/smsTemplate/edit'], resolve))
        }, 
        {
          path: 'smsTask/index',
          name: 'smsTaskIndex',
          meta: { title: '短信日志' },
          roles: ['admin', 'smsTaskIndex:view','smsTaskIndex:detail'],
          component: resolve => (require(['@/views/settingCenter/sms/smsTask/index'], resolve))
        }
      ]
    }

    // 暂时不上  adjourned
    // {
    //   path: 'paySetting/index',
    //   name: 'paySetting',
    //   meta: { title: '支付设置' },
    //   component:resolve=>(require(['@/views/settingCenter/paySetting/index'],resolve)) 
    // },
    // {
    //   path: 'weChat/edit',
    //   name: 'weChatEdit',
    //   meta: { title: '微信公众号' },
    //   component: resolve => (require(['@/views/settingCenter/weChat/edit'], resolve))
    // }, 

  ]
}

export default settingRouter
