import request from '@/utils/request'
var qs = require('qs')
import {html2Text} from "@/utils";
export function merchantGroupList(query) {
  return request({
    url: `/merchant/admin/merchantGroup/page`,
    method: 'post',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
export function merchantGroupListNew(query) {
  return request({
    url: `/merchant/admin/merchantGroup/pageMerchantGroupList`,
    method: 'post',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
export function addGroup(query) {
  return request({
    url: '/merchant/admin/merchantGroup',
    method: 'post',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
export function editGroup(query) {
  return request({
    url: '/merchant/admin/merchantGroup',
    method: 'put',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//根据用户查询销售商信息
export function findByUserIdSale(userId) {
  return request({
    url: `/merchant/admin/saleMerchant/findByUserIdSale/${userId}`,
    method: 'get'
  })
}
//删除分组
export function deleteGroup(ids) {
  return request({
    url: `/merchant/admin/merchantGroup`,
    method: 'delete',
    params: {
      ids: ids
    }
  })
}
//查看分组下的客户
export function queryPageSaleMerchantGroupCustomerListDTO(query) {
  return request({
    url: '/merchant/admin/merchantPurSaleRel/queryPageSaleMerchantGroupCustomerListDTO',
    method: 'post',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//添加客户
export function updateMerchantPurSaleRelMerchantGroup(ids,merchantGroupId) {
  let params = {
    ids: ids,
    merchantGroupId: merchantGroupId
  }
  return request({
    url: '/merchant/admin/merchantPurSaleRel/batchUpdateMerchantPurSaleRelMerchantGroup',
    method: 'post',
    data: params,
    transformRequest: [function() {
      return qs.stringify(params, { arrayFormat: 'brackets' })
    }]
  })
}
//客户档案列表(新增控销：指定客户)
export function merchantPurSaleRelList(query) {
  return request({
    url: `/merchant/admin/merchantPurSaleRel/pagePurMerchantBySaleMerchant`,
    method: 'post',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//客户档案详情
export function getPurMerchantLicenseDetail(purMerchantId) {
  return request({
    url: `/merchant/admin/purMerchant/findPurMerchantDetail/${purMerchantId}`,
    method: 'get'
  })
}
//获取资质 PRODUCT(商品）,BUYER（采购商），MERCHANT（销售商）
export function getListByLicenseBaseType(type) {
  return request({
    url: `/merchant/admin/licenseBase/anno/listByLicenseBaseType?type=${type}`,
    method: 'get'
  })
}
//根据商家id查询该商家的客户分组
export function listMerchantGroupBySaleMerchantId(saleMerchantId) {
  return request({
    url: `/merchant/admin/merchantGroup/listMerchantGroupBySaleMerchantId/${saleMerchantId}`,
    method: 'get'
  })
}
//地区
export function trees() {
  return request({
    url: '/authority/area/anno/tree',
    method: 'get'
  })
}
//根据商家类型查找客户
export function merchantTypeList(query) {
  return request({
    url: '/merchant/admin/merchantType/page',
    method: 'post',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//根据id删除客户分组
export function delGroup(purMerchantId,merchantGroupId) {
  return request({
    url: `/merchant/admin/merchantPurSaleRel/updateMerchantPurSaleRelMerchantGroup/${purMerchantId},${merchantGroupId}`,
    method: 'POST'

  })
}
//修改商家资质表
export function editMerchantLicense(query) {
  return request({
    url: '/merchant/admin/merchantLicense',
    method: 'PUT',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}

//公告
export function merchantNoticeList(query) {
  return request({
    url: '/general/admin/article/pageMerchantArticle',
    method: 'post',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//删除
export function delNoitice(ids) {
  return request({
    url: '/merchant/admin/merchantNotice',
    method: 'delete',
    params: {
      ids: ids
    }
  })
}
//详情
export function detailNotice(id) {
  return request({
    url: `/general/admin/article/${id}`,
    method: 'get'
  })
}
//修改首营状态，变更备注
export function updateSetFirstCampStatusAndRemark(query) {
  return request({
    url: '/merchant/admin/merchantPurSaleRel/updateSetFirstCampStatusAndRemark',
    method: 'post',
    params: query
  })
}
/*
* 采购商档案列表，获取采购商统计
* */
export function getPurLicenseCount (data) {
  return request({
    url: "/merchant/admin/merchantPurSaleRel/getPurLicenseCount",
    method: "post",
    data
  })
}
