<template>
  <div id="taskManagement">
    <div class="search_box">
      <el-select
        v-model="searchForm.taskType"
        style="width: 200px"
        placeholder="任务类型"
        clearable
        @change="getData()"
      >
        <el-option
          v-for="item in typeOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
      <el-select
        v-model="searchForm.taskStatus"
        style="width: 200px"
        placeholder="任务状态"
        clearable
        @change="getData()"
      >
        <el-option
          v-for="item in statusOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
      <el-date-picker
        v-model="searchForm.month"
        type="monthrange"
        range-separator="至"
        start-placeholder="开始月份"
        end-placeholder="结束月份"
        value-format="yyyy-MM"
        clearable
        @change="getData()"
      >
      </el-date-picker>
      <el-button type="primary" @click="getData()">搜索</el-button>
      <el-button @click="reset()">重置</el-button>
    </div>
    <p>
      <el-button type="primary" @click="toEdit()">新增任务</el-button>
    </p>
    <div class="main_box">
      <table-pager
        ref="pager-table"
        :pageSize="10"
        :options="tableTitle"
        :data.sync="tableData"
        :operation-width="260"
        :remote-method="load"
      >
        <div slot-scope="scope" align="center">
          <div class="operationBtn">
            <el-button v-if="scope.row.taskStatus !== 0" v-throttle type="text" @click="toDetail(scope.row)"
              >详情</el-button
            >
            <el-button
              v-if="scope.row.taskStatus === 0"
              v-throttle
              type="text"
              @click="toEdit(scope.row)"
              >编辑</el-button
            >
            <el-button
              v-throttle
              type="text"
              v-if="scope.row.taskStatus === 0"
              @click="toDelete(scope.row)"
              >删除</el-button
            >
            <el-button
              v-throttle
              type="text"
              v-if="[1, 2].includes(scope.row.taskStatus)"
              @click="toAchievement(scope.row)"
              >达成明细</el-button
            >
            <el-button
              v-throttle
              type="text"
              v-if="scope.row.taskType === 3 && scope.row.taskStatus === 1"
              @click="toUpdate(scope.row)"
              >更新指标</el-button
            >
          </div>
        </div>
        <el-table-column label="状态" align="center" slot="taskStatusName">
          <template slot-scope="{ row }">
            <div>
              <span
                class="status"
                :class="{
                  status_warning: row.taskStatus === 0,
                  status_success: row.taskStatus === 1,
                  status_gray: row.taskStatus === 2,
                }"
                >{{ row.taskStatusName }}</span
              >
            </div>
          </template>
        </el-table-column>
      </table-pager>
    </div>
  </div>
</template>

<script>
import { salesmanTaskList, salesmanTaskDelete, salesmanTask3Update } from "@/api/taskManagement.js";
// import { findByUserIdSale } from "@/api/group";
export default {
  name: "taskManagement",
  components: {},
  props: {},
  data() {
    return {
      // saleMerchantId: "",
      searchForm: {
        taskType: "",
        taskStatus: "",
        month: "",
      },
      typeOptions: [
        {
          label: "拜访任务",
          value: 0,
        },
        {
          label: "协访任务",
          value: 1,
        },
        {
          label: "拓客任务",
          value: 2,
        },
        {
          label: "业绩任务",
          value: 3,
        },
        {
          label: "客户数发展目标",
          value: 4,
        },
      ],
      statusOptions: [
        {
          label: "创建中",
          value: -1,
        },
        {
          label: "待开始",
          value: 0,
        },
        {
          label: "进行中",
          value: 1,
        },
        {
          label: "已过期",
          value: 2,
        },
        {
          label: "更新中",
          value: 3,
        }
      ],
      tableTitle: [
        {
          key: 1,
          prop: "taskTypeName",
          name: "taskTypeName",
          label: "任务类型",
          width: "",
          align: "center",
          disabled: true,
        },
        {
          key: 2,
          prop: "taskTime",
          name: "taskTime",
          label: "任务周期",
          width: "",
          align: "center",
        },
        {
          key: 3,
          prop: "createUserName",
          name: "createUserName",
          label: "配置人",
          width: "",
          align: "center",
        },
        {
          key: 4,
          prop: "updateTime",
          name: "updateTime",
          label: "更新时间",
          width: "",
          align: "center",
        },
        {
          key: 5,
          prop: "taskStatusName",
          name: "taskStatusName",
          label: "状态",
          width: "",
          align: "center",
          slot: true,
        },
      ],
      tableData: [],
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    // 查询
    getData() {
      this.$refs["pager-table"].doRefresh({
        page: 1,
        pageSize: 10,
      });
    },
    // 重置
    reset() {
      this.searchForm = Object.assign(this.searchForm, {
        taskType: "",
        taskStatus: "",
        month: "",
      });
      this.getData();
    },
    // 获取经销商id
    // async getSaleId() {
    //   let userId = localStorage.getItem("userId");
    //   const { data } = await findByUserIdSale(userId);
    //   this.saleMerchantId = data.id;
    // },
    // 组件查询
    async load(params) {
      try {
        let temp = {
          ...(params ?? {}),
          model: {
            ...this.searchForm,
            startYearMonth: this.searchForm.month?.[0] ?? "",
            endYearMonth: this.searchForm.month?.[1] ?? "",
            // saleMerchantId: this.saleMerchantId ?? "",
          },
        };
        this.loading = true;
        let res = await salesmanTaskList(temp);
        this.loading = false;
        if (res?.data?.records.length) {
          res.data.records = res.data.records.map((item) => {
            return {
              ...item,
              taskTypeName:
                this.typeOptions.find((it) => it.value === item.taskType)
                  ?.label ?? "",
              taskStatusName:
                this.statusOptions.find((it) => it.value === item.taskStatus)
                  ?.label ?? "",
              taskTime: `${item.startYearMonth} 至 ${item.endYearMonth}`,
            };
          });
        }
        return res || { data: {} };
        // return {
        //   data: {
        //     records: [
        //       {
        //         id: 1,
        //         taskStatus: 1,
        //         taskStatusName: "进行中",
        //         taskType: 3,
        //         taskTypeName: "业绩任务",
        //         taskTime: "2024-01 至 2024-12",
        //       },
        //       {
        //         id: 2,
        //         taskStatus: 0,
        //         taskStatusName: "待开始",
        //         taskType: 4,
        //         taskTypeName: "客户数发展目标",
        //         taskTime: "2024-01-01 至 2024-12-31",
        //       },
        //       {
        //         id: 3,
        //         taskStatus: 1,
        //         taskStatusName: "进行中",
        //         taskType: 1,
        //         taskTypeName: "协访任务",
        //         taskTime: "2024-01-02 至 2024-12-26",
        //       },
        //       {
        //         id: 4,
        //         taskStatus: 2,
        //         taskStatusName: "已过期",
        //         taskType: 2,
        //         taskTypeName: "拓客任务",
        //         taskTime: "2024-05-03 至 2024-06-03",
        //       },
        //       {
        //         id: 5,
        //         taskStatus: -1,
        //         taskStatusName: "创建中",
        //         taskType: 0,
        //         taskTypeName: "拜访任务",
        //         taskTime: "2024-01-1 至 2024-12-12",
        //       },
        //     ],
        //     total: 5,
        //   },
        // };
      } catch (error) {
        this.loading = false;
        console.log(error);
      }
    },
    // 新增/编辑页面
    toEdit(item) {
      this.$router.push({
        path: "taskManagement/edit",
        query: { id: item?.id ?? "" },
      });
    },
    // 查看界面
    toDetail(item) {
      this.$router.push({
        path: "taskManagement/edit",
        query: { id: item?.id ?? "", isDisable: true },
      });
    },
    // 删除
    async toDelete(item) {
      let res = await salesmanTaskDelete(item.id);
      if (res.code === 0 && res.data) {
        this.$message.success("删除成功！");
        this.getData();
      } else {
        this.$message.error(res.msg);
      }
    },
    // 达成明细页面
    toAchievement(item) {
      this.$router.push({
        path: "taskManagement/detail",
        query: {
          id: item.id,
          type: item.taskType,
          taskTypeName: item.taskTypeName,
          taskTime: item.taskTime,
        },
      });
    },
    // 业绩报表-更新指标
    async toUpdate(item) {
      let res = await salesmanTask3Update(item.id);
      if (res.code === 0 && res.data) {
        this.$message.success("更新成功！");
        this.getData();
      } else {
        this.$message.error(res.msg);
      }
    },
  },
};
</script>

<style scoped lang="scss">
#taskManagement {
  // height: calc(100vh - 86px - 32px);
  display: flex;
  flex-direction: column;
  background: #fff;
  > div {
    padding: 16px;
  }
  > div + div {
    margin-top: 20px;
  }
  > p {
    padding-left: 16px;
    text-align: left;
  }
  .search_box {
    height: 68px;
    // border-left: 4px solid #0056e5;
    display: flex;
    align-items: center;
    > * + * {
      margin-left: 10px;
    }
  }
  .main_box {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: scroll;
    .main_content {
      flex: 1;
    }
    .pagination-container {
      justify-content: right;
    }
    .status {
      line-height: 16px;
      position: relative;
      &::before {
        content: "";
        width: 8px;
        height: 8px;
        background-color: royalblue;
        border-radius: 50%;
        position: absolute;
        top: 4px;
        left: -15px;
      }
    }
    .status_warning::before {
      background: orange;
    }
    .status_success::before {
      background: green;
    }
    .status_gray::before {
      background: grey;
    }
  }
  .operationBtn {
    > .el-button + .el-button {
      border-left: 1px solid rgba(0, 0, 0, 0.09);
      padding: 0;
      padding-left: 10px;
    }
  }
}
</style>
