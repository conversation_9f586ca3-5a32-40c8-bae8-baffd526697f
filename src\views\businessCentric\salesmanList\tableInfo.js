export default {
  ACCEPTED: [
    {
      key: 0,
      label: '账户状态',
      name: "status",
      width: '100px',
      disabled: true
    },
    {
      key: 1,
      label: '业务员编码',
      name: "salesmanCode",
      width: '160px',
      disabled: true
    },
    {
      key: 2,
      label: '业务员姓名',
      name: "realName",
      width: '120px',
      disabled: true
    },
    {
      key: 3,
      label: '手机号码',
      name: "contactNumber",
      width: '160px',
      disabled: true
    },
    {
      key:4,
      label: '所在区域',
      name: 'area',
      width: '200px'
    },
    {
      key: 5,
      label: '在职状态',
      name: "positionStatus",
      width: '140px'
    },
    {
      key: 6,
      label: '登录账号',
      name: "account",
      width: '180px'
    },
    {
      key: 7,
      label: '客户数量',
      name: "customersNumber",
      width: '160px'
    },
    {
      key: 7,
      label: '代理商品数',
      name: "agencyProductNumber",
      width: '160px'
    },
    {
      key: 8,
      label: '代理区域',
      name: "agencyAreaNumber",
      width: '160px'
    },
    {
      key: 9,
      label:'操作时间',
      name:"updateTime",
      width:'160'
    }
  ],
  PENDING: [
    {
      key: 0,
      label: '账户状态',
      name: "status",
      width: '100px',
      disabled: true
    },
    {
      key: 1,
      label: '业务员编码',
      name: "salesmanCode",
      width: '160px',
      disabled: true
    },
    {
      key: 2,
      label: '业务员姓名',
      name: "realName",
      width: '120px',
      disabled: true
    },
    {
      key: 3,
      label: '手机号码',
      name: "contactNumber",
      width: '160px',
      disabled: true
    },
    {
      key:4,
      label: '所在区域',
      name: 'area',
      width: '200px'
    },
    {
      key: 5,
      label: '在职状态',
      name: "positionStatus",
      width: '160px'
    },
    {
      key: 6,
      label: '登录账号',
      name: "account",
      width: '160px'
    },
    {
      key:7,
      label:"操作时间",
      name:'updateTime',
      width:"160px"
    }

    // {  key: 0,
    //   label: 7客户数量',
    //   name: "publishStatus",
    //   width: '160px'
    // },
    // {  key:70,
    //   label: '代理商品数',
    //   name: "publishStatus",
    //   width: '160px'
    // },
    // {  key:80,
    //   label: '代理区域',
    //   name: "publishStatus",
    //   width: '160px'
    // }
  ],
  REJECTED: [
    {
      key: 0,
      label: '账户状态',
      name: "status",
      width: '100px',
      disabled: true
    },
    {
      key: 1,
      label: '业务员编码',
      name: "salesmanCode",
      width: '160px',
      disabled: true
    },
    {
      key: 2,
      label: '业务员姓名',
      name: "realName",
      width: '120px',
      disabled: true
    },
    {
      key: 3,
      label: '手机号码',
      name: "contactNumber",
      width: '160px',
      disabled: true
    },
    {
      key:4,
      label: '所在区域',
      name: 'area',
      width: '200px'
    },
    {
      key: 5,
      label: '在职状态',
      name: "positionStatus",
      width: '160px'
    },
    {
      key: 6,
      label: '登录账号',
      name: "account",
      width: '160px'
    },
    {
      key: 7,
      label: '操作时间',
      name: "updateTime",
      width: '180px'
    },
    // {  key: 0,
    //   label: 7客户数量',
    //   name: "publishStatus",
    //   width: '160px'
    // },
    // {  key:70,
    //   label: '代理商品数',
    //   name: "publishStatus",
    //   width: '160px'
    // },
    // {  key:80,
    //   label: '代理区域',
    //   name: "publishStatus",
    //   width: '160px'
    // }
  ]
}