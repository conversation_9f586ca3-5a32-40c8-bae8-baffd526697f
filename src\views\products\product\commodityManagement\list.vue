<template>
  <div class="archivesPageContent">
    <im-search-pad
      :is-expand.sync="isExpand"
      :model="searchForm"
      @reset="resetForm('searchForm')"
      @search="onSearchSubmitFun"
    >
      <im-search-pad-item prop="productName">
        <el-input v-model="searchForm.productName" placeholder="请输入商品名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="manufacturer">
        <el-input v-model="searchForm.manufacturer" placeholder="请输入生产厂家/品牌" />
      </im-search-pad-item>
      <im-search-pad-item prop="stockCondition">
        <el-select filterable v-model="searchForm.platformCategoryCode" placeholder="请选择平台分类" style="width:160px">
          <el-option v-for="item in platformCategoryList" :key="item.id" :label="item.label" :value="item.categoryCode" />
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item prop="stockCondition">
        <el-select v-model="searchForm.stockCondition"  placeholder="是否有货" style="width:120px">
          <el-option label="全部" value="ALL"></el-option>
          <el-option label="有货" value="WITH"></el-option>
          <el-option label="缺货" value="WITHOUT"></el-option>
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="saleMerchantName">
        <el-input v-model="searchForm.saleMerchantName" placeholder="请输入商家名称" />
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="pictIdsIsEmpty">
        <el-select v-model="searchForm.pictIdsIsEmpty"  placeholder="是否有图片" style="width:120px">
          <el-option label="有图" value="Y"></el-option>
          <el-option label="无图" value="N"></el-option>
        </el-select>
      </im-search-pad-item>
    </im-search-pad>

    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        :tabs="approvalStatusList"
        v-model="listQuery.model.approvalStatus"
        @change="handleChangeTab"
      >
        <template slot="button">
          <div>
            <el-button v-if="checkPermission(['admin','admin-platform-product:reject','admin-platform-product:reject'])&&listQuery.model.approvalStatus=='PENDING'" :disabled="multipleSelection.length==0" @click="setRejectReasonListFun">批量驳回</el-button>
            <el-button v-if="checkPermission(['admin','admin-platform-product:accept','admin-saas-product:accept'])&&listQuery.model.approvalStatus=='PENDING'" :disabled="multipleSelection.length==0" @click="submitProductPlatformPassListFun">批量通过</el-button>
            <el-button v-if="checkPermission(['admin','admin-platform-product:outExcel','admin-saas-product:outExcel'])" @click="outExcel()" :disabled="multipleSelection.length==0">导出商品</el-button>
            <el-button v-if="checkPermission(['admin','admin-platform-product:inputExcel','admin-saas-product:inputExcel'])" @click="inputExcel()">导入商品</el-button>
            <el-button @click="getList()">刷新</el-button>
          </div>
        </template>
      </tabs-layout>
      <div class="table">
        <el-table v-if="list"
                  ref="table"
                  @select="onSelect"
                  @select-all="onAllSelect"
                  v-loading="listLoading" :data="list" row-key="id"  border fit highlight-current-row  style="width: 100%" >
          <el-table-column
            align="center"
            width="65"
            show-overflow-tooltip
            :render-header="renderHeader"
            fixed
          >
            <template slot-scope="scope">
              <span>{{ scope.$index+1 }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :reserve-selection="true"
            align="center"
            type="selection"
            width="55"
            fixed
          >
          </el-table-column>
          <el-table-column
            v-for="(item, index) in tableTitle"
            :key="index"
            :min-width="(item.width?item.width:'180px')"
            :width="item.name=='pictIdS'?item.width:''"
            :label="item.label"
            :align="item.name=='pictIdS'?'center':'left'"
          >
            <template slot-scope="{row}">
              <span v-if="item.name=='approvalStatus'">{{row[item.name] && row[item.name].desc}}</span>
              <span v-else-if="item.name === 'pictIdS'">
                <el-popover placement="right" trigger="hover" >
                  <el-image
                    style="width: 200px; height: 200px"
                    fit="contain"
                    :src="row.pictIdS|imgFilter"
                  >
                  </el-image>
                  <el-image
                    slot="reference"
                    style="width: 50px; height: 50px"
                    fit="cover"
                    :src="row.pictIdS|imgFilter"
                  ></el-image>
                </el-popover>
              </span>
              <span v-else-if="item.name=='whetherOnSale'">
                {{ row[item.name].code=='Y' ? '已上架' : '' }}
                {{ row[item.name].code=='N' ? '已下架' : '' }}
                {{ row.approvalStatus ? '/ '+row.approvalStatus.desc : '' }}
                <div style="color: red">{{row.rejectReason}}</div>
              </span>
              <span v-else-if="item.name=='grossProfitRate'">
                <div>毛利额：{{row.grossProfit}}</div>
                <div>毛利率：{{row.grossProfitRate}}</div>
              </span>
              <span v-else-if="item.name=='productName'">
                <div>{{row[item.name]}}</div>
                <div>{{row.spec}}</div>
                <div>{{row.manufacturer}}</div>
                <div>{{row.approvalNumber}}</div>
              </span>
              <span v-else-if="item.name === 'otherInfo'">
                <div>通用名：{{row.drugName}}</div>
                <div>剂型：{{row.agentiaType}}</div>
                <div>品牌：{{row.brandName}}</div>
                <div>产地：{{row.area}}</div>
                <div>条形码：{{row.barCode}}</div>
              </span>
              <span v-else-if="item.name === 'updateTime'">
                <div>创建：{{row.createTime}} </div>
                <div>审核：{{row.approvalTime}}</div>
                <div>最新：{{row[item.name]}}</div>
              </span>
              <span v-else-if="item.name === 'realStockQuantity'">
                <div>实际库存：{{row.realStockQuantity}}</div>
                <div>可卖库存：{{row.stockQuantity}}</div>
                <div>仓库：{{row.warehouseName}}</div>
              </span>
              <span v-else-if="item.name === 'salePrice'">
                <div>销售价：{{row.salePrice}}</div>
                <div>成本价：{{row.costPrice}}</div>
                <div>建议批发价：{{row.wholesalePrice}}</div>
                <div>建议零售价：{{row.retailPrice}}</div>
              </span>
              <!-- 平台分类 -->
              <span v-else-if="item.name === 'platformCategoryId'">
                {{ filterPlatformCategoryLabel2Id(row.platformCategoryId) }}
              </span>
              <el-button v-else-if="item.name=='salePrice'" type="text" style="color:#FF6E1B">{{row[item.name]}}</el-button>
              <el-button v-else-if="item.name=='costPrice'" type="text" style="color:#2DAC0C">{{row[item.name]}}</el-button>
              <el-button v-else-if="item.name=='grossProfit'" type="text" style="color:#2DAC0C">{{row[item.name]}}</el-button>
              <span  v-else>{{ row[item.name] }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" align="center" label="操作" :width="listQuery.model.approvalStatus != 'ACCEPTED'?'180':'160'" class="itemAction">
            <template slot-scope="scope">
              <el-row class="table-edit-row">
                <span class="table-edit-row-item" v-if="checkPermission(['admin','admin-platform-product:relation','admin-saas-product:relation'])">
                  <el-link type="primary" @click="toDetailFun(scope.row)">详情</el-link>
                </span>
                <span v-if="checkPermission(['admin','admin-platform-product:qualificationPreview','admin-saas-product:qualificationPreview'])" class="table-edit-row-item">
                  <el-link type="primary" @click="handleBigImage(scope.row)">资质</el-link>
                </span>
                <!-- 审核 -->
                <span v-if="listQuery.model.approvalStatus != 'ACCEPTED'&&checkPermission(['admin','admin-platform-product:audit','admin-saas-product:audit'])" class="table-edit-row-item">
                  <ExamineButton :isReload.sync="submitReload" :row.sync="scope.row"></ExamineButton>
                </span>
              </el-row>
              <el-image :ref="`ref${scope.row.id}`" style="width:0;height:0" :src="previewImages[0]" :preview-src-list="previewImages"></el-image>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="listQuery.current" :limit.sync="listQuery.size" @pagination="getList" />
      </div>
    </div>
    <excelUpload v-if="showUpload" :visible.sync="showUpload" :isReload.sync="submitReload" :uploadDatas.sync="uploadDatas"></excelUpload>
  </div>
</template>
<script>
import {downloadFile} from '@/utils/commons'
import checkPermission from '@/utils/permission'
import { list,pageCount,setBatchAcceptProduct,setBatchRejectProduct } from '@/api/products/product'
import { getForProductLicense } from '@/api/productLicenseRel'
import ExamineButton from '@/views/products/product/ExamineButton'
import excelUpload from "@/views/products/product/excel/upload";
import CarouselButton from '@/components/eyaolink/Product/CarouselButton'
import CascaderOfProductType from '@/components/eyaolink/CascaderOfProductType'
import tableInfo from './tableInfo'
import Pagination from '@/components/Pagination'
import { getQueryString } from '@/utils/index'
import { query } from "@/api/products/categoryPlatform";
import TabsLayout from '@/components/TabsLayout'

export default {
  components: {
    excelUpload,
    Pagination,
    CarouselButton,
    CascaderOfProductType,
    ExamineButton,
    TabsLayout
  },
  props: {
    commerceModel: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      isExpand: false,
      pageCountInfo:{
        "acceptedCount": "0",
        "pendingCount": "0",
        "rejectedCount": "0",
        "putOnSaleCount": "0",
        "pullOffShelvesCount": "0"
      },
      platformCategoryList: [],
      showUpload:false,
      uploadDatas:[],
      previewImages:[],
      showSelectTitle:false,
      row:{},
      multipleSelection:[],
      tableTitle:[],
      submitReload:false,
      list: [],
      total: 0,
      listLoading: true,
      tableSelectTitle:[0,1,2,3],
      searchForm:{
        productName:"",
        saleMerchantName:'',
        productCode:"",
        brandCode:"",
        // drugName:"",
        platformCategoryCode:"",
        stockCondition:"",
        whetherOnSale: "",
        pictIdsIsEmpty: "",
        manufacturer: ""
      },
      listQuery: {
        model:{
          approvalStatus : "ALL",
          whetherOnSale: ""
        },
        current: 1,
        size: 10
      },
    };
  },
  computed: {
    approvalStatusList() {
      return [
        {
          name: '全部商品' + ((+this.pageCountInfo.totalCount) > 0 ? '(' + this.pageCountInfo.totalCount + ')' : ''),
          value: 'ALL',
          hide: !checkPermission(['admin','admin-platform-product:allView','admin-saas-product:allView'])
        },
        {
          name: '在售商品' + ((+this.pageCountInfo.onSaleCount) > 0 ? '(' + this.pageCountInfo.onSaleCount + ')' : ''),
          value: 'SALEING',
          hide: !checkPermission(['admin','admin-platform-product:onSale','admin-saas-product:onSale'])
        },
        {
          name: '待售商品' + ((+this.pageCountInfo.forSaleCount) > 0 ? '(' + this.pageCountInfo.forSaleCount + ')' : ''),
          value: 'SALEPENDING',
          hide: !checkPermission(['admin','admin-platform-product:penddingSale','admin-saas-product:penddingSale'])
        },
        {
          name: '待审商品' + ((+this.pageCountInfo.pendingCount) > 0 ? '(' + this.pageCountInfo.pendingCount + ')' : ''),
          value: 'PENDING',
          hide: !checkPermission(['admin','admin-platform-product:penddingView','admin-saas-product:penddingView'])
        },
        {
          name: '已驳回商品' + ((+this.pageCountInfo.rejectCount) > 0 ? '(' + this.pageCountInfo.rejectCount + ')' : ''),
          value: 'REJECTED',
          hide: !checkPermission(['admin','admin-platform-product:rejectedView','admin-saas-product:rejectedView'])
        }
      ]
    }
  },
  watch:  {
    submitReload:function(newVal,oldVal){
      if(newVal){
        this.submitReload=false;
        this.getPageCount()
        this.getList()
      }
    }
  },
  filters:{
    imgFilter:function(value){
      if(value!=""&&value!=null){
        return value.split(",")[0]
      }else{
        return require("@/assets/img/index/product_default.png");
      }
    }
  },
  methods: {
    checkPermission,
    async getPageCount() {
      const listParams = this.handelParams()
      const { data } = await pageCount(listParams)
      this.pageCountInfo= Object.assign(this.pageCountInfo,data)
    },
    // 获取平台分类
    async getCategoryPlatformList() {
      const { code, data } = await query({})
      if(code==0)
          this.platformCategoryList = data
    },
    // 通过id获取平台分类label
    filterPlatformCategoryLabel2Id(id) {
      const { platformCategoryList } = this
      if (Array.isArray(platformCategoryList) && platformCategoryList.length > 0) {
        const platformCategoryItem = platformCategoryList.find(item => item.id === id)
        if (platformCategoryItem) {
          return platformCategoryItem.label
        }
        return '-'
      }
      return '-'
    },
    inputExcel(){
      this.showUpload=true;
    },
    reviewedFun(row) {
        this.row=row
        this.showReviewedPage=true
    },

    renderHeader (h,{column}) {
      var titles=tableInfo[this.listQuery.model.approvalStatus];
      var titlesName=[ '显示字段项','隐藏字段项'];
      return (
        <div style="position:relative">
          <div onClick={this.showHeaer} >
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            append-to-body
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="640px"
            center>
            <el-transfer vModel={this.tableSelectTitle} onChange={this.setleftTitleFun} data={titles} titles={titlesName}></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button  onClick={this.closeHeaer}>取消</el-button>
              <el-button type="primary" onClick={this.setHeaer}>确认</el-button>
            </div>
          </el-dialog>
        </div>
      )
    },
    async handleBigImage(row){
      var {code,data} = await getForProductLicense(row.id);
      if(code == 0){
        console.log('data---->',data);
        if(data!=null&&data.productLicenseRelVoList!=null){
          data.productLicenseRelVoList.forEach((item)=>{
            item.fileIds.split(',').forEach(o=>{
              this.previewImages.push(o);
              this.$refs[`ref${row.id}`].showViewer = true;
            })
          })
        } else {
          this.$message.warning('无资质可查看')
        }
      }else {
        this.$message.warning('无资质可查看')
      }
    },
    setleftTitleFun(val){
      this.tableSelectTitle=val
    },
    setHeaer:function(){
      var titles=tableInfo[this.listQuery.model.approvalStatus];
      var listinfo = titles.filter((element, index, self) => {
        return !this.tableSelectTitle.includes(element.key)
      });
      this.tableTitle=listinfo;
      this.showSelectTitle=!this.showSelectTitle
    },

    showHeaer:function(){
      this.showSelectTitle=true
    },
    closeHeaer:function(){
      this.showSelectTitle=false
      this.tableSelectTitle=[]
    },
    // 处理数据
    handelParams() {
      const { model } = this.listQuery
      const { approvalStatus, whetherOnSale } = model
      let approvalStatusTemp = approvalStatus
      let whetherOnSaleTemp = whetherOnSale
      switch (approvalStatus) { 
        case 'SALEING':
          whetherOnSaleTemp = 'Y'
          approvalStatusTemp = ''
          break
        case 'SALEPENDING':
          whetherOnSaleTemp = 'N'
          approvalStatusTemp = ''
          break
        case 'PENDING':
        case 'REJECTED':
          whetherOnSaleTemp = ''
          break
        default:
          approvalStatusTemp = ''
          whetherOnSaleTemp = ''
      }
      // 处理参数
      const listParams = {
        ...this.listQuery,
        model: {
          ...model,
          approvalStatus : approvalStatusTemp,
          whetherOnSale: whetherOnSaleTemp,
          commerceModel: this.commerceModel
        }
      }
      return listParams
    },
    async getList() {
      this.listLoading = true
      const listParams = this.handelParams()
      const { data } = await list(listParams, true)
      this.list = data.records
      this.total = data.total
      this.listLoading = false
    },

    onSearchSubmitFun:function(){
      this.listQuery={
        model:{
          approvalStatus:this.listQuery.model.approvalStatus
        },
        current: 1,
        size: 10
      }
      this.listQuery.model["productName"]=this.searchForm.productName;
      this.listQuery.model["productName"]=this.searchForm.productName;

      this.listQuery.model["manufacturer"]=this.searchForm.manufacturer

      this.listQuery.model["brandCode"]=this.searchForm.brandCode
      this.listQuery.model["pictIdsIsEmpty"]=this.searchForm.pictIdsIsEmpty

      this.listQuery.model["saleMerchantName"]=this.searchForm.saleMerchantName
      // this.listQuery.model["drugName"]=this.searchForm.drugName

      this.listQuery.model["platformCategoryCode"]=this.searchForm.platformCategoryCode

      this.listQuery.model.stockCondition=this.searchForm.stockCondition;
      this.listQuery.model.whetherOnSale=this.searchForm.whetherOnSale;
      this.listQuery.model.platformCategoryCode = this.searchForm.platformCategoryCode;
      this.listQuery.model.labelIds = this.searchForm.labelIds ? [this.searchForm.labelIds] : [];
      this.list = []
      this.getList()
    },
    handleChangeTab(tab) {
      this.multipleSelection=[],
      this.$refs.table.clearSelection();
      this.list = []
      this.listQuery.current = 1;
      this.initTbaleTitle()
      this.getList()
      this.getPageCount()
    },
    resetForm(formName) {
       this.searchForm={
        productName:"",
        saleMerchantName:'',
        productCode:"",
        brandCode:"",
        // drugName:"",
        platformCategoryCode:"",
        stockCondition:"ALL",
        whetherOnSale: "",
        pictIdsIsEmpty: "",
        manufacturer: ""
      }
      this.listQuery.model["productName"]="";
      this.listQuery.model["manufacturer"]="";

      this.listQuery.model["productCode"]=""
      this.listQuery.model["whetherOnSale"]=""

      this.listQuery.model["brandCode"]=""
      this.listQuery.model["pictIdsIsEmpty"]=""

      // this.listQuery.model["drugName"]=""
      this.listQuery.model["saleMerchantName"]=""
      this.listQuery.model["platformCategoryId"]=""
      this.listQuery.model["labelIds"]=[]

      this.listQuery.model["platformCategoryCode"]=""
      this.getList()
      this.getPageCount()
    },
    initTbaleTitle(){
      this.tableSelectTitle=[99]
      this.tableTitle=tableInfo[this.listQuery.model.approvalStatus]
      this.tableTitle = this.tableTitle.filter(item=>{
          return item.key!=99
      })
    },

    // table 选中
    onAllSelect(selection) {
      this.onSelect(selection);
    },
    onSelect:function(val){
     this.multipleSelection = val;
    },
    async outExcel() {
      if(this.multipleSelection.length>0){
        const tHeader = ['id']
        const filterVal = ['id']
        this.tableTitle.forEach(function(item){
            tHeader.push(item.label)
            filterVal.push(item.name)
        })
        let exportData= this.formatJson(this.multipleSelection,filterVal)
        downloadFile({tHeader:tHeader,fileName:"商品列表",exportData:exportData})
      }else{
        this.$message.error('请在商品列表中勾选需要导出的商品')
      }
    },
    formatJson(dataList,filterVal) {
      return dataList.map(v => filterVal.map(j => {
        if (j === 'approvalStatus') {
          return v[j].desc
        } else if (j === 'publishStatus') {
          return v[j].desc
        } else if (j === 'stockQuantityStatus') {
          return v[j].desc
        } else {
          return v[j]
        }
      }))
    },
    async setRejectReasonListFun(){
      //批量驳回提交
      let _this=this;
      if(this.multipleSelection.length>0){
        let subObj={"list": []}
        this.multipleSelection.forEach(function(item){
          subObj.list.push({
              id:item.id,
              approvalStatus:"REJECTED",
              rejectReason: "批量驳回"
          })
        })
        _this.submitRejectProductPlatformApiFun(subObj)
       }else{
         _this.$message.error('请在商品列表中勾选需要驳回的产品')
      }
    },
    async submitRejectProductPlatformApiFun(subObj){
      this.$confirm("此操作将批量驳回商品，是否继续？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: 'warning',
      }).then(async ()=>{
        let _this=this;
        var data = await setBatchRejectProduct(subObj);
        if(data.code==0){
          _this.showDetailPage=false;
          _this.rejectReason="";
          _this.multipleSelection = [];
          _this.getPageCount()
          _this.getList()
        } else{
          _this.$message.error("驳回提交失败!")
        }
      })
    },
    async submitProductPlatformPassListFun(row){
      //批量通过提交
      let _this=this;
      if(this.multipleSelection.length>0){
        let subObj={"list": []}
        this.multipleSelection.forEach(function(item){
          subObj.list.push({
              id:item.id,
              approvalStatus:"ACCEPTED",
              rejectReason: "通过"
          })
        })
        _this.submitAcceptProductPlatformApiFun(subObj)
       }else{
         _this.$message.error('请在商品列表中勾选需要通过的产品')
      }
    },
    async submitAcceptProductPlatformApiFun(subObj){
      this.$confirm("此操作将批量通过商品，是否继续？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: 'warning',
      }).then(async ()=>{
        let _this=this;
        var data = await setBatchAcceptProduct(subObj);
        if(data.code==0){
          _this.showDetailPage=false;
          _this.rejectReason="";
          _this.multipleSelection = [];
          _this.getPageCount()
          _this.getList()
        }else{
          _this.$message.error("批量通过提交失败!")
        }
      })
    },
    toDetailFun(row){
      this.$router.push({path:"/products/product/detail",
      query: {
        id: row.id
      }
    })
    }

  },
  mounted() {
      if(getQueryString("code")!=null){
        this.listQuery.model.brandCode=getQueryString("code")
        this.searchForm.brandCode=getQueryString("code")
      }
      this.initTbaleTitle()
      this.getPageCount()
      this.getList()
      this.getCategoryPlatformList()
  },
  beforeDestroy() {}
};
</script>
<style lang="scss" scoped>
@import "@/styles/element-variables.scss";

.archivesPageContent {
  padding:0 ;
  .temp_searchBox{height: 64px;overflow: hidden; margin-bottom: 0; }
  .form-inline{height:60px; overflow: hidden;}
  .title{
        border-bottom:2px solid #EBECEE;
        margin-bottom: 16px;
      span{
        margin-bottom: -2px;
        padding:0 15px;
        height: 40px;
        line-height: 30px;
        display:block;
        background: rgba(255,255,255,0);
        border-bottom:2px solid rgb(64, 158, 255);
        font-size: 16px;
        font-family: 'PingFangSC-Regular', 'PingFang SC', 'PingFangSC-Regular', 'PingFang SC'-400;
        font-weight: 400;
        color:rgb(64, 158, 255);
      }
  }

  .formItem{width:586px;}
  .line{color:#dfe6ec; margin:0 6px;}
  .typeTabs{height: 40px;margin-bottom: -2px; margin-left:30px;}

}
</style>
<style lang="less" scoped>
  /deep/ .el-table-column--selection .cell{padding:0;}
</style>
