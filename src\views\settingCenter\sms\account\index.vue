<template>
  <div class="WeChatPageContent">
    <tabs-layout
      ref="tabs-layout"
      :tabs="[ { name: '短信账号设置' } ]"
    >
      <template slot="button">
        <el-button >刷新</el-button>
        <!-- <el-button type="primary" @click="newFun">+新增短信账号设置</el-button> -->
      </template>
    </tabs-layout>
    <div class="table">
      <el-table
        v-loading="listLoading"
        :data="list"
        row-key="id"
        border
        fit

        highlight-current-row
        style="width: 100%"
      >
        <el-table-column
          align="center"
          width="65"
          :render-header="renderHeader"
          fixed
        >
          <template slot-scope="{ row }">
            <span>{{ row.id }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-for="(item, index) in tableTitle"
          :key="index"
          min-width="300px"
          :label="item.label"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            <span>{{ row[item.name] }}</span>
          </template>
        </el-table-column>
        <el-table-column
            fixed="right"
            align="right"
            label="操作"
            width="180"
            class="itemAction"
        >
            <template slot-scope="scope">
                <el-button @click="handleClick(scope.row)" type="text" >充值</el-button>
                <!-- <span class="line">|</span>
                <el-button type="text" >删除</el-button> -->
            </template>
        </el-table-column>
      </el-table>
       <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>
    <!-- 设置 充值短信 -->
    <el-dialog v-if="showEdit" :title="'充值短信'" :visible.sync="showEdit" width="450" >
        <edit :visible.sync="showEdit" :isReload.sync="submitReload" :row.sync="row"></edit>
    </el-dialog>
    <!-- 设置 充值短信 -->
  </div>
</template>
<script>
import { accountList } from '@/api/setting/sms/account'
// import edit from './edit'
import edit from '@/views/settingCenter/sms/account/edit'
import Pagination from '@/components/Pagination'
import TabsLayout from '@/components/TabsLayout'
export default {
  data() {
    return {
      showEdit:false,
      row:{
          id:0,
          type:"",
          name:"",
      },

      tableTitle:[
        {
          key: 0,
          label:'短信账号',
          name: "account",
          disabled: false
        },
        {
          key: 1,
          label:'短信服务商',
          name: "serverType",
          disabled: false
        },
        {
          key: 1,
          label:'剩余短信',
          name: "surplus",
          disabled: false
        }
      ],
      submitReload:false,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 10
      },
    };
  },
  watch:  {
    submitReload:function(newVal,oldVal){
      if(newVal){
        this.submitReload=false;
        console.info("重新加载")
      }
    }
  },
 components: {
   Pagination,
   edit,
   TabsLayout
 },

  methods: {
    newFun:function(){
        this.row={
            id: 0,
            account: '',
            serverType: '',
            surplus:0,
            crDate: null,
        };
        this.showEdit=true
    },
    handleClick(row) {
        this.row=row
        this.showEdit=true
    },
    renderHeader (h,{column}) {
      return (
        <div style="position:relative">
          <div>
            <i class="el-icon-menu" />
          </div>
        </div>
      )
    },
    setHeaer:function(){
      this.showSelectTitle=!this.showSelectTitle
    },
    async getList() {
      this.listLoading = true
      const { data } = await accountList(this.listQuery)
      this.list = data.items
      this.total = data.total
      this.listLoading = false
    }
  },
  mounted() {
      this.getList()
  },
  beforeDestroy() {}
};
</script>
<style lang="scss" scoped>
@import "@/styles/element-variables.scss";

.WeChatPageContent {
  padding: 15px;
  .title{
       border-bottom:2px solid #EBECEE;
       margin-bottom:35px;
      span{
        margin-bottom: -2px;
        padding:0 15px;
        height: 40px;
        line-height: 30px;
        display:block;
        background: rgba(255,255,255,0);
        border-bottom:2px solid rgb(64, 158, 255);
        font-size: 16px;
        font-family: 'PingFangSC-Regular', 'PingFang SC', 'PingFangSC-Regular', 'PingFang SC'-400;
        font-weight: 400;
        color:rgb(64, 158, 255);
      }
  }
  .formItem{width:586px;}
  .line{color:#dfe6ec; margin:0 6px;}
}
</style>
