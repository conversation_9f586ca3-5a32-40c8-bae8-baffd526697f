<template>
  <div class="temp_ProductTableButton">
    <el-button style="width:100px;padding:0 -10px; text-indent: -10px;" @click="showProductTableDialogFun()">选择专题页
    </el-button>
    <el-dialog v-if="showProductTableDialog" append-to-body title="选择专题页" :visible.sync="showProductTableDialog"
      :before-close="closeDialogFun" :close-on-click-modal="false" width="80%" :show-close="false">
      <div style="position:absolute;top:10px; right:15px; background:#fff; height:38px;">
        <div>
          <el-button @click="clearFun()">取 消</el-button>
          <!-- <el-button type="primary" @click="submitFun()">确 定</el-button> -->
        </div>
      </div>
      <div class="showProductTable">
        <div class="temp_searchBox">
          <el-form ref="searchForm" :inline="true" :model="listQuery" class="form-inline">
            <el-form-item label="">
              <el-input v-model="listQuery.model.productName" placeholder="请输入商品名称" style="width:250px" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="getList()">搜索</el-button>
              <el-button @click="resetForm('searchForm')">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="table">
          <el-table v-if="list" ref="table" v-loading="listLoading" :data="list" row-key="id" border fit
            highlight-current-row style="width: 100%">
            <el-table-column align="center" width="50" label="序号" fixed show-overflow-tooltip>
              <template slot-scope="scope">
                <span>{{ scope.$index+1 }}</span>
              </template>
            </el-table-column>
            
            <el-table-column v-for="(item, index) in tableTitle" :key="index"
              :width="item.width" :min-width="(item.width?item.width:'350px')" :label="item.label" show-overflow-tooltip
              align="center">
              <template slot-scope="{row}">

                <span v-if="item.name=='approvalStatus'">{{ row[item.name].desc }}</span>
                <span
                  v-else-if="item.name=='publishStatus'&&row[item.name].code=='PUT_ON_SALE'">{{ row[item.name].desc }}</span>
                <el-button v-else-if="item.name=='publishStatus'&&row[item.name].code=='PULL_OFF_SHELVES'" type="text">
                  {{ row[item.name].desc }}</el-button>

                <span v-else-if="item.name=='stockQuantityStatus'">{{ row[item.name].desc }}</span>
                <el-button v-else-if="item.name=='salePrice'" type="text" style="color:#FF6E1B">{{ row[item.name] }}
                </el-button>
                <el-button v-else-if="item.name=='costPrice'" type="text" style="color:#2DAC0C">{{ row[item.name] }}
                </el-button>
                <el-button v-else-if="item.name=='grossProfit'" type="text" style="color:#2DAC0C">{{ row[item.name] }}
                </el-button>
                <span v-else>{{ row[item.name] }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" :min-width="'100px'" label="操作" fixed="right">
              <template slot-scope="scope">
                <el-button type="text" v-if="scope.row.id !=selectItems" @click="selectProductItemFun(scope.row)">选中</el-button>
                <el-button type="text" v-else style="color:red">已选中</el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination v-show="total>0" :total="total" :page.sync="listQuery.current" :limit.sync="listQuery.size"
            @pagination="getList" />
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import { getList } from "@/api/setting/data/dictionaryItem";
  import Pagination from '@/components/Pagination'
  export default {
    components: {
      Pagination
    },
    props: {
      selectItems: {
        type: String,
        default: '',
        required: true
      }
    },
    data() {
      return {
        tableTitle: [{
            label: '专题页名称',
            name: 'featuredName',
            prop: 'featuredName',
            width: 200
          },
          {
            label: '专题页描述',
            name: 'featuredDescribe',
            prop: 'featuredDescribe'
          },
          {
            label: '推荐商品数',
            name: 'productAmount',
            prop: 'productAmount',
            width: 150
            // slot: true
          },
          {
            label: '操作时间',
            name: 'updateTime',
            prop: 'updateTime'
          }

        ],
        showProductTableDialog: false,
        listQuery: {
          model: {
              featuredName:''
            // whetherOnSale: 'Y',
            // approvalStatus: 'ACCEPTED'
          },
          current: 1,
          size: 10
        },
        selectRowItems: [],
        list: [],
        total: 0,
        listLoading: true
      }
    },
    computed: {
      selectRowVal: {
        get() {
          this.selectItems
        },
        set(val) {
          this.$emit('update:selectItems', val)
          this.$emit('confirm')
        }
      }
    },
    methods: {
      clearFun() {
        this.listQuery = {
            model: {
                featuredName:''
            //   whetherOnSale: 'Y',
              // approvalStatus: 'ACCEPTED'
            },
            current: 1,
            size: 10
          },
          this.list = [],
          this.total = 0,
          this.listLoading = true,
          this.showProductTableDialog = false
      },
      showProductTableDialogFun() {
        this.listQuery = {
            model: {
              featuredName: '',
              // approvalStatus: 'ACCEPTED'
            },
            current: 1,
            size: 10
          },
          this.list = [],
          this.total = 0,
          this.listLoading = true,
          this.showProductTableDialog = true
        this.getList()
      },
      async getList() {
        this.listLoading = true
        // const { data } = await productList(this.listQuery)
        const {
          data
        } = await getList(this.listQuery);
        if (data != null) {
          this.list = data.records
          this.total = data.total
        }
        this.listLoading = false
        this.displayTable()
      },
      resetForm() {
        this.listQuery = {
          model: {
              featuredName:'',
            // whetherOnSale: 'Y',
          },
          current: 1,
          size: 10
        };
        this.getList();
      },
      closeDialogFun() {
        this.showProductTableDialog = false
      },
      onAllSelect(selection) {
        this.onSelect(selection)
      },
      onSelect: function (val, row) {
        if (val.length < 10) {
          this.selectRowItems = val
        } else {
          this.$refs.table.toggleRowSelection(row, false)
        }
      },
      displayTable() {
        const vm = this
        vm.selectItems.forEach(item => {
          vm.list.forEach(rowItem => {
            if (item.id === rowItem.dataParam) {
              vm.$refs.table.toggleRowSelection(item, true)
            }
          })
        })
      },
      selectProductItemFun(item) {
        this.selectRowVal = item.id
        this.clearFun()
      }
    }
  }

</script>
<style lang="less" scoped>
  .temp_ProductTableButton {
    width: 100%;

  }

  .showProductTable {
    margin: -30px -20px;
    border-top: 1px solid #ebecee;
    padding: 10px 20px;
  }

  .temp_searchBox {
    height: 45px;
    overflow: hidden;
    margin-bottom: 0;
    padding: 0;
    border-left: none;
  }

</style>
