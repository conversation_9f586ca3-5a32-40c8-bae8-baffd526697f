<template>
  <div class="detail-wrapper">
    <page-title title="退款单详情" />
    <div class="order-detail-items" v-if="detail.refundStatusEnum">
      <ul class="orders"><li>订单编号：{{detail.orderNo}}</li><li>下单时间：{{detail.orderCreateTime}}</li><li>下单渠道：{{detail.orderChannel}}</li></ul>
      <div class="clearfix"></div>
      <el-steps :active="active" align-center v-if="detail.refundStatusEnum.code !=='REFUSE_REFUND'">
        <el-step title="待审核" :description="detail.waitRefundTime"></el-step>
        <el-step title="退款中" :description="detail.salesRefundTime"></el-step>
        <el-step title="已完成" :description="detail.hadRefundTime"></el-step>
      </el-steps>
      <div class="order-status" v-if="detail.refundStatusEnum">
        <h3>
          <img src="../../../../assets/imgs/order_icon.png">{{detail.refundStatusEnum.desc}}
          <span style="margin-left: 20px;">总退款金额：<span class="text-warning">
            ￥<span class="text-warning" v-text="detail.salesRefundMoney"></span></span>
          </span>
          <!-- <a class="text-primary" style="font-weight: 500;font-size: 12px;margin-left: 10px;" v-if="detail.refundStatusEnum.code === 'HAD_REFUND'">钱款去向</a> -->
        </h3>
        <p v-if="detail.refundStatusEnum.code === 'WAIT_REFUND'">订单包裹已提交退款申请，等待管理员进行审核</p>
        <p v-if="detail.refundStatusEnum.code === 'BEING_REFUND'">已同意退款申请</p>
        <p v-if="detail.refundStatusEnum.code === 'HAD_REFUND'">退款已完成</p>
        <p v-if="detail.refundStatusEnum.code === 'REFUSE_REFUND'">拒绝原因：{{detail.rejectionReason}}</p>
        <div class="fr" style="margin-top: -65px;">
          <!--待审核：导出单据、审核；-->
          <!-- <el-button v-if="detail.refundStatusEnum.code === 'WAIT_REFUND'&&checkPermission(['admin','refundDetail:export'])">导出单据</el-button> -->
          <el-button type="primary" v-if="detail.refundStatusEnum.code === 'WAIT_REFUND'&&checkPermission('admin','refundDetail:verify')" @click="verifyVisible = true">审核</el-button>
        </div>
      </div>
      <page-module-card title="退款信息">
        <ul class="client-info" v-if="detail.purMerchant">
          <li>退款单号：{{detail.salesRefundNo}}</li>
          <li>客户编码：{{detail.purMerchant.code}}</li>
          <li>客户名称：{{detail.purMerchant.name}}</li>
          <li>申请人：{{detail.applicant}}</li>
          <li>退款时间：{{detail.salesRefundTime}}</li>
          <!--<li style="width:100%;">退款原因：{{detail.rejection}}</li>-->
        </ul>
      </page-module-card>

      <page-module-card title="退款商品信息">
        <el-table border :data="detail.salesReturnItemList" >
          <el-table-column type="index"></el-table-column>
          <el-table-column label="商品主图" prop="picturePath" class-name="img-cell">
            <template slot-scope="scope">
              <img :src="scope.row.picturePath | imgFilter" class="productImg">
            </template>
          </el-table-column>
          <el-table-column label="商品编码" prop="productCode" width="200" show-overflow-tooltip></el-table-column>
          <el-table-column label="商品名称" prop="productName" width="200" show-overflow-tooltip></el-table-column>
          <el-table-column label="规格" prop="spec" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column label="生产厂家" prop="saleMerchantName" min-width="200" show-overflow-tooltip></el-table-column>
          <!--<el-table-column label="有效期至" prop="expireTime"></el-table-column>-->
          <el-table-column label="单价" prop="unitMoney" width="120" show-overflow-tooltip></el-table-column>
          <!--<el-table-column label="购买数量" prop="buyNum">-->
          <el-table-column label="退货数量" prop="totalNum" width="120" show-overflow-tooltip></el-table-column>
         <!-- <el-table-column label="退货批号" prop="sku"></el-table-column>-->
          <el-table-column label="退款金额" prop="money" width="120" show-overflow-tooltip>
            <template slot-scope="scope">
              <span v-text="formatterMoney(scope.row.money,2)"></span>
            </template>
          </el-table-column>
        </el-table>
        <template>
          <tabs-layout
            :tabs="[{ name: '退款日志', value: 'first' }]"
            v-model="activeName"
          />
          <template v-if="activeName === 'first'">
            <el-timeline>
              <el-timeline-item
                hide-timestamp
                v-for="(activity, index) in activities"
                :key="index"
                :icon="activity.icon"
                :type="activity.type"
                :color="activity.color"
                :size="activity.size">
                {{activity.time}}
                <span class="active-content">{{activity.content}}</span>
              </el-timeline-item>
            </el-timeline>
          </template>
        </template>
      </page-module-card>
      <!--审核-->
      <verify  v-bind:title="title" :verifyVisible="verifyVisible" @changeShow="changeVerify" @verifyRefuse="verifyRefuse" @verifyAgree="verifyAgree" :isRefund="false"></verify>
    </div>
  </div>
</template>

<script>
  import checkPermission from '@/utils/permission'
  import { refundDetail,refuseSalesRefund,agreeSalesRefund } from "@/api/trade";
  import verify from "../dialogs/verify";
  export default {
    name: "detail",
    components: {
      verify
    },
    data(){
      return {
        activeName: 'first',
        title: '该退款单是否通过申请',
        visible: true,
        verifyVisible: false,//审核弹出框
        remarkVisible: false,
        remarks: '',
        innerVisible: false,
        agreeVisible: false,//同意申请
        reason: '',
        cancelVisible: false,
        currentComponent: '',
        detail: {},
        productData: [],
        activities: [],
        agreeForm: {},
        active: 1,
      }
    },
    mounted() {
      this.getDetail()
    },
    methods: {
      checkPermission,
      changeVerify(data) {
        if (data === 'false') {
          this.verifyVisible = false
        } else {
          this.verifyVisible = true
        }
      },
      //审核拒绝
      verifyRefuse(reason) {
        refuseSalesRefund(this.$route.query.id,reason).then(res=>{
          this.$message.success('审核已拒绝！')
          this.verifyVisible = false
          this.getDetail()
        })
      },
      //审核通过
      verifyAgree(agreeReason,freight) {
        agreeSalesRefund({
          agreeReason: agreeReason,
          freight: 0,
          id: this.$route.query.id}).then(res=>{
          this.$message.success('审核已通过！')
          this.verifyVisible = false
          this.getDetail()
        })
      },
      arraySpanMethod({ row, column, rowIndex, columnIndex }) {
        if(columnIndex === 1) {
          return [1, 10]
        }
      },
      async getDetail() {
        let id = this.$route.query.id
        const {data} = await refundDetail(id)
        this.detail = data
        this.activities = data.rejectionJournal
        if(data.refundStatusEnum.code === 'PENDING') {
          this.active = 0
        } else if(data.refundStatusEnum.code=== 'BEING_REFUND') {
          this.active = 1
        } else if(data.refundStatusEnum.code=== 'HAD_REFUND') {
          this.active = 2
        }
      },
      handleClick() {},
      componentResult() {
      },
      handleVeriry() {
      },
      handleRefuse() {
      },
      //备注提交
      remarkSubmit() {},
      //取消订单
      cancelSubmit() {},
      formatterMoney(s, n) {
        n = n > 0 && n <= 20 ? n : 2;
        s = parseFloat((s + "").replace(/[^\d\.-]/g, "")).toFixed(n) + "";
        let l = s.split(".")[0].split("").reverse(), r = s.split(".")[1];
        let t = "";
        for (let i = 0; i < l.length; i++) {
          t += l[i] + ((i + 1) % 3 == 0 && (i + 1) != l.length ? "," : "");
        }
        return t.split("").reverse().join("") + "." + r;
      }
    }
  }
</script>

<style lang="scss">
  .detail-wrapper {
    padding: 0 20px;
  }
</style>
