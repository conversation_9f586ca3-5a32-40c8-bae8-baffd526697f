<template>
  <div v-if="listData.length>0">
    <el-table border :data="listData" width="100%" ref="multipleTable" :row-key="getRowKeys" @selection-change="handleSelectionChange"
      @select-all="handleSelectAll">
      <el-table-column label="序号" type="index" width="50" align="center" :reserve-selection="true" />
      <el-table-column type="selection" align="center" :reserve-selection="true"  />
      <template v-for="(item,index) in columnData" v-key="index">
          <el-table-column show-overflow-tooltip v-if="item.prop =='updateTime' || item.prop =='productGroupName' || item.prop == 'saleMerchantName'"  :prop="item.prop" :label="item.label" :width="item.width">
            <template slot-scope="{row}">
                {{row[item.prop]}}
            </template>
          </el-table-column>
          <el-table-column v-else :prop="item.prop" :label="item.label" :min-width="item.width">
            <template slot-scope="{row}">
              <div v-if="item.prop == 'sumData'">
                <div>会员推广费：<span v-if="row.numMemberRatio!=null && row.numMemberRatio != ''">{{row.numMemberRatio}}%</span>
                </div>
                <div style="margin-top:5px">非会员推广费：<span
                    v-if="row.numNoMemberRatio!=null && row.numNoMemberRatio != ''">{{row.numNoMemberRatio}}%</span>
                </div>
              </div>
              <div v-else-if="item.prop == 'productInfo'">
                <div>ERP编码：{{ row.erpCode }}</div>
                <div>{{ row.manufacturer }}</div>
                <div>{{ row.productName }}</div>
                <div>{{ row.spec }} / {{row.unit}}</div>
              </div>
              <div v-else class="table_column" v-for="(its,index) in row.orgProductPopularizeDetailDTOS" :key="index">
                <div v-if="item.prop == its.roleId">
                  <div>会员推广费：<span v-if="its.memberRatio!=null && its.memberRatio != ''">{{its.memberRatio}}%</span>
                  </div>
                  <div style="margin-top:5px">非会员推广费：<span
                      v-if="its.noMemberRatio!=null && its.noMemberRatio != ''">{{its.noMemberRatio}}%</span>
                  </div>
                </div>
                <div v-if="item.prop == its.roleId">
                  <i class="el-icon-edit" style="color: blue;cursor:pointer;" @click="openEdit(item,row)"></i>
                </div>
              </div>
            </template>
          </el-table-column>
      </template>
    </el-table>
    <el-pagination style="margin-bottom: 16px;margin-top: 15px;" background :total="total"
      :current-page.sync="query.current" :page-size.sync="query.size" layout="->, prev, pager, next, sizes, jumper"
      @current-change="sizeChange()" @size-change="sizeChange()" />
    <dialogSetting ref="dialogSetting" @submitSetting="submitSetting" :currentRow="currentRow"></dialogSetting>
  </div>
</template>


<script>
  import {
    mapGetters
  } from 'vuex'
  import {
    findByBindProduct,
    updateProductRatio,
    removeBind
  } from '@/api/organization/index';
  import dialogSetting from '@/views/team/promote/components/dialogSetting'
  export default {
    //import引入的组件
    components: {
      dialogSetting
    },

    data() {
      return {
        getRowKeys(row) {
          return row.productId
        },
        query: {
          current: 1,
          map: {},
          model: {},
          order: "descending",
          size: 10,
          sort: "id"
        },
        total: 0,
        columnData: [], // 表头
        listData: [], // 数据
        otherColumn: [{
            label: '总推广费',
            prop: 'sumData',
            width: 190
          },
          {
            label: '操作时间',
            prop: 'updateTime',
            width: 160
          }
        ],
        currentRow: null,
        selectList: [],
      }
    },
    //生命周期 - 挂载完成（可以访问DOM元素）
    mounted() {},

    computed: {
      ...mapGetters([
        'organizationInfo'
      ]),
    },

    created() {
      this.initData()
    },

    filters: {},

    //方法集合
    methods: {
      async initData() {
        const loading = this.$loading({
          lock: true,
          text: '正在加载中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.4)'
        });
        this.columnData = [];
        this.query.model.organizationId = this.organizationInfo.id;
        let result = await findByBindProduct(this.query);
        loading.close();
        if (result.code != 0 && result.msg != 'ok') {
          return
        };
        this.listData = result.data.records;
        this.total = result.data.total;
        let headerList = [{
          label: '商家名称',
          prop: 'saleMerchantName',
          width: 180
        }, {
          label: '商品信息',
          prop: 'productInfo',
          width: 250
        }];
        if (result.data && result.data.records.length > 0) {
          result.data.records[0].orgProductPopularizeDetailDTOS.forEach(element => {
            headerList.push(
              Object.assign({}, {
                label: element.roleName,
                prop: element.roleId,
                width: 190
              })
            )
          });
          this.columnData = [...headerList, ...this.otherColumn];
        }
      },
      sizeChange() {
        this.initData();
      },
      handleSelectionChange(val) {
        console.log('val---->', val);
        this.selectList = val;
      },
      handleSelectAll(val) {
        console.log('val---->', val);
        this.selectList = val;
      },
      //   开启编辑
      openEdit(item, row) {
        console.log('item,row', item, row);
        let rowData = {};
        row.orgProductPopularizeDetailDTOS.forEach(element => {
          if (element.roleId == item.prop) {
            rowData = element;
          }
        })
        this.currentRow = {
          ...item,
          productId:row.productId,
          rowData
        };
        this.$refs.dialogSetting.openDia(this.currentRow);
      },
      submitSetting(form) {
        if (this.currentRow == null) {
          return
        }
        console.log('form---->', form);
        let params = {
          id:this.currentRow.rowData.id,
          ...form
        }
        updateProductRatio(params).then(res => {
          if (res.code == 0 && res.msg == 'ok') {
            this.$message.success('设置单品成功');
            this.initData();
            this.currentRow = null;
          }
        })
      },
      //   批量移除
      remove() {
        if (this.selectList.length < 1) {
          this.$message.warning('请选择需要移除的单品');
          return
        }
        this.$confirm('您确定要移除这些单品吗?', "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          let productIds = [];
          this.selectList.forEach(element => {
            productIds.push(element.productId);
          });
          let params = {
            organizationId: this.organizationInfo.id,
            productIds
          };
          removeBind(params).then(res => {
            if (res.code == 0 && res.msg == 'ok') {
              this.$message.success('批量移除成功');
              this.$refs.multipleTable.clearSelection();
              this.selectList = [];
              this.initData();
            }
          })
        })
      },
    },

  }

</script>


<style lang='scss' scoped>
  .table_column {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

</style>
