<template>
  <div>
    <im-search-pad
      :is-expand.sync="isExpand"
      :model="model"
      @reset="reload"
      @search="searchLoad"
    >
      <im-search-pad-item prop="rejectionNo">
        <el-input v-model="model.rejectionNo" placeholder="请输入退款单号" />
      </im-search-pad-item>
      <im-search-pad-item prop="code">
        <el-input v-model="model.code" placeholder="请输入客户编码" />
      </im-search-pad-item>
      <im-search-pad-item prop="purMerchantName">
        <el-input v-model="model.purMerchantName" placeholder="请输入客户名称" />
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="during">
        <el-date-picker
          type="daterange"
          range-separator="至"
          v-model="model.during"
          value-format="yyyy-MM-dd"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </im-search-pad-item>
    </im-search-pad>

    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        :tabs="tabs"
        @change="handleChangeTab"
      >
        <template slot="button">
          <!-- <el-button  v-if="checkPermission(['admin','refund:export'])">导出单据</el-button> -->
          <el-button  @click="reload">刷新</el-button>
        </template>
      </tabs-layout>
      <table-pager ref="todoTable" :options="tableTitle" :remote-method="load" :data.sync="tableData">
        <div slot-scope="props" style="width: 65px;">
          <el-button v-if="checkPermission(['admin','refund:detail'])" type="text" @click="$router.push({path: '/tradeCenter/refund/detail',query:{id: props.row.id}})">查看详情</el-button>
        </div>

      </table-pager>
    </div>
  </div>
</template>

<script>
  const TableColumns = [
    { label: "退款单号", name: "salesRefundNo",prop: "salesRefundNo",width:'180'},
    { label: "申请时间", name: "salesRefundCreateTime",prop: 'salesRefundCreateTime',width: '160' },
    { label: "退款类型", name: "salesRefundTypeEnum.desc", prop:'salesRefundTypeEnum.desc',width: '130'  },
    { label: "关联单号", name: "orderNo", prop:'orderNo',width: '180'},
    { label: "销售商家", name: "saleMerchantName",prop: 'saleMerchantName',width:'180'},
    { label: "客户编码", name: "code",prop: 'code',width:'150'},
    { label: "客户名称", name: "name",prop:'name',width: '150' },
    { label: "申请人", name: "applyUser",prop:'applyUser',width: '180' },
    { label: "退还金额", name: "totalRealMoney",prop: 'totalRealMoney',},
    { label: "退款状态", name: "refundStatusEnum.desc",prop:'refundStatusEnum.desc',  },
  ];
  const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({ key: i, ...TableColumns[i] });
  }
  import { salesRefundInfoList, refundStatistics } from '@/api/trade'
  import checkPermission from '@/utils/permission'
  import TabsLayout from '@/components/TabsLayout'
  export default {
    components: {
      TabsLayout
    },
    data () {
      return {
        isExpand: false,
        loading: '',
        search: '',
        controlType: '',
        currentTab: 0,
        tabs: [
          { name: '全部', value: 'ALL',count: 0,countName: 'all', hide: !checkPermission(['admin', 'refund-all:view']) },
          { name: '待退款', value: 'PENDING',count: 0,countName: 'pending', hide: !checkPermission(['admin', 'refund-pending:view'])},
          { name: '退款中', value: 'BEING_REFUND',count: 0,countName: 'being', hide: !checkPermission(['admin', 'refund-refunding:view']) },
          { name: '已完成', value: 'COMPLETE',count: 0,countName: 'finish', hide: !checkPermission(['admin', 'refund-finish:view']) },
          // { name: '已拒绝', value: 'REJECTED',count: 0,countName: 'refuse', hide: !checkPermission(['admin', 'refund-refuse:view']) },
        ],
        tableData: [],
        page: 1,
        pageSize: 10,
        totalPage: 0,
        total: 0,
        tableTitle: TableColumnList,
        model: {
          salesRefundAuditStatusEnum: '',
          purMerchantName: '',
          rejectionNo: '',
          during: '',
          code: ''
        },
      }
    },
    mounted() {
      // TODO 暂时无实际接口
      // this.getCount()
    },
    methods: {
      checkPermission,
      //tab数量
      async getCount() {
        const {data} = await refundStatistics()
        this.tabs.forEach(item => {
          item.count = data[item.countName]
        })
      },
      searchLoad() {
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      async load(params) {
        let listQuery = {
          model: {
            purMerchantName: this.model.purMerchantName,
            code: this.model.code,
            rejectionNo: this.model.rejectionNo,
            salesRefundAuditStatusEnum: this.model.salesRefundAuditStatusEnum,
            startTime: this.model.during[0],
            endTime: this.model.during[1],
          }
        }
        Object.assign(listQuery, params)
        this.loading = true
        return await salesRefundInfoList(listQuery)
        return data
      },
      handleChangeTab (tab) {
        if(tab.value === 'ALL') {
          this.model.salesRefundAuditStatusEnum = ''
        } else {
          this.model.salesRefundAuditStatusEnum = tab.value
        }
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      reload() {
        this.currentTab = 0
        this.model = {
          ...this.model,
          ...{
            purMerchantName: '',
            rejectionNo: '',
            code: '',
            during: ''
          }
        }
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      },
      handleRefresh(pageParams) {
        this.$refs.todoTable.doRefresh(pageParams)
      },
      handleSelectionChange(val) {
        this.ids = val.map(function(item,index) {
          return item.id;
        })

      },
      formatterMoney(s, n) {
        n = n > 0 && n <= 20 ? n : 2;
        s = parseFloat((s + "").replace(/[^\d\.-]/g, "")).toFixed(n) + "";
        let l = s.split(".")[0].split("").reverse(), r = s.split(".")[1];
        let t = "";
        for (let i = 0; i < l.length; i++) {
          t += l[i] + ((i + 1) % 3 == 0 && (i + 1) != l.length ? "," : "");
        }
        return t.split("").reverse().join("") + "." + r;
      }
    }
  }
</script>

<style lang="scss" scoped>
</style>
