<template>
  <div class="list-index">
    <!--搜索Form-->
    <im-search-pad :has-expand="false" :is-expand.sync="isExpand" :model="model" @reset="reload" @search="searchLoad">
      <im-search-pad-item prop="tagName">
        <el-input v-model="model.tagName" @keyup.enter.native="searchLoad" placeholder="请输入标签名称" />
      </im-search-pad-item>
    </im-search-pad>

    <div class="tab_bg">
      <!--Tabs布局-->
      <tabs-layout ref="tabs-layout" :tabs="[{ name: '客户标签' }]">
        <!--tabs右上角相关按钮-->
        <template slot="button">
          <el-button @click="reload">刷新</el-button>
          <el-button type="primary" @click="showDialogFun">+ 新增客户标签</el-button>
        </template>
      </tabs-layout>

      <!-- table -->
      <table-pager ref="pager-table" :options="tableColumns" :remote-method="load" :data.sync="tableData"
        :pageSize="pageSize" :operation-width="120">
        <template slot="exhibit">
          <el-table-column label="标签展示" width="150">
            <slot slot-scope="{row}">
              <div class="boxStyle disp" :style="{ backgroundColor: row.tagColor }">{{ row.tagName }}</div>
            </slot>
          </el-table-column>
        </template>
        <template slot="showStatus">
          <el-table-column label="前端是否显示" width="200">
            <slot slot-scope="{row}">
              <el-radio-group v-model="row.showStatus.code" @change="showStatusChange(row)">
                <el-radio :label="'Y'">显示</el-radio>
                <el-radio :label="'N'">隐藏</el-radio>
              </el-radio-group>
            </slot>
          </el-table-column>
        </template>
        
        <!--操作栏-->
        <div slot-scope="{ row }">
          <el-row class="table-edit-row">
            <span class="table-edit-row-item">
              <el-button type="text" @click="handleEdit(row.id)">编辑</el-button>
              <del-el-button style="margin-left:5px" :targetId="row.id" :text="delText" @handleDel="handleDel">
              </del-el-button>
            </span>
          </el-row>
        </div>
      </table-pager>
    </div>
    <el-dialog :title="diaText" :visible.sync="dialogVisible" width="30%">
      <el-form ref="productFrom" :model="productFrom" label-width="130px">
        <el-form-item label="标签名称：" prop="tagName" :rules="[
            { required: true, trigger: 'blur', message: '请选择标签名称' },
          ]">
          <el-input v-model="productFrom.tagName" style="width: 200px" :maxlength="5" placeholder="请输入标签名称,限五个字">
          </el-input>
        </el-form-item>
        <el-form-item label="标签颜色：" prop="tagColor" :rules="[
            { required: true, trigger: 'change', message: '请选择标签颜色' },
          ]">
          <div style="display: flex; align-items: center">
            <el-color-picker v-model="productFrom.tagColor"></el-color-picker>
            <div class="boxStyle" v-if="
                productFrom.tagName.length > 0 &&
                productFrom.tagColor != '#FFFFFF'
              " :style="{ backgroundColor: productFrom.tagColor }">
              {{ productFrom.tagName }}
            </div>
          </div>
        </el-form-item>
        <el-form-item class="formItem" prop="sort" label="排序:">
          <el-input-number clearable :min="1" v-model="productFrom.sort" placeholder="请填写排序"></el-input-number>
        </el-form-item>
        <el-form-item class="formItem" prop="showStatus" label="前端是否显示:" :rules="[
            { required: true, message: '请填写前端是否显示', trigger: 'change' },
          ]">
          <el-radio v-model="productFrom.showStatus" :label="'Y'">显示</el-radio>
          <el-radio v-model="productFrom.showStatus" :label="'N'">隐藏</el-radio>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>


<script>
  const TableColumns = [{
      label: "标签编码",
      name: "tagCode",
      prop: "tagCode",
      width: "120",
    },
    {
      label: "标签名称",
      name: "tagName",
      prop: "tagName",
      width: "150",
    },
    {
      label: "分组颜色",
      name: "tagColor",
      prop: "tagColor",
      width: "150",
    },
    {
      label: "分组展示",
      name: "exhibit",
      prop: "exhibit",
      width: "150",
      slot: true
    },
    {
      label: "排序",
      name: "sort",
      prop: "sort",
      width: "100",
    },
    {
      label: "前端是否展示",
      name: "showStatus",
      prop: "showStatus",
      width: "200",
      slot: true
    }
  ];
  const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({
      key: i,
      ...TableColumns[i],
    });
  }

  import delElButton from "@/components/eyaolink/delElButton";
  import { pageCustTag, detailCustTag, delCustTag, addCustTag, editCustTag } from "@/api/purchasingAgent/customerTag/index"
  export default {
    //import引入的组件
    components: {
      delElButton
    },

    data() {
      return {
        isExpand: false,
        model: {
          tagName: "",
        },
        diaText: '新增客户标签',
        productFrom: {
          tagName: "",
          tagColor: "#00CB25",
          sort: 1,
          showStatus: 'Y',
        },
        currentId:'',
        delText: '您确定删除该客户标签吗？',
        dialogVisible: false,
        productStatus: false,
        tableData: [],
        pageSize: 10,
        tableColumns: TableColumnList,
      };
    },
    watch: {
      dialogVisible(val) {
        if (!val) {
          this.productFrom = {
            tagName: "",
            tagColor: "#00CB25",
            sort: 1,
            showStatus: "Y",
          };
        }
      }
    },
    //生命周期 - 挂载完成（可以访问DOM元素）
    mounted() {},

    computed: {},

    created() {},

    filters: {},

    //方法集合
    methods: {
      async load(params) {
        let listQuery = {
          model: this.model,
        };
        Object.assign(listQuery, params);
        return await pageCustTag(listQuery);
      },
      // 表格中前端是否展示的radio的改变事件
      showStatusChange(val) {
        let params = {
          tagName: val.tagName,
          tagColor: val.tagColor,
          sort: val.sort,
          showStatus: val.showStatus,
          id: val.id
        };
        editCustTag(params).then(res => {
          if (res.code == 0 && res.msg == 'ok') {
            this.$message.success('修改显示状态成功');
            this.reload();
          } else {
            this.reload();
          }
        })
      },
      //    编辑
      handleEdit(id) {
        detailCustTag(id).then(res => {
          if (res.code == 0 && res.msg == 'ok') {
            this.dialogVisible = true;
            this.diaText = "编辑客户标签";
            this.productFrom = {
              tagName: res.data.tagName,
              id: res.data.id,
              showStatus: res.data.showStatus.code,
              tagColor: res.data.tagColor,
              sort: res.data.sort,
            }
          }
        })
      },
      // 删除
      handleDel(id) {
        delCustTag(id).then(res => {
          if (res.code == 0 && res.msg == "ok") {
            this.$message.success('删除成功');
            this.reload();
          }
        })
      },
      // 新增客户标签
      showDialogFun() {
        this.diaText = "新增客户标签";
        this.dialogVisible = true;
      },
      reload() {
        this.$refs['tabs-layout'].reset()
        this.handleRefresh({
          page: 1,
          pageSize: this.pageSize
        })
      },
      searchLoad() {
        this.handleRefresh({
          page: 1,
          pageSize: this.pageSize
        })
      },
      handleRefresh(pageParams) {
        this.$refs['pager-table'].doRefresh(pageParams)
      },
      closeProduct(){
        this.productStatus = false;
        this.reload();
      },
      submit() {
        this.$refs["productFrom"].validate((valid) => {
          if (valid) {
            // 验证成功
            let params = {
              ...this.productFrom
            }
            if (params.id && this.diaText == "编辑客户标签") {
              // 编辑
              editCustTag(params).then(res => {
                if (res.code == 0 && res.msg == 'ok') {
                  this.$message.success('修改客户标签成功');
                  this.dialogVisible = false;
                  this.reload();
                }
              })
            } else {
              // 新增
              addCustTag(params).then(res => {
                if (res.code == 0 && res.msg == 'ok') {
                  this.$message.success("新增客户标签成功");
                  this.dialogVisible = false;
                  this.reload();
                }
              })
            }
          } else {
            // 验证失败
          }
        });
      },
    },
  };

</script>


<style lang='scss' scoped>
  .boxStyle {
    margin-left: 10px;
    padding: 0 10px;
    border-radius: 5px;
    color: #fff;
  }

  .disp {
    display: inline;
    padding: 5px 10px;
  }

</style>
