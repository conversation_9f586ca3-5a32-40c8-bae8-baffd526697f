<template>
  <div class="archivesPageContent">
    <im-search-pad
      :is-expand.sync="isExpand"
      :model="listQuery"
      @reset="resetForm('searchForm')"
      @search="onSearchSubmitFun"
    >
      <im-search-pad-item prop="productCode">
        <el-input v-model="listQuery.model.productCode" placeholder="请输入商品编码" />
      </im-search-pad-item>
      <im-search-pad-item prop="productName">
        <el-input v-model="listQuery.model.productName" placeholder="请输入商品名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="manufacturer">
        <el-input v-model="listQuery.model.manufacturer" placeholder="请输入生产厂家" />
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="merchantName">
        <el-input v-model="listQuery.model.merchantName" placeholder="请输入所属商家" />
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        :tabs="[{ name: '推广商品' }]"
        @change="chageTabsFun"
      >
        <template slot="button">
          <el-button icon="el-icon-refresh" @click="getlist">刷新</el-button>
        </template>
      </tabs-layout>
      <div class="table">
        <el-table
          ref="table"
          v-if="list"
          @selection-change="selectTableItemFun"
          v-loading="listLoading"
          :data="list"
          row-key="id"
          border
          fit
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column
            align="center"
            width="80"
            :render-header="renderHeader"
            fixed
          >
            <template slot-scope="scope">
              <span>{{ scope.$index + 1 }} </span>
            </template>
          </el-table-column>
          <!-- <el-table-column type="selection" align="center" width="55"></el-table-column> -->
          <el-table-column label="主图" prop="pictIdS" class-name="img-cell">
            <template slot-scope="scope">
              <!-- <img v-if="!scope.row.pictIdS && scope.row.pictIdS!='' && scope.row.pictIdS!='null' && scope.row.pictIdS!=null" :src="scope.row.pictIdS.split(',')[0]" class="productImg">
              <img v-else src="@/assets/product.png" class="productImg"> -->
              <img :src="scope.row.pictIdS | imgFilter" class="productImg">
            </template>
          </el-table-column>
          <el-table-column
            v-for="(item, index) in tableTitle"
            :key="index"
            :min-width="item.width ? item.width : '350px'"
            :label="item.label"
            show-overflow-tooltip
            align="left"
          >
            <template slot-scope="{ row }">
            <span v-if="item.name == 'publishStatus'">{{
               row[item.name] && row[item.name].code == "PUT_ON_SALE" ? "上架中" : "已下架"
              }}</span>
              <el-button
                v-else-if="item.name == 'area'"
                type="text"

                @click="showAreaFun(row)"
              >查看区域</el-button
              >
              <el-button
                v-else-if="item.name == 'salesmanNum'"
                type="text"

                :disabled="row[item.name] == 0"
                @click="showGoodsTable(row)"
              >{{ row[item.name] }}</el-button
              >
              <span
                v-else-if="item.name == 'promotionExpenses'"
                style="color: #ff6e1b"
              >{{ row[item.name] }}</span
              >
              <span v-else>{{ row[item.name] }}</span>
            </template>
          </el-table-column>

          <!-- <el-table-column v-if="listQuery.model.publishStatusEnum == 'PENDING'" fixed="right" align="center" label="操作" width="150" class="itemAction">
            <template slot-scope="row">
              <el-button type="text" size="mini" @click="accepteFun(row)">同意</el-button>
              <el-button type="text" size="mini" @click="rejectFun(row)">驳回</el-button>
            </template>
          </el-table-column>

          <el-table-column v-else fixed="right" align="center" label="操作" width="150" class="itemAction">
            <template slot-scope="row">
              <el-button @click="detailFun(row)" type="text" >查看详情</el-button>
            </template>
          </el-table-column> -->
        </el-table>
        <pagination
          v-if="total > 0"
          :pageSizes="[2, 10, 20, 50]"
          :total="total"
          :page.sync="listQuery.current"
          :limit.sync="listQuery.size"
          @pagination="getlist"
        />
      </div>
    </div>
    <!-- <el-dialog title="审核代理品种上架" :visible.sync="dialogVisible" width="25%" :close-on-click-modal="false" :append-to-body="true">
      <el-input v-if="!isReject" type="textarea" placeholder="请输入审批意见" :autosize="{ minRows: 6, maxRows: 7}"></el-input>
      <el-input v-else type="textarea" placeholder="请输入驳回理由" :autosize="{ minRows: 6, maxRows: 7}"></el-input>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button v-if="!isReject" type="primary" @click="dialogVisible = false">确 定</el-button>
        <el-button v-else type="primary" @click="dialogVisible = false">驳 回</el-button>
      </span>
    </el-dialog> -->
    <el-dialog
      v-if="showAreaFlag"
      :visible.sync="showAreaFlag"
      width="80%"
      :close-on-click-modal="false"
      :append-to-body="true"
    >
      <div slot="title">
        <span>可代理销售区域</span>
        <el-button
          disabled

          plain
          type="primary"
          style="margin-left: 10px"
          >{{ row.productName }}</el-button
        >
      </div>
      <areass :visible.sync="showAreaFlag" :row.sync="row"></areass>
    </el-dialog>
    <el-dialog
      v-if="showSalesManFlag"
      :visible.sync="showSalesManFlag"
      width="80%"
      :close-on-click-modal="false"
      :append-to-body="true"
    >
      <div slot="title">
        <span>业务员列表({{ salesMalNum }})</span>
      </div>
      <!-- :isReload.sync="submitReload" -->

      <salesManList
        :visible.sync="showSalesManFlag"
        :row.sync="row"
        :salesMalNum.sync="salesMalNum"
        :areaTree="areaTree"
      ></salesManList>
    </el-dialog>
  </div>
</template>

<script>
import {areas} from "@/api/businessCenter/businessList";
import salesManList from "./salesManList";
import checkPermission from "@/utils/permission";
import { setContextData, getContextData } from "@/utils/auth";
import tableInfo from "./tableInfo";
import Pagination from "@/components/Pagination";
import { getList } from "@/api/businessCentric/promotionGoods";
import areass from "@/views/businessCentric/promotionGoods/areas";
import TabsLayout from '@/components/TabsLayout'
export default {
  data() {
    return {
      isExpand: false,
      listLoading: false,
      listQuery: {
        current: 1,
        size: 10,
        model: {
          publishStatusEnum: "HAVE",
        },
      },
      list: [],
      total: 1,
      tableSelectTitle: [0, 1, 2, 3, 4],
      showSelectTitle: false,
      tableTitle: [],
      dialogVisible: false,
      isReject: false,
      tabType: "list",
      showAreaFlag: false,
      row: {},
      showSalesManFlag: false,
      salesMalNum: 0,
      areaTree: []
    };
  },
  methods: {
    async getareas() {
      let { data } = await areas();
      this.areaTree = data;
    },
    showGoodsTable(row) {
      this.showSalesManFlag = true;
      this.row = row;
    },
    resetForm() {
      this.list = [];
      this.listQuery = {
        current: 1,
        size: 10,
        model: {
          publishStatusEnum: this.listQuery.model.publishStatusEnum,
        },
      };
      this.getlist();
    },
    showAreaFun(row) {
      this.row = row;
      this.showAreaFlag = true;
    },
    splitString(val) {
      if (!val) {
        return "";
      }
      return val.split(",");
    },
    checkPermission,
    rejectFun() {
      this.dialogVisible = true;
      this.isReject = true;
    },
    accepteFun() {
      this.dialogVisible = true;
      this.isReject = false;
    },
    detailFun(row) {
      setContextData("");
      this.$router.push({
        path: "/businessCentric/promotionGoodsdetail",
      });
    },
    selectTableItemFun() {},
    closePop() {},
    async getlist() {
      this.listLoading = true;
      let { data } = await getList(this.listQuery);
      this.list = data.records;
      this.total = data.total;
      this.listLoading = false;
    },
    chageTabsFun(e) {
      this.list = [];
      this.initTbaleTitle();
      this.resetListQuery();
      this.getlist();
    },
    onSearchSubmitFun() {
      this.list = [];
      this.getlist();
    },
    initTbaleTitle() {
      this.tableTitle = tableInfo["list"];
      this.tableSelectTitle = [];
    },
    resetListQuery() {
      this.listQuery = {
        current: 1,
        size: 10,
        model: {
          acceptedCount: 0,
          approvalStatus: {
            code: "list",
          },
        },
      };
      this.total = 0;
    },
    renderHeader(h, { column }) {
      var titles = tableInfo["list"];
      var titlesName = ["显示字段项", "隐藏字段项"];
      return (
        <div style="position:relative">
          <div onClick={this.showHeaer}>
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="640px"
            center
            append-to-body={true}
          >
            <el-transfer
              vModel={this.tableSelectTitle}
              data={titles}
              onChange={this.setleftTitleFun}
              titles={titlesName}
            ></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button onClick={this.closeHeaer}>取消</el-button>
              <el-button type="primary" onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      );
    },
    setleftTitleFun(val) {
      this.tableSelectTitle = val;
    },
    showHeaer: function () {
      this.showSelectTitle = true;
    },
    closeHeaer: function () {
      this.showSelectTitle = false;
      this.tableSelectTitle = [];
    },
    setHeaer: function () {
      var titles = tableInfo["list"];
      var listinfo = titles.filter((element, index, self) => {
        return !this.tableSelectTitle.includes(element.key);
      });
      this.tableTitle = listinfo;
      this.showSelectTitle = !this.showSelectTitle;
    },
  },
  components: {
    Pagination,
    areass,
    salesManList,
    TabsLayout
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (
        from.path == "/businessCentric/salesmanListeditItem" ||
        from.path == "/businessCentric/salesmanListeditItem"
      ) {
        if (getContextData("businessCentric_salesmanList") != "") {
          vm.listQuery = getContextData("businessCentric_salesmanList");
        }
      }
      vm.initTbaleTitle();
      // vm.getSaleMerhcantCount();
      vm.getlist();
    });
  },
  created() {
    this.initTbaleTitle();
    this.getareas()
  },
};
</script>


<style lang="scss" scoped>
.archivesPageContent {
  padding: 0;
  .temp_searchBox {
    height: 64px;
    overflow: hidden;
    margin-bottom: 0;
  }
  .form-inline {
    height: 60px;
    overflow: hidden;
  }
  .title {
    border-bottom: 2px solid #ebecee;
    margin-bottom: 16px;
    span {
      margin-bottom: -2px;
      padding: 0 15px;
      height: 40px;
      line-height: 30px;
      display: block;
      background: rgba(255, 255, 255, 0);
      border-bottom: 2px solid rgb(64, 158, 255);
      font-size: 16px;
      font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
        "PingFang SC"-400;
      font-weight: 400;
      color: rgb(64, 158, 255);
    }
  }
  .formItem {
    width: 586px;
  }
  .line {
    color: #dfe6ec;
    margin: 0 6px;
  }
  .typeTabs {
    height: 40px;
    margin-bottom: -2px;
  }
}
</style>
