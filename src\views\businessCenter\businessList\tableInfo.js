export default {
    ACCEPTED: [
        {   key: -1,   
            label: '商家类型',
            name: "commerceModel",
            width:'100px',
            disabled: true
        },
        {   key: 0,   
            label: '商家状态',
            name: "publishStatus",
            width:'80px',
            disabled: true
        },
        {   key: 1,
            label: '商家ID',
            name: "id",
            width:'170px',
            hidden:true
        },
        {   key: 2,
            label: '商家编码',
            name: "code",
            width:'140px',
            disabled: true
        },
        {   key:3,
            label: '店铺名称',
            name: "shopName",
            width:'200px',
            disabled: true
        },
        {   key: 4,
            label: '商家名称',
            name: "name",
            width:'200px',
            disabled: true
        },
        {   key: 5,
            label: '社会统一信用代码',
            name: "socialCreditCode",
            width:'200px'
        },
        {   key: 6,
            label: '法人代表',
            name: "legalPerson",
            width:'80px'
        },
        {   key: 7,
            label: '负责人',
            name: "ceoN<PERSON>",
            width:'80px'
        },
        {   key: 8,
            label: '负责人手机',
            name: "ceoMobile",
            width: "112px"
        },
        {   key: 9,
            label: '所在地区',
            name: "region",
            width:'168px'
        },
        {   key: 10,
            label: '注册地址',
            name: "registerAddress",
            width:'320px'
        },
        {   key: 11,
            label: '审批人',
            name: "approvalUserName",
            width:'120px'
        },
        {   key: 12,
            label: '审批时间',
            name: "approvalDate",
            width:'170px'
        }
    ],
    PENDING: [
        {   key: -1,   
            label: '商家类型',
            name: "commerceModel",
            width:'100px',
            disabled: true
        },
        {   key: 0,   
            label: '商家状态',
            name: "publishStatus",
            width:'80px',
            disabled: true
        },
        {   key: 1,
            label: '商家ID',
            name: "id",
            width:'170px',
            hidden:true
        },
        {   key: 2,
            label: '商家编码',
            name: "code",
            width:'140px',
            disabled: true
        },
        {   key: 3,
            label: '店铺名称',
            name: "shopName",
            width:'200px',
            disabled: true
        },
        {   key: 4,
            label: '商家名称',
            name: "name",
            width:'200px',
            disabled: true
        },
        {   key: 5,
            label: '社会统一信用代码',
            name: "socialCreditCode",
            width:'200px',
            disabled: true
        },
        {   key: 6,
            label: '负责人',
            name: "ceoName",
            width:'80px'
        },
        {   key: 7,
            label: '负责人手机',
            name: "ceoMobile",
            width: "112px"
        },
        {   key: 8,
            label: '所在地区',
            name: "region",
            width:'168px'
        },
        {   key: 9,
            label: '注册地址',
            name: "registerAddress",
            width:'417px'
        }
    ],
    REJECTED: [
        {   key: -1,   
            label: '商家类型',
            name: "commerceModel",
            width:'100px',
            disabled: true
        },
        {   key: 0,   
            label: '商家状态',
            name: "publishStatus",
            width:'80px',
            disabled: true
        },
        {   key: 1,
            label: '商家ID',
            name: "id",
            width:'170px',
            hidden:true
        },
        {   key: 2,
            label: '商家编码',
            name: "code",
            width:'140px',
            disabled: true
        },
        {   key: 3,
            label: '店铺名称',
            name: "shopName",
            width:'200px',
            disabled: true
        },
        {   key: 4,
            label: '商家名称',
            name: "name",
            width:'200px',
            disabled: true
        },
        {   key: 5,
            label: '社会统一信用代码',
            name: "socialCreditCode",
            width:'200px',
            disabled: true
        },
        {   key: 6,
            label: '负责人',
            name: "ceoName",
            width:'80px'
        },
        {   key: 7,
            label: '负责人手机',
            name: "ceoMobile",
            width: "112px"
        },
        {   key: 8,
            label: '所在地区',
            name: "region",
            width:'168px'
        },
        {   key: 9,
            label: '注册地址',
            name: "registerAddress",
            width:'320px'
        },
        {   key: 10,
            label: '审批人',
            name: "approvalUserName",
            width:'120px'
        },
        {   key: 11,
            label: '审批原因',
            name: "rejectReason",
            width:'160px'
        },
        {   key: 12,
            label: '审批时间',
            name: "approvalDate",
            width:'170px'
        }
    ],
    stale: [
        {   key: -1,   
            label: '商家类型',
            name: "commerceModel",
            width:'100px',
            disabled: true
        },
        {   key: 0,
            label: '商家状态',
            name: "publishStatus",
            width:'80px',
            disabled: true
        },
        {   key: 1,
            label: '商家ID',
            name: "id",
            width:'170px',
            hidden:true
        },
        {   key: 2,
            label: '商家编码',
            name: "code",
            width:'140px',
            disabled: true
        },
        {   key: 3,
            label: '店铺名称',
            name: "shopName",
            width:'200px',
            disabled: true
        },
        {   key: 4,
            label: '商家名称',
            name: "name",
            width:'200px',
            disabled: true
        },
        {   key: 5,
            label: '社会统一信用代码',
            name: "socialCreditCode",
            width:'200px',
            disabled: true
        },
        {   key: 6,
            label: '负责人',
            name: "ceoName",
            width:'80px'
        },
        {   key: 7,
            label: '负责人手机',
            name: "ceoMobile",
            width: "112px"
        },
        {   key: 8,
            label: '所在地区',
            name: "region",
            width:'168px'
        },
        {   key: 9,
            label: '注册地址',
            name: "registerAddress",
            width:'320px'
        },
        {   key: 10,
            label: '审批人',
            name: "approvalUserName",
            width:'120px'
        },
        {   key: 12,
            label: '审批时间',
            name: "approvalDate",
            width:'170px'
        },
        {   key: 11,
            label: '过期时间',
            name: "managementEndDate",
            width:'170px'
        }
    ]
}