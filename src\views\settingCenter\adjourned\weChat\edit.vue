<template>
  <div class="WeChatPageContent">
    <div class="title flex_between_center">
      <span>微信公众号配置</span>
      <el-button type="primary">保存</el-button>
    </div>
    <el-form class="form" ref="form" :model="form" label-width="146px">
      <el-form-item required  class="formItem" label="Appld:">
        <el-input  v-model="form.appld"></el-input>
      </el-form-item>
      <el-form-item required  class="formItem" label="AppSecret:">
        <el-input  v-model="form.appSecret"></el-input>
      </el-form-item>
      <el-form-item required  class="formItem" label="消息验证Token:">
        <el-input  v-model="form.token"></el-input>
      </el-form-item>
      <el-form-item required  class="formItem" label="消息加密Key:">
        <el-input  v-model="form.key"></el-input>
      </el-form-item> 
    </el-form>
  </div>
</template>
<script>
export default {
  data() {
    return {
      form: {
        payName: "银联支付",
        detail: "银联支付",
        start: true,
        APPID: "",
        AppSecret: "",
        merchantNumber: "",
        merchantKey: ""
      }
    };
  },

  methods: {},
  mounted() {},
  beforeDestroy() {}
};
</script>
<style lang="scss" scoped>
@import "@/styles/element-variables.scss";

.WeChatPageContent {
  padding: 15px;
  .title{
       border-bottom:2px solid #EBECEE;
       margin-bottom:35px;
      span{
        color:rgb(64, 158, 255);
        margin-bottom: -2px;
        padding:0 15px;
        height: 40px;
        line-height: 30px;
        display:block;
        background: rgba(255,255,255,0);
        border-bottom:2px solid rgb(64, 158, 255);
        font-size: 16px;
        font-family: 'PingFangSC-Regular', 'PingFang SC', 'PingFangSC-Regular', 'PingFang SC'-400;
        font-weight: 400;
      }
  } 
  .formItem{width:586px;}
}
</style>
