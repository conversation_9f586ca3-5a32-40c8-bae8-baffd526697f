<template>
  <div class="systemPageContent">
    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        :tabs="[ { name: '采购商导入' } ]"
      >
        <template slot="button">
          <div>
            <el-button
              v-if="checkPermission(['admin', 'admin-platform-pur-merchant:import'])"
              type="primary"
              icon="el-icon-plus"
              @click="showImportFun"
            >新建导入</el-button
            >
            <el-button

              icon="el-icon-refresh"
              @click="onSearchSubmitFun"
            >刷新</el-button
            >
          </div>
        </template>
      </tabs-layout>
      <div class="table">
        <el-table
          ref="tableDom"
          v-loading="listLoading"
          :data="list"
          row-key="id"
          border
          fit
        >
          <el-table-column
            align="center"
            width="65"
            show-overflow-tooltip
            :render-header="renderHeader"
            fixed
          >
            <template slot-scope="scope">
              <span>{{ scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :min-width="'180px'"
            prop="createTime"
            label="导入时间"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            width="180px"
            show-overflow-tooltip
            label="导入状态"
            column-key="uploadStatus"
          >
            <template slot-scope="{ row }">
              <el-tag v-if="row['uploadStatus'] == '1'" type="success">成功</el-tag>
              <el-tag v-if="row['uploadStatus'] == '2'" type="danger">失败</el-tag>
              <el-tag v-if="row['uploadStatus'] == '0'">处理中</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="createUser"
            width="250"
            show-overflow-tooltip
            label="操作人"
          ></el-table-column>
          <el-table-column
            fixed="right"
            align="center"
            label="操作"
            width="200"
            class="itemAction"
          >
            <template slot-scope="scope">
              <el-row class="table-edit-row">
                <span v-if="scope.row['uploadStatus'].code !== 'PROCESSING' && checkPermission(['admin', 'admin-platform-pur-merchant:download'])" class="table-edit-row-item">
                   <el-button @click="downloadFun(scope.row)" type="text" >下载失败结果</el-button>
                </span>
                <span v-if="scope.row['uploadStatus'].code !== 'PROCESSING' && checkPermission(['admin', 'admin-platform-pur-merchant:delete'])" class="table-edit-row-item">
                   <el-button @click="deleteFun(scope.row)" type="text" >删除</el-button>
                </span>
              </el-row>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="listQuery.current"
          :limit.sync="listQuery.size"
          @pagination="getList"
        />
      </div>
    </div>
    <el-dialog
      title="导入采购商信息"
      :visible.sync="showImport"
      width="428px"
      :append-to-body="true"
      :before-close="closeDialogFun"
    >
      <div class="uploadContent">
        <el-upload
          ref="upload"
          class="avatar-uploader"
          drag
          :action="'/api/merchant/admin/purMerchant/import'"
          :headers="headersProgram"
          :on-change="beforeUpload"
          :file-list="fileList"
          :on-success="uploadSuccess"
          :show-file-list="true"
          :auto-upload="false"
          :multiple="false"
        >
          <!-- :limit="1" -->
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖拽至此区域或选择文件上传</div>
          <div class="el-upload__tip" slot="tip">
            当前仅支持xlsx格式文件，文件大小1M以内且在5000条
            数据以内，若超出限制，请分批导入
            <el-link
              type="primary"
              href="https://eyaolink-dev-bucket.oss-cn-shenzhen.aliyuncs.com/0/2021/04/36e1d645-56b6-4e9a-a229-fbcc8d7073d1.xlsx"
              target="_blank"
              download="@/view/purchasingAgent/xlsxTemp/demo.xlsx"
              >下载模板</el-link
            >
          </div>
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showImport = false">取 消</el-button>
        <el-button type="primary" @click="submitUpload">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { getToken } from "@/utils/auth";
import checkPermission from "@/utils/permission";
import {
  list,
  exportProductLog,
  importProductDelete,
} from "@/api/purchasingAgent/importList";
import Pagination from "@/components/Pagination";
import TabsLayout from '@/components/TabsLayout'
export default {
  name: 'importArchives',
  data() {
    return {
      headersProgram: {
        token: getToken(),
        Authorization: "Basic YWRtaW5fdWk6YWRtaW5fdWlfc2VjcmV0",
      },
      fileList: [],
      showEdit: false,
      showImport: false,
      showItemTable: false,
      row: {},
      submitReload: false,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        current: 1,
        map: {},
        model: {},
        size: 10,
      },
    };
  },
  watch: {
    submitReload: function (newVal, oldVal) {
      if (newVal) {
        this.submitReload = false;
        this.getList();
      }
    },
  },
  components: {
    Pagination,
    TabsLayout
  },
  methods: {
    checkPermission,
    closeDialogFun() {
      this.showImport = false;
    },
    renderHeader(h, { column }) {
      return (
        <div style="position:relative">
          <div>
            <i class="el-icon-menu" />
          </div>
        </div>
      );
    },
    onSearchSubmitFun() {
      this.listQuery.size = 10;
      this.getList();
    },
    async getList() {
      this.listLoading = true;
      const { data } = await list(this.listQuery);
      this.list = data.records;
      this.total = data.total;
      this.listLoading = false;
    },
    showImportFun() {
      this.showImport = true;
    },
    editFun(row) {
      this.row = row;
      this.showEdit = true;
    },
    initQuery() {
      this.listQuery = {
        size: 10,
        model: {},
        current: 1,
      };
    },
    async actionDeleteFun(row) {
      let data = await importProductDelete({ids:[row.id]});
      if (data.code == 0) {
        this.initQuery();
        this.getList();
      }
    },
    deleteFun(row) {
      var _this = this;
      this.$confirm("此操作将永久删除该信息, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          _this.actionDeleteFun(row);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    beforeUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < 5;
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 2MB!");
      }
      var uploadFiles = this.$refs.upload.uploadFiles;
      if (uploadFiles.length > 1) {
        uploadFiles.splice(0, 1);
      }
      return isLt2M;
    },
    uploadSuccess(res, file) {
      if (res.code == 0) {
        this.fileList = [];
        this.showImport = false;
        this.initQuery();
        this.getList();
      } else {
        this.$message.error("上传失败!");
      }
    },
    submitUpload() {
      this.$refs.upload.submit();
    },
    downloadFun: async function (row) {
    let data = await  exportProductLog({
        current: 1,
        map: {},
        model: {
          purMerchantUploadId: row.id,
        },
        order: "descending",
        size: 10,
        sort: "id",
      })
      const blob = new Blob([data.data], {
        //取响应回来的数据
        // type: "application/vnd.ms-excel;",  xls
        type:
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;",
      });

      const href = window.URL.createObjectURL(blob); // 创建下载的链接
      const downloadElement = document.createElement("a");
      downloadElement.href = href;
       downloadElement.download = decodeURI(data.headers["filename"]);
      document.body.appendChild(downloadElement);
      downloadElement.click(); // 点击下载
      document.body.removeChild(downloadElement); // 下载完成移除元素
      window.URL.revokeObjectURL(href);
    },
  },
  created() {
    this.getList();
  },
};
</script>
<style lang="scss" scoped>
@import "@/styles/element-variables.scss";
.temp_searchBox {
  height: 64px;
  overflow: hidden;
  margin-bottom: 0;
}
.form-inline {
  height: 60px;
  overflow: hidden;
}
.systemPageContent {
  padding: 0;
  // padding: 15px;
  .title {
    // border-bottom: 2px solid #ebecee;
     margin-bottom:16px;
    span {
      margin-bottom: -2px;
      padding: 0 15px;
      height: 40px;
      line-height: 30px;
      display: block;
      background: rgba(255, 255, 255, 0);
      border-bottom: 2px solid rgb(64, 158, 255);
      font-size: 16px;
      font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
        "PingFang SC"-400;
      font-weight: 400;
      color: rgb(64, 158, 255);
    }
  }
  .formItem {
    width: 586px;
  }
  .line {
    color: #dfe6ec;
    margin: 0 6px;
  }
  .el-dropdown-link {
    margin-left: 12px;
    cursor: pointer;
    font-size: 12px;
    color: #0056e5;
  }
  .el-icon-arrow-down {
    font-size: 12px;
  }
}
</style>


<style lang="less" scoped>
/deep/ .uploadContent .avatar-uploader .el-upload-dragger {
  // width:100%;
  // height:180px;
  width:392px;
}
/deep/ .uploadContent .avatar-uploader .el-upload__tip {
  width: 360px;
  margin-top: 10px;
  line-height: 20px;
}
</style>
