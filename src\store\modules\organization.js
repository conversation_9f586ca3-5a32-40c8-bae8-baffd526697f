import { setContextData,getContextData } from '@/utils/auth'


const state = {
    organizationNavNode: getContextData("organizationNavNode")||null,
    organizationInfo:getContextData("organizationInfo")||null,
    paremDepartmentInfo: getContextData("paremDepartmentInfo")||null,
    departmentInfo: getContextData("departmentInfo")||null
}

const mutations = {
    // 选中组织全节点
  SET_ORGANIZATION_NAV_NODE: (state, organizationNavNode) => {
    state.organizationNavNode = organizationNavNode
    setContextData("organizationNavNode",organizationNavNode)
  },
    //当前组织
  SET_ORGANIZATIONINFO: (state, organizationInfo) => {
    state.organizationInfo = organizationInfo
    setContextData("organizationInfo",organizationInfo)
  },
    //上级部门
  SET_PAREM_DEPARTMENTINFO: (state, paremDepartmentInfo) => {
    state.paremDepartmentInfo = paremDepartmentInfo
    setContextData("paremDepartmentInfo",paremDepartmentInfo)
  },
    //当前部门
  SET_DEPARTMENTINFO: (state, departmentInfo) => {
    state.departmentInfo = departmentInfo
    setContextData("departmentInfo",departmentInfo)
  }
}

const actions = {
    setOrganizationNavNode({ commit },organizationNavNode ) {
        return new Promise((resolve, reject) => {
            commit('SET_ORGANIZATION_NAV_NODE',organizationNavNode)
            if(organizationNavNode&& organizationNavNode.length>0){
                // 设置当前组织
                commit('SET_ORGANIZATIONINFO', organizationNavNode[0]) 
                if(organizationNavNode.length==2){
                    console.info(organizationNavNode.length)
                    commit('SET_PAREM_DEPARTMENTINFO',  organizationNavNode[0]) 
                    commit('SET_DEPARTMENTINFO', organizationNavNode[1])
                }else if(organizationNavNode.length>=3){
                    console.info(organizationNavNode[organizationNavNode.length-2])
                    commit('SET_PAREM_DEPARTMENTINFO', organizationNavNode[organizationNavNode.length-2]) 
                    commit('SET_DEPARTMENTINFO', organizationNavNode[organizationNavNode.length-1])
                }else{
                    commit('SET_PAREM_DEPARTMENTINFO', null) 
                    commit('SET_DEPARTMENTINFO',null)
                }
            }else{
                commit('SET_ORGANIZATIONINFO', null)   
                commit('SET_PAREM_DEPARTMENTINFO', null)   
                commit('SET_DEPARTMENTINFO', null)   
            }
            resolve()
        }).catch(error => {
            reject(error)
        })
  },
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
