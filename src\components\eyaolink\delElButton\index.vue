<template>
   <div class="delButton">
       <el-button :size="sizeTyep" :type="showType" @click="delButton">{{btnTxt}}</el-button>
   </div>
</template>


<script>

export default {
   //import引入的组件
   components: {},

   props:{
       sizeTyep:{
           type:String,
           default:"small"
       },
       showType:{
           type:String,
           default:"text"
       },
       /**
        * @description  删除目标记录ID
        * @param {String} targetId
        */
       targetId:{
           type:String,
           default:""
       },
       /**
        * @description  按钮文案
        * @param {String} btnTxt
        */
       btnTxt:{
           type:String,
           default:"删除"
       },
       /**
        * @description  删除确认信息
        * @param {String} text
        */
       text:{
           type:String,
           default:""
       }
   },

   data() {
      return {}
   },
   //生命周期 - 挂载完成（可以访问DOM元素）
   mounted() {},

   computed: {},

   created() {},

   filters: {},

   //方法集合
   methods: {
       delButton(){
           this.$confirm(this.text, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async ()=>{
                this.$emit("handleDel",this.targetId)
            })
       },
   },

}
</script>


<style lang='scss' scoped>
.delButton {
    display: inline-block;
}
</style>