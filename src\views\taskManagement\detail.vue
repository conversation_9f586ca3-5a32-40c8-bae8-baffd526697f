<template>
  <div class="taskDetail">
    <div class="taskDetail_Header">
      <span class="title">任务达成明细</span>
      <el-button @click="toBack">返回</el-button>
    </div>
    <div class="taskDetail_Content">
      <p>{{ taskTitle }}</p>
      <div class="taskDetail_Content_search">
        <el-select
          multiple
          collapse-tags
          v-model="searchForm.saleMerchantIds"
          style="width: 300px"
          placeholder="关联商家"
          clearable
          @change="
            getOrgOptions();
            getData();
          "
        >
          <el-option
            v-for="item in merchantOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
        <el-cascader
          style="width: 180px"
          placeholder="请选择组织架构"
          v-model="searchForm.orgList"
          :options="orgOptions"
          :props="{ checkStrictly: true, value: 'id', label: 'name' }"
          clearable
          @change="getData()"
          :show-all-levels="false"
        ></el-cascader>
        <el-input
          v-if="btnType.code !== 2"
          v-model="searchForm.salesmanErpCodeOrName"
          style="width: 180px"
          placeholder="员工姓名/工号"
          clearable
          @clear="getData()"
          @blur="getData()"
          @keydown.enter.native="getData()"
        ></el-input>
        <el-date-picker
          v-if="!dateFormatTaskType.includes(type)"
          v-model="searchForm.month"
          type="monthrange"
          range-separator="至"
          start-placeholder="开始月份"
          end-placeholder="结束月份"
          value-format="yyyy-MM"
          :clearable="false"
          @change="handleMonthRange"
        >
        </el-date-picker>
        <el-date-picker
          v-if="dateFormatTaskType.includes(type)"
          v-model="searchForm.month"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          :clearable="false"
          @change="handleMonthRange"
        >
        </el-date-picker>
        <el-button type="primary" @click="getData">搜索</el-button>
        <el-button @click="reset">重置</el-button>
        <el-button type="primary" :loading="downloadLoading" @click="toDownload"
          >导出</el-button
        >
        <div v-if="type !== 1" class="btn_group">
          <div
            :class="btnType.code === 1 ? 'is_active' : ''"
            @click="chooseBtnType({ code: 1, label: 'STAFF' })"
          >
            人员
          </div>
          <div
            :class="btnType.code === 2 ? 'is_active' : ''"
            @click="chooseBtnType({ code: 2, label: 'DEPARTMENT' })"
          >
            部门
          </div>
          <div
            :class="btnType.code === 3 ? 'is_active' : ''"
            @click="chooseBtnType({ code: 3, label: 'DETAILS' })"
          >
            明细
          </div>
        </div>
      </div>
      <div class="taskDetail_Content_table">
        <table-pager
          ref="pager-table"
          :pageSize="10"
          :options="tableTitle"
          :data.sync="tableData"
          :remote-method="load"
          :showHide="false"
          :isNeedButton="false"
          :showSummary="true"
          :summaryMethod="summaryMethod"
        >
        </table-pager>
      </div>
    </div>
  </div>
</template>

<script>
import * as apiList from "@/api/taskManagement.js";
import { downloadFile2Url } from "@/utils/commons";
import tableTitleGroup from "./tableTitle.json";
import dayjs from "dayjs";
import big from "big.js";
export default {
  name: "taskDetail",
  components: {},
  props: {},
  data() {
    return {
      f: false, // 是否未调用getMerchant
      id: null, // taskId
      type: null, // 报表类型
      btnType: { code: 1, label: "STAFF" }, // 报表统计类型
      dateFormatTaskType: [0, 1, 3], // 年月日格式的任务类型，其余的为年月
      dateFormat: "YYYY-MM-DD", // 时间格式
      taskTitle: "",
      taskStartDate: "",
      taskEndDate: "",
      searchForm: {
        orgList: null,
        salesmanErpCodeOrName: "",
        month: null,
        saleMerchantIds: null,
      },
      tableTitle: [],
      tableData: [],
      orgOptions: [],
      merchantOptions: [],
      tableTitleList: tableTitleGroup.title,
      summaryList: tableTitleGroup.summary,
      excludesList: tableTitleGroup.excludes,
      downloadLoading: false, // 导出按钮loading
    };
  },
  computed: {},
  watch: {},
  created() {
    this.id = this.$route.query.id ?? null;
    this.type = Number(this.$route.query.type);
    this.dateFormat = this.dateFormatTaskType.includes(this.type) ? "YYYY-MM-DD" : "YYYY-MM";
    this.taskTitle = `${this.$route.query.taskTypeName ?? ""}  ${
      this.$route.query.taskTime ?? ""
    }`;
    this.taskStartDate = this.$route.query.taskTime.split("至")[0];
    this.taskEndDate = this.$route.query.taskTime.split("至")[1];
    this.searchForm.month = this.setMonthValue();
    this.chooseTableTitle();
    this.f = true;
  },
  mounted() {},
  methods: {
    // 日期初始化
    setMonthValue() {
      if (!this.taskStartDate) {
        this.searchForm.month = null;
        return;
      }
      let flag = this.dateFormatTaskType.includes(this.type);
      let formatFun = (date) => dayjs(date).format(this.dateFormat);
      let start = flag
        ? formatFun(dayjs(this.taskStartDate).startOf("month"))
        : formatFun(this.taskStartDate);
      let end = flag
        ? formatFun(dayjs(this.taskEndDate).endOf("month"))
        : formatFun(this.taskEndDate);

      return [start, end];
    },
    // 获取企业
    async getMerchant() {
      this.f = false;
      this.merchantOptions = [];
      let res = await apiList.getMerchantList({ saleMerchantIds: null, taskId: this.id });
      if (res.code === 0) {
        this.merchantOptions = res.data || [];
      }
    },
    // 获取组织架构
    async getOrgOptions() {
      if (this.searchForm.saleMerchantIds.length !== 1) {
        this.orgOptions = [];
        this.searchForm.orgList = null;
        return;
      }
      let temp = await apiList.getDepartmentId(
        this.searchForm.saleMerchantIds[0]
      );
      if (temp.code !== 0 || !temp.data.length) {
        this.orgOptions = [];
        return;
      }
      let id = temp.data[0].id;
      let res = await apiList.getDepartmentList(id);
      if (res.code === 0 && res.data[0]) {
        this.orgOptions = res.data[0]?.children ?? [];
      }
    },
    // 返回任务管理页面
    toBack() {
      this.$store.dispatch("tagsView/delView", this.$route);
      this.$router.push("/businessCentric/taskManagement");
    },
    getRequestData() {
      let temp = {
        taskId: this.id ?? null,
        erpCode: this.searchForm.salesmanErpCodeOrName ?? null,
        salesmanErpCodeOrName: this.searchForm.salesmanErpCodeOrName ?? "",
        saleMerchantIds: this.searchForm.saleMerchantIds ?? null,
        startTime: this.searchForm.month?.[0] ?? null,
        endTime: this.searchForm.month?.[1] ?? null,
        orgId: this.searchForm.orgList?.length
          ? this.searchForm.orgList[this.searchForm.orgList.length - 1]
          : null,
      };
      if(this.btnType.code === 2){
        // 以部门维度查询时，salesmanErpCodeOrName为空
        temp.erpCode = null;
        temp.salesmanErpCodeOrName = "";
      }
      if ([0, 1].includes(this.type)) {
        // 拜访任务/协访任务
        temp.taskVisitType = { code: this.btnType.label };
        temp.classification = this.btnType.code;
        temp.startYearMonth = temp.startTime;
        temp.endYearMonth = temp.endTime;
        !temp.saleMerchantIds?.length
          ? (temp.saleMerchantIds = this.merchantOptions.map((item) => item.id))
          : null;
        temp.departmentId = temp.orgId;
      }
      if (this.type === 3) {
        // 业绩任务
        temp.departId = this.searchForm.orgList?.length
          ? this.searchForm.orgList[this.searchForm.orgList.length - 1]
          : null;
        temp.taskQueryType = { code: this.btnType.label };
        !temp.saleMerchantIds?.length
          ? (temp.saleMerchantIds = this.merchantOptions.map((item) => item.id))
          : null;
      }
      if ([2, 4].includes(this.type)) {
        // 拓客任务/客户数发展目标
        temp.startBusinessYearMonth = this.searchForm.month?.[0] ?? null;
        temp.endBusinessYearMonth = this.searchForm.month?.[1] ?? null;
        temp.exportType = this.btnType.code;
      }
      return temp;
    },
    // 组件查询
    async load(params) {
      this.f ? await this.getMerchant() : null;
      try {
        let temp = {
          ...(params ?? {}),
          model: { ...this.getRequestData() },
        };
        let res = await apiList[
          `taskReportDetail${this.type}${
            [2, 4].includes(this.type) ? this.btnType.code : ""
          }`
        ](temp);
        return res || { data: {} };
      } catch (error) {
        console.log(error);
      }
    },
    // 查询
    getData() {
      this.$refs["pager-table"].doRefresh({
        page: 1,
        pageSize: 10,
      });
    },
    // 重置
    reset() {
      this.searchForm.orgList = null;
      this.searchForm.saleMerchantIds = null;
      this.searchForm.salesmanErpCodeOrName = "";
      this.searchForm.month = this.setMonthValue();
      this.getData();
    },
    // 下载
    async toDownload() {
      this.downloadLoading = true;
      try {
        let temp = { ...this.getRequestData() };
        let res = await apiList[`taskReportExport${this.type}`](temp);
        this.downloadLoading = false;
        let name =
          this.$route.query.taskTypeName +
          "_" +
          dayjs().format("YYYYMMDDHHmm") +
          ".xlsx";
        if (res?.code == 0 && res?.data) {
          if(res.data.exportExcelUrl){
            downloadFile2Url(res.data.exportExcelUrl, name);
          }else{
            this.$message.error('导出失败，请联系管理员');
          }
        }
      } catch (error) {
        this.downloadLoading = false;
      }
    },
    // 根据type选择表头
    chooseTableTitle() {
      this.tableTitle = this.tableTitleList[this.type][this.btnType.code];
    },
    chooseBtnType(type) {
      this.btnType.code = type.code;
      this.btnType.label = type.label;
      this.chooseTableTitle();
      this.getData();
    },
    // 日期限制
    handleMonthRange(value) {
      if (!value) {
        this.searchForm.month = value;
        return;
      }
      let f = false;
      let [start, end] = value; // 当前日期
      let [startD, endD] = this.setMonthValue(); // 日期范围
      let type = this.dateFormat === "YYYY-MM" ? "months" : "days";
      let judgeDate = (date) => {
        let dayjsDate = dayjs(date);
        if (dayjsDate.isBefore(dayjs(startD), type)) {
          f = true;
          return dayjs(startD).format(this.dateFormat);
        }
        if (dayjsDate.isAfter(dayjs(endD), type)) {
          f = true;
          return dayjs(endD).format(this.dateFormat);
        }
        return date;
      }
      start = judgeDate(start);
      end = judgeDate(end);
      f ? this.$message.error("筛选日期不得超出任务周期之外！") : null;
      this.$nextTick(() => {
        this.searchForm.month = [start, end];
        this.getData();
      });
    },
    // 自定义合计
    summaryMethod(param) {
      let { columns, data } = param;
      let sum = [];
      sum = columns.map((item, index) => {
        if (index === 0) {
          return "合计";
        }
        let res = "";
        if (this.summaryList[this.type].includes(item.property)) {
          let temp = 0;
          let start = dayjs(this.searchForm.month[0]);
          let end = dayjs(this.searchForm.month[1]);
          if (
            this.excludesList[this.type].includes(item.property) &&
            ((this.type !== 0 && !start.isSame(end, "month")) ||
              (this.type === 0 && !start.isSame(end, "day")))
          ) {
            // 跨月查询符合条件置空 || 拜访任务跨天查询符合条件置空
            res = "";
          } else if (this.summaryList.percent.includes(item.property)) {
            // 百分比计算
            temp = data.reduce((pre, cur) => {
              let num = cur[item.property]?.split("%")[0] ?? 0;
              return Number(new big(pre).plus(Number(num)));
            }, 0);
            res = temp
              ? Number(new big(temp).div(data.length)).toFixed(2) + "%"
              : "0%";
          } else {
            // 常规合计
            temp = data.reduce(
              (pre, cur) =>
                Number(new big(pre).plus(Number(cur[item.property]))),
              0
            );
            res = temp;
          }
        }
        return res;
      });
      return sum;
    },
  },
};
</script>

<style scoped lang="scss">
.taskDetail {
  background: #fff;
  // height: calc(100vh - 86px - 32px);
  display: flex;
  flex-direction: column;
  padding: 0 20px 20px 20px;
  .taskDetail_Header {
    height: 56px;
    min-height: 56px;
    border-bottom: 1px solid #eeeeee;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    .title {
      font-size: 18px;
    }
  }
  .taskDetail_Content {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 0;
    > * + * {
      margin-top: 20px;
    }
    .taskDetail_Content_search {
      > * + * {
        margin-left: 10px;
      }
      .btn_group {
        display: inline-flex;
        float: right;
        > div {
          padding: 10px 20px;
          border: 1px solid #0056e5;
          color: #0056e5;
          font-size: 14px;
          cursor: pointer;
          &:first-child {
            border-right: none;
            border-radius: 3px 0 0 3px;
          }
          &:last-child {
            border-left: none;
            border-radius: 0 3px 3px 0;
          }
        }
        .is_active {
          background: #0056e5;
          color: #fff;
        }
      }
    }
    .taskDetail_Content_table {
      flex: 1;
      height: 0;
    }
  }
}
</style>
