<template>
  <div class="archivesEditContent">
    <el-form :inline="true" label-width="140px" :model="query" ref="editForm" :rules="rules">
      <div class="item">
        <page-module-title title="基础信息"/>
        <div>
          <el-form-item class="formItem" prop="code" label="商家编码:">
            <el-input :disabled="true" clearable style="width: 200px" v-model="query.code"
                      placeholder="商家编码由系统生成"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="name" label="商家名称:" :rules="[
              { required: true, message: '请填写商家名称', trigger: 'blur' },
            ]">
            <el-input :disabled="isEdit" clearable style="width: 200px" v-model="query.name"
                      placeholder="请填写商家名称"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="identifyCode" label="商家识别码:" :rules="[
              {
                required: true,
                min:4,
                max:4,
                message: '请输入4位客户识别码，如： ZKYY',
                trigger: 'blur',
              },
            ]">
            <el-input :disabled="isEdit" clearable style="width: 200px" v-model="query.identifyCode"
                      placeholder="请填写商家识别码"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="socialCreditCode" label="社会统一信用代码:" :rules="[
              {
                required: true,
                message: '请填写社会统一信用代码',
                trigger: 'blur',
              },
            ]">
            <el-input :disabled="isEdit" clearable style="width: 200px" v-model="query.socialCreditCode"
                      placeholder="请填写社会统一信用代码"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="legalPerson" label="法定代表人:" :rules="[
              { required: false, message: '请填写法定代表人', trigger: 'blur' },
            ]">
            <el-input clearable :disabled="isEdit" style="width: 200px" v-model="query.legalPerson"
                      placeholder="请填写法定代表人"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="ceoName" label="负责人:" :rules="[
              { required: true, message: '请填写负责人', trigger: 'blur' },
            ]">
            <el-input clearable :disabled="isEdit" style="width: 200px" v-model="query.ceoName"
                      placeholder="请填写负责人"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="ceoMobile" label="负责人手机:">
            <el-input clearable :disabled="isEdit" style="width: 200px" v-model="query.ceoMobile"
                      placeholder="请填写负责人手机"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="regionId" label="所在区域:" :rules="[
              { required: true, message: '请选择所在区域', trigger: 'blur' },
            ]">
            <!-- <el-input :value="query.region" placeholder="请选择所在区域" disabled :style="isEdit?'width:200px;position:absolute;z-index:1000': 'display:none'"></el-input> -->
            <!-- <el-cascader ref="city" :disabled="isEdit" placeholder="请选择所在区域"  v-model="query.regionId" :props="props" @change="cityChange " clearable>
            </el-cascader> -->
            <el-cascader :disabled="isEdit" ref="city" v-model="query.regionId" style="width: 200px"
                         placeholder="请选择所在区域" :props="{ value: 'id', label: 'label'}" @change="cityChange"
                         :options="areasTree" clearable>
            </el-cascader>
          </el-form-item>
          <el-form-item class="formItem" prop="registerAddress" label="注册地址:" :rules="[
              { required: true, message: '请填写注册地址', trigger: 'blur' },
            ]">
            <el-input clearable :disabled="isEdit" style="width: 200px" v-model="query.registerAddress"
                      placeholder="请填写注册地址"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="registerCapital" label="注册资金:">
            <el-input :disabled="isEdit" clearable style="width: 200px" type="number" v-model="query.registerCapital"
                      placeholder="请填写注册资金"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="shopName" label="店铺名称:" min:rules="[
              { required: true, message: '请填写店铺名称', trigger: 'blur' },
            ]">
            <el-input :disabled="isEdit" clearable style="width: 200px" v-model="query.shopName"
                      placeholder="请填写店铺名称"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="managementStartDate" label="经营开始时间:" :rules="[
              {
                required: true,
                message: '请选择经营开始时间',
                trigger: 'blur',
              },
            ]">
            <el-date-picker :disabled="isEdit" v-model="query.managementStartDate" type="datetime" style="width: 200px"
                            placeholder="选择日期" value-format="yyyy-MM-dd HH:mm:ss">
            </el-date-picker>
          </el-form-item>

          <el-form-item class="formItem" prop="managementEndDate" label="经营结束时间:" :rules="[
              {
                required: true,
                message: '请选择经营结束时间',
                trigger: 'blur',
              },
            ]">
            <el-date-picker :disabled="isEdit" v-model="query.managementEndDate" type="datetime" style="width: 200px"
                            placeholder="选择日期" value-format="yyyy-MM-dd HH:mm:ss">
            </el-date-picker>
          </el-form-item>
        </div>
      </div>

      <div class="item" v-if="!this.row.id">
        <page-module-title title="账户信息"/>
        <div>
          <el-form-item class="formItem" prop="loginAccount" label="登录账号:" :rules="[
              { required: true, message: '请填写登录账号', trigger: 'blur' },
            ]">
            <el-input :disabled="isEdit" clearable style="width: 240px" v-model="query.loginAccount"
                      placeholder="请填写登录账号"></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="password" label="登录密码:">
            <el-input :disabled="isEdit" style="width: 240px" placeholder="请输入密码" v-model="query.password"
                      show-password></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="confirmPassword" label="确认密码:">
            <el-input :disabled="isEdit" style="width: 240px" placeholder="请输入密码" v-model="query.confirmPassword"
                      show-password></el-input>
          </el-form-item>
        </div>
      </div>

      <div class="item">
        <page-module-title title="经营范围"/>
        <!-- <el-form-item style="width:100%"> -->
        <el-row :gutter="12" v-for="(list,key) of businessScope" :key="key" style="padding-bottom: 20px;">
          <el-col :span="2">
            <div style="text-align: right;">{{key}}</div>
          </el-col>
          <el-col :span="22">
            <el-checkbox-group v-model="checkList" :disabled="isEdit" @change="checkListFun">
              <el-checkbox v-for="item in list" :key="item.id" :label="item.id">
                <span>{{item.childrenName}}</span>
              </el-checkbox>
            </el-checkbox-group>
          </el-col>
        </el-row>
        <!-- </el-form-item> -->
      </div>

      <div class="item">
        <page-module-title title="结算规则"/>
        <div>
          <el-form-item label-width="150px" label="佣金计算规则: " :rules="[{message: '请选择佣金计算规则'}]">
            <el-radio-group v-model="query.statementRule" :disabled="isEdit">
              <el-radio label="N">不收取佣金</el-radio>
              <el-radio label="Y">按订单金额比例</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div>
          <el-form-item v-if="query.statementRule=='Y'" label-width="150px" class="formItem" prop="orderAmountRate"
                        label="金额比例:">
            <el-input :disabled="isEdit" style="width: 200px" v-model="query.orderAmountRate" placeholder="请填写金额比例">
              <i slot="suffix">%</i>
            </el-input>
          </el-form-item>
        </div>
      </div>
      <div class="item">
        <page-module-title title="商家资质"/>
        <div>
          <template>
            <el-table :data="lisenceTableDate" style="width: 100%" border>
              <el-table-column prop="label" label="证件类型"></el-table-column>
              <el-table-column prop="licenseNumber" label="证件号">
                <template slot-scope="{ row }">
                  <el-input v-if="row.isEdit" placeholder="请输入证件号" v-model="row.licenseNumber"></el-input>
                  <span v-else>{{ row.licenseNumber }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="licenseEndTime" label="过期时间">
                <template slot-scope="{ row }">
                  <el-date-picker v-if="row.isEdit" v-model="row.licenseEndTime" type="datetime" style="width: 240px"
                                  placeholder="选择日期" value-format="yyyy-MM-dd HH:mm:ss"></el-date-picker>
                  <span v-else>{{ row.licenseEndTime }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="filePath" label="附件">
                <template slot-scope="{ row }">
                  <el-button v-if="!row.isEdit&&!row.filePath" type="text">查看示例图片</el-button>
                  <el-upload v-if="row.isEdit" :class="{hide: !row.isEdit}" ref="uploadlisence" :limit="3"
                             :file-list="row.filePathList" :action="$uploadUrl" :data="insertProgram"
                             :headers="headersProgram" list-type="picture-card" :on-remove="handleRemove"
                             :on-success="uploadSuccess" :before-upload="beforeUpload">
                    <i class="el-icon-plus"></i>
                  </el-upload>

                  <span v-else>
                    <img v-for="file in row.filePathList" :key="file.url" class="el-upload-list__item-thumbnail"
                         :src="file.url" alt="" style="contain:cover;width:40px;height:40px;margin-right:5px">
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template slot-scope="{ row }">
                  <el-button type="text" v-show="!row.isEdit" @click="editLisenceFun(row)"> 编 辑</el-button>
                  <el-button type="text" v-show="row.isEdit" @click="cancelLisenceEdit(row)"
                             style="color: rgb(127, 127, 127);"> 取 消
                  </el-button>
                  <el-button type="text" v-show="row.isEdit" @click="confirmLisenceEdit(row)"> 确 定</el-button>
                  <el-button type="text" v-show="!row.isEdit">预览</el-button>
                  <el-button type="text" v-show="!row.isEdit">下载</el-button>
                </template>
              </el-table-column>
            </el-table>
          </template>
        </div>
      </div>


      <div class="item">
        <page-module-title title="发票信息"/>
        <template>
          <div>
            <el-form-item class="formItem" prop="fpinvoiceType" label="发票类型:"
                          :rules="[{ required: true, message: '请选择发票类型', trigger: 'blur' },]">
              <el-radio v-model="isInvoiceInfo" label="VATINVOICE">普通发票</el-radio>
              <el-radio v-model="isInvoiceInfo" label="SPECIALINVOICE">增值税专用发票</el-radio>
            </el-form-item>
          </div>
          <div>
            <el-form-item class="formItem" prop="name" label="发票抬头:"
                          :rules="[{ required: true, message: '请填写发票抬头', trigger: 'blur' },]">
              <el-input :disabled="isEdit" clearable style="width: 400px" v-model.trim="query.invoiceInfo.name"
                        placeholder="请填写发票抬头"></el-input>
            </el-form-item>
          </div>
          <div>
            <el-form-item class="formItem" prop="taxNumber" label="税号:"
                          :rules="[{ required: true, message: '请填写税号', trigger: 'blur' },]">
              <el-input :disabled="isEdit" clearable style="width: 400px" v-model.trim="query.invoiceInfo.taxNumber"
                        placeholder="请填写税号"></el-input>

            </el-form-item>
          </div>

          <div v-if="isInvoiceInfo== 'SPECIALINVOICE'">
            <el-form-item class="formItem" prop="registerAddress" label="注册地址:"
                          :rules="[{ required: true, message: '请填写注册地址', trigger: 'blur' },]">
              <el-input :disabled="isEdit" clearable style="width: 400px"
                        v-model.trim="query.invoiceInfo.registerAddress" placeholder="请填写注册地址"></el-input>
            </el-form-item>
          </div>

          <div v-if="isInvoiceInfo== 'SPECIALINVOICE'">
            <el-form-item class="formItem" prop="registerMobile" label="注册电话:"
            >
              <el-input :disabled="isEdit" clearable style="width: 400px"
                        v-model.trim="query.invoiceInfo.registerMobile" placeholder="请填写注册电话"></el-input>
            </el-form-item>
          </div>

          <div v-if="isInvoiceInfo== 'SPECIALINVOICE'">
            <el-form-item class="formItem" prop="bankNumber" label="银行账号:"
                          :rules="[{ required: true, message: '请填写银行账号', trigger: 'blur' },]">
              <el-input :disabled="isEdit" clearable style="width: 400px" v-model.trim="query.invoiceInfo.bankNumber"
                        placeholder="请填写银行账号"></el-input>
            </el-form-item>
          </div>

          <div v-if="isInvoiceInfo=='SPECIALINVOICE'">
            <el-form-item class="formItem" prop="depositBank" label="开户银行:"
                          :rules="[{ required: true, message: '请填写开户银行', trigger: 'blur' },]">
              <el-input :disabled="isEdit" clearable style="width: 400px" v-model.trim="query.invoiceInfo.depositBank"
                        placeholder="请填写开户银行"></el-input>

            </el-form-item>
          </div>
        </template>
      </div>


      <div class="item">
        <page-module-title title="发货地址"/>
        <template>
          <el-table :data="addrtableDate" style="width: 100%" border>
            <el-table-column prop="name" label="收货人姓名" width="120">
              <template slot-scope="{ row }">
                <el-input v-if="row.isEdit" placeholder="请输入收货人" v-model="row.name"></el-input>
                <span v-else>{{row.name}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="mobilPhone" label="联系手机" width="170">
              <template slot-scope="{ row }">
                <el-input v-if="row.isEdit" placeholder="请输入联系手机" v-model="row.mobilPhone"></el-input>
                <span v-else>{{row.mobilPhone}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="fixedPhone" label="联系电话" width="170">
              <template slot-scope="{ row }">
                <el-input v-if="row.isEdit" placeholder="请输入联系电话" v-model="row.fixedPhone"></el-input>
                <span v-else>{{row.fixedPhone}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="region" label="收获区域" width="320">
              <template slot-scope="{ row }">
                <el-cascader :disabled="!row.isEdit" ref="addr" v-model="row.address" style="width: 240px"
                             placeholder="请选择所在区域" :props="{ value: 'id', label: 'label'}"
                             :options="areasTree" clearable>
                </el-cascader>
              </template>
            </el-table-column>
            <el-table-column prop="detailedAddress" label="详细地址" width="220">
              <template slot-scope="{ row }">
                <el-input v-if="row.isEdit" placeholder="请输入详细地址" v-model="row.detailedAddress"></el-input>
                <span v-else>{{row.detailedAddress}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="region" align="center" width="250" label="是否启用">
              <template slot-scope="{row}">
                <!-- <span v-if="row.isOpen.code">
                  <el-button type="text" v-if="row.isOpen.code == 'Y'"  @click="editAddr(row)" style="color:#2DAC0C">启用</el-button>
                  <el-button type="text" v-if="row.isOpen.code == 'N'"  @click="editAddr(row)" style="color:#f00">禁用</el-button>
                </span>
                <span v-else>
                  <span></span>
                  <el-button type="text" v-if="row.isOpen == 'Y'"  @click="editAddr(row)" style="color:#2DAC0C">启用</el-button>
                  <el-button type="text" v-if="row.isOpen == 'N'"  @click="editAddr(row)" style="color:#f00">禁用</el-button>
                </span> -->
                <span v-if="row.isEdit">
                  <el-radio v-model="row.isOpen" label="Y">启用</el-radio>
                  <el-radio v-model="row.isOpen" label="N">禁用</el-radio>
                </span>
                <span v-else> {{row.isOpen == 'Y'? '启用' : '禁用'}} </span>
              </template>
            </el-table-column>
            <el-table-column fixed="right" align="center" label="操作" width="130" class="itemAction">
              <template slot-scope="scope">
                <el-button type="text" @click="editAddrItem(scope.row, scope.$index)" v-show="!scope.row.isEdit">编辑
                </el-button>
                <el-button type="text" v-show="scope.row.isEdit" @click="canceladdrEdit(scope.row, scope.$index)"
                           style="color: rgb(127, 127, 127);"> 取 消
                </el-button>
                <el-button type="text" @click="confirmaddrEdit(scope.row, scope.$index)" v-show="scope.row.isEdit">确定
                </el-button>
                <el-button type="text" v-show="!scope.row.isEdit" @click="delAddr(scope.row, scope.$index)">删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div style="text-align: center;line-height:38px; border:1px solid #dfe6ec;border-top:0;">
            <el-button @click="address" type="text" slot="reference">+添加发货地址</el-button>
          </div>
        </template>
      </div>

      <div :model="query" style="
        position: absolute;
        top: 2vh;
        right: 15px;
        background: #fff;
        height: 38px;
      ">
        <template v-if="!query.id">
          <el-popover v-model="rejectFlag" placement="bottom-end" title="取消提醒" width="300" trigger="click">
            <el-button slot="reference">取消</el-button>
            确定取消编辑?取消后编辑内容将不被保存!
            <div style="text-align: right; margin: 0;padding-top:14px">
              <el-button size="mini" @click="rejectFlag = false">取消</el-button>
              <el-button type="primary" size="mini" @click="clearFun()">确定</el-button>
            </div>
          </el-popover>
          <el-button @click="edit('editForm')" type="primary">提交</el-button>
        </template>
        <template v-else>
          <template v-if="tabType == 'PENDING'">
            <el-popover v-model="rejectFlag" placement="bottom-end" title="驳回理由" width="300" trigger="click">
              <el-button slot="reference">驳回</el-button>
              <el-form ref="rejectform" :model="rejectText">
                <el-form-item prop="text"
                              :rules="[{required: true, message: '请填写驳回理由',trigger: 'blur'},{required: true,min:5, message: '请至少填写5个字！',trigger: 'blur'}]">
                  <el-input type="textarea" :rows="3" placeholder="请输入驳回理由" v-model="rejectText.text">
                  </el-input>
                </el-form-item>
              </el-form>
              <div style="text-align: right; margin: 0;padding-top:14px">
                <el-button size="mini" @click="rejectFlag = false">取消</el-button>
                <el-button type="primary" size="mini" @click="rejected">确定</el-button>
              </div>
            </el-popover>
            <el-button type="primary" @click="accepted">通 过</el-button>
          </template>
          <template v-else-if="tabType == 'REJECTED'">
            <el-popover v-model="rejectFlag" placement="bottom-end" title="驳回理由" width="300" trigger="click">
              <el-button slot="reference">驳回理由</el-button>
              {{query.rejectReason}}
              <div style="text-align: right; margin: 0;padding-top:14px">
                <el-button type="primary" size="mini" @click="rejectFlag = false">知道了</el-button>
              </div>
            </el-popover>
          </template>
          <template v-else-if="tabType == 'ACCEPTED'">
            <el-button v-if="query.publishStatus.code == 'Y'" v-show="isEdit" @click="frozen">冻结</el-button>
            <el-button @click="enable" v-else v-show="isEdit">启用</el-button>
            <el-button @click="isEdit = !isEdit" v-show="isEdit">编辑</el-button>
            <el-button @click="isEdit = !isEdit" v-show="!isEdit">取消</el-button>
            <el-button @click="edit('editForm')" v-show="!isEdit" type="primary">提交</el-button>
          </template>
        </template>
        <!-- <el-popover placement="top" width="250" v-model="query.showRefuteContent">
            <div style="line-height: 36px">驳回理由</div>
            <div>
              <el-input type="textarea" v-model="query.textarea" placeholder="" rows="4"></el-input>
            </div>
            <div style="text-align: right; margin: 0; padding-top: 10px">
              <el-button type="text" @click="query.showRefuteContent = false">取消</el-button>
              <el-button type="primary" @click="submitFun('ruleForm')">确认</el-button>
            </div>
          </el-popover> -->
      </div>
    </el-form>
  </div>
</template>
<script>
  import {getToken} from "@/utils/auth";
  import {checkNumPot2} from "@/utils/rules";
  import rule from "@/utils/rules";
  import {
    getitem,
    findSaleScope,
    // getAllParentCodeOrChildren,
    saleMerchant,
    enable,
    frozen,
    rejected,
    pdateSaleMerchantAcceptedById,
    listByLicenseBaseType,
    addrList,
    adaddr,
    deladdr,
    editaddr,
    areas,
  } from "@/api/businessCenter/businessList";
  import Tinymce from "@/components/Tinymce";
  import {add} from "@/api/businessCenter/businessList/index";
  // import { areas } from "@/api/enterprise";
  // import businessScope from "@/views/businessCenter/businessList/businessScope";
  export default {
    data() {
      var checkNumPot3 = (rule, value, callback) => {
        const reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
        if (!value) {
          return callback(new Error("请填写数字"));
        } else if (!reg.test(value)) {
          return callback(new Error("请填写正整数,最多2位小数"));
        } else if (value <= 0 || value > 100) {
          return callback(new Error("请填写0-100以内的数"));
        } else {
          callback();
        }
      };
      var validatePass2 = (rule, value, callback) => {
        if (value !== this.query.password) {
          callback(new Error("两次输入密码不一致!"));
        }
        callback();
      };
      return {
        arealist: [],
        isInvoiceInfo: "",
        props: {
          lazy: true,
          async lazyLoad(node, resolve) {
            const {level} = node;
            let id = node.data ? node.data.id : "";
            let res = await areas({parentId: id});
            let list = res.data;
            list.forEach((item) => {
              item.value = item.id;
              item.leaf = level >= 2;
            });
            resolve(list);
          },
        },
        rules: {
          password: [
            {required: true, message: "确认密码不能为空", trigger: "blur"},
            {
              required: true,
              min: 6,
              max: 20,
              message: "长度在 6 到 20 个字符",
              trigger: "blur",
            },
          ],
          confirmPassword: [
            {required: true, message: "确认密码不能为空", trigger: "blur"},
            {
              required: true,
              min: 6,
              max: 20,
              message: "长度在 6 到 20 个字符",
              trigger: "blur",
            },
            {validator: validatePass2, trigger: "blur"},
          ],
          registerCapital: [
            {validator: checkNumPot2, trigger: "blur", required: true},
          ],
          ceoMobile: rule.phone,
          orderAmountRate: [
            {validator: checkNumPot3, trigger: "blur", required: true},
          ],
        },
        addrRules: {
          mobilPhone: rule.phone,
        },
        rejectFlag: false,
        listLoading: false,
        // cityValue: [],
        isEdit: false,
        checkList: [],
        adAddressflag: false,
        businessScope: {},
        editBusiness: false,
        tableDate: [],
        businessTableLabel: [
          {
            name: "label",
            label: "证件类型",
            width: "320px",
          },
          {
            name: "licenseNumber",
            label: "证件号",
            width: "300px",
          },
          {
            name: "licenseEndTime",
            label: "过期时间",
            width: "300px",
          },
          {
            name: "filePath",
            label: "附件",
            width: "340px",
          },
        ],
        editCertificates: false,
        dialogImageUrl: "",
        isShowBigPic: false,
        query: {},
        statementRule: "N",
        baseType: [],
        // 预览Dialog
        imgDialogFlag: false,
        //  商家资质编辑Dialog
        editFunFlag: false,
        editFunForm: {},
        editFunItem: {},
        rejectText: {},
        // 地址
        adAddr: {},
        addrtableDate: [],
        addrSelectText: "",
        editLisenceItem: {},
        lisenceTableDate: [],
        insertProgram: {
          folderId: 0,
        },
        headersProgram: {
          token: getToken(),
          Authorization: "Basic YWRtaW5fdWk6YWRtaW5fdWlfc2VjcmV0",
        },
        lisenceClickItem: {},
        oldAddrtableDate: [],
        areasTree: [],
      };
    },
    props: {
      row: {
        type: Object,
      },
      visible: {
        type: Boolean,
        default: false,
        required: true,
      },
      isReload: {
        type: Boolean,
        default: false,
        required: true,
      },
      tabType: {
        type: String,
        required: true,
      },
    },
    components: {
      Tinymce,
    },
    methods: {
      address() {
        let obj = {
          cityId: "",
          countyId: "",
          detailedAddress: "",
          fixedPhone: "",
          isOpen: "Y",
          merchantId: this.row.id || "",
          isEdit: true,
          mobilPhone: "",
          name: "",
          provinceId: "",
          address: [],
        };
        this.addrtableDate.push(obj);
      },
      // 删除地址
      async delAddr(row, index) {
        if (row.id) {
          let {data} = await deladdr({ids: [row.id]});
          if (data) {
            this.$message.success("已删除该地址！");
            this.addrtableDate.forEach((item, index) => {
              if (item.id == row.id) {
                this.addrtableDate.splice(index, 1);
              }
            });
          }
        } else {
          this.addrtableDate.splice(index, 1);
        }
        this.oldAddrtableDate = this.addrtableDate.slice(0);
      },
      // 获取地址
      async getaddrList() {
        if (this.row.id) {
          let {data} = await addrList(this.row.id);
          data.forEach((item) => {
            item.isOpen = item.isOpen.code;
            item.isEdit = false;
            item.address = [item.provinceId, item.cityId, item.countyId];
          });
          this.addrtableDate = data;
          this.oldAddrtableDate = JSON.parse(JSON.stringify(data));
        }
      },
      canceladdrEdit(row, index) {
        if (this.oldAddrtableDate.length == 0) {
          this.addrtableDate = [];
          return;
        }
        this.addrtableDate = JSON.parse(JSON.stringify(this.oldAddrtableDate));
      },
      async confirmaddrEdit(row, index) {
        if (row.address.length == 0 || row.name == "" || row.mobilPhone == "") {
          this.addrtableDate = JSON.parse(JSON.stringify(this.oldAddrtableDate));
          return;
        }
        row.provinceId = row.address[0];
        row.cityId = row.address[1];
        row.countyId = row.address[2];
        row.isEdit = false;
        if (this.row.id) {
          if (row.id) {
            let {data} = await editaddr(row);
            if (data.id) {
              this.$message.success("修改地址成功");
              row = data;
            }
          } else {
            let {data} = await adaddr(row);
            if (data.id) {
              this.$message.success("添加地址成功");
              data.address = [data.provinceId, data.cityId, data.countyId];
              this.$set(this.addrtableDate, index, data);
            }
          }
        }
        this.oldAddrtableDate = JSON.parse(JSON.stringify(this.addrtableDate));
      },
      editAddrItem(row, index) {
        row.isEdit = true;
      },
      async getareas() {
        let {data} = await areas();
        this.areasTree = data;
      },
      editLisenceFun(row) {
        row.isEdit = true;
        this.editLisenceItem = JSON.parse(JSON.stringify(row));
      },
      cancelLisenceEdit(row) {
        this.editLisenceItem.isEdit = false;
        this.lisenceTableDate.forEach((item, index) => {
          if (item.licenseBaseId == row.licenseBaseId) {
            this.$set(this.lisenceTableDate, index, this.editLisenceItem);
          }
        });
      },
      confirmLisenceEdit(row) {
        row.isEdit = false;
        row.filePathList.forEach((item) => {
          item.url = item.response.data.url;
        });
      },
      getsrc(str) {
        if (!str) {
          return [];
        } else {
          let arr = str.split(",");
          let list = [];
          arr.forEach((item) => {
            let obj = {
              response: {
                data: {
                  url: "",
                },
              },
            };
            obj.response.data.url = item;
            obj.url = item;
            list.push(obj);
          });
          return list;
        }
      },
      uploadSuccess(res, file, fileList) {
        this.lisenceTableDate.forEach((item, index) => {
          if (item.licenseBaseId == this.editLisenceItem.licenseBaseId) {
            item.filePath = this.getFilePath(fileList);
            item.filePathList = fileList;
          }
        });
      },
      handleRemove(file, fileList) {
        this.lisenceTableDate.forEach((item, index) => {
          if (item.licenseBaseId == this.editLisenceItem.licenseBaseId) {
            item.filepath = this.getFilePath(fileList);
            item.filePathList = fileList;
          }
        });
      },
      getFilePath(fileList) {
        let str = "";
        fileList.forEach((item) => {
          let url = item.response.data.url;
          str += item.response.data.url + ",";
        });
        return str.substr(0, str.length - 1);
      },
      beforeUpload(file) {
        const isJPG = file.type === "image/jpeg";
        const isLt2M = file.size / 1024 / 1024 < 5;

        if (!isJPG) {
          this.$message.error("上传头像图片只能是 JPG 格式!");
        }
        if (!isLt2M) {
          this.$message.error("上传头像图片大小不能超过 2MB!");
        }
        return isJPG && isLt2M;
      },
      resetSucss(type, msg, tabType) {
        if (type) {
          this.$message.success(msg);
          this.$emit("update:visible", false);
          this.$emit("update:isReload", true);
          this.$emit("update:tabType", !tabType ? this.tabType : tabType);
        }
      },
      // 启用
      async enable() {
        let {data} = await enable(this.query.id);
        this.resetSucss(data, "已启用该销售商！");
      },
      // 冻结
      async frozen() {
        let {data} = await frozen(this.query.id);
        this.resetSucss(data, "已冻结该销售商！");
      },
      // 通过审核
      async accepted() {
        let {data} = await pdateSaleMerchantAcceptedById(this.query.id);
        this.resetSucss(data, "已通过改商家信息！", "ACCEPTED");
      },
      // 驳回
      rejected() {
        this.$refs.rejectform.validate(async (valid) => {
          if (valid) {
            let {data} = await rejected({
              id: this.query.id,
              rejectReason: this.rejectText.text,
            });
            this.resetSucss(data, "已通驳回商家信息！", "REJECTED");
          }
        });
      },
      checkListFun(e) {
        this.query.businessCategoryId = e;
      },
      cityChange(e) {
        this.query.provinceId = e[0];
        this.query.cityId = e[1];
        this.query.countyId = e[2];
        this.regionId = e;
      },
      getaddrSelectText(item) {
        if (!item) return;
        this.addrSelectText = item.label + this.addrSelectText;
        if (item.parent) {
          this.getaddrSelectText(item.parent);
        }
        return this.addrSelectText;
      },
      addrcityChange(e, row) {

        this.adAddr.region = this.getaddrSelectText(
          this.$refs.addr.getCheckedNodes()[0]
        );
        this.adAddr.provinceId = e[0];
        this.adAddr.cityId = e[1];
        this.adAddr.countyId = e[2];
      },

      adAddrFun() {
        this.$refs.addrform.validate(async (valid) => {
          if (valid) {
            if (this.query.id) {
              if (this.adAddr.merchantId) {
                let {data} = await editaddr(this.adaddr);
              } else {
                this.adAddr.merchantId = this.row.id;
                let {data} = await adaddr(this.adAddr);
                if (data.id) {
                  let obj = {
                    cityId: this.adAddr.cityId,
                    countyId: this.adAddr.countyId,
                    detailedAddress: this.adAddr.detailedAddress,
                    fixedPhone: this.adAddr.fixedPhone,
                    isOpen: {code: this.adAddr.isOpen},
                    mobilPhone: this.adAddr.mobilPhone,
                    name: this.adAddr.name,
                    provinceId: this.adAddr.provinceId,
                    region: this.adAddr.region,
                  };
                  this.addrtableDate.push(obj);
                  this.adAddr = {};
                  this.addrSelectText = "";
                  this.adAddressflag = false;
                }
              }
            } else {
              let obj = {
                cityId: this.adAddr.cityId,
                countyId: this.adAddr.countyId,
                detailedAddress: this.adAddr.detailedAddress,
                fixedPhone: this.adAddr.fixedPhone,
                isOpen: {code: this.adAddr.isOpen},
                mobilPhone: this.adAddr.mobilPhone,
                name: this.adAddr.name,
                provinceId: this.adAddr.provinceId,
                region: this.adAddr.region,
              };

              this.adAddr = {};
              this.adAddressflag = false;

            }
          }
        });
      },
      async getjyfw() {
        let {data} = await findSaleScope();
        let obj = {};
        data.forEach((item) => {
          if (obj[item.parentName + ":"]) {
            obj[item.parentName + ":"].push(item);
          } else {
            obj[item.parentName + ":"] = [];
            obj[item.parentName + ":"].push(item);
          }
        });
        this.businessScope = obj;
        // let member = await getAllParentCodeOrChildren({
        //   parentId:this.query.id,
        // });
      },
      async getitem() {
        this.listLoading = true;
        if (!this.row.id) {
          let tableDate = (await listByLicenseBaseType()).data;
          tableDate.forEach((item) => {
            let obj = {
              licenseBaseId: item.id,
              licenseEndTime: "",
              filePath: "",
              licenseNumber: "",
              label: item.name,
              isEdit: false,
              filePathList: this.getsrc(item.filePath),
            };
            this.lisenceTableDate.push(obj);
          });
          this.listLoading = false;
          this.query.deliveryAddressSaveDTOList = [];
          return;
        }
        let {data} = await getitem(this.row.id);
        if (data.businessCategoryList) {
          data.businessCategoryList.forEach((item) => {
            this.checkList.push(item.id);
          });
        }
        let list = (await listByLicenseBaseType()).data;
        list.forEach((item) => {
          let obj = {
            licenseBaseId: item.id,
            licenseEndTime: "",
            filePath: "",
            isForever: "",
            licenseNumber: "",
            label: item.name,
            isEdit: false,
          };
          data.merchantLicenses.find((ids) => {
            if (item.id == ids.licenseBaseId) {
              obj.licenseEndTime = ids.licenseEndTime;
              obj.filePath = ids.filePath;
              obj.filePathList = this.getsrc(ids.filePath);
              obj.licenseNumber = ids.licenseNumber;
              obj.label = item.name;
              obj.merchantId = ids.merchantId;
            }
          });
          this.lisenceTableDate.push(obj);
        });
        this.query = data;
        this.query.statementRule = data.statementRule.code;
        this.query.invoiceInfo = data.invoiceInfo;
        this.isInvoiceInfo = data.invoiceInfo.invoiceType.code
        this.query.region = this.row.region;
        this.query.regionId = [data.provinceId, data.cityId, data.county_id];
        this.query.deliveryAddressSaveDTOList = [];
        this.tableDate = list;
        this.listLoading = false;
      },
      async getlistByLicenseBaseType() {
        let {data} = await listByLicenseBaseType();
        this.list = data;
      },
      // 编辑资商家资质
      editFun(row) {
        this.editFunFlag = true;
        this.editFunItem = row;
      },
      async edit(content) {
        if (content == "editForm") {
          // if (this.query.id) {
          // this.$refs[content].validate(async (valid) => {
          //   if (valid) {
          //
          //   } else {
          //     return false;
          //   }
          // });
          if (this.query.id) {
            let arr = [];
            this.lisenceTableDate.forEach((item) => {
              if (item.filePath) {
                arr.push({
                  licenseBaseId: item.licenseBaseId,
                  licenseEndTime: item.licenseEndTime,
                  filePath: item.filePath,
                  licenseNumber: item.licenseNumber,
                });
              }
            });
            this.query.businessCategoryId = this.checkList;
            this.query.merchantLicenses = arr;
            let {data} = await saleMerchant(this.query);
          } else {
            let arr = [];
            this.lisenceTableDate.forEach((item) => {
              if (item.filePath) {
                arr.push({
                  licenseBaseId: item.licenseBaseId,
                  licenseEndTime: item.licenseEndTime,
                  filePath: item.filePath,
                  licenseNumber: item.licenseNumber,
                });
              }
            });
            this.addrtableDate.forEach((item) => {
              this.query.deliveryAddressSaveDTOList.push({
                cityId: item.address[1],
                provinceId: item.address[0],
                countyId: item.address[2],
                fixedPhone: item.fixedPhone,
                mobilPhone: item.mobilPhone,
                name: item.name,
                detailedAddress: item.detailedAddress,
              });
            });
            this.query.merchantLicenseSaveDTOListDTOList = arr;
            let data = await add(this.query);
          }
          this.$emit("update:visible", false);
          this.$emit("update:isReload", true);
          this.isEdit = false;
        }
      },
      detailFun() {
      },
      clearFun() {
        this.$emit("update:visible", false);
        this.$emit("update:row", {});
      },


      showBigPic(file) {
        this.isShowBigPic = true;
        this.dialogImageUrl = file.url;
      },
      editCertificatesFun(file) {
        this.editCertificates = true;
      },
      submitFun: function (archivesForm) {
        this.$refs[archivesForm].validate((valid) => {
          if (valid) {
            this.$emit("update:visible", false);
            this.$emit("update:isReload", true);
          } else {
            return false;
          }
        });
      },

    },
    created() {
      this.getareas();
      this.getjyfw();
      this.getitem();
      this.getaddrList();
    },
    mounted() {
      this.query = this.row;
      if (this.query.id) {
        this.isEdit = true;
      }
    },
    beforeDestroy() {
    },
  };
</script>
<style lang="less" scoped>
  .archivesEditContent {
    height: 70vh;
    overflow-y: scroll;
    margin: -30px -20px;
    border-top: 1px solid #ebecee;
    padding: 30px 20px;
    background: #fff;

    .item {
      width: 100%;
      margin-bottom: 30px;
      border-bottom: 1px solid #eeeeee;

      .title {
        padding: 0 0 15px;

        span {
          font-size: 16px;
          padding-left: 10px;
          border-left: 4px solid rgba(64, 158, 255, 1);
        }
      }
    }

    .pop_btn {
    }

    .uploadPic {
      padding-bottom: 100%;
      margin-bottom: -100%;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-wrap: wrap;

      > div {
        min-width: 100%;
        height: 25px;
      }
    }

    .productPicContent .text p {
      font-family: "PingFangSC-Regular", "PingFang SC", sans-serif,
      "PingFangSC-Regular", "PingFang SC", sans-serif-400;
      font-weight: 400;
      color: #aaaaaa;
      line-height: 20px;
      font-size: 13px;
      margin: 0;
    }

    .detailMsg {
      font-family: "PingFangSC-Regular", "PingFang SC", sans-serif,
      "PingFangSC-Regular", "PingFang SC", sans-serif-400;
      font-weight: 400;
      color: #aaaaaa;
      line-height: 20px;
      padding-bottom: 20px;
      font-size: 13px;
    }

    /deep/ .el-input.is-disabled .el-input__inner {
      color: #04060c;
      background-color: #eaf7e7;
    }

    /deep/ .is-checked .is-disabled .el-checkbox__inner {
      color: #2fa338;
      background-color: #1b9e38;
    }

    /deep/ .is-checked .el-checkbox__label {
      color: #04060c;
    }

    /deep/ .is-disabled .is-checked .el-radio__inner {
      background-color: #1b9e38;
    }

    /deep/ .is-disabled.is-checked.el-radio > .el-radio__label {
      color: #04060c;
    }

    /deep/ .el-col {
      line-height: 40px;
    }

    /deep/ .el-upload {
      width: 40px;
      height: 40px;
      position: relative;
    }

    /deep/ .el-upload > i {
      font-size: 16px;
      position: absolute;
      left: 50%;
      top: 50%;
      -webkit-transform: translateX(-50%) translateY(-50%);
      transform: translateX(-50%) translateY(-50%);
    }

    /deep/ .el-upload-list .el-upload-list__item {
      width: 40px;
      height: 40px;
    }

    /deep/ .hide .el-upload--picture-card {
      display: none;
    }
  }
</style>
