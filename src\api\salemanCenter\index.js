import request from '@/utils/request'
import requestExport from '@/utils/requestExport'
import qs from "qs"

export function fetchList(data) {
  return request({
    url: 'https://www.fastmock.site/mock/67b32875bdfe83ef01352fc9d4be4119/distribution/distributorBalance/page',
    method: 'post',
    data
  })
}

// 产品采集-采集数据列表
export function productCollectItem(data) {
  return request({
    url: '/crm/admin/productCollect/item/page',
    method: 'post',
    data
  })
}

// 产品采集-采集记录列表
export function productCollectPage(data) {
  return request({
    url: '/crm/admin/productCollect/page',
    method: 'post',
    data
  })
}

// 产品采集-采集记录详情
export function queryByCollectId(data) {
  return request({
    url: '/crm/admin/productCollect/queryByCollectId',
    method: 'post',
    data
  })
}

// 产品采集-历史采集详情
export function getHistoryByProductId(id) {
  return request({
    url: `/crm/admin/productCollect/getHistoryByProductId/${id}`,
    method: 'get'
  })
}

// 采集详情 - 列表
export function getHistoryByProductIdList(data) {
  return request({
    url: '/crm/merchant/productCollect/product/history/page',
    method: 'post',
    data
  })
}

// 辅助陈列
export function productShowingPage(data) {
  return request({
    url: '/crm/admin/productShowing/page',
    method: 'post',
    data
  })
}

// 辅助陈列详情
export function productShowingDetail(id) {
  return request({
    url: `/crm/admin/productShowing/get/${id}`,
    method: 'get'
  })
}

// 业务员管理-分页列表查询
export function salesmanPage(data) {
  return request({
    url: '/crm/admin/salesman/page',
    method: 'post',
    data
  })
}

// 业务员管理-查询
export function getsalesman(id) {
  return request({
    url: `/crm/admin/salesman/${id}`,
    method: 'get'
  })
}
// 业务员管理-查询签署合约
export function getSigningContract(data) {
  return request({
    url: `/crm/admin/salesman/getSigningContract?id=${data.id}`,
    method: 'post',
    data
  })
}

// 业务员管理-删除
export function deleteSalesman(id) {
  return request({
    url: '/crm/admin/salesman',
    params: {
      'id': id
    },
    method: 'delete'
  })
}

// 业务员管理-新增
export function addSalesman(data) {
  return request({
    url: '/crm/admin/salesman',
    method: 'post',
    data
  })
}

// 业务员管理-修改
export function editSalesman(data) {
  return request({
    url: '/crm/admin/salesman',
    method: 'put',
    data
  })
}

// 业务员管理 - 已绑定企业
export function bondedSaleMerchants(data) {
  return request({
    url: `/crm/admin/salesman/bonded/saleMerchants`,
    method: 'put',
    data
  })
}

// 业务员管理 - 未绑定企业
export function unboundSaleMerchants(data) {
  return request({
    url: `/crm/admin/salesman/unbound/saleMerchants`,
    method: 'put',
    data
  })
}

// 业务员管理 - 绑定企业
export function bindingSaleMerchant(data) {
  return request({
    url: '/crm/admin/salesman/binding/saleMerchant',
    method: 'put',
    data
  })
}

// 业务员管理 - 解绑企业
export function unbindSaleMerchant(data) {
  return request({
    url: '/crm/admin/salesman/unbind/saleMerchant',
    method: 'put',
    data
  })
}

// 业务员管理 - 已绑定的客户
export function bondedPurMerchants(data) {
  return request({
    url: `/crm/admin/salesman/bonded/purMerchants`,
    method: 'post',
    data
  })
}

// 业务员管理 - 未绑定的客户
export function unboundPurMerchants(data) {
  return request({
    url: `/crm/admin/salesman/unbound/purMerchants`,
    method: 'post',
    data
  })
}

// 业务员管理 - 绑定客户
export function bindingPurMerchants(data) {
  return request({
    url: `/crm/admin/salesman/binding/purMerchant`,
    method: 'put',
    data
  })
}

// 业务员管理 - 解绑客户
export function unbindPurMerchants(data) {
  return request({
    url: `/crm/admin/salesman/unbind/purMerchant`,
    method: 'put',
    data
  })
}

// 业务员管理
export function purMerchantsNumber(data) {
  return request({
    url: '/crm/admin/salesman/bound/purMerchants/number',
    method: 'post',
    data
  })
}

// 业务管理 - 店内培训 - 列表
export function purMerchantTraining(data) {
  return request({
    url: '/crm/admin/purMerchantTraining/page',
    method: 'post',
    data
  })
}

// 拍照打卡
export function punchSiginlist(data) {
  return request({
    url: '/crm/admin/punch/siginlist',
    method: 'post',
    data
  })
}
/**
 * 2022-07-11 离职人员需求 begin
 */

/**
 * 模糊搜索业务员列表
 */
export function fetchSalesmanListByName(data) {
  return request({
    url: `/crm/admin/salesman/getSalesManByName`,
    method: 'post',
    data
  })
}


/**
 * 批量转移客户
 */
export function postBulkTransferCustomersToSalesman(data) {
  return request({
    url: `/crm/admin/salesman/batchTransferSalesman`,
    method: 'post',
    data
  })
}

export const batchSendProtocol = data => {
  return request({
    url: `/crm/admin/salesman/batchSendProtocol`,
    method: 'post',
    data
  })
}

/**
 * 更新合同状态为通过
 */
export function updateContractStatusToAccept(data) {
  return request({
    url: '/crm/admin/purMerchantTraining/updateContractAcceptedByIds',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: qs.stringify(data)
  })
}

/**
 * 更新合同状态为驳回
 */
export function updateContractStatusToReject(data) {
  return request({
    url: '/crm/admin/purMerchantTraining/updateContractRejectedByIds',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: qs.stringify(data)
  })
}

/**
 * 获取合同列表excel文件流
 */
 export function fetchContractExcelBlob(data) {
  return requestExport({
    url: '/api/crm/merchant/purMerchantTraining/export/ContractInfo',
    method: 'post',
    data,
    headers: { responseType: 'blob' }
  })
}

/**
 * 获取任职商家列表
 */
export function getSaleMerchantList() {
  return request({
    url: '/merchant/admin/saleMerchant/saleMerchantList',
    method: 'post',
  })
}