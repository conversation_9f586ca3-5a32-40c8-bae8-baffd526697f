<template>
  <div>
    <el-dialog title="设置推广费" :visible.sync="dialogSetting" v-if="dialogSetting" :close-on-click-modal="false" width="30%" :before-close="closeDia">
      <el-form :model="settingForm" :rules="rules" ref="ruleForm" label-width="70px" class="demo-ruleForm">
        <el-form-item label="会员" prop="memberRatio">
            <div class="form_item">
                <el-input v-model="settingForm.memberRatio"></el-input>
                <span>%</span>
            </div>
        </el-form-item>
        <el-form-item label="非会员" prop="noMemberRatio">
            <div class="form_item">
                <el-input v-model="settingForm.noMemberRatio"></el-input>
                <span>%</span>
            </div>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogSetting = false">取 消</el-button>
        <el-button type="primary" @click="submitFrom">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>


<script>
  import {
    mapGetters
  } from 'vuex'
  export default {
    //import引入的组件
    components: {},
    props:{

    },

    data() {
        var ratioNum = (rule, value, callback) =>{
            if(!value){
                return callback(new Error('数值不能为空'));
            }
            if (isNaN(value)) {
                callback(new Error('请输入数字'));
            } else {
                if(value < 0 || value > 100){
                    callback(new Error('输入的数值不能小于0或者不能大于100'));
                } else {
                    callback();
                }
            }
        }
      return {
          settingForm:{
              memberRatio: '',
              noMemberRatio: ''
          },
          rules:{
            memberRatio:[
                { required: true, validator: ratioNum, trigger: 'blur'}
            ],
            noMemberRatio:[
                { required: true, validator: ratioNum, trigger: 'blur'}
            ]
        },
        dialogSetting: false
      }
    },
    //生命周期 - 挂载完成（可以访问DOM元素）
    mounted() {},

    computed: {
      ...mapGetters([
        'organizationInfo'
      ]),
    },

    created() {},

    filters: {},

    //方法集合
    methods: {
      openDia(currentRow) {
        this.dialogSetting = true;
        console.log('currentRow',currentRow);
        if(currentRow != null) {
            this.settingForm.memberRatio = currentRow.rowData.memberRatio || ''
            this.settingForm.noMemberRatio = currentRow.rowData.noMemberRatio || ''
        }
      },
      closeDia() {
        this.dialogSetting = false;
      },
      submitFrom(){
          this.$refs['ruleForm'].validate((valid)=>{
              if(valid) {
                  this.$emit('submitSetting', this.settingForm);
                  setTimeout(() => {
                      this.closeDia();
                  }, 500);
              }
          })
      }
    },

  }

</script>


<style lang='scss' scoped>
.form_item {
    display: flex;
    align-items: center;
    span {
        margin-left: 5px;
    }
}
</style>
